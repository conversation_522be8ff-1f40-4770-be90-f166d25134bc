package com.onedx.common.utils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StreamUtils;
import com.onedx.common.constants.values.LoginConst;
import com.onedx.common.exception.ErrorKey;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;

public class SecurityUtil {

    private static final String[] organizationNames = {
        "vnpt", "erp", "onesme"
    };

    private static final String[] consecutiveCharacters = {
        "abcd", "1234", "asdf", "qwerty"
    };

    private static final String[] commonPasswords = {
        "p@ssword", "bravo@123", "admin@123", "qwert@1111", "123456aa@", "123456ab@", "password", "vnpt@123", "vnpt@1234"
    };

    private static final Set<String> commonPasswordSet = Arrays.stream(commonPasswords).collect(Collectors.toSet());

    private static final Pattern patternPassword = Pattern.compile(LoginConst.PASSWORD_REGEX);

    public static XSSRequestWrapper handleXSS(ServletRequest req) throws IOException {
        byte[] requestBody = StreamUtils.copyToByteArray(req.getInputStream());
        String body = new String(requestBody, StandardCharsets.UTF_8);
        XSSRequestWrapper wrappedRequest = new XSSRequestWrapper((HttpServletRequest) req, requestBody);
        wrappedRequest.resetInputStream(stripXSS(body).getBytes());
        return wrappedRequest;
    }

    private static String stripXSS(String value) {
        if (value != null) {
            // NOTE: It's highly recommended to use the ESAPI library and uncomment the following line to
            // avoid encoded attacks.
            // value = ESAPI.encoder().canonicalize(value);

            // Avoid null characters
            value = value.replaceAll("", "");

            // Avoid anything between script tags
            Pattern scriptPattern = Pattern.compile("<script>(.*?)</script>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            // Avoid anything in a src='...' type of expression
            scriptPattern = Pattern.compile("src[\r\n]*=[\r\n]*\\'(.*?)\\'", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("src[\r\n]*=[\r\n]*\\\"(.*?)\\\"", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            // Remove any lonesome </script> tag
            scriptPattern = Pattern.compile("</script>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            // Remove any lonesome <script ...> tag
            scriptPattern = Pattern.compile("<script(.*?)>", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            // Remove any lonesome <img ...> tag
            scriptPattern = Pattern.compile("<img(.*?)>", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            // Avoid eval(...) expressions
            scriptPattern = Pattern.compile("eval\\((.*?)\\)", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            // Avoid expression(...) expressions
            scriptPattern = Pattern.compile("expression\\((.*?)\\)", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            // Avoid javascript:... expressions
            scriptPattern = Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            // Avoid vbscript:... expressions
            scriptPattern = Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            // Avoid onload= expressions
            scriptPattern = Pattern.compile("onload(.*?)=", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            // replace all special characters: <, >, (, ), ', "
            value = value.replaceAll("<", "");
            value = value.replaceAll(">", "");
            value = value.replaceAll("\\(", "");
            value = value.replaceAll("\\)", "");
            value = value.replaceAll("'", "");
        }
        return value;
    }

    public static void validatePassword(String password, ExceptionFactory exceptionFactory) {
        if (!patternPassword.matcher(password).matches()) {
            throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_FIELD, ErrorKey.User.PASSWORD, ErrorKey.User.PASSWORD);
        }
        if (!SecurityUtil.checkStrongPassword(password)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.WEEK_PASSWORD, ErrorKey.User.PASSWORD, ErrorKey.User.PASSWORD);
        }
    }

    /**
     * kiểm tra mật khẩu đủ mạnh
     * @return true nếu mật khẩu mạnh, false nếu mk yếu
     */
    public static boolean checkStrongPassword(String password) {
        if (StringUtils.isBlank(password)) {
            return false;
        }

        // kiểm tra 3 ký tự, số liên tiếp theo 2 chiều
        for (int i = 0; i <= password.length() - 3; i++) {
            char c1 = password.charAt(i);
            char c2 = password.charAt(i + 1);
            char c3 = password.charAt(i + 2);

            if ((Character.isAlphabetic(c1) && Character.isAlphabetic(c2) && Character.isAlphabetic(c3)) ||
                (Character.isDigit(c1) && Character.isDigit(c2) && Character.isDigit(c3))) {
                // Kiểm tra thứ tự tăng dần
                if ((c1 + 1 == c2) && (c2 + 1 == c3)) {
                    return false;
                }

                // Kiểm tra thứ tự giảm dần
                if ((c1 - 1 == c2) && (c2 - 1 == c3)) {
                    return false;
                }
            }
        }

        password = password.toLowerCase();
        for (String item : organizationNames) {
            if (password.contains(item)) {
                return false;
            }
        }
        for (String item : consecutiveCharacters) {
            if (password.contains(item)) {
                return false;
            }
        }

        return !commonPasswordSet.contains(password);
    }

    /**
     * kiểm tra chuỗi content có chứa các chuỗi con liên tiếp độ dài = length theo chiều xuôi hay ngược
     * @param content chuỗi cần kiểm tra
     * @param asc chiều xuôi hay chiều ngược
     * @param length độ dài chuỗi
     * @return true nếu thỏa mãn, false nếu không chưa chuỗi con liên tiếp ...
     */
    public static boolean checkConsecutive(String content, boolean asc, int length) {
        boolean result = false;
        if (content == null || content.isEmpty() || length <= 1) {
            return result;
        }
        int countConsecutive = 1;
        char previousChar = content.charAt(0);
        int increment = 1;
        if (!asc) {
            increment = -1;
        }
        for (int i = 1; i < content.length(); i++) {
            char currentChar = content.charAt(i);
            if ((previousChar + increment) == content.charAt(i)) {
                countConsecutive++;
                if (countConsecutive == length) {
                    return true;
                }
            } else {
                countConsecutive = 1;
            }
            previousChar = currentChar;
        }
        return result;
    }

    public static String[] parseWhiteListApis(List<String> whiteListApis) {
        return whiteListApis.stream().filter(api -> api.endsWith("|-1"))
            .map(api -> api.replace("|-1", "").replaceAll("\\{.*?}", "*")).distinct().toArray(String[]::new);
    }
}
