package com.onedx.common.dto.systemParams;

import com.onedx.common.constants.enums.otp.FormatTypeOTPEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.utils.ObjectUtil;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AuthenticateAccountConfigDTO {
    
    private Boolean enableOTPRegistry;
    
    private Boolean enableOTPChangePassword;
    
    private Boolean enableOTPLogin;
    
    private Boolean enableOTPSwitchAccount;
    
    private Integer lengthOTP;
    
    private String prefixOTP;
    
    private String suffixOTP;
    
    private FormatTypeOTPEnum formatTypeOTP;

    public Boolean getEnableOTPRegistry() {
        return ObjectUtil.getOrDefault(this.enableOTPRegistry, Boolean.FALSE);
    }

    public Boolean getEnableOTPChangePassword() {
        return ObjectUtil.getOrDefault(this.enableOTPChangePassword, Boolean.FALSE);
    }

    public Boolean getEnableOTPLogin() {
        return ObjectUtil.getOrDefault(this.enableOTPLogin, Boolean.FALSE);
    }

    public Boolean getEnableOTPSwitchAccount() {
        return ObjectUtil.getOrDefault(this.enableOTPSwitchAccount, Boolean.FALSE);
    }

    public String getPrefixOTP() {
        return ObjectUtil.getOrDefault(this.prefixOTP, CharacterConstant.BLANK);
    }

    public String getSuffixOTP() {
        return ObjectUtil.getOrDefault(this.suffixOTP, CharacterConstant.BLANK);
    }

    public FormatTypeOTPEnum getFormatTypeOTP() {
        return ObjectUtil.getOrDefault(this.formatTypeOTP, FormatTypeOTPEnum.LETTER_NUMBER);
    }
}
