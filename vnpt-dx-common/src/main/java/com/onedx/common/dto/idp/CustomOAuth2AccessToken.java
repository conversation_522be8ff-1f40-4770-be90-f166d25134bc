package com.onedx.common.dto.idp;

import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.springframework.security.oauth2.common.DefaultOAuth2RefreshToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import com.onedx.common.dto.oauth2.TokenIDPDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CustomOAuth2AccessToken implements OAuth2AccessToken, Serializable {

    private static final long serialVersionUID = 1L;
    @JsonProperty("access_token")
    String accessToken;
    @JsonProperty("token_type")
    String tokenType;
    @JsonProperty("refresh_token")
    String refreshToken;
    @JsonProperty("scope")
    String scope;
    @JsonProperty("expires_in")
    int expiresIn;
    Map<String, Object> additionalInformation = new HashMap<>();

    @Override
    public Map<String, Object> getAdditionalInformation() {
        return this.additionalInformation;
    }

    @Override
    public Set<String> getScope() {
        return Objects.nonNull(this.scope) ? new HashSet<>(Arrays.asList(this.scope.split(","))) : new HashSet<>();
    }

    @Override
    public OAuth2RefreshToken getRefreshToken() {
        return new DefaultOAuth2RefreshToken(this.refreshToken);
    }

    @Override
    public String getTokenType() {
        return this.tokenType;
    }

    @Override
    public boolean isExpired() {
        return this.getExpiration().before(new Date());
    }

    @Override
    public Date getExpiration() {
        return Date.from(Instant.ofEpochSecond(this.expiresIn));
    }

    @Override
    public int getExpiresIn() {
        return this.expiresIn;
    }

    @Override
    public String getValue() {
        return this.accessToken;
    }

    public CustomOAuth2AccessToken(TokenIDPDTO tokenIDPDTO) {
        this.accessToken = tokenIDPDTO.getAccessToken();
        this.tokenType = tokenIDPDTO.getTokenType();
        this.refreshToken = tokenIDPDTO.getRefreshToken();
        this.expiresIn = tokenIDPDTO.getExpiresIn();
        this.additionalInformation = new HashMap<>();
        this.scope = "openid";
    }
}
