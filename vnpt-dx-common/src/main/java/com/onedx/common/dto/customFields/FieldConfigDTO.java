package com.onedx.common.dto.customFields;

import java.io.Serializable;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldConfigDTO implements Serializable {

    @JsonProperty("label")
    String label;

    @JsonProperty("labelEnabled")
    Boolean labelEnabled;

    @JsonProperty("hintText")
    String hintText;

    @JsonProperty("isUnique")
    Boolean isUnique;

    @JsonProperty("mandatory")
    String mandatory;

    @JsonProperty("mandatoryCondition")
    MandatoryConditionConfigDTO mandatoryCondition;

    @JsonProperty("tooltipsEnabled")
    Boolean tooltipsEnabled;

    @JsonProperty("tooltipsContent")
    String tooltipsContent;

    @JsonProperty("noteEnabled")
    Boolean noteEnabled;

    @JsonProperty("noteContent")
    String noteContent;

    @JsonProperty("smeEnabled")
    Boolean smeEnabled;

    @JsonProperty("devEnabled")
    Boolean devEnabled;

    @JsonProperty("adminEnabled")
    Boolean adminEnabled;

    @JsonProperty("displayOnDetailPage")
    Boolean displayOnDetailPage;

    @JsonProperty("canEdit")
    Boolean canEdit = true;

    // Checkbox | Dropdown | MultiSelect | Radiobox
    @JsonProperty("lstValue")
    List<String> lstValue;

    @JsonProperty("lstValueSub")
    List<CustomFieldKeyAndValueDTO> lstValueSub;

    @JsonProperty("lstApiApply")
    List<Long> lstApiApply;

    // DatePicker | DateTime
    @JsonProperty("defaultValue")
    String defaultValue;

    // MultilineText | Number | SingleLineText
    @JsonProperty("pattern")
    String pattern;

    // type pattern
    @JsonProperty("lstPatternToken")
    List<Integer> lstPatternToken;

    // operand pattern
    @JsonProperty("patternCombination")
    Integer patternCombination;

    // MultilineText | Number | SingleLineText
    @JsonProperty("maxLength")
    Integer maxLength;

    // Timestamp
    @JsonProperty("displayFormat")
    String displayFormat;

    @JsonProperty("defaultDisplay")
    String defaultDisplay;

    // UploadFile | UploadImage | UploadVideo
    @JsonProperty("uploadType")
    String uploadType;

    // UploadFile | UploadImage | UploadVideo
    @JsonProperty("uploadExtension")
    List<String> uploadExtension;

    // UploadFile | UploadImage | UploadVideo
    @JsonProperty("uploadMaxSize")
    Integer uploadMaxSize;

    // UploadFile | UploadImage | UploadVideo
    @JsonProperty("uploadMaxFile")
    Integer uploadMaxFile;

    // UploadFile | UploadImage | UploadVideo
    @JsonProperty("getUploadMaxFile")
    Integer getUploadMaxFile;

    // Url
    @JsonProperty("urlMaxNum")
    Integer urlMaxNum;

    // Url
    @JsonProperty("urlPattern")
    List<String> urlPattern;

    @JsonProperty("other")
    private Object other;

    @JsonIgnore
    public boolean getUniqueId() {
        return this.isUnique != null ? this.isUnique : false;
    }
}
