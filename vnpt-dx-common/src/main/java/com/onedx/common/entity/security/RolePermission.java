package com.onedx.common.entity.security;

import lombok.*;
import org.hibernate.annotations.Immutable;

import javax.persistence.*;

/**
 *
 * <AUTHOR>
 * @since 14/06/2021
 */
@Entity
@Table(name = "roles_permissions")
@Immutable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RolePermission {

    /**
     *
     */
    private static final long serialVersionUID = 9067204826099995492L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "role_id")
    private Long roleId;

    @Column(name = "permission_id")
    private Long permissionId;

    @Column(name = "allow_edit")
    private Integer allowEdit;
}
