package com.onedx.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@AllArgsConstructor
@NoArgsConstructor
public class BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	@Column(name = "deleted_flag")
	protected Integer deletedFlag = 1;

	@Column(name = "created_by", updatable= false)
	protected Long createdBy;

	@Column(name = "created_at", updatable= false)
	@CreatedDate
	protected LocalDateTime createdAt;

	@Column(name = "modified_by", insertable = false)
	protected Long modifiedBy;

	@Column(name = "modified_at")
	protected LocalDateTime modifiedAt;
	
	@Column(name = "status")
	protected Integer status;
	
	@PrePersist
	public void prePersist() {
		this.createdAt = LocalDateTime.now();
	}

	@PreUpdate
	public void preUpdate() {
		this.modifiedAt = LocalDateTime.now();
	}
}
