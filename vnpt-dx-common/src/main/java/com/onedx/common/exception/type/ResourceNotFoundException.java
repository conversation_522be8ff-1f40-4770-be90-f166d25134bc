package com.onedx.common.exception.type;

import com.onedx.common.exception.BaseException;

/**
 * 404
 * Whren throw new this class, the error returned will contain an error code is 404
 */
/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 14/1/2021
 */
public class ResourceNotFoundException extends BaseException {
    /**
	 * 
	 */
	private static final long serialVersionUID = -5803702402883889153L;

	public ResourceNotFoundException(String title, String entityName, String field, String errorCode) {
        super(title, entityName, field, errorCode);
    }
}
