package com.onedx.common.constants.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * <AUTHOR> DangNDH
 * @version    : 1.0
 * 18/3/2021
 */
@Getter
public enum PortalType {
	ADMIN(1), DEV(2), SME(3), ONE_BSS(4), MOBILE_APP(5), IOT(6), UNSET(-1), ALL(-2);
	
	private final int type;
	
	PortalType(int type) {
		this.type = type;
	}

	private static final Map<Integer, PortalType> map = new HashMap<>();

	static {
		for (PortalType portalType : PortalType.values()) {
			map.put(portalType.type, portalType);
		}
	}

	public static PortalType valueOf(int value) {
		return map.get(value);
	}

	/**
	 * lấy ra PortalType theo name
	 */
	public static PortalType getByName(String portalTypeName) {
		for (PortalType portalType : PortalType.values()) {
			if (portalType.name().equals(portalTypeName)) {
				return portalType;
			}
		}

		return null;
	}

	public static String getPortalTypeStr(Integer portalType) {
		switch (portalType) {
			case 1:
				return "ADMIN";
			case 2:
				return "DEV";
			case 3:
				return "SME";
			case -2:
				return "ALL";
			default:
				return "UNSET";
		}
	}

    public int getValue() {
        return this.type;
    }
}
