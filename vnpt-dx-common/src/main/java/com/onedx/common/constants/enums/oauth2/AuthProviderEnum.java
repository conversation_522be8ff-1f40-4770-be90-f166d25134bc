package com.onedx.common.constants.enums.oauth2;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> HuyLD
 * @version : 1.0 16/03/2022
 */
public enum AuthProviderEnum {
    LOCAL(0),
    GOOGLE(1),
    FACEBOOK(2),
    PHONE(3),
    APPLE(4);

    public final int value;

    AuthProviderEnum(int value) {
        this.value = value;
    }

    private static final Map<Integer, AuthProviderEnum> map = new HashMap<>();

    static {
        for (AuthProviderEnum authProvider : AuthProviderEnum.values()) {
            map.put(authProvider.value, authProvider);
        }
    }

    public static Integer valueOfName(String text) {
        for (AuthProviderEnum b : AuthProviderEnum.values()) {
            if (b.name().equalsIgnoreCase(text)) {
                return b.value;
            }
        }
        return null;
    }

    public static AuthProviderEnum getValueOf(Integer value) {
        if (value == null) {
            return LOCAL;
        }
        return map.get(value);
    }
}
