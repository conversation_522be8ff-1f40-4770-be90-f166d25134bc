package com.onedx.common.constants.enums.integration.dhsxkd;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> HuyLD
 * @version : 1.0 02/08/2021
 */
public enum DHSXKDPaymentStatusEnum {
    PAID("Da thanh toan", "1"),
    WAITING("KH chua thanh toan", "0");
    public final String key;
    public final String value;

    DHSXKDPaymentStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private static final Map<String, DHSXKDPaymentStatusEnum> mapKey = new HashMap<>();
    private static final Map<String, DHSXKDPaymentStatusEnum> mapValue = new HashMap<>();

    static {
        for (DHSXKDPaymentStatusEnum status : DHSXKDPaymentStatusEnum.values()) {
            mapKey.put(status.key, status);
            mapValue.put(status.value, status);
        }
    }

    public static String getValueByKeyDHSXKD(String key) {
        if (Objects.isNull(key)) return null;
        for (DHSXKDPaymentStatusEnum test : DHSXKDPaymentStatusEnum.values()) {
            if (key.equals(test.key)) {
                return test.value;
            }
        }
        return null;
    }

    public static String getNameByKeyDHSXKD(String key) {
        if (Objects.isNull(key)) return null;
        for (DHSXKDPaymentStatusEnum test : DHSXKDPaymentStatusEnum.values()) {
            if (key.equals(test.key)) {
                return test.name();
            }
        }
        return null;
    }
}
