package com.onedx.common.constants.enums.migration;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;

/**
 @author: Huynhbq  Jun 07, 2022
 */
@Getter
public enum CreatedSourceMigrationEnum {
    ONE_SME(0),
    DHSXKD(1),
    DX_PLACE(2),
    THIRD_PARTY(3),
    BAN_KHCN(4),
    ONE_BSS(5),
    IOT_PORTAL(6),

    UNSET(-1);

    private final Integer value;

    CreatedSourceMigrationEnum(Integer value) {
        this.value = value;
    }

    private static final Map<Integer, CreatedSourceMigrationEnum> map = new HashMap<>();

    static {
        for (CreatedSourceMigrationEnum valueEnum : CreatedSourceMigrationEnum.values()) {
            map.put(valueEnum.value, valueEnum);
        }
    }

    public static CreatedSourceMigrationEnum valueOf(Integer value) {
        return Objects.isNull(value) ? map.get(0) : map.get(value);
    }
}
