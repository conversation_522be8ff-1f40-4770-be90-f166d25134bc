package com.controller.dashboard.region;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dto.revenue.admin.request.DashboardAdminBaseReqDTO;
import com.dto.revenue.admin.response.IRegionRevenueDetailDTO;
import com.dto.revenue.admin.response.IRegionRevenueResDTO;
import com.service.dashboard.PlatformRevenueAdminService;

@RestController
@RequestMapping("/api/admin-portal/dashboards/revenue/provinces")
public class RegionRevenueAdminController {
    @Autowired
    PlatformRevenueAdminService revenueAdminService;

    @PostMapping("/top-highest/chart")
    public List<IRegionRevenueResDTO> getOverviewTopRegionRevenue(
        @RequestBody DashboardAdminBaseReqDTO requestDTO
    ) {
        return revenueAdminService.getOverviewTopRegionRevenue(requestDTO);
    }


    @PostMapping("/top-lowest/chart")
    public List<IRegionRevenueResDTO> getOverviewBottomRegionRevenue(
        @RequestBody DashboardAdminBaseReqDTO requestDTO
    ) {
        return revenueAdminService.getOverviewBottomRegionRevenue(requestDTO);
    }

    @PostMapping("/{provinceId}/preview")
    public Page<IRegionRevenueDetailDTO> getPreviewRegionRevenue(
        @PathVariable Long provinceId,
        @RequestBody DashboardAdminBaseReqDTO requestDTO
    ) {
        return revenueAdminService.getPreviewRegionRevenue(provinceId, requestDTO);
    }

    @PostMapping("/{provinceId}/export")
    public InputStreamResource getExportRegionRevenue(
        @PathVariable Long provinceId,
        @RequestBody DashboardAdminBaseReqDTO requestDTO
    ) throws SQLException, IOException {
        return revenueAdminService.exportRegionRevenue(provinceId, requestDTO);
    }

}
