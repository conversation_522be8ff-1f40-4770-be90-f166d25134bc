package com.service.dashboardDevCombobox;

import java.util.List;
import org.springframework.data.domain.Slice;
import com.dto.dashboardDevCombobox.IComboboxPricingNameDTO;
import com.dto.dashboardDevCombobox.IComboboxProvinceDTO;
import com.dto.dashboardDevCombobox.IComboboxServiceProductDTO;
import com.dto.dashboardDevCombobox.LatestPricingNameReqDTO;

public interface DashboardDevComboboxService {
    
    Slice<IComboboxPricingNameDTO> getLatestPricingName(LatestPricingNameReqDTO reqDTO);

    List<IComboboxProvinceDTO> comboboxProvince(String provinceName, Integer size);

    List<IComboboxServiceProductDTO> comboboxServiceProduct(String serviceProductName, Integer size);
    
}
