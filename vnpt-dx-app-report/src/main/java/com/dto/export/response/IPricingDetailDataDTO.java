package com.dto.export.response;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

public interface IPricingDetailDataDTO {

    String getPricingName();

    String getPricingCode();

    String getStatus();

    String getServiceName();

    String getProvider();

    String getPricingPlan();

    String getPaymentCycle();

    String getNumberOfCycle();

    String getUnit();

    @Schema(description = "Đơn giá")
    String getPricingPrice();

    String getFreeQuantity();

    @Schema(description = "Từ - đến, đơn giá")
    String getFromToPrice();

    String getNumberOfTrial();

    BigDecimal getSetupFee();

    @Schema(description = "Thuế áp dụng cho gói cước")
    String getPricingTax();

    @Schema(description = "Thuế áp dụng phí thiết lập")
    String getSetupTax();

    @Schema(description = "Số lượng dịch vụ bổ sung")
    Integer getNumAddon();

    @Schema(description = "Dịch vụ bổ sung bắt buộc")
    String getManAddonName();

    @Schema(description = "Dịch vụ bổ sung lựa chọn")
    String getOptAddonName();

    Integer getNumUserUse();

    Integer getNumTrialSub();

    Integer getNumOfficalSub();

    Integer getNumWaitSub();

    Integer getNumRenewSub();

    Integer getNumCancelSub();

    Integer getNumReactSub();

    Integer getNumChangeSub();

    String getCreatedAt();

    String getUpdatedAt();

    String getCustomerType();
}
