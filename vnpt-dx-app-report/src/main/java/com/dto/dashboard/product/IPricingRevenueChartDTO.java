package com.dto.dashboard.product;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.util.DateUtil;

public interface IPricingRevenueChartDTO {

    Long getPricingDraftId();

    BigDecimal getAmountPreTax();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS, timezone = DateUtil.TIME_ZONE)
    Date getGroupTime();

    String getPricingName();

    String getDisplayTime();

}
