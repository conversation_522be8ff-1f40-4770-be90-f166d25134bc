package com.dto.dashboard;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;

public interface IOrderRevenueDTO {

    @Schema(description = "Tổng doanh thu đặt hàng")
    BigDecimal getTotalOrderRevenue();

    @Schema(description = "Doanh thu đặt hàng tháng này")
    BigDecimal getOrderRevenueThisMonth();

    @Schema(description = "Doanh thu đặt hàng cùng kỳ tháng trước")
    BigDecimal getOrderRevenueSamePeriodLastMonth();

    @Schema(description = "Doanh thu đặt hàng cùng kỳ 2 tháng trước")
    BigDecimal getOrderRevenueSamePeriodTwoMonthsAgo();

    @Schema(description = "Doanh thu đặt hàng cùng kỳ 3 tháng trước")
    BigDecimal getOrderRevenueSamePeriodThreeMonthsAgo();
}
