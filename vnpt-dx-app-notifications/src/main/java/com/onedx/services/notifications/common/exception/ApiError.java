/**
 * <AUTHOR> VienNN
 * @version : 1.0
 */

package com.onedx.services.notifications.common.exception;

import java.util.ArrayList;
import java.util.List;
import javax.validation.ConstraintViolation;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.validation.ObjectError;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiError {

    private String systemCode;

    @Schema(description = "Thoi gian xay ra loi")
    private Long timestamp;

    @Schema(description = "Thong bao loi", example = "User not found")
    private String message;

    @Schema(description = "Ten doi tuong xay ra loi", example = "User")
    private String object;

    @Schema(description = "Truong cua doi tuong xay ra loi", example = "ID")
    private String field;

    @Schema(description = "Ma loi ", example = "error.object.not.found")
    private String errorCode;

    @Schema(description = "Mô tả mã lỗi", example = "error.object.not.found")
    private String errorDescription;

    @Schema(description = "Trong truong hop nhieu truong xay ra loi ")
    private List<ApiSubError> fields;

    public ApiError(BaseException e) {
        this.setMessage(e.getTitle());
        this.setField(e.getField());
        this.setObject(e.getEntityName());
        this.setErrorCode(e.getErrorCode());
    }

    public void addSubError(ApiSubError subError) {
        if (fields == null) {
            fields = new ArrayList<>();
        }
        fields.add(subError);
    }

    public void addValidationError(String object, String field, Object rejectedValue, String message, String errorCode) {
        addSubError(new ApiValidationError(object, field, rejectedValue, message, errorCode));
    }

    public void addValidationError(String object, String message) {
        addSubError(new ApiValidationError(object, message));
    }

    public void addValidationError(ObjectError objectError) {
        this.addValidationError(objectError.getObjectName(), objectError.getDefaultMessage());
    }

    public void addValidationError(ConstraintViolation<?> cv, String message) {
        this.addValidationError(cv.getRootBeanClass().getSimpleName(), ((PathImpl) cv.getPropertyPath()).getLeafNode().asString(),
            cv.getInvalidValue(), message, cv.getMessageTemplate());
    }

    /**
     *
     */
    public void apiValidationSubError(String field, Object rejectedValue, String apiErrorCode, String message) {
        addSubError(new ApiValidationSubError(field, rejectedValue, apiErrorCode, message));
    }

    /**
     *
     */
    public void apiValidationBusinessSubError(String field, Object rejectedValue, String apiErrorCode, String message, boolean limitMaxRecord) {
        addSubError(new ApiValidationBusinessSubError(field, rejectedValue, apiErrorCode, message, limitMaxRecord));
    }

    public interface ApiSubError {

    }

    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    @Getter
    @Setter
    public static class ApiValidationError implements ApiSubError {

        private String object;
        private String field;
        private Object rejectedValue;
        private String message;
        private String apiErrorCode;

        ApiValidationError(String object, String message) {
            this.object = object;
            this.message = message;
        }
    }

    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    static class ApiValidationSubError implements ApiSubError {

        private String field;
        private Object rejectedValue;
        private String apiErrorCode;
        private String message;
    }

    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    @Getter
    @Setter
    static class ApiValidationBusinessSubError implements ApiSubError {

        private String field;
        private Object rejectedValue;
        private String apiErrorCode;
        private String message;
        private boolean limitMaxRecord;
    }
}
