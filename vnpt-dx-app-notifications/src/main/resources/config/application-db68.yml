spring:
  flyway:
    enabled: true
    url: *******************************************
    user: vnpt
    password: vnpt@654321
    schemas: dx10_notifications
  application:
    name: notifications

  master:
    datasource:
      driver: org.postgresql.Driver
      url: *******************************************
      schemas: dx10_notifications
      username: vnpt
      password: vnpt@654321
      maximum-pool-size: 20
      minimum-idle: 6
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      leak-detection-threshold: 200000
    hikari:
      data-source-properties:
        stringtype: unspecified
  slave:
    datasource:
      driver: org.postgresql.Driver
      url: *******************************************
      schemas: dx10_notifications
      username: vnpt
      password: vnpt@654321
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      leak-detection-threshold: 200000
    hikari:
      data-source-properties:
        stringtype: unspecified
  jpa:
    show-sql: true
    hibernate:
      # Hibernate_sequence' doesn't exist
      use-new-id-generator-mappings: false
      # Drop n create table, good for testing, comment this in production
      ddl-auto: none
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        default_schema: dx10_notifications
        format_sql: false
  redis:
    host: ************
    password: vnptredis@2021
    port: 6399
server:
  port: 9002
  servlet:
    context-path: /notifications
batch:
  active: false
async:
  thread-pool:
    core-thread: 5
    max-thread: 10
    queue-capacity: 100
kafka:
  prod-active: false
  bootstrap: ************:29092
  group-id: ONESME-NOTIFICATIONS
  timeout: 10000
  username: admin
  password: admin-secret