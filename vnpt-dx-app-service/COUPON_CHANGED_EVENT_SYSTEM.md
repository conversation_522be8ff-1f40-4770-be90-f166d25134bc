# Hệ thống xử lý Events theo cấu trúc chuẩn

## Tổng quan

Hệ thống này được thiết kế để xử lý các events theo cấu trúc metadata chuẩn từ bảng events. Khi có event x<PERSON>y ra, hệ thống sẽ:

1. Nhận event với metadata chuẩn theo từng loại
2. Parse metadata để lấy thông tin component
3. T<PERSON><PERSON> tất cả các packages có chứa component đó
4. Tính toán lại giá cho các packages đó
5. Cập nhật thông tin packages

## Các loại Events được hỗ trợ

### COUPON Events
- **COUPON_EXPIRED**: Chương trình khuyến mại hết hạn
- **COUPON_APPLY_EXCEED**: Chương trình khuyến mại hết số lượng áp dụng tối đa
- **COUPON_UPGRADED**: Chương trình khuyến mại đượ<PERSON> cập nh<PERSON> (có version mới)

### PRODUCT Events
- **PRICING_STATUS_CHANGED**: Trạng thái pricing thay đổi
- **PRICING_UPGRADED**: Gói dịch vụ được cập nhật (có version mới)
- **VARIANT_STATUS_CHANGED**: Trạng thái variant thay đổi
- **VARIANT_UPGRADED**: Biến thể sản phẩm được cập nhật (có version mới)
- **ADDON_STATUS_CHANGED**: Trạng thái addon thay đổi
- **ADDON_UPGRADED**: Addon sản phẩm được cập nhật (có version mới)

## Các thành phần chính

### 1. CouponEventMetadata
- **File**: `com.dto.events.CouponEventMetadata`
- **Mục đích**: Metadata cho các event liên quan đến Coupon
- **Thuộc tính**:
  - `couponId`: ID của coupon
  - `couponDraftId`: ID của coupon draft (chỉ có trong COUPON_UPGRADED)
  - `status`: Trạng thái (nếu có)

### 2. ProductEventMetadata
- **File**: `com.dto.events.ProductEventMetadata`
- **Mục đích**: Metadata cho các event liên quan đến Product
- **Thuộc tính**:
  - `pricingId`, `pricingDraftId`: Cho PRICING events
  - `variantId`, `variantDraftId`: Cho VARIANT events
  - `addonId`, `addonDraftId`: Cho ADDON events
  - `status`: Trạng thái (cho STATUS_CHANGED events)

### 3. PackageBundlingService.handleComponentChanged()
- **File**: `com.service.product_solutions.impl.PackageBundlingServiceImpl`
- **Mục đích**: Xử lý logic chính khi component thay đổi
- **Tham số**:
  - `componentId`: ID component bị thay đổi
  - `type`: Loại component (COUPON, ADDON, PRICING, VARIANT)

### 4. EventTask - Các handler methods
- **File**: `com.scheduled.batch.task.EventTask`
- **Mục đích**: Xử lý từng loại event cụ thể
- **Methods**:
  - `handleCouponExpiredEvent()`
  - `handleCouponApplyExceedEvent()`
  - `handleCouponUpgradedEvent()`
  - `handlePricingStatusChangedEvent()`
  - `handlePricingUpgradedEvent()`
  - `handleVariantStatusChangedEvent()`
  - `handleVariantUpgradedEvent()`
  - `handleAddonStatusChangedEvent()`
  - `handleAddonUpgradedEvent()`

## Cách sử dụng

### 1. Khi component thay đổi - gửi ID và type
```java
@Autowired
private CouponService couponService;

// Khi coupon thay đổi - chỉ cần gửi ID và type
Long couponId = 123L;
String type = "COUPON"; // COUPON, ADDON, PRICING, VARIANT

couponService.publishComponentChangedEvent(couponId, type);
```

### 2. Các loại component khác
```java
// Addon thay đổi
Long addonId = 456L;
couponService.publishComponentChangedEvent(addonId, "ADDON");

// Pricing thay đổi
Long pricingId = 789L;
couponService.publishComponentChangedEvent(pricingId, "PRICING");

// Variant thay đổi
Long variantId = 101L;
couponService.publishComponentChangedEvent(variantId, "VARIANT");
```

### 3. Xử lý trực tiếp (đồng bộ) với EventType
```java
@Autowired
private PackageBundlingService packageBundlingService;

// Xử lý coupon hết hạn - sẽ XÓA coupon khỏi packages
packageBundlingService.handleComponentChanged(couponId, "COUPON", "EXPIRED");

// Xử lý coupon cập nhật - sẽ TÍNH TOÁN LẠI giá packages
packageBundlingService.handleComponentChanged(couponId, "COUPON", "UPGRADED");
```

## Luồng xử lý chi tiết (đã đơn giản hóa)

### Bước 1: Publish Event
```
CouponService.publishComponentChangedEvent(componentId, type)
├── Tạo ComponentChangedEventMetadata với componentId và type
├── Lưu event vào database (status = 0)
└── Đăng ký publish ComponentChangedEvent sau commit
```

### Bước 2: Event Processing
```
Transaction commit thành công
├── ComponentChangedEvent được publish với eventId
├── EventTask.componentChangedEvent() nhận event
├── Lấy thông tin event từ database
├── Kiểm tra event type (COUPON_UPGRADED, ADDON_UPGRADED, etc.)
└── Gọi handleComponentChangedEvent(eventData, componentType)
```

### Bước 3: Handle Component Changed với EventType
```
PackageBundlingService.handleComponentChanged(componentId, type, eventType)
├── Tìm tất cả packages có chứa componentId theo type
└── Với mỗi package: gọi processPackageForComponentChange()
```

### Bước 4: Xử lý theo EventType
```
processPackageForComponentChange(packageId, componentId, type, eventType)
├── Nếu eventType = "EXPIRED" hoặc "APPLY_EXCEED"
│   └── removeCouponFromPackage() - XÓA coupon khỏi package
├── Nếu eventType = "UPGRADED" hoặc "STATUS_CHANGED"
│   └── recalculatePackagePrice() - TÍNH TOÁN LẠI giá package
└── Tạo event thông báo package đã được cập nhật
```

### Bước 5: Xóa Coupon hoặc Tính toán lại
```
removeCouponFromPackage() (cho EXPIRED/APPLY_EXCEED)
├── Xóa coupon khỏi package_item_promotions
├── Xóa coupon khỏi package_item_addon_promotions
└── Tính toán lại giá sau khi xóa

recalculatePackagePrice() (cho UPGRADED/STATUS_CHANGED)
├── Lấy thông tin package draft và items
├── Build lại PackageBundlingCreateDTO
├── Gọi calculatePackage() để tính toán lại giá
└── Lưu package draft đã cập nhật
```

### Bước 5: Hoàn thành
```
EventTask
├── Cập nhật status event thành 1 (thành công)
└── Hoặc -1 (nếu có lỗi)
```

## EventType và Hành động tương ứng

### Events XÓA COUPON khỏi packages
- **COUPON_EXPIRED** → eventType: "EXPIRED"
- **COUPON_APPLY_EXCEED** → eventType: "APPLY_EXCEED"

### Events TÍNH TOÁN LẠI GIÁ packages
- **COUPON_UPGRADED** → eventType: "UPGRADED"
- **PRICING_STATUS_CHANGED** → eventType: "STATUS_CHANGED"
- **PRICING_UPGRADED** → eventType: "UPGRADED"
- **VARIANT_STATUS_CHANGED** → eventType: "STATUS_CHANGED"
- **VARIANT_UPGRADED** → eventType: "UPGRADED"
- **ADDON_STATUS_CHANGED** → eventType: "STATUS_CHANGED"
- **ADDON_UPGRADED** → eventType: "UPGRADED"

## Lưu ý quan trọng

1. **Bất đồng bộ**: Hệ thống xử lý bất đồng bộ qua event system
2. **Transaction Safety**: Event chỉ được publish sau khi transaction commit thành công
3. **Error Handling**: Lỗi ở một package không ảnh hưởng đến xử lý các packages khác
4. **Logging**: Đầy đủ log để theo dõi quá trình xử lý
5. **Rollback**: Nếu có lỗi, event status sẽ được đánh dấu là -1

## Monitoring và Debug

### Kiểm tra events
```sql
-- Xem các events coupon changed
SELECT * FROM events 
WHERE type = 'COUPON_UPGRADED' 
ORDER BY created_at DESC;

-- Xem events chưa xử lý
SELECT * FROM events 
WHERE type = 'COUPON_UPGRADED' AND status = 0;

-- Xem events lỗi
SELECT * FROM events 
WHERE type = 'COUPON_UPGRADED' AND status = -1;
```

### Log files
- Tìm kiếm log với keyword: `"coupon thay đổi"`, `"CouponChangedEvent"`, `"handleCouponChanged"`

## Mở rộng

Hệ thống có thể dễ dàng mở rộng để xử lý các loại thay đổi khác:
- PRICING_UPGRADED
- ADDON_UPGRADED  
- VARIANT_UPGRADED

Chỉ cần thêm case mới trong `EventTask.handleEventByType()` và implement logic tương ứng.
