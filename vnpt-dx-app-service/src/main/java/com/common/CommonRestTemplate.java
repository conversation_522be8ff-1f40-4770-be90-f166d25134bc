package com.common;

import java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import com.dto.payment.TradingResultDTO;
import com.dto.payment.TransactionStatusVnptResDTO;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.utils.HttpRestUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0
 * 06/05/2021
 */
@Component
@Slf4j
public class CommonRestTemplate {

    @Autowired
    public HttpRestUtil httpUtil;

    public TransactionStatusVnptResDTO postResponse(String tokenString, String url,
        TradingResultDTO data) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(tokenString);
        try {
            return httpUtil.exchange(url, HttpMethod.POST, headers, data, TransactionStatusVnptResDTO.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BadRequestException(e.getMessage(), Resources.PAYMENT, ErrorKey.User.ID, null);
        }
    }

}
