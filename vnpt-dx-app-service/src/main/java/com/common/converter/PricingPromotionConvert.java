package com.common.converter;

import java.util.Objects;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import org.springframework.util.StringUtils;
import com.dto.pricing.PricingPromotionDTO;
import com.onedx.common.utils.ObjectMapperUtil;

@Converter
public class PricingPromotionConvert implements AttributeConverter<PricingPromotionDTO,String> {

    @Override
    public String convertToDatabaseColumn(PricingPromotionDTO pricingPromotionDTO) {
        if (Objects.isNull(pricingPromotionDTO)) return null;
        return ObjectMapperUtil.toJsonString(pricingPromotionDTO);
    }

    @Override
    public PricingPromotionDTO convertToEntityAttribute(String o) {
        if (Objects.isNull(o)) return null;
        PricingPromotionDTO res = new PricingPromotionDTO();
        if (!StringUtils.isEmpty(o)) {
            return ObjectMapperUtil.fromJson(o, PricingPromotionDTO.class);
        }
        return res;
    }
}
