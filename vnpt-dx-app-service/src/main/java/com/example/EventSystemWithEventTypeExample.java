package com.example;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.service.events.EventsService;
import com.service.product_solutions.PackageBundlingService;
import com.enums.EventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * Ví dụ về cách sử dụng hệ thống events với eventType để phân biệt hành động
 * 
 * EventType được sử dụng để xác định hành động cụ thể:
 * - EXPIRED/APPLY_EXCEED: Xóa coupon khỏi package
 * - UPGRADED/STATUS_CHANGED: Tính toán lại giá package
 */
@Component
@Slf4j
public class EventSystemWithEventTypeExample {

    @Autowired
    private EventsService eventsService;
    
    @Autowired
    private PackageBundlingService packageBundlingService;

    // ==================== COUPON EVENTS - XÓA COUPON ====================
    
    /**
     * Ví dụ 1: Coupon hết hạn - sẽ XÓA coupon khỏi packages
     */
    public void exampleCouponExpired() {
        Long couponId = 123L;
        
        log.info("Ví dụ 1: Coupon {} hết hạn - sẽ XÓA khỏi packages", couponId);
        
        // Tạo metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("couponId", couponId);
        
        // Lưu event - EventTask sẽ gọi handleComponentChanged(couponId, "COUPON", "EXPIRED")
        eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, metadata);
        
        log.info("Đã tạo COUPON_EXPIRED event - coupon sẽ bị XÓA khỏi packages");
    }

    /**
     * Ví dụ 2: Coupon hết số lượng áp dụng - sẽ XÓA coupon khỏi packages
     */
    public void exampleCouponApplyExceed() {
        Long couponId = 456L;
        
        log.info("Ví dụ 2: Coupon {} hết số lượng áp dụng - sẽ XÓA khỏi packages", couponId);
        
        // Tạo metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("couponId", couponId);
        
        // Lưu event - EventTask sẽ gọi handleComponentChanged(couponId, "COUPON", "APPLY_EXCEED")
        eventsService.saveEvent(EventTypeEnum.COUPON_APPLY_EXCEED, metadata);
        
        log.info("Đã tạo COUPON_APPLY_EXCEED event - coupon sẽ bị XÓA khỏi packages");
    }
    
    // ==================== COUPON EVENTS - TÍNH TOÁN LẠI GIÁ ====================
    
    /**
     * Ví dụ 3: Coupon được cập nhật - sẽ TÍNH TOÁN LẠI giá packages
     */
    public void exampleCouponUpgraded() {
        Long couponId = 789L;
        Long couponDraftId = 790L;
        
        log.info("Ví dụ 3: Coupon {} được cập nhật - sẽ TÍNH TOÁN LẠI giá packages", couponId);
        
        // Tạo metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("couponId", couponId);
        metadata.put("couponDraftId", couponDraftId);
        
        // Lưu event - EventTask sẽ gọi handleComponentChanged(couponId, "COUPON", "UPGRADED")
        eventsService.saveEvent(EventTypeEnum.COUPON_UPGRADED, metadata);
        
        log.info("Đã tạo COUPON_UPGRADED event - packages sẽ được TÍNH TOÁN LẠI giá");
    }
    
    // ==================== PRODUCT EVENTS ====================
    
    /**
     * Ví dụ 4: Pricing thay đổi trạng thái - sẽ TÍNH TOÁN LẠI giá packages
     */
    public void examplePricingStatusChanged() {
        Long pricingId = 101L;
        Long pricingDraftId = 102L;
        String status = "INACTIVE";
        
        log.info("Ví dụ 4: Pricing {} thay đổi trạng thái - sẽ TÍNH TOÁN LẠI giá packages", pricingId);
        
        // Tạo metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("pricingId", pricingId);
        metadata.put("pricingDraftId", pricingDraftId);
        metadata.put("status", status);
        
        // Lưu event - EventTask sẽ gọi handleComponentChanged(pricingId, "PRICING", "STATUS_CHANGED")
        eventsService.saveEvent(EventTypeEnum.PRICING_STATUS_CHANGED, metadata);
        
        log.info("Đã tạo PRICING_STATUS_CHANGED event - packages sẽ được TÍNH TOÁN LẠI giá");
    }

    /**
     * Ví dụ 5: Addon được cập nhật - sẽ TÍNH TOÁN LẠI giá packages
     */
    public void exampleAddonUpgraded() {
        Long addonId = 201L;
        Long addonDraftId = 202L;
        
        log.info("Ví dụ 5: Addon {} được cập nhật - sẽ TÍNH TOÁN LẠI giá packages", addonId);
        
        // Tạo metadata
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("addonId", addonId);
        metadata.put("addonDraftId", addonDraftId);
        
        // Lưu event - EventTask sẽ gọi handleComponentChanged(addonId, "ADDON", "UPGRADED")
        eventsService.saveEvent(EventTypeEnum.ADDON_UPGRADED, metadata);
        
        log.info("Đã tạo ADDON_UPGRADED event - packages sẽ được TÍNH TOÁN LẠI giá");
    }

    // ==================== XỬ LÝ TRỰC TIẾP ====================
    
    /**
     * Ví dụ 6: Xử lý trực tiếp với eventType
     */
    public void exampleDirectProcessingWithEventType() {
        Long couponId = 999L;
        
        log.info("Ví dụ 6: Xử lý trực tiếp với eventType");
        
        try {
            // Xử lý coupon hết hạn - sẽ XÓA coupon khỏi packages
            packageBundlingService.handleComponentChanged(couponId, "COUPON", "EXPIRED");
            log.info("Đã XÓA coupon {} khỏi packages do hết hạn", couponId);
            
            // Xử lý coupon cập nhật - sẽ TÍNH TOÁN LẠI giá packages
            packageBundlingService.handleComponentChanged(couponId, "COUPON", "UPGRADED");
            log.info("Đã TÍNH TOÁN LẠI giá packages do coupon {} cập nhật", couponId);
            
        } catch (Exception e) {
            log.error("Lỗi khi xử lý trực tiếp: {}", e.getMessage(), e);
        }
    }

    /**
     * Ví dụ 7: So sánh các eventType khác nhau
     */
    public void exampleCompareEventTypes() {
        Long couponId = 888L;
        
        log.info("Ví dụ 7: So sánh các eventType khác nhau cho cùng một coupon");
        
        // EXPIRED - Xóa coupon khỏi packages
        Map<String, Object> expiredMetadata = new HashMap<>();
        expiredMetadata.put("couponId", couponId);
        eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, expiredMetadata);
        log.info("COUPON_EXPIRED: Coupon {} sẽ bị XÓA khỏi packages", couponId);
        
        // UPGRADED - Tính toán lại giá packages
        Map<String, Object> upgradedMetadata = new HashMap<>();
        upgradedMetadata.put("couponId", couponId);
        upgradedMetadata.put("couponDraftId", couponId + 1);
        eventsService.saveEvent(EventTypeEnum.COUPON_UPGRADED, upgradedMetadata);
        log.info("COUPON_UPGRADED: Packages chứa coupon {} sẽ được TÍNH TOÁN LẠI giá", couponId);
    }
}

/**
 * Tóm tắt EventType và hành động tương ứng:
 * 
 * 1. EVENTS XÓA COUPON (processPackageForComponentChange -> removeCouponFromPackage):
 *    - COUPON_EXPIRED (eventType: "EXPIRED")
 *    - COUPON_APPLY_EXCEED (eventType: "APPLY_EXCEED")
 *    
 * 2. EVENTS TÍNH TOÁN LẠI GIÁ (processPackageForComponentChange -> recalculatePackagePrice):
 *    - COUPON_UPGRADED (eventType: "UPGRADED")
 *    - PRICING_STATUS_CHANGED (eventType: "STATUS_CHANGED")
 *    - PRICING_UPGRADED (eventType: "UPGRADED")
 *    - VARIANT_STATUS_CHANGED (eventType: "STATUS_CHANGED")
 *    - VARIANT_UPGRADED (eventType: "UPGRADED")
 *    - ADDON_STATUS_CHANGED (eventType: "STATUS_CHANGED")
 *    - ADDON_UPGRADED (eventType: "UPGRADED")
 * 
 * 3. LUỒNG XỬ LÝ:
 *    Event → EventTask → handleComponentChanged(componentId, type, eventType) 
 *    → processPackageForComponentChange() 
 *    → removeCouponFromPackage() HOẶC recalculatePackagePrice()
 * 
 * 4. ƯU ĐIỂM:
 *    - Phân biệt rõ ràng hành động: xóa vs tính toán lại
 *    - Linh hoạt xử lý theo từng loại event
 *    - Dễ mở rộng cho các eventType mới
 *    - Log chi tiết để theo dõi
 */
