package com.example;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.service.coupon.CouponService;
import com.service.product_solutions.PackageBundlingService;
import com.service.events.EventsService;
import com.dto.events.CouponEventMetadata;
import com.dto.events.ProductEventMetadata;
import com.enums.EventTypeEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * V<PERSON> dụ về cách sử dụng hệ thống xử lý events theo cấu trúc chuẩn
 *
 * Hệ thống hỗ trợ các loại events:
 * - COUPON: COUPON_EXPIRED, COUPON_APPLY_EXCEED, COUPON_UPGRADED
 * - PRODUCT: PRICING_STATUS_CHANGED, PRICING_UPGRADED, VARIANT_STATUS_CHANGED,
 *           VARIANT_UPGRADED, ADDON_STATUS_CHANGED, ADDON_UPGRADED
 */
@Component
@Slf4j
public class EventSystemUsageExample {

    @Autowired
    private EventsService eventsService;

    @Autowired
    private PackageBundlingService packageBundlingService;

    // ==================== COUPON EVENTS ====================

    /**
     * Ví dụ 1: Coupon hết hạn
     */
    public void exampleCouponExpired() {
        Long couponId = 123L;

        log.info("Ví dụ 1: Coupon {} hết hạn", couponId);

        // Tạo metadata cho coupon expired
        CouponEventMetadata metadata = new CouponEventMetadata(couponId);

        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, metadata);

        log.info("Đã tạo COUPON_EXPIRED event cho couponId: {}", couponId);
    }

    /**
     * Ví dụ 2: Coupon hết số lượng áp dụng
     */
    public void exampleCouponApplyExceed() {
        Long couponId = 456L;

        log.info("Ví dụ 2: Coupon {} hết số lượng áp dụng", couponId);

        // Tạo metadata cho coupon apply exceed
        CouponEventMetadata metadata = new CouponEventMetadata(couponId);

        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.COUPON_APPLY_EXCEED, metadata);

        log.info("Đã tạo COUPON_APPLY_EXCEED event cho couponId: {}", couponId);
    }

    /**
     * Ví dụ 3: Coupon được cập nhật
     */
    public void exampleCouponUpgraded() {
        Long couponId = 789L;
        Long couponDraftId = 790L;

        log.info("Ví dụ 3: Coupon {} được cập nhật với draft {}", couponId, couponDraftId);

        // Tạo metadata cho coupon upgraded
        CouponEventMetadata metadata = new CouponEventMetadata(couponId, couponDraftId);

        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.COUPON_UPGRADED, metadata);

        log.info("Đã tạo COUPON_UPGRADED event cho couponId: {}, couponDraftId: {}", couponId, couponDraftId);
    }

    // ==================== PRODUCT EVENTS ====================

    /**
     * Ví dụ 4: Pricing thay đổi trạng thái
     */
    public void examplePricingStatusChanged() {
        Long pricingId = 101L;
        Long pricingDraftId = 102L;
        String status = "INACTIVE";

        log.info("Ví dụ 4: Pricing {} thay đổi trạng thái thành {}", pricingId, status);

        // Tạo metadata cho pricing status changed
        ProductEventMetadata metadata = new ProductEventMetadata(pricingId, pricingDraftId, status);

        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.PRICING_STATUS_CHANGED, metadata);

        log.info("Đã tạo PRICING_STATUS_CHANGED event cho pricingId: {}", pricingId);
    }

    /**
     * Ví dụ 5: Addon được cập nhật
     */
    public void exampleAddonUpgraded() {
        Long addonId = 201L;
        Long addonDraftId = 202L;

        log.info("Ví dụ 5: Addon {} được cập nhật với draft {}", addonId, addonDraftId);

        // Tạo metadata cho addon upgraded
        ProductEventMetadata metadata = ProductEventMetadata.forAddon(addonId, addonDraftId, null);

        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.ADDON_UPGRADED, metadata);

        log.info("Đã tạo ADDON_UPGRADED event cho addonId: {}", addonId);
    }

    /**
     * Ví dụ 6: Xử lý trực tiếp không qua event system (đồng bộ)
     */
    public void exampleDirectProcessing() {
        Long componentId = 789L;
        String type = "PRICING";

        log.info("Ví dụ 6: Xử lý trực tiếp component thay đổi");

        try {
            // Gọi trực tiếp method xử lý (đồng bộ) - chỉ cần ID và type
            packageBundlingService.handleComponentChanged(componentId, type);
            log.info("Đã xử lý thành công component thay đổi");
        } catch (Exception e) {
            log.error("Lỗi khi xử lý component thay đổi: {}", e.getMessage(), e);
        }
    }
}

/**
 * Luồng xử lý chi tiết theo cấu trúc chuẩn:
 *
 * 1. Tạo event với metadata chuẩn:
 *    - Tạo metadata theo cấu trúc: CouponEventMetadata hoặc ProductEventMetadata
 *    - Lưu event vào bảng events với EventTypeEnum tương ứng và status = 0
 *    - ComponentChangedEvent được publish với eventId
 *
 * 2. EventTask.componentChangedEvent() nhận event:
 *    - Lấy thông tin event từ database theo eventId
 *    - Kiểm tra event type và gọi handler tương ứng:
 *      + COUPON_EXPIRED → handleCouponExpiredEvent()
 *      + COUPON_APPLY_EXCEED → handleCouponApplyExceedEvent()
 *      + COUPON_UPGRADED → handleCouponUpgradedEvent()
 *      + PRICING_STATUS_CHANGED → handlePricingStatusChangedEvent()
 *      + PRICING_UPGRADED → handlePricingUpgradedEvent()
 *      + VARIANT_STATUS_CHANGED → handleVariantStatusChangedEvent()
 *      + VARIANT_UPGRADED → handleVariantUpgradedEvent()
 *      + ADDON_STATUS_CHANGED → handleAddonStatusChangedEvent()
 *      + ADDON_UPGRADED → handleAddonUpgradedEvent()
 *
 * 3. Trong mỗi handler:
 *    - Parse metadata theo class tương ứng (CouponEventMetadata/ProductEventMetadata)
 *    - Lấy componentId từ metadata (couponId, pricingId, variantId, addonId)
 *    - Gọi packageBundlingService.handleComponentChanged(componentId, type)
 *
 * 4. Trong packageBundlingService.handleComponentChanged():
 *    - Tìm tất cả packages có chứa componentId theo type
 *    - Với mỗi package: gọi recalculatePackagePrice()
 *
 * 5. Trong recalculatePackagePrice():
 *    - Lấy thông tin package draft và package items
 *    - Build lại PackageBundlingCreateDTO từ dữ liệu hiện tại
 *    - Gọi calculatePackage() để tính toán lại giá
 *    - Lưu package draft đã cập nhật
 *
 * 6. Cuối cùng:
 *    - Cập nhật status của event thành 1 (đã xử lý thành công)
 *    - Hoặc -1 nếu có lỗi xảy ra
 *
 * Ưu điểm của cấu trúc mới:
 * - Metadata chuẩn theo từng loại event
 * - Xử lý riêng biệt cho từng event type
 * - Dễ mở rộng và maintain
 * - Tuân thủ cấu trúc events hiện tại của hệ thống
 */
