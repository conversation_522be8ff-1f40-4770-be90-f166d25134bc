package com.example;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.service.events.EventsService;
import com.enums.EventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * Ví dụ về cách sử dụng hệ thống events với JSON metadata trực tiếp
 * Không cần tạo object metadata, chỉ cần tạo Map hoặc JSON string
 */
@Component
@Slf4j
public class EventSystemWithJsonUsageExample {

    @Autowired
    private EventsService eventsService;

    // ==================== COUPON EVENTS ====================
    
    /**
     * Ví dụ 1: Coupon hết hạn - sử dụng Map
     */
    public void exampleCouponExpiredWithMap() {
        Long couponId = 123L;
        
        log.info("Ví dụ 1: Coupon {} hết hạn - sử dụng Map", couponId);
        
        // Tạo metadata dưới dạng Map
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("couponId", couponId);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, metadata);
        
        log.info("Đã tạo COUPON_EXPIRED event với Map metadata");
    }

    /**
     * Ví dụ 2: Coupon hết số lượng áp dụng - sử dụng JSON string
     */
    public void exampleCouponApplyExceedWithJsonString() {
        Long couponId = 456L;
        
        log.info("Ví dụ 2: Coupon {} hết số lượng áp dụng - sử dụng JSON string", couponId);
        
        // Tạo metadata dưới dạng JSON string
        String jsonMetadata = String.format("{\"couponId\": %d}", couponId);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.COUPON_APPLY_EXCEED, jsonMetadata);
        
        log.info("Đã tạo COUPON_APPLY_EXCEED event với JSON string metadata");
    }

    /**
     * Ví dụ 3: Coupon được cập nhật - có cả couponId và couponDraftId
     */
    public void exampleCouponUpgraded() {
        Long couponId = 789L;
        Long couponDraftId = 790L;
        
        log.info("Ví dụ 3: Coupon {} được cập nhật với draft {}", couponId, couponDraftId);
        
        // Tạo metadata dưới dạng Map
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("couponId", couponId);
        metadata.put("couponDraftId", couponDraftId);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.COUPON_UPGRADED, metadata);
        
        log.info("Đã tạo COUPON_UPGRADED event");
    }
    
    // ==================== PRODUCT EVENTS ====================
    
    /**
     * Ví dụ 4: Pricing thay đổi trạng thái
     */
    public void examplePricingStatusChanged() {
        Long pricingId = 101L;
        Long pricingDraftId = 102L;
        String status = "INACTIVE";
        
        log.info("Ví dụ 4: Pricing {} thay đổi trạng thái thành {}", pricingId, status);
        
        // Tạo metadata dưới dạng Map
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("pricingId", pricingId);
        metadata.put("pricingDraftId", pricingDraftId);
        metadata.put("status", status);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.PRICING_STATUS_CHANGED, metadata);
        
        log.info("Đã tạo PRICING_STATUS_CHANGED event");
    }

    /**
     * Ví dụ 5: Pricing được cập nhật
     */
    public void examplePricingUpgraded() {
        Long pricingId = 201L;
        Long pricingDraftId = 202L;
        
        log.info("Ví dụ 5: Pricing {} được cập nhật với draft {}", pricingId, pricingDraftId);
        
        // Tạo metadata dưới dạng JSON string
        String jsonMetadata = String.format(
            "{\"pricingId\": %d, \"pricingDraftId\": %d}", 
            pricingId, pricingDraftId
        );
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.PRICING_UPGRADED, jsonMetadata);
        
        log.info("Đã tạo PRICING_UPGRADED event");
    }

    /**
     * Ví dụ 6: Variant thay đổi trạng thái
     */
    public void exampleVariantStatusChanged() {
        Long pricingId = 301L;
        Long pricingDraftId = 302L;
        String status = "INACTIVE";
        
        log.info("Ví dụ 6: Variant (pricing {}) thay đổi trạng thái thành {}", pricingId, status);
        
        // Tạo metadata dưới dạng Map
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("pricingId", pricingId);
        metadata.put("pricingDraftId", pricingDraftId);
        metadata.put("status", status);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.VARIANT_STATUS_CHANGED, metadata);
        
        log.info("Đã tạo VARIANT_STATUS_CHANGED event");
    }

    /**
     * Ví dụ 7: Variant được cập nhật
     */
    public void exampleVariantUpgraded() {
        Long variantId = 401L;
        Long variantDraftId = 402L;
        
        log.info("Ví dụ 7: Variant {} được cập nhật với draft {}", variantId, variantDraftId);
        
        // Tạo metadata dưới dạng Map
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("variantId", variantId);
        metadata.put("variantDraftId", variantDraftId);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.VARIANT_UPGRADED, metadata);
        
        log.info("Đã tạo VARIANT_UPGRADED event");
    }

    /**
     * Ví dụ 8: Addon thay đổi trạng thái
     */
    public void exampleAddonStatusChanged() {
        Long addonId = 501L;
        Long addonDraftId = 502L;
        String status = "INACTIVE";
        
        log.info("Ví dụ 8: Addon {} thay đổi trạng thái thành {}", addonId, status);
        
        // Tạo metadata dưới dạng JSON string
        String jsonMetadata = String.format(
            "{\"addonId\": %d, \"addonDraftId\": %d, \"status\": \"%s\"}", 
            addonId, addonDraftId, status
        );
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.ADDON_STATUS_CHANGED, jsonMetadata);
        
        log.info("Đã tạo ADDON_STATUS_CHANGED event");
    }

    /**
     * Ví dụ 9: Addon được cập nhật
     */
    public void exampleAddonUpgraded() {
        Long addonId = 601L;
        Long addonDraftId = 602L;
        
        log.info("Ví dụ 9: Addon {} được cập nhật với draft {}", addonId, addonDraftId);
        
        // Tạo metadata dưới dạng Map
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("addonId", addonId);
        metadata.put("addonDraftId", addonDraftId);
        
        // Lưu event vào database
        eventsService.saveEvent(EventTypeEnum.ADDON_UPGRADED, metadata);
        
        log.info("Đã tạo ADDON_UPGRADED event");
    }

    /**
     * Ví dụ 10: Tạo nhiều events cùng lúc
     */
    public void exampleMultipleEvents() {
        log.info("Ví dụ 10: Tạo nhiều events cùng lúc");
        
        // Coupon expired
        Map<String, Object> couponMetadata = new HashMap<>();
        couponMetadata.put("couponId", 1001L);
        eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, couponMetadata);
        
        // Pricing status changed
        Map<String, Object> pricingMetadata = new HashMap<>();
        pricingMetadata.put("pricingId", 2001L);
        pricingMetadata.put("pricingDraftId", 2002L);
        pricingMetadata.put("status", "INACTIVE");
        eventsService.saveEvent(EventTypeEnum.PRICING_STATUS_CHANGED, pricingMetadata);
        
        // Addon upgraded
        String addonJson = "{\"addonId\": 3001, \"addonDraftId\": 3002}";
        eventsService.saveEvent(EventTypeEnum.ADDON_UPGRADED, addonJson);
        
        log.info("Đã tạo 3 events khác nhau");
    }
}

/**
 * Cách sử dụng đơn giản:
 * 
 * 1. Tạo metadata dưới dạng Map<String, Object>:
 *    Map<String, Object> metadata = new HashMap<>();
 *    metadata.put("couponId", 123L);
 *    eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, metadata);
 * 
 * 2. Hoặc tạo metadata dưới dạng JSON string:
 *    String jsonMetadata = "{\"couponId\": 123}";
 *    eventsService.saveEvent(EventTypeEnum.COUPON_EXPIRED, jsonMetadata);
 * 
 * 3. EventTask sẽ tự động parse JSON và lấy các field cần thiết:
 *    - couponId từ COUPON events
 *    - pricingId từ PRICING events
 *    - variantId từ VARIANT_UPGRADED event
 *    - addonId từ ADDON events
 * 
 * 4. Không cần tạo object metadata class, chỉ cần đảm bảo JSON có đúng field names
 */
