package com.helpers.marketingCampaign;

import com.onedx.common.helpers.sendSms.SendSmsUtils;
import com.onedx.common.repository.emails.EmailMainRepository;
import com.service.email.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SendTools {
    private EmailService emailService;
    private EmailMainRepository emailMainRepository;
    private SendSmsUtils sendSmsUtils;

    @Autowired
    public void setEmailMainRepository(EmailMainRepository emailMainRepository) {
        this.emailMainRepository = emailMainRepository;
    }

    @Autowired
    public void setSendSmsUtils(SendSmsUtils sendSmsUtils) {
        this.sendSmsUtils = sendSmsUtils;
    }

    @Autowired
    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }

    public void byEmail(SendItem sendItem) {

    }

    public void bySms(SendItem sendItem) {
        //TODO: bổ sung thêm logic tính số lượng gửi success, total
        sendSmsUtils.sendSms(sendItem.getPhone(), sendItem.getSmsContent());
    }

    public void byNotificationFirebase(SendItem sendItem) {

    }
}
