package com.config;

import java.util.Collections;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import com.onedx.common.constants.values.CharacterConstant;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import io.swagger.v3.oas.models.security.SecurityScheme.Type;
import io.swagger.v3.oas.models.servers.Server;
import lombok.var;

/**
 * <AUTHOR> HuyNV
 * @version    : 1.0
 * 14/1/2021
 */
@Configuration
@EnableWebMvc
public class SwaggerConfiguration {

    @Value(value = "${web.host}")
    private String webHost;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            // Thiết lập các server dùng để test api
            .servers(Collections.singletonList(
                new Server().url(webHost)
            ))
            // info
            .info(apiEndPointsInfo())
            .components(new Components().addSecuritySchemes("bearerAuth", new SecurityScheme().type(Type.HTTP).scheme("bearer").in(In.HEADER)))
            .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }
    private Info apiEndPointsInfo() {
        return new Info().title("Spring Boot REST API")
                .description("Platform SASS")
                .contact(new Contact()
                        .url("https://onedx.vnpt-technology.vn")
                        .name("onedx.vnpt-technology.vn"))
                .title("VNPT")
                .license(new License()
                        .name("VNPT  v1.0.0")
                        .url("https://onedx.vnpt-technology.vn"))
                .version("1.0.0");
    }

    // Gom nhóm api cho mobile app
    @Bean
    public GroupedOpenApi mobileGroup() {
        return GroupedOpenApi.builder()
            .group("mobile-app")
            .displayName("Mobile App APIs")
            .pathsToMatch("/api/v1/mobile-app/**")
            .build();
    }

    // Gom nhóm api cho đối tác
    @Bean
    public GroupedOpenApi partnerGroup() {
        return GroupedOpenApi.builder()
            .group("partners")
            .displayName("Partner APIs")
            .pathsToMatch("/api/v1/categories/**",
                "/api/v1/products/**",
                "/api/v1/orders/**",
                "/api/v1/subscriptions/**",
                "/api/v1/payments/**",
                "/api/v1/logistics/**",
                "/api/v1/addresses/**",
                "/api/v1/webhooks/**")
            .addOpenApiCustomiser(openApi -> {
                openApi.addSecurityItem(new SecurityRequirement().addList(CharacterConstant.X_API_KEY));
                var securityScheme = new SecurityScheme();
                securityScheme.setType(SecurityScheme.Type.APIKEY);
                securityScheme.setIn(SecurityScheme.In.HEADER);
                securityScheme.setName(CharacterConstant.X_API_KEY);
                openApi.getComponents().addSecuritySchemes(CharacterConstant.X_API_KEY, securityScheme);
            })
            .build();
    }

    // Gom nhóm api cho giải pháp/gói bundling
    @Bean
    public GroupedOpenApi packageGroup() {
        return GroupedOpenApi.builder()
            .group("packages")
            .displayName("API giải pháp và gói bundling")
            .pathsToMatch("/api/portal/packages/**",
                "/api/admin-portal/packages/**",
                "/api/dev-portal/packages/**",
                "/api/portal/product-solutions/**",
                "/api/admin-portal/product-solutions/**",
                "/api/dev-portal/product-solutions/**")
            .build();
    }

    // Gom nhóm api nội bộ
    @Bean
    public GroupedOpenApi commonGroup() {
        return GroupedOpenApi.builder()
            .group("")
            .displayName("Internal APIs")
            .pathsToMatch("/api/**")
            .packagesToExclude("/api/portal/packages/**",
                "/api/admin-portal/packages/**",
                "/api/dev-portal/packages/**",
                "/api/portal/product-solutions/**",
                "/api/admin-portal/product-solutions/**",
                "/api/dev-portal/product-solutions/**")
            .build();
    }
}
