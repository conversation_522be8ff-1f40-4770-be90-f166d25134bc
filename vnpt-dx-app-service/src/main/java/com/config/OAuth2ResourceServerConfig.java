package com.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.PathRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import com.common.aop.props.WhiteListProperties;
import com.onedx.common.constants.enums.security.apis.ApiPermitAll;
import com.onedx.common.utils.SecurityUtil;

/**
 * The type O auth 2 resource server config.
 */
@SuppressWarnings("deprecation")
@Configuration
@EnableResourceServer
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class OAuth2ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Autowired
    private DefaultTokenServices tokenServices;
    @Autowired
    private WhiteListProperties whiteListProperties;

    @Override
    public void configure(final HttpSecurity http) throws Exception {
        http.headers().xssProtection().and().contentSecurityPolicy("script-src 'self'");
        // NgoNC: Bypass các API được truy cập từ tất cả các portal, không cần đăng nhập
        String[] lstWhitelistApi = SecurityUtil.parseWhiteListApis(whiteListProperties.getApis());
        setEntryPoint(http).authorizeRequests()
            .requestMatchers(PathRequest.toStaticResources().atCommonLocations()).authenticated()
            // Whitelist APIs in YML
            .antMatchers(lstWhitelistApi).permitAll()
            // Whitelist APIs for Swagger
            .antMatchers("/swagger-ui/**").permitAll()
            .antMatchers("/swagger-resources/**").permitAll()
            .antMatchers("/resources/**").permitAll()
            .antMatchers("/api/v3/**").permitAll()
            .antMatchers("/v3/api-docs/**").permitAll()
            .antMatchers("/swagger-ui.html").permitAll()
            .antMatchers("/actuator/**").permitAll()
            // Others
            .antMatchers("/api-dev/integrate/**").permitAll()
            .antMatchers("/api/sme-portal/combos/**").permitAll()
            .antMatchers("/api/admin-portal/affiliate/member/register").permitAll()
            .antMatchers("/api/admin-portal/affiliate/member/resend-info").permitAll()
            .antMatchers("/api/admin-portal/affiliate/member/change-status").permitAll()
            .antMatchers("/api/admin-portal/affiliate/member/update-profile").permitAll()
            .antMatchers("/api/admin-portal/affiliate/member/check-referral").permitAll()
            .antMatchers("/api/admin-portal/affiliate/member/convert").permitAll()
            .antMatchers("/api/admin-portal/affiliate-link/get-commission-success").permitAll()
            .antMatchers("/api/admin-portal/crm/enterprise-mgmt/update/update-enterprise-change-email-account").permitAll()
            .antMatchers("/api/portal/file/site-map").permitAll()
            .antMatchers("/api/enterprise/**").permitAll()
            .antMatchers("/api/integration/**").permitAll()
            .antMatchers("/api/sme-portal/seo/find-all").permitAll()
            .antMatchers("/api/sme-portal/seo/detail").permitAll()
            .antMatchers("/api/portal/seo/default").permitAll()
            .antMatchers("/api/sme-portal/system-param/**").permitAll()
            .antMatchers("/api/admin-portal/system-param/**").permitAll()
            .antMatchers("/api/sme-portal/page-builder/**").permitAll()
            .antMatchers("/api/migration/user/active-imported/{activationKey}").permitAll()
            .antMatchers("/api/migration/user/new-password/{id}").permitAll()
            .antMatchers("/api/portal/integrate-service/**").permitAll()
            .antMatchers("/api/tmdt/report").permitAll()
            .antMatchers("/api/collect-info-preorder").permitAll()
            .antMatchers("/api/update-info-preorder").permitAll()
            .antMatchers("/api/portal/pre-order").permitAll()
            .antMatchers("/api/sme-portal/shopping-cart/get-spdv-detail").permitAll()
            .antMatchers("/api/sme-portal/shopping-cart/update-cart").permitAll()
            .antMatchers("/api/sme-portal/shopping-cart/get-cart").permitAll()
            .antMatchers("/api/sme-portal/shopping-cart/get-detail").permitAll()
            .antMatchers("/api/sme-portal/shorten-link/detail").permitAll()
            .antMatchers("/api/sme-portal/topic/**").permitAll()
            .antMatchers("/api/sme-portal/affiliate-link/statistical-click").permitAll()
            .antMatchers("/api/portal/attributes/payment/**").permitAll()
            .antMatchers("/api/portal/variant/**").permitAll()
            .antMatchers("/api/portal/pricing/detail/**").permitAll()
            .antMatchers("/api/pricing/import").permitAll()
            .antMatchers("/api/portal/attributes/overview/**").permitAll()
            .antMatchers("/api/v1/addresses/**").permitAll()
            .antMatchers("/api/bos/homepage/**").permitAll() // Bypass tất cả các api cùng controller
            .antMatchers("/api/portal/packages/**").permitAll()
            .anyRequest().authenticated()
            .and().csrf().disable();
    }

    private HttpSecurity setEntryPoint(HttpSecurity security) throws Exception {
        // config cho cac api public
        for (ApiPermitAll api : ApiPermitAll.values()) {
            if (api.getHttpMethod() == null) {
                security.authorizeRequests().antMatchers(api.getApi()).permitAll();
            } else {
                security.authorizeRequests().antMatchers(api.getHttpMethod(), api.getApi()).permitAll();
            }
        }
        return security;
    }

    @Override
    public void configure(final ResourceServerSecurityConfigurer config) {
        config.tokenServices(tokenServices);
    }
}
