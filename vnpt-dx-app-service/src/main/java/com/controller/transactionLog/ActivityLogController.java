package com.controller.transactionLog;

import javax.validation.Valid;
import com.dto.transaction_log.ResendActivityLogDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.ActivityLog;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.dto.transaction_log.ActivityLogDTO;
import com.dto.transaction_log.ActivityLogReqDTO;
import com.service.transactionLog.ActivityLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Thucnb
 * @version    	: 1.0
 * August 16,  2022
 */

@RestController
@RequestMapping("/api/admin-portal/activity-log")
@Slf4j
public class ActivityLogController {
    @Autowired
    ActivityLogService activityLogService;


    /**
     * Xoa hoat dong
     */
    @Operation(description = "Xóa activityLog")
    @DeleteMapping("delete/{id}")
    public ResponseEntity<HttpStatus> deleteActivityLog(
        @Parameter(description = SwaggerConstant.ActivityLog.ID, example = SwaggerConstant.Example.ID) @PathVariable Long id)
        throws AccessDeniedException {
        log.info("--- Execute deleteActivity method: Start ---");
        activityLogService.deleteActivityLog(id);
        log.info("--- Execute deleteActivity method: End ---");
        return ResponseEntity.ok().body(HttpStatus.OK);
    }

    /**
     * Cập nhật trạng thái chưa xử lý của activityLog
     */
    @Operation(description = "Cập nhật trạng thái chưa xử lý của activityLog")
    @PutMapping("/{id}")
    public ResponseEntity<ActivityLogDTO> UpdateActivityLogProcessStatus(
        @Parameter(description = SwaggerConstant.ActivityLog.ID, example = SwaggerConstant.Example.ID)
        @PathVariable("id") Long id,
        @Parameter(description = ActivityLog.CONTENT_DETAIL, example = Example.CONTENT_DETAIL)
        @Validated @RequestBody ActivityLogDTO dto) throws AccessDeniedException {
        log.info("--- Execute UpdateActivityLogProcessStatus method: Start ---");
        ActivityLogDTO respond =  activityLogService.UpdateActivityLogProcessStatus(id, dto);
        log.info("--- Execute UpdateActivityLogProcessStatus method: End ---");
        return ResponseEntity.ok().body(respond);
    }

    /**
     * Cập nhật trạng thái chưa xử lý của activityLog chia sẻ lưu lượng
     */
    @Operation(description = "Cập nhật trạng thái chưa xử lý của activityLog")
    @PutMapping("/traffic-wallet/{id}")
    public ResponseEntity<ActivityLogDTO> UpdateTrafficActivityLogProcessStatus(
            @Parameter(description = SwaggerConstant.ActivityLog.ID, example = SwaggerConstant.Example.ID)
            @PathVariable("id") Long id,
            @Parameter(description = ActivityLog.CONTENT_DETAIL, example = Example.CONTENT_DETAIL)
            @Validated @RequestBody ActivityLogDTO dto) throws AccessDeniedException {
        log.info("--- Execute UpdateActivityLogProcessStatus method: Start ---");
        ActivityLogDTO respond =  activityLogService.UpdateTrafficActivityLogProcessStatus(id, dto);
        log.info("--- Execute UpdateActivityLogProcessStatus method: End ---");
        return ResponseEntity.ok().body(respond);
    }

    /**
     * Gửi lại các activityLog thất bại
     */
    @Operation(description = "Gửi lại các activityLog thất bại")
    @PostMapping("/resend/{id}")
    public ResponseEntity<String> resendFailedActivityLog(
        @Parameter(description = SwaggerConstant.ActivityLog.ID, example = SwaggerConstant.Example.ID)
        @PathVariable("id") Long id,
        @Parameter(description = SwaggerConstant.ActivityLog.IS_EDIT_REQUEST, example = SwaggerConstant.Example.TRUE)
        @RequestParam(name = "isEditRequest", defaultValue = "FALSE") Boolean isEditRequest) {
        log.info("--- Execute resendFailedActivityLog method: Start ---");
        activityLogService.resendFailedActivityLog(id, isEditRequest);
        log.info("--- Execute resendFailedActivityLog method: End ---");
        return ResponseEntity.ok().build();
    }

    /**
     * Gửi lại list các activityLog thất bại
     */
    @Operation(description = "Gửi lại các activityLog thất bại")
    @PostMapping("/resend")
    public ResponseEntity<String> resendFailedListActivityLog(@RequestBody @Valid ResendActivityLogDTO dto) {
        log.info("--- Execute resendFailedListActivityLog method: Start ---");
        activityLogService.resendFailedListActivityLog(dto);
        log.info("--- Execute resendFailedActivityLog method: End ---");
        return ResponseEntity.ok().build();
    }
    
    
    /**
     * Cập nhật thông tin request activity trên hệ thống Admin Portal
     *
     */
    @Operation(description = "Cập nhật thông tin chi tiết activity trên hệ thống Admin Portal")
    @PutMapping("/request/{id}")
    public ResponseEntity<Long> updateRequestActivity(
            @Parameter(description = SwaggerConstant.Activity.ID, example = SwaggerConstant.Example.ID)
            @PathVariable("id") Long id,
            @Valid @RequestBody ActivityLogReqDTO activityLogReqDTO
          ) {
        log.info("--- Execute updateRequestActivity method: Start ---");
        activityLogService.updateRequestActivityLog(id, activityLogReqDTO);
        log.info("--- Execute updateRequestActivity method: End ---");
        return ResponseEntity.ok().body(id);
    }
}
