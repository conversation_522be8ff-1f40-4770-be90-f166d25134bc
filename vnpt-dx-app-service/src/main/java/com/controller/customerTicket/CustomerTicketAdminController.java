package com.controller.customerTicket;

import java.util.Date;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.component.BaseController;
import com.constant.enums.customerTicket.CustomerTicketStatusEnum;
import com.dto.customerTicket.TicketResponseReqDTO;
import com.dto.customerTicket.requestDTO.CustomerTicketCreateDTO;
import com.dto.customerTicket.requestDTO.CustomerTicketUpdateDTO;
import com.dto.customerTicket.requestDTO.TicketAssigneesUpdateDTO;
import com.dto.customerTicket.requestDTO.TicketDescriptionUpdateDTO;
import com.dto.customerTicket.responseDTO.GetListCustomerTicket;
import com.dto.customerTicket.responseDTO.TicketDetailsAdminDTO;
import com.dto.customerTicket.responseDTO.TicketDetailsAdminDTO.IGetStatusHistory;
import com.dto.customerTicket.responseDTO.TicketSupportHistoryDTO;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.values.DatabaseConstant;
import com.service.customerTicket.CustomerTicketService;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> TinhNX
 * @version    : 1.0
 * 19/1/2021
 */
@RestController
@RequestMapping("/api/admin-portal/tickets")
@Slf4j
public class CustomerTicketAdminController {

	@Autowired
	private CustomerTicketService customerTicketService;

	/**
	 * Thêm phản hồi phiếu hỗ trợ
	 */
	@PostMapping("/add-respond")
	public void createTicketResponse(@RequestBody TicketResponseReqDTO requestDTO){
		customerTicketService.createTicketResponse(requestDTO, PortalType.ADMIN);
	}

	/**
	 * Sửa phản hồi phiếu hỗ trợ
	 */
	@PostMapping("/update-response")
	public void updateTicketResponse(@RequestBody TicketResponseReqDTO requestDTO) {
		customerTicketService.updateTicketResponse(requestDTO);
	}

	/**
	 * Xóa phản hồi phiếu hỗ trợ
	 */
	@DeleteMapping("/delete-response")
	public void deleteTicketResponse(@RequestBody TicketResponseReqDTO requestDTO) {
		customerTicketService.deleteTicketResponse(requestDTO, PortalType.ADMIN);
	}

	/**
	 * Tạo mới customer ticket với quyền admin
	 */
	@PostMapping("/create-ticket")
	@Operation(description = "API tạo mới customer ticket với quyền ADMIN")
	public void createTicketAdmin(@RequestBody CustomerTicketCreateDTO createDTO)
	{
		customerTicketService.createTicket(createDTO,PortalType.ADMIN);
	}

	@GetMapping("/get-list")
	@Operation(description = "API xem danh sách phiếu hỗ trợ")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public Page<GetListCustomerTicket> getListCustomerTicket(
		@RequestParam(value = "value", required = false, defaultValue = "") String value,
		@RequestParam(value = "fastLook", required = false, defaultValue = "-1") Integer fastLook,
		@RequestParam(value = "searchCode", required = false, defaultValue = "0") Integer searchCode,
		@RequestParam(value = "searchServiceName", required = false, defaultValue = "0") Integer searchServiceName,
		@RequestParam(value = "searchUserName", required = false, defaultValue = "0") Integer searchUserName,
		@RequestParam(value = "lstStatus", required = false, defaultValue = "-1") Set<Integer> lstStatus,
		@RequestParam(value = "customerType", required = false, defaultValue = "ALL") List<String> customerType,
		@RequestParam(value = "lstProvinceId", required = false, defaultValue = "-1") List<Long> lstProvinceId,
		@RequestParam(value = "lstSupportType", required = false, defaultValue = "-1") List<Integer> lstSupportType,
		@RequestParam(value = "priority", required = false, defaultValue = "-1") Integer priority,
		@RequestParam(name = "deadlineTo", required = false, defaultValue = "01/01/1970")
		@DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) Date deadlineTo,
		@RequestParam(value = "tagId", required = false, defaultValue = "-1") Long tagId,
		@RequestParam(value = "lstCreatedSource", required = false, defaultValue = "-1") List<Integer> lstCreatedSource,
		@RequestParam(value = "assigneeId", required = false, defaultValue = "-1") Long assigneeId,
		@RequestParam(value = "creator", required = false, defaultValue = "-1") Integer creator,
		@RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
		@RequestParam(value = "size", required = false, defaultValue = "50") Integer size,
		@RequestParam(value = "sort", required = false, defaultValue = "createdAt,desc") String sort) {

		BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
		return customerTicketService.getListTicket(value, fastLook, searchCode, searchServiceName,
			searchUserName, lstStatus, customerType, lstProvinceId, lstSupportType, priority, deadlineTo, tagId, lstCreatedSource, assigneeId, creator,
			pageInfo.getPageable(), PortalType.ADMIN);
	}


	/**
	 * Lấy thông tin chi tiết ticket với quyền admin
	 */
	@GetMapping("/detail-ticket")
	public TicketDetailsAdminDTO getDetailsTicket(@RequestParam(name = "id") Long id) throws AccessDeniedException {
		log.info("--- Execute getDetailsTicket  method: Start ---");
		return  (TicketDetailsAdminDTO) customerTicketService.getTicketsDetail(id,PortalType.ADMIN);
	}

	/**
	 * Lấy lịch sử chỉnh sửa phiếu hỗ trợ Admin Portal
	 */
	@GetMapping("/get-status-history")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public Page<IGetStatusHistory> getStatusHistories(@RequestParam("ticketId") Long ticketId,
		@DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
		@RequestParam(name = "startDate", required = false, defaultValue = "01/01/1970") Date startDate,
		@DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
		@RequestParam(name = "endDate", required = false, defaultValue = "01/01/1970") Date endDate,
		@RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
		@RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
		@RequestParam(name = "sort", required = false, defaultValue = "createdAt,desc") String sort) {
		BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
		return customerTicketService.getStatusHistory(ticketId, startDate, endDate, pageInfo.getPageable());
	}

	/**
	 * Cập nhật thông tin người hỗ trợ và người theo dõi Admin Portal
	 */
	@PutMapping("/update-assignees")
	public void updateAssignees(@RequestBody @Validated TicketAssigneesUpdateDTO updateDTO)
	{
		customerTicketService.updateAssignees(updateDTO);
	}

	/**
	 * Cập nhật trạng thái phiếu hỗ trợ Admin Portal
	 */
	@PostMapping("/update-status/{id}/{status}")
	public void updateTicketStatus(@PathVariable(name = "id") Long id,
				 				   @PathVariable("status") CustomerTicketStatusEnum status){
		customerTicketService.updateTicketStatus(id,status, PortalType.ADMIN);
	}

	/**
	 * Cập nhật thông tin phiếu hỗ trợ Admin Portal
	 */
	@PostMapping("/update-info/{id}")
	public void updateTicketInfos(@PathVariable("id") Long id,
		@RequestBody CustomerTicketUpdateDTO ticketUpdateDTO){
		customerTicketService.updateTicketInfos(id,ticketUpdateDTO,PortalType.ADMIN);
	}

	/**
	 * Cập nhật mô tả phiếu hỗ trợ ( tiêu đề, mô tả, file đính kèm ) cho Admin Portal
	 */
	@PostMapping("/update-description/{id}")
	public void updateTicketDescription(@PathVariable(name = "id") Long id,
		@RequestBody TicketDescriptionUpdateDTO updateDTO) {
		customerTicketService.updateDescription(id, updateDTO, PortalType.ADMIN);
	}

	/**
	 * Lấy danh sách lịch sử hỗ trợ Admin portal
	 */
	@GetMapping("/get-support-history")
	public Page<TicketSupportHistoryDTO> getListTicketSupportHistory(@RequestParam(name = "ticketId") Long ticketId,
		@RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
		@RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
		@RequestParam(value = "sort", required = false, defaultValue = "createdAt,desc") String sort) {
		BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
		return customerTicketService.getListTicketResponse(ticketId, pageInfo.getPageable());
	}

	@GetMapping("check-exist-by-ticket-title")
	public boolean existByTicketTitle(@RequestParam(name = "ticketTitle") String ticketTitle) {
		return customerTicketService.existByTicketTitle(ticketTitle);
	}
}
