package com.controller.openapis.v1.address;

import java.util.List;
import java.util.stream.Collectors;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Slice;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.component.BaseController;
import com.dto.common.ComboBoxDTO;
import com.dto.enterprise.ComboboxDistrictResponseDTO;
import com.dto.enterprise.ComboboxStreetDTO;
import com.dto.enterprise.ComboboxWardResponseDTO;
import com.onedx.common.constants.values.DatabaseConstant;
import com.service.openapis.v1.address.AddressV1Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/addresses")
@RequiredArgsConstructor
@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
public class AddressV1Controller {

    private final AddressV1Service addressV1Service;

    /**
     * @param: name
     * @return: Id, tên, code vùng miền
     */
    @GetMapping("/regions")
    @Cacheable(value = "regions", key = "#name")
    public List<ComboBoxDTO> getRegions(@RequestParam(value = "name", required = false, defaultValue = "") String name) {
        return addressV1Service.getRegions(name).stream().map(ComboBoxDTO::new).collect(Collectors.toList());
    }

    /**
     * @param: Danh sách regionIds
     * @param: Tên tỉnh
     * @return: ID, tên, code tỉnh thành
     */
    @GetMapping("/provinces")
    @Cacheable(value = "provinces", key = "T(java.util.Objects).hash(#regionIds, #provinceName)")
    public List<ComboBoxDTO> getProvinces(
        @RequestParam(value = "regionIds", required = false, defaultValue = "-1") List<Long> regionIds,
        @RequestParam(value = "name", required = false, defaultValue = "") String provinceName) {
        return addressV1Service.getProvinceByRegion(regionIds, provinceName).stream()
            .map(ComboBoxDTO::new).collect(Collectors.toList());
    }

    /**
     * @param: page
     * @param: size
     * @param: sort
     * @param: provinceIds : danh sách Id tỉnh
     * @param: districtName: Tên huyện
     * @return:
     */
    @GetMapping("/districts")
    public Slice<ComboboxDistrictResponseDTO> getDistrictByProvinceId(
        @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(value = "size", required = false, defaultValue = "50") Integer size,
        @RequestParam(value = "sort", required = false, defaultValue = "name,asc") String sort,
        @RequestParam(value = "provinceIds", required = false, defaultValue = "-1") List<Long> provinceIds,
        @RequestParam(value = "name", required = false, defaultValue = "") String districtName) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return addressV1Service.getDistrictByProvince(pageInfo.getPageable(), provinceIds, districtName);
    }

    @GetMapping("/wards")
    public Slice<ComboboxWardResponseDTO> getWardByProvinceIdDistrictId(
        @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(value = "size", required = false, defaultValue = "50") Integer size,
        @RequestParam(value = "sort", required = false, defaultValue = "wardName,asc") String sort,
        @RequestParam(value = "districtIds", required = false, defaultValue = "-1") List<Long> districtIds,
        @RequestParam(value = "name", required = false, defaultValue = "") String wardName,
        @RequestParam(value = "provinceIds", required = false, defaultValue = "-1") List<Long> provinceIds) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return addressV1Service.getSliceWardByProvinceIdDistrictId(pageInfo.getPageable(), districtIds, provinceIds, wardName);
    }

    @GetMapping("/business-areas")
    @Cacheable(value = "business-areas", key = "T(java.util.Objects).hash(#name, #code, #page, #size, #sort)")
    public Slice<ComboBoxDTO> getBusinessArea(
        @RequestParam(value = "name", required = false, defaultValue = "") String name,
        @RequestParam(value = "code", required = false, defaultValue = "") String code,
        @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
        @RequestParam(value = "sort", required = false, defaultValue = "name,asc") String sort) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return addressV1Service.getBusinessArea(pageInfo.getPageable(), name, code).map(ComboBoxDTO::new);
    }

    @GetMapping("/streets")
    public Page<ComboboxStreetDTO> getStreetByProvinceId(
        @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(value = "size", required = false, defaultValue = "50") Integer size,
        @RequestParam(value = "sort", required = false, defaultValue = "name,asc") String sort,
        @RequestParam(value = "provinceIds", required = false, defaultValue = "-1") List<Long> provinceIds,
        @RequestParam(value = "wardIds", required = false, defaultValue = "-1") List<Long> wardIds,
        @RequestParam(value = "name", required = false, defaultValue = "") String name) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return addressV1Service.getStreetByProvince(pageInfo.getPageable(), provinceIds, wardIds, name);
    }
}