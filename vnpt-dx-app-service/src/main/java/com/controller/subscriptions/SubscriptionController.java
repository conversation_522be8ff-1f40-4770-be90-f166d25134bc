package com.controller.subscriptions;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.component.BaseController.ListRequest;
import com.constant.CouponConst;
import com.constant.CreditNoteConst;
import com.constant.PricingConst;
import com.constant.SubscriptionConstant;
import com.constant.enums.addons.AddonPopUpTypeEnum;
import com.constant.enums.marketingCampaign.McIconIndicatorEnum;
import com.constant.enums.pricing.PricingCancelTimeActiveEnum;
import com.constant.enums.subscription.CancelMultiSubscriptionTypeEnum;
import com.constant.enums.subscription.PopupTypeEnum;
import com.constant.enums.subscription.PromotionPopupType;
import com.constant.enums.subscription.PurchaseItemEnum;
import com.constant.enums.subscription.SubscriptionRenewTypeEnum;
import com.constant.enums.transactionLog.TransactionCodeEnum;
import com.dto.addons.AddonPriceBeforeTaxDTO;
import com.dto.applyFee.ApplyFeeByDevAdminDTO;
import com.dto.applyFee.ApplyFeeDTO;
import com.dto.marketingCampaign.smePortal.McCampaignPromotionDTO;
import com.dto.marketingCampaign.smePortal.McPurchasingInfoDTO;
import com.dto.marketingCampaign.smePortal.McPurchasingItemDTO;
import com.dto.payment.TransactionStatusResDTO;
import com.dto.pricing.PlanPeriodResDTO;
import com.dto.report.dashboardSme.PricingComboPlanReportResDTO;
import com.dto.report.dashboardSme.ServiceComboReportResDTO;
import com.dto.report.dashboardSme.UserNameFilterResDTO;
import com.dto.subscriptions.CouponMcDevAdminReqDTO;
import com.dto.subscriptions.CouponMcPopupDTO;
import com.dto.subscriptions.CouponPopupDTO;
import com.dto.subscriptions.CustomerDTO;
import com.dto.subscriptions.CustomerInfoDTO;
import com.dto.subscriptions.MultiSubscriptionCancelDTO;
import com.dto.subscriptions.SubsValidReqDTO;
import com.dto.subscriptions.SubsValidResDTO;
import com.dto.subscriptions.SubscriptionAccumulateDTO;
import com.dto.subscriptions.SubscriptionAddonResDTO;
import com.dto.subscriptions.SubscriptionBillingsResDTO;
import com.dto.subscriptions.SubscriptionByDevOrAdminDTONew;
import com.dto.subscriptions.SubscriptionCalculateDTO;
import com.dto.subscriptions.SubscriptionCalculateDevComboReqDTO;
import com.dto.subscriptions.SubscriptionCalculateDevReqDTO;
import com.dto.subscriptions.SubscriptionCalculateDevResDTO;
import com.dto.subscriptions.SubscriptionComboBasicDTO;
import com.dto.subscriptions.SubscriptionComboBillingsDTO;
import com.dto.subscriptions.SubscriptionInfoDTO;
import com.dto.subscriptions.SubscriptionOfficialJointScreenDTO;
import com.dto.subscriptions.SubscriptionPricingPopupResDTO;
import com.dto.subscriptions.SubscriptionRegisterDevResDTO;
import com.dto.subscriptions.SubscriptionRenewResDTO;
import com.dto.subscriptions.SubscriptionSmeCompaniesResponseDTO;
import com.dto.subscriptions.SubscriptionSwapComboDevDTO;
import com.dto.subscriptions.SubscriptionUpdateDevResDTO;
import com.dto.subscriptions.UpdateStatusOrderDeviceDTO;
import com.dto.subscriptions.calculate.UnitLimitedNewDTO;
import com.dto.subscriptions.calculate.UnitLimitedReqDTO;
import com.dto.subscriptions.combo.SubComboDevReqDTO;
import com.dto.subscriptions.combo.SubComboPlanDetailDTO;
import com.dto.subscriptions.combo.SubsComboValidReqDTO;
import com.dto.subscriptions.combo.SubscriptionComboPlanPopupResDTO;
import com.dto.subscriptions.detail.NewestPricingOfSubDTO;
import com.dto.subscriptions.detail.SubscriptionDetailDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.subscriptions.responseDTO.MyServiceSubscriptionResDTO;
import com.dto.subscriptions.responseDTO.SubDetailEcontract;
import com.dto.subscriptions.responseDTO.SubscriptionDetailCartDTO;
import com.dto.transaction_log.OnlineServiceProgressDTO;
import com.enums.ProductTypeEnum;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.services.ComboTypeEnum;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.ActionLog;
import com.onedx.common.constants.values.SwaggerConstant.Billing;
import com.onedx.common.constants.values.SwaggerConstant.Coupon;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.constants.values.SwaggerConstant.User;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.utils.ObjectUtil;
import com.repository.subscriptions.SubscriptionRepository;
import com.service.bills.BillsService;
import com.service.calculator.PlanPriceListCalculator;
import com.service.combo.ComboPlanService;
import com.service.coupon.CouponService;
import com.service.marketingCampaign.MarketingCampaignSmeService;
import com.service.payment.impl.PaymentService;
import com.service.pricing.PricingService;
import com.service.shoppingCart.ShoppingCartSmeService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.subscriptions.SubscriptionCalculateService;
import com.service.subscriptions.SubscriptionDetailService;
import com.service.subscriptions.SubscriptionOrderService;
import com.service.subscriptions.SubscriptionRegisterService;
import com.service.subscriptions.SubscriptionService;
import com.service.subscriptions.SubscriptionValidateService;
import com.util.AuthUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0
 * 06/07/2021
 */
@RestController
@RequestMapping("/api/portal/subscription")
@Slf4j
public class SubscriptionController {

    @Autowired
    private SubscriptionService subscriptionService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private SubscriptionRegisterService subscriptionRegisterService;
    @Autowired
    private SubscriptionDetailService subscriptionDetailService;
    @Autowired
    private SubscriptionCalculateService subscriptionCalculateService;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private ShoppingCartSmeService shoppingCartSmeService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private ComboPlanService comboPlanService;
    @Autowired
    private SubscriptionOrderService subscriptionOrderService;
    @Autowired
    private SubscriptionValidateService subscriptionValidateService;
    @Autowired
    private MarketingCampaignSmeService campaignSmeService;
    @Autowired
    private BillsService billsService;

    /**
     * Danh sách phương thức thanh toán
     */
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    @Operation(description = "Danh sách phương thức thanh toán")
    @GetMapping("/payment-methods")
    public Set<PaymentMethodEnum> getListPaymentMethodEnum(
        @Parameter(description = "Hành động với thuê bao", example = "NEW/RENEW/UPDATE")
        @RequestParam(required = false, defaultValue = "NEW") TransactionCodeEnum actionCode,
        @Parameter(description = "Loại sản phẩm", example = "SERVICE/COMBO/SERVICE_GROUP/DEVICE")
        @RequestParam(required = false, defaultValue = "SERVICE") ProductTypeEnum objectType,
        @Parameter(description = "ID tương ứng với loại đối tượng", example = "123")
        @RequestParam(required = false, defaultValue = "-1") Long objectId,
        @Parameter(description = "ID với trường hợp mua gói", example = "1")
        @RequestParam(required = false, defaultValue = " ") Long pricingId,
        @Parameter(description = "ID với trường hợp mua combo plan", example = "1")
        @RequestParam(required = false, defaultValue = " ") Long comboPlanId
    ) {
        return subscriptionRegisterService.getListPaymentMethod(actionCode, objectType, objectId, pricingId, comboPlanId);
    }

    @GetMapping("/detail/bos/cart/{cartCode}")
    public SubscriptionDetailCartDTO getDetailSubscriptionCart(@PathVariable String cartCode,
        @RequestParam(required = false, defaultValue = "") PortalType portalType) {
        return shoppingCartSmeService.getDetailSubscriptionCart(cartCode, portalType);
    }

    @GetMapping("/detail/bos/{id}")
    public SubscriptionDetailCartDTO getDetailSubscriptionById(@PathVariable Long id,
        @RequestParam(required = false, defaultValue = "") PortalType portalType) {
        return shoppingCartSmeService.getDetailSubscriptionById(id, portalType);
    }

    @GetMapping("/detail/bos/e-contract/{id}")
    public SubDetailEcontract getDetailEcontractSubscriptionById(@PathVariable Long id) {
        return shoppingCartSmeService.getDetailEcontractSubscriptionById(id);
    }

    @GetMapping("/detail/bos/e-contract/cart/{cartCode}")
    public SubDetailEcontract getDetailEcontractSubscriptionByCart(@PathVariable String cartCode) {
        return shoppingCartSmeService.getDetailEcontractSubscriptionByCart(cartCode);
    }

    @PostMapping("/update/status/bos")
    public void updateStatusOrder(@Valid @RequestBody UpdateStatusOrderDeviceDTO UpdateStatusOrderDeviceDTO) {
        shoppingCartSmeService.updateStatusOrder(UpdateStatusOrderDeviceDTO);
    }

    @PutMapping("/update/status/bill/bos")
    public void updateStatusBillOrder(@RequestParam(name = "subIds", required = false, defaultValue = "-1") List<Long> subIds) {
        shoppingCartSmeService.updateStatusBillOrder(subIds);
    }
    @GetMapping("/sme")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<CustomerDTO> getCustomer(
            @Parameter(description = "Tên khách hàng", example = "Công ty TNHH ABC")
            @RequestParam(required = false, defaultValue = "") String companyName,
            @Parameter(description = "Tên người đại diện", example = "Nguyễn An")
            @RequestParam(required = false, defaultValue = "") String adminName,
            @Parameter(description = "Mã số thuế", example = "64356143564")
            @RequestParam(required = false, defaultValue = "") String tin,
            @Parameter(description = "Tên tỉnh thành", example = "Hà Nội")
            @RequestParam(required = false, defaultValue = "") String provinceName,
            @Parameter(description = "id", example = "1")
            @RequestParam(required = false, defaultValue = "-1") Long removeId,
            @Parameter(description = "customerType", example = "KHDN")
            @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
            @Parameter(description = "repPersonalCertNumber", example = "0214536")
            @RequestParam(required = false, defaultValue = "") String repPersonalCertNumber,
            @RequestParam(required = false, defaultValue = "-1") Long userId,
            @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @Parameter(description = "Kích thước trang", example = "0")
            @RequestParam(required = false, defaultValue = "10") Integer size,
            @Parameter(description = "Sắp xếp", example = "adminName,desc")
            @RequestParam(required = false, defaultValue = "companyName,asc") String sort) {
        sort = Objects.equals(customerType, CustomerTypeEnum.PERSONAL) ? "adminName,asc" : "companyName,asc";
        ListRequest listRequest = new ListRequest(size, page, sort);
        return subscriptionDetailService.getCustomer(companyName, adminName, tin, provinceName, removeId, customerType, repPersonalCertNumber,
            userId, listRequest.getPageable());
    }

    /**
     * Popup danh sách số giấy tờ chứng thực
     */
    @Operation(description = "Dropdown List danh sách số giấy tờ chứng thực")
    @GetMapping("/sme-rep-personal-cert-number")
    @Deprecated
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CustomerInfoDTO>> getRepPersonalCertNumber(
        @Parameter(description = "số giấy tờ chứng thực", example = "0325656546")
        @RequestParam(required = false, defaultValue = "") String repPersonalCertNumber,
        @Parameter(description = "customerType", example = "KHDN")
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "0")
        @RequestParam(required = false, defaultValue = "2147483647") Integer size,
        @Parameter(description = "Sắp xếp", example = "name,desc")
        @RequestParam(required = false, defaultValue = "name,asc") String sort) {
        log.info("--- Execute getCustomerName method: Start--");
        ListRequest listRequest = new ListRequest(size, page, sort);
        Page<CustomerInfoDTO> customerDTOS = subscriptionOrderService
            .getInfoSMEtoSub(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, repPersonalCertNumber, customerType, listRequest.getPageable());
        log.info("--- Execute getCustomerName method: End--");
        return ResponseEntity.ok(customerDTOS);
    }

    /**
     * Popup danh sách tên khách hàng
     */
    @Operation(description = "Dropdown List danh sách tên khách hàng")
    @GetMapping("/sme-name")
    @Deprecated
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CustomerInfoDTO>> getCustomerName(
        @Parameter(description = "Tên khách hàng", example = "Công ty TNHH ABC")
        @RequestParam(required = false, defaultValue = "") String name,
        @Parameter(description = "customerType", example = "KHDN")
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "0")
        @RequestParam(required = false, defaultValue = "2147483647") Integer size,
        @Parameter(description = "Sắp xếp", example = "name,desc")
        @RequestParam(required = false, defaultValue = "name,asc") String sort) {
        log.info("--- Execute getCustomerName method: Start--");
        ListRequest listRequest = new ListRequest(size, page, sort);
        Page<CustomerInfoDTO> customerDTOS = subscriptionOrderService
            .getInfoSMEtoSub(name, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, customerType, listRequest.getPageable());
        log.info("--- Execute getCustomerName method: End--");
        return ResponseEntity.ok(customerDTOS);
    }
    
    /**
     * Popup danh sách họ tên sme admin
     */
    @Operation(description = "Dropdown List danh sách tên người đại diện")
    @GetMapping("/sme/admin-name")
    @Deprecated
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CustomerInfoDTO>> getCustomerAdminName(
        @Parameter(description = "Tên khách hàng", example = "Công ty TNHH ABC")
        @RequestParam(required = false, defaultValue = "") String adminName,
        @Parameter(description = "customerType", example = "KHDN")
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "0")
        @RequestParam(required = false, defaultValue = "2147483647") Integer size,
        @Parameter(description = "Sắp xếp", example = "name,desc")
        @RequestParam(required = false, defaultValue = "adminName,asc") String sort) {
        log.info("--- Execute getCustomerAdminName method: Start--");
        ListRequest listRequest = new ListRequest(size, page, sort);
        Page<CustomerInfoDTO> customerDTOS = subscriptionOrderService
            .getInfoSMEtoSub(StringUtils.EMPTY, adminName, StringUtils.EMPTY, StringUtils.EMPTY, customerType, listRequest.getPageable());
        log.info("--- Execute getCustomerAdminName method: End--");
        return ResponseEntity.ok(customerDTOS);
    }

    /**
     * Popup tìm kiếm user theo mã số thuế
     */
    @Operation(description = "Dropdown List danh sách mã số thuế")
    @GetMapping("/sme-tin")
    @Deprecated
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CustomerInfoDTO>> getCustomerTin(
        @Parameter(description = "Tên khách hàng", example = "Công ty TNHH ABC")
        @RequestParam(required = false, defaultValue = "") String tin,
        @Parameter(description = "customerType", example = "KHDN")
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "0")
        @RequestParam(required = false, defaultValue = "2147483647") Integer size,
        @Parameter(description = "Sắp xếp", example = "name,desc")
        @RequestParam(required = false, defaultValue = "tax,asc") String sort) {
        log.info("--- Execute getCustomerTin method: Start--");
        ListRequest listRequest = new ListRequest(size, page, sort);
        Page<CustomerInfoDTO> customerDTOS = subscriptionOrderService
            .getInfoSMEtoSub(StringUtils.EMPTY, StringUtils.EMPTY, tin, StringUtils.EMPTY, customerType, listRequest.getPageable());
        log.info("--- Execute getCustomerTin method: End--");
        return ResponseEntity.ok(customerDTOS);
    }

    /**
     * Popup tìm kiếm tỉnh thành
     */
    @Operation(description = "Dropdown List danh sách tỉnh thành")
    @GetMapping("/sme-province")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CustomerInfoDTO>> getProvinceCustomer(
        @Parameter(description = "Tên khách hàng", example = "Công ty TNHH ABC")
        @RequestParam(required = false, defaultValue = "") String provinceName,
        @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "0")
        @RequestParam(required = false, defaultValue = "2147483647") Integer size,
        @Parameter(description = "Sắp xếp", example = "name,desc")
        @RequestParam(required = false, defaultValue = "provinceName,asc") String sort) {
        log.info("--- Execute getProvinceCustomer method: Start--");
        ListRequest listRequest = new ListRequest(size, page, sort);
        Page<CustomerInfoDTO> customerDTOS = subscriptionOrderService.getProvinceSMEtoSub(provinceName, listRequest.getPageable());
        log.info("--- Execute getProvinceCustomer method: End--");
        return ResponseEntity.ok(customerDTOS);
    }

    /**
     * Pop up danh sách dịch vụ bổ sung
     *
     */
    @Operation(description = "Pop up danh sách dịch vụ bổ sung")
    @GetMapping("/addons")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<SubscriptionAddonResDTO>> getSubscriptionAddon(
            @Parameter(description = Service.ID, example = Example.ID)
            @RequestParam(name = "developerId", required = false, defaultValue = "-1") Long developerId,
            @Parameter(description = Service.ID, example = Example.ID)
            @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
            @Parameter(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
            @RequestParam(name = "addonId", required = false, defaultValue = "-1") Long addonId,
            @Parameter(description = SwaggerConstant.User.NAME_DEV, example = SwaggerConstant.Example.BUSINESS_SCALE)
            @RequestParam(name = "periodId", required = false, defaultValue = "-1") Long periodId,
            @Parameter(description = SwaggerConstant.User.NAME_DEV, example = SwaggerConstant.Example.BUSINESS_SCALE)
            @RequestParam(name = "periodPlanId", required = true, defaultValue = "-1") Long periodPlanId,
            @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
            @RequestParam(name = "portalType", required = true, defaultValue = "DEV") PortalType portalType,
            @Parameter(description = "ID của doanh nghiệp", example = "1")
            @RequestParam(name = "companyId", required = true, defaultValue = "0") Long companyId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "2147483647") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "objectName,ASC") String sortBy,
            @Parameter(description = SwaggerConstant.Subscription.PRICING_ID_OR_COMBO_PLAN_ID, example = SwaggerConstant.Example.ID)
            @RequestParam(name = "typeId", required = true, defaultValue = "-1") Long typeId,
            @Parameter(description = SwaggerConstant.Subscription.ADDON_POP_UP_TYPE, example = SwaggerConstant.Example.ADDON_POP_UP_TYPE)
            @RequestParam(name = "popUpType", defaultValue = "PRICING") AddonPopUpTypeEnum popUpType,
            @Parameter(description = SwaggerConstant.Subscription.ADDON_IDS_NOT_IN, example = SwaggerConstant.Example.ID)
            @RequestParam(name = "addonIdsNot", required = false, defaultValue = "-1") List<Long> addonIdsNot,
            @Parameter(description = SwaggerConstant.Subscription.ADDON_POP_UP_TYPE, example = SwaggerConstant.Example.ADDON_POP_UP_TYPE)
            @RequestParam(name = "paymentCyclePricing",required = true, defaultValue = "-1") Long paymentCyclePricing,
            @Parameter(description = SwaggerConstant.Subscription.ADDON_IDS_NOT_IN, example = SwaggerConstant.Example.ID)
            @RequestParam(name = "circleTypePricing", required = true, defaultValue = "DAILY") String circleTypePricing,
            @Parameter(description = Pricing.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
            @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType
            ) {
        log.info("--- Execute getSubscriptionAddon method: Start--");
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        Page<SubscriptionAddonResDTO> subscriptionAddon = subscriptionDetailService.getSubscriptionAddon(developerId, serviceId, addonId,
            periodId, portalType, typeId, popUpType, addonIdsNot, paymentCyclePricing, circleTypePricing, periodPlanId, companyId, customerType,
            listRequest.getPageable());
        log.info("--- Execute getSubscriptionAddon method: End--");
        return ResponseEntity.ok(subscriptionAddon);
    }

    /**
     * Pop up danh sách dịch vụ
     *
     */
    @Operation(description = "Pop up danh sách dịch vụ")
    @GetMapping("/pricing")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<SubscriptionPricingPopupResDTO>> getSubscriptionPricing(
            @Parameter(description = User.ID, example = Example.ID)
            @RequestParam(name = "developId", required = false, defaultValue = "0") Long developId,
            @Parameter(description = Service.ID, example = Example.ID)
            @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
            @Parameter(description = Service.SERVICE_NAME, example = SwaggerConstant.Example.SERVICE_NAME)
            @RequestParam(name = "serviceName", required = false, defaultValue = "") String serviceName,
            @Parameter(description = Pricing.NAME, example = Example.ID)
            @RequestParam(name = "pricingName", required = false, defaultValue = "") String pricingName,
            @Parameter(description = Pricing.ID, example = Example.ID)
            @RequestParam(name = "apiType", required = false, defaultValue = "") String apiType,
            @Parameter(description = Pricing.ID, example = Example.ID)
            @RequestParam(name = "pricingIdRemove", required = false, defaultValue = "-1") Long pricingIdRemove,
            @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
            @RequestParam(name = "portalType", required = true, defaultValue = "DEV") PortalType portalType,
            @Parameter(description = "id của chu kỳ thanh toán theo gói", example = Example.ID)
            @RequestParam(name = "periodId", required = false, defaultValue = "-1") Long periodId,
            @Parameter(description = "id của chu kỳ thanh toán theo gói", example = Example.ID)
            @RequestParam(name = "periodChangeId", required = false, defaultValue = "-1") Long periodChangeId,
            @Parameter(description = Pricing.OS_SERVICE, example = Example.ID)
            @RequestParam(name = "osService", required = false, defaultValue = "UNSET") YesNoEnum isOsService,
            @Parameter(description = Pricing.PAYMENT_CYCLE, example = Example.NUMBER)
            @RequestParam(name = "numberOfCycle", required = false, defaultValue = "-1") Integer numberOfCycle,
            @Parameter(description = Pricing.CYCLE_TYPE, example = Example.CYCLE_TYPE)
            @RequestParam(name = "type", required = false, defaultValue = "UNSET") TimeTypeEnum type,
            @Parameter(description = Pricing.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
            @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
            @Parameter(description = Service.REGISTER_ECONTRACT, example = Example.REGISTER_ECONTRACT)
            @RequestParam(name = "registerEcontract", required = false, defaultValue = "-1") Integer registerEcontract,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "serviceName,ASC") String sortBy
    ) {
        log.info("--- Execute getSubscriptionPricing method: Start--");
        Page<SubscriptionPricingPopupResDTO> subscriptionPricing = subscriptionDetailService.getSubscriptionPricing(serviceId, numberOfCycle,
            type, serviceName, pricingName, pricingIdRemove, portalType, periodId, periodChangeId, isOsService, developId, customerType,
            registerEcontract, apiType, page, size, sortBy);
        log.info("--- Execute getSubscriptionPricing method: End--");
        return ResponseEntity.ok(subscriptionPricing);
    }

    /**
     * Danh sách tên dịch vụ filter
     */
    @Operation(description = "Danh sách tên dịch vụ filter")
    @GetMapping("/service-filter")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<ServiceComboReportResDTO>> getServiceNameFilter(
        @Parameter(description = Pricing.OS_SERVICE, example = Example.ID)
        @RequestParam(name = "osService", required = false, defaultValue = "UNSET") YesNoEnum isOsService,
        @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
        @RequestParam(name = "portalType", required = true, defaultValue = "DEV") PortalType portalType,
        @Parameter(description = Service.ID, example = Example.ID)
        @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
        @Parameter(description = Service.NAME, example = Example.SERVICE_NAME)
        @RequestParam(name = "name", required = false, defaultValue = "") String name,
        @Parameter(description = Service.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
        @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = Service.REGISTER_ECONTRACT, example = Example.REGISTER_ECONTRACT)
        @RequestParam(name = "registerEcontract", required = false, defaultValue = "-1") Integer registerEcontract){
        log.info("--- Execute getPricingNameFilter method: Start--");
        List<ServiceComboReportResDTO> result = pricingService.getServiceNameFilter(serviceId, name, isOsService, portalType, customerType, registerEcontract);
        log.info("--- Execute getPricingNameFilter method: End--");
        return ResponseEntity.ok(result);
    }

    /**
     * Danh sách tên gói dịch vụ cho filter
     */
    @Operation(description = "Danh sách tên gói dịch vụ cho filter")
    @GetMapping("/pricing-filter")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<PricingComboPlanReportResDTO>> getPricingNameFilter(
        @Parameter(description = Pricing.OS_SERVICE, example = Example.ID)
        @RequestParam(name = "osService", required = false, defaultValue = "UNSET") YesNoEnum isOsService,
        @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
        @RequestParam(name = "portalType", required = true, defaultValue = "DEV") PortalType portalType,
        @Parameter(description = Service.ID, example = Example.ID)
        @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
        @Parameter(description = Pricing.NAME, example = Example.SERVICE_NAME)
        @RequestParam(name = "name", required = false, defaultValue = "") String name,
        @Parameter(description = Pricing.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
        @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = Service.REGISTER_ECONTRACT, example = Example.REGISTER_ECONTRACT)
        @RequestParam(name = "registerEcontract", required = false, defaultValue = "-1") Integer registerEcontract) {
        log.info("--- Execute getPricingNameFilter method: Start--");
        List<PricingComboPlanReportResDTO> result = pricingService.getPricingNameFilter(serviceId, name, isOsService, portalType, customerType, registerEcontract);
        log.info("--- Execute getPricingNameFilter method: End--");
        return ResponseEntity.ok(result);
    }
    /**
     * Danh sách chu kỳ thanh toán filter
     */
    @Operation(description = "Danh sách chu kỳ thanh toán filter")
    @GetMapping("/pediod-filter")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<PlanPeriodResDTO>> getPediodFilter(
        @Parameter(description = Pricing.OS_SERVICE, example = Example.ID)
        @RequestParam(name = "osService", required = false, defaultValue = "UNSET") YesNoEnum isOsService,
        @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
        @RequestParam(name = "portalType", required = true, defaultValue = "DEV") PortalType portalType,
        @Parameter(description = Service.ID, example = Example.ID)
        @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
        @RequestParam(name = "comboId", required = false, defaultValue = "-1") Long comboId,
        @Parameter(description = "Nhập tìm kiếm chu kỳ thanh toán", example = Example.PERIOD)
        @RequestParam(name = "name", required = false, defaultValue = "") String name,
        @Parameter(description = Pricing.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
        @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = Service.REGISTER_ECONTRACT, example = Example.REGISTER_ECONTRACT)
        @RequestParam(name = "registerEcontract", required = false, defaultValue = "-1") Integer registerEcontract) {
        log.info("--- Execute getPediodFilter method: Start--");
        List<PlanPeriodResDTO> result = pricingService.getPeriodFilter(serviceId, comboId, name, isOsService, portalType, customerType, registerEcontract);
        log.info("--- Execute getPediodFilter method: End--");
        return ResponseEntity.ok(result);
    }

    /**
     * Danh sách tên nhà PT của popup pricing
     */
    @GetMapping("/user-filter")
    @Operation(description = "Lấy danh sách các ten nha PT")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<UserNameFilterResDTO>> getUserName(
        @Parameter(description = Pricing.OS_SERVICE, example = Example.ID)
        @RequestParam(name = "osService", required = false, defaultValue = "UNSET") YesNoEnum osService,
        @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
        @RequestParam(name = "portalType", required = true, defaultValue = "ADMIN") PortalType portal,
        @Parameter(description = Service.ID, example = Example.ID)
        @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
        @Parameter(description = Service.NAME, example = Example.SERVICE_NAME)
        @RequestParam(name = "name", required = false, defaultValue = "") String name) {
        log.info("--- Execute getUserNameFilter method: Start --");
        List<UserNameFilterResDTO> userNameList = pricingService.getUserNameFilter(serviceId, osService, portal, name);
        log.info("--- Execute getUserNameFilter method: End --");
        return ResponseEntity.ok().body(userNameList);
    }

    /**
     * Popup chi tiết cách tính – Khối lượng
     *
     */
    @Operation(description = "Popup chi tiết cách tính – Khối lượng")
    @PostMapping("/calculate/volume/type/{type}/{id}/quantity/{quantity}")
    public ResponseEntity<List<SubscriptionCalculateDTO>> getCalculateQuantity(
            @Parameter(description = "ID pricing", example = "1")
            @PathVariable Long id,
            @Parameter(description = "Số lượng gói", example = "10")
            @PathVariable Long quantity,
            @Parameter(description = "Loại", example = "ADDON")
            @PathVariable PopupTypeEnum type,
            @Valid @RequestBody UnitLimitedReqDTO unitLimitedReqDTO,
            @Parameter(description = "pricingMultiPlanId", example = "1")
            @RequestParam(required = false) Long pricingMultiPlanId) {
        log.info("--- Execute getCalculateStairStep method: Start--");
        List<SubscriptionCalculateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
				quantity == null ? null : quantity.longValue(), pricingMultiPlanId, PricingPlanEnum.VOLUME, i -> i,
				type, unitLimitedReqDTO.getUnitLimitedNews().stream().map(PlanPriceListCalculator::map)
						.collect(Collectors.toList()));
        log.info("--- Execute getCalculateStairStep method: End--");
        return ResponseEntity.ok(res);
    }

    /**
     * Popup chi tiết cách tính Khối lượng
     *
     */
    @Operation(description = "Popup chi tiết cách tính Khối lượng (Admin, Dev)")
    @PostMapping("/calculate/volume/pricing/{id}")
    public ResponseEntity<List<SubscriptionCalculateDTO>> getCalculateAdminAndDevVolume(
            @Parameter(description = "ID pricing", example = "1")
            @PathVariable Long id,
            @RequestBody List<SubscriptionCalculateDTO> data,
            @Parameter(description = "pricingMultiPlanId", example = "1")
            @RequestParam(required = false) Long pricingMultiPlanId) {
        log.info("--- Execute getCalculateAdminAndDevVolume method: Start--");
        List<SubscriptionCalculateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
				data.stream().map(SubscriptionCalculateDTO::getQuantity).reduce(0L, (i1, i2) -> i1 + i2),
				pricingMultiPlanId, PricingPlanEnum.VOLUME, i -> i, PopupTypeEnum.PRICING, data);
        log.info("--- Execute getCalculateAdminAndDevVolume method: End--");
        return ResponseEntity.ok(res);
    }


    /**
     * Popup chi tiết cách tính – Bậc thang
     *
     */
    @Operation(description = "Popup chi tiết cách tính – Bậc thang")
    @PostMapping("/calculate/stair-step/type/{type}/{id}/quantity/{quantity}")
    public ResponseEntity<List<SubscriptionCalculateDTO>> getCalculateStairStep(
            @Parameter(description = "ID pricing", example = "1")
            @PathVariable Long id,
            @Parameter(description = "Số lượng gói", example = "10")
            @PathVariable Long quantity,
            @Parameter(description = "Loại", example = "ADDON")
            @PathVariable PopupTypeEnum type,
            @Valid @RequestBody UnitLimitedReqDTO unitLimitedReqDTO,
            @Parameter(description = "pricingMultiPlanId", example = "1")
            @RequestParam(required = false) Long pricingMultiPlanId) {
        log.info("--- Execute getCalculateStairStep method: Start--");
        List<SubscriptionCalculateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
				quantity == null ? null : quantity.longValue(), pricingMultiPlanId, PricingPlanEnum.STAIR_STEP, i -> i,
				type, unitLimitedReqDTO.getUnitLimitedNews().stream().map(PlanPriceListCalculator::map)
						.collect(Collectors.toList()));
        log.info("--- Execute getCalculateStairStep method: End--");
        return ResponseEntity.ok(res);
    }

    /**
     * Popup chi tiết cách tính Bậc thang
     *
     */
    @Operation(description = "Popup chi tiết cách tính Bậc thang (Admin, Dev)")
    @PostMapping("/calculate/stair-step/pricing/{id}")
    public ResponseEntity<List<SubscriptionCalculateDTO>> getCalculateAdminAndDevStairStep(
            @Parameter(description = "ID pricing", example = "1")
            @PathVariable Long id,
            @RequestBody List<SubscriptionCalculateDTO> data,
            @Parameter(description = "pricingMultiPlanId", example = "1")
            @RequestParam(required = false) Long pricingMultiPlanId) {
        log.info("--- Execute getCalculateAdminAndDevStairStep method: Start--");
        List<SubscriptionCalculateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
				data.stream().map(SubscriptionCalculateDTO::getQuantity).reduce(0L, (i1, i2) -> i1 + i2),
				pricingMultiPlanId, PricingPlanEnum.STAIR_STEP, i -> i, PopupTypeEnum.PRICING, data);
        log.info("--- Execute getCalculateAdminAndDevStairStep method: End--");
        return ResponseEntity.ok(res);
    }

    /**
     * Popup chi tiet cach tinh lũy kế dịch vụ bổ sung
     *
     */
    @PostMapping("/calculate/tier/addon/{id}/{quantity}")
    @Operation(description = "Chi tiết cách tính lũy kế dịch vụ bổ sung")
    public ResponseEntity<List<SubscriptionAccumulateDTO>> getAddonTier(
            @Parameter(description = SwaggerConstant.Addon.ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long id,
            @Parameter(description = SwaggerConstant.Subscription.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
            @PathVariable Integer quantity,
            @Parameter(description = Subscription.ID, example = Example.ID)
            @RequestParam(name = "pricingMultiPlanId", defaultValue = "-1", required = false) Long pricingMultiPlanId,
            @Valid @RequestBody UnitLimitedNewDTO unitLimitedNewDTO
    ) {
        log.info("--- Execute getAddonTier method: Start--");
        List<SubscriptionAccumulateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
				quantity == null ? null : quantity.longValue(), pricingMultiPlanId, PricingPlanEnum.TIER,
				PlanPriceListCalculator::map, PopupTypeEnum.ADDON, unitLimitedNewDTO.getUnitLimitedNews().stream()
						.map(PlanPriceListCalculator::map).collect(Collectors.toList()));
        log.info("--- Execute getAddonTier method: End--");
        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    /**
     * Add phí áp dụng một lần
     *
     */
    @Operation(description = "Add phí áp dụng một lần")
    @PostMapping("/fees-apply")
    public ResponseEntity<ApplyFeeDTO> addApplyFee(
        @Validated @RequestBody ApplyFeeByDevAdminDTO applyFeeByDevAdminDTO
    ) {
        log.info("--- Execute AddApplyFee method: Start--");
        ApplyFeeDTO applyFee = subscriptionService.addApplyFee(applyFeeByDevAdminDTO);
        log.info("--- Execute AddApplyFee method: End--");
        return ResponseEntity.ok(applyFee);
    }

    /**
     * Xoá phí áp dụng một lần
     *
     * @return id phí bị xóa
     */
    @Operation(description = "Xóa phí áp dụng một lần")
    @DeleteMapping("/delete-apply-fees")
    public ResponseEntity<ApplyFeeDTO> deleteApplyFee(
        @Valid @RequestBody ApplyFeeDTO applyFeeDTO
    ) {
        log.info("--- Execute DeleteApplyFee method: Start--");
        ApplyFeeDTO applyFee = subscriptionService.deleteApplyFee(applyFeeDTO);
        log.info("--- Execute DeleteApplyFee method: Start--");
        return ResponseEntity.ok(applyFee);
    }

    /**
     * Danh sach thue bao tao boi dev/admin
     *
     * @param searchText searchText
     * @param status     status
     * @param portalType portalType
     * @param cusName    cusName
     * @param developerName    developerName
     * @param size       size
     * @param page       page
     * @param sort       sort
     * @return Page<SubscriptionByDevOrAdminDTO>
     */
    @Operation(summary = "Tìm kiếm danh sách subscription dev/admin tương ứng tạo",
        description = "Tìm kiếm danh sách subscription dev/admin tương ứng tạo",
        tags = {"subscription"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Tìm kiếm thành công",
            content = @Content(
                array = @ArraySchema(
                    schema = @Schema(implementation = MyServiceSubscriptionResDTO.class)))),
        @ApiResponse(responseCode = "400", description = "Input không hợp lệ")
    })
    @GetMapping
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<SubscriptionByDevOrAdminDTONew>> findAllByPortalType(
        @Parameter(description = "tên gói/dịch vụ", example = "abc")
        @RequestParam(value = "search",defaultValue = "",required = false) String searchText,
        @Parameter(description = "Trạng thái hoạt động", example = "ACTIVE")
        @RequestParam(value = "status", required = false) SubscriptionStatusEnum status,
        @Parameter(description = "kiểu tài khoản ", example = "dev")
        @RequestParam(value = "portalType", required = false) String portalType,
        @Parameter(description = SwaggerConstant.Service.CUSTOMER_TYPE_CODE, example = SwaggerConstant.Example.CUSTOMER_TYPE_NAME)
        @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = "Họ tên khách hàng", example = "Cường Lương")
        @RequestParam(value = "cusName", defaultValue = "", required = false) String cusName,
        @Parameter(description = "Ho ten khach hang ", example = "Luong Cuong")
        @RequestParam(value = "developerName", defaultValue = "", required = false) String developerName,
        @Parameter(description = User.TIN, example = Example.TIN)
        @RequestParam(value = "tin", defaultValue = "", required = false) String tin,
        @Parameter(description = "Email của khách hàng doanh nghiệp ", example = SwaggerConstant.Example.EMAIL)
        @RequestParam(value = "email", defaultValue = "", required = false) String email,
        @Parameter(description = "Số điện thoại của doanh nghiệp", example = SwaggerConstant.Example.PHONE)
        @RequestParam(value = "phone", defaultValue = "", required = false) String phone,
        @Parameter(description = "Người đại diện", example = SwaggerConstant.Example.NAME_DEV)
        @RequestParam(value = "representative", defaultValue = "", required = false) String representative,
        @RequestParam(name = "foundingFrom", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MIN_DATE)
        @DateTimeFormat(pattern = "dd/MM/yyyy") LocalDate foundingFrom,
        @RequestParam(name = "foundingTo", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MAX_DATE)
        @DateTimeFormat(pattern = "dd/MM/yyyy") LocalDate foundingTo,
        @Parameter(description = "Id ngành nghề kinh doanh", example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "businessAreaId", required = false, defaultValue = "-1") Integer businessAreaId,
        @Parameter(description = "Id quy mô doanh nghiệp", example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "businessSizeId", required = false, defaultValue = "-1") Integer businessSizeId,
        @Parameter(description = "Nguồn tạo", example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "createdSource", required = false, defaultValue = "-1") Integer createdSource,
        @RequestParam(name = "createdFrom", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MIN_DATE_TIME)
        @DateTimeFormat(pattern = "dd/MM/yyyy HH:mm:ss") LocalDateTime createdFrom,
        @RequestParam(name = "createdTo", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MAX_DATE_TIME)
        @DateTimeFormat(pattern = "dd/MM/yyyy HH:mm:ss") LocalDateTime createdTo,
        @Parameter(description = "Mã tỉnh thành", example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "provinceId", required = false, defaultValue = "-1") Long provinceId,
        @RequestParam(name = "paymentDateFrom", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MIN_DATE_TIME)
        @DateTimeFormat(pattern = "dd/MM/yyyy HH:mm:ss") LocalDateTime paymentDateFrom,
        @RequestParam(name = "paymentDateTo", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MAX_DATE_TIME)
        @DateTimeFormat(pattern = "dd/MM/yyyy HH:mm:ss") LocalDateTime paymentDateTo,
        @Parameter(description = "Chu kỳ thanh toán", example = SwaggerConstant.Example.DATA)
        @RequestParam(value = "paymentCycleStr", defaultValue = "-1", required = false) String paymentCycle,
        @RequestParam(name = "nextPaymentDateFrom", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MIN_DATE_TIME)
        @DateTimeFormat(pattern = "dd/MM/yyyy HH:mm:ss") LocalDateTime nextPaymentDateFrom,
        @RequestParam(name = "nextPaymentDateTo", required = false, defaultValue = CreditNoteConst.SqlVar.DEFAULT_MAX_DATE_TIME)
        @DateTimeFormat(pattern = "dd/MM/yyyy HH:mm:ss") LocalDateTime nextPaymentDateTo,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = "sắp xếp theo", example = "createAt,desc")
        @RequestParam(name = "sort", required = false, defaultValue = "createAt,desc") String sort) {
        log.info("--- Execute findAll method: Start--");
        ListRequest req = new ListRequest(size, page, sort);
        Page<SubscriptionByDevOrAdminDTONew> result = subscriptionDetailService.findAllByPortalType(searchText, customerType, status, portalType,
            cusName, developerName, tin, email, phone, representative, businessAreaId, businessSizeId, createdFrom, createdTo, paymentCycle,
            createdSource, foundingFrom, foundingTo, nextPaymentDateFrom, nextPaymentDateTo, paymentDateFrom, paymentDateTo, provinceId,
            req.getPageable());
        log.info("--- Execute findAll method: End --");
        return ResponseEntity.ok().body(result);
    }

    /**
     * Popup chi tiet cach tinh lũy kế dịch vụ bổ sung
     *
     */
    @PostMapping("/calculate/tier/pricing/{id}/{quantity}")
    @Operation(description = "Chi tiết cách tính lũy kế gói dịch vụ")
    public ResponseEntity<List<SubscriptionAccumulateDTO>> getPricingTier(
            @Parameter(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long id,
            @Parameter(description = SwaggerConstant.Subscription.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
            @PathVariable Long quantity,
            @Parameter(description = Subscription.ID, example = Example.ID)
            @RequestParam(name = "pricingMultiPlanId", defaultValue = "-1", required = false) Long pricingMultiPlanId,
            @Valid @RequestBody UnitLimitedNewDTO unitLimitedNewDTO
    ) {
        log.info("--- Execute getPricingTier method: Start--");
        List<SubscriptionAccumulateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id, quantity,
				pricingMultiPlanId, PricingPlanEnum.TIER, PlanPriceListCalculator::map, PopupTypeEnum.PRICING,
				unitLimitedNewDTO.getUnitLimitedNews().stream().map(PlanPriceListCalculator::map)
						.collect(Collectors.toList()));
        log.info("--- Execute getPricingTier method: End--");
        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    /**
     * Popup chi tiết cách tính Bậc thang (Addon)
     *
     */
    @Operation(description = "Popup chi tiết cách tính Bậc thang (Admin, Dev)")
    @PostMapping("/calculate/stair-step/addon/{id}")
    public ResponseEntity<List<SubscriptionCalculateDTO>> getCalculateAdminAndDevStairStepAddon(
        @Parameter(description = "ID Addon", example = "1")
        @PathVariable Long id,
        @RequestBody List<SubscriptionCalculateDTO> data,
        @Parameter(description = "pricingMultiPlanId", example = "1")
        @RequestParam(required = false) Long pricingMultiPlanId) {
        log.info("--- Execute getCalculateAdminAndDevStairStepAddon method: Start--");
        List<SubscriptionCalculateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
				data.stream().map(SubscriptionCalculateDTO::getQuantity).reduce(0L, (i1, i2) -> i1 + i2),
				pricingMultiPlanId, PricingPlanEnum.STAIR_STEP, i -> i, PopupTypeEnum.ADDON, data);
        log.info("--- Execute getCalculateAdminAndDevStairStepAddon method: End--");
        return ResponseEntity.ok(res);
    }

    /**
     * Popup chi tiết cách tính khối lượng (Addon)
     *
     */
    @Operation(description = "Popup chi tiết cách tính khối lượng (Admin, Dev)")
    @PostMapping("/calculate/volume/addon/{id}")
    public ResponseEntity<List<SubscriptionCalculateDTO>> getCalculateAdminAndDevVolumeAddon(
        @Parameter(description = "ID pricing", example = "1")
        @PathVariable Long id,
        @RequestBody List<SubscriptionCalculateDTO> data,
        @Parameter(description = "pricingMultiPlanId", example = "1")
        @RequestParam(required = false) Long pricingMultiPlanId) {
        log.info("--- Execute getCalculateAdminAndDevVolumeAddon method: Start--");
        List<SubscriptionCalculateDTO> res = subscriptionCalculateService.calculatePlanPriceList(id,
            data.stream().map(SubscriptionCalculateDTO::getQuantity).reduce(0L, (i1, i2) -> i1 + i2), pricingMultiPlanId, PricingPlanEnum.VOLUME,
            i -> i, PopupTypeEnum.ADDON, data);
        log.info("--- Execute getCalculateAdminAndDevVolumeAddon method: End--");
        return ResponseEntity.ok(res);
    }

    /**
     * Popup chọn CTKM cho gói dịch vụ chính
     */
    @Operation(description = "Popup chọn CTKM cho gói dịch vụ chính")
    @GetMapping("/pricing/coupons/{companyId}/{pricingId}")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CouponPopupDTO>> getSubscriptionPricingCoupons(
        @Parameter(description = "ID của doanh nghiệp", example = "1")
        @PathVariable("companyId") Long companyId,
        @Parameter(description = "ID của gói dịch vụ", example = "1")
        @PathVariable("pricingId") Long pricingId,
        @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
        @Parameter(description = "ID của chu kỳ thanh toán", example = Example.ID)
        @RequestParam(name = "multiPlanId", required = false, defaultValue = "-1") Long multiPlanId,
        @RequestParam(name = "variantId", required = false, defaultValue = "-1") Long variantId,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
        @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
        @RequestParam(name = "portalType", required = false) PortalType portalType,
        @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
        @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
        @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
        @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
        @Parameter(description = Coupon.ID, example = Example.ID)
        @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        log.info("--- Execute getCouponsPopup method: Start ---");
        String classify = CouponConst.COUPON_OF_PRICING;
        Page<CouponPopupDTO> couponsPopup = couponService.getCouponsPopup(variantId, multiPlanId, companyId, customerType, pricingId, page, size,
            sort, classify, couponIds, portalType, price, quanlity);
        log.info("--- Execute getCouponsPopup method: End ---");
        return ResponseEntity.ok().body(couponsPopup);
    }

    /**
     * Popup chọn CTKM + CDQC cho gói dịch vụ chính
     */
    @Operation(description = "Popup chọn CTKM cho gói dịch vụ chính")
    @PostMapping("/pricing/coupon-mc-promotion/{companyId}/{pricingId}")
    public ResponseEntity<Page<CouponMcPopupDTO>> getSubscriptionPricingCouponMc(
            @Valid @RequestBody CouponMcDevAdminReqDTO couponMcDevAdminReqDTO,
            @Parameter(description = "ID của doanh nghiệp", example = "1")
            @PathVariable("companyId") Long companyId,
            @Parameter(description = "ID của gói dịch vụ", example = "1")
            @PathVariable("pricingId") Long pricingId,
            @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
            @Parameter(description = "ID của chu kỳ thanh toán", example = Example.ID)
            @RequestParam(name = "multiPlanId", required = false, defaultValue = "-1") Long multiPlanId,
            @RequestParam(name = "variantId", required = false, defaultValue = "-1") Long variantId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
            @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
            @RequestParam(name = "portalType", required = false) PortalType portalType,
            @Parameter(description = Coupon.ID, example = Example.ID)
            @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        String classify = CouponConst.COUPON_OF_PRICING;
        if (Objects.equals(PortalType.SME, portalType)) companyId = AuthUtil.getCurrentUserId();
        // Danh sách CTKM
        List<CouponPopupDTO> couponsPopup = couponService.getAllCouponsPopup(variantId, multiPlanId, companyId, customerType,
            pricingId, classify, couponIds, portalType);
        // Danh sách CDQC
        McPurchasingInfoDTO mcPurchasingInfoDTO = couponMcDevAdminReqDTO.getMcPurchaseInfoDTO();
        mcPurchasingInfoDTO.setUserId(companyId);
        AtomicReference<McPurchasingItemDTO> focusItem = new AtomicReference<>();
        PurchaseItemEnum focusType = PurchaseItemEnum.PRICING;
        Long focusPmpId = (multiPlanId == -1 ? null : multiPlanId);
        mcPurchasingInfoDTO.getLstPricing().forEach(item -> {
            val mainItem = item.getPricingInfo();
            if (mainItem.getItemId() != null && mainItem.getType().ordinal() == focusType.ordinal() &&
                mainItem.getItemId().equals(pricingId) &&
                Objects.equals(mainItem.getPmpId(), focusPmpId)) {
                focusItem.set(mainItem);
            }
        });

        val mcPromotionList = campaignSmeService.getListPromotion(mcPurchasingInfoDTO,
                focusItem.get());

        List<CouponMcPopupDTO> totalList = new LinkedList<>();
        // Check giá tối thiểu
        if (couponMcDevAdminReqDTO.totalPreTaxAmount == null) {
            couponMcDevAdminReqDTO.totalPreTaxAmount = BigDecimal.ZERO;
        }
        couponsPopup.forEach(item -> {
            if(item.getMinimumAmount() != null && couponMcDevAdminReqDTO.totalPreTaxAmount.compareTo(BigDecimal.valueOf(item.getMinimumAmount())) >= 0) {
                totalList.add(new CouponMcPopupDTO(item));
            }
        });

        if (mcPromotionList != null && mcPromotionList.size() > 0) {
            mcPromotionList.forEach(item -> {
                totalList.add(new CouponMcPopupDTO(item));
            });
        }

        // Sort coupon and mc
        val mapScore = totalList
            .stream()
            .collect(Collectors.toMap(Function.identity(), item -> {
                boolean applicable;
                DiscountTypeEnum discountType;
                if (item.promotionPopupType == PromotionPopupType.COUPON) {
                    applicable =
                        ((couponMcDevAdminReqDTO.totalPreTaxAmount != null) ? couponMcDevAdminReqDTO.totalPreTaxAmount.longValue() : 0) >=
                            item.couponPopupDTO.getMinimumAmount();
                    discountType = item.couponPopupDTO.getDiscountType();
                } else {
                    applicable = item.mcPopupDTO.isApplicable();
                    discountType = item.mcPopupDTO.getIconIndicator() == McIconIndicatorEnum.DISCOUNT_AMOUNT ? DiscountTypeEnum.PRICE : DiscountTypeEnum.PERCENT;
                }

                return calculateOrderScore(applicable, item.promotionPopupType, discountType);
            } ));

        totalList.sort((item1, item2) -> mapScore.get(item2) - mapScore.get(item1));

        // Create page
        int startIdx = page * size;
        if (startIdx > totalList.size()) {
            startIdx = totalList.size();
        }
        int endIdx = startIdx + size;
        if (endIdx > totalList.size()) {
            endIdx = totalList.size();
        }
        Page<CouponMcPopupDTO> promotionPage = new PageImpl<>(totalList.subList(startIdx, endIdx), PageRequest.of(page, size), totalList.size());

        log.info("--- Execute getSubscriptionPricingCouponMc method: End ---");
        return ResponseEntity.ok().body(promotionPage);
    }

    @Operation(description = "Popup chọn CTKM cho gói dịch vụ chính")
    @PostMapping("/pricing-addon/coupon-mc-promotion/{companyId}/{pricingId}")
    public ResponseEntity<Page<CouponMcPopupDTO>> getSubscriptionPricingAddonCouponMc(
        @Valid @RequestBody CouponMcDevAdminReqDTO couponMcDevAdminReqDTO,
        @Parameter(description = "ID của doanh nghiệp", example = "1")
        @PathVariable("companyId") Long companyId,
        @Parameter(description = "ID của gói dịch vụ", example = "1")
        @PathVariable("pricingId") Long pricingId,
        @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
        @Parameter(description = "ID của chu kỳ thanh toán", example = Example.ID)
        @RequestParam(name = "pricingMultiPlanId", required = false, defaultValue = "-1") Long pricingMultiPlanId,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
        @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
        @RequestParam(name = "portalType", required = false) PortalType portalType,
        @Parameter(description = Coupon.ID, example = Example.ID)
        @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        if (Objects.equals(PortalType.SME, portalType)) {
            companyId = AuthUtil.getCurrentUserId();
        }
        // Danh sách CTKM
        List<CouponPopupDTO> couponsPopup = couponService.getAllCouponsPricingAddonPopup(pricingId, pricingMultiPlanId,
            couponMcDevAdminReqDTO.getAddonIds(), couponMcDevAdminReqDTO.getLstAddonMultiPlanId(), companyId,
            customerType, couponIds, portalType);
        // Danh sách CDQC
        McPurchasingInfoDTO mcPurchasingInfoDTO = couponMcDevAdminReqDTO.getMcPurchaseInfoDTO();
        mcPurchasingInfoDTO.setUserId(companyId);
        AtomicReference<McPurchasingItemDTO> focusItem = new AtomicReference<>();
        Long focusPmpId = (pricingMultiPlanId == -1 ? null : pricingMultiPlanId);
        List<Long> focusItemAddonId = couponMcDevAdminReqDTO.getAddonIds();
        mcPurchasingInfoDTO.getLstPricing().forEach(item -> {
            val mainItem = item.getPricingInfo();
            if (mainItem.getItemId() != null &&
                mainItem.getType() == CalculateTypeEnum.PRICING &&
                mainItem.getItemId().equals(pricingId) &&
                Objects.equals(mainItem.getPmpId(), focusPmpId)) {
                focusItem.set(mainItem);
            }
            val addonList = item.getLstAddon();
            if (addonList != null && !addonList.isEmpty()) {
                addonList.forEach(addon -> {
                    if (focusItemAddonId.contains(addon.getItemId())) {
                        focusItem.set(addon);
                    }
                });
            }
        });
        val mcPromotionList = campaignSmeService.getListPromotion(mcPurchasingInfoDTO, focusItem.get());
        // Thông tin tổng tiền hóa đơn
        BigDecimal currentTotalPreTaxAmount = ObjectUtil.getOrDefault(couponMcDevAdminReqDTO.totalPreTaxAmount, BigDecimal.ZERO);
        // Tổng hợp và sắp xếp thông tin
        Page<CouponMcPopupDTO> promotionPage = gatheringPopupDTO(currentTotalPreTaxAmount, couponsPopup, mcPromotionList, page, size);
        return ResponseEntity.ok().body(promotionPage);
    }

    private int calculateOrderScore(boolean applicable, PromotionPopupType popupType, DiscountTypeEnum discountType) {
        int score = 0;
        if (applicable) {
            score += 1000000;
        }

        if (popupType == PromotionPopupType.COUPON) {
            score += 100000;
        }

        if (discountType == DiscountTypeEnum.PRICE) {
            score += 10000;
        }

        return score;
    }

    /**
     * Popup chọn CTKM cho gói combo plan
     */
    @Operation(description = "Popup chọn CTKM cho gói combo plan")
    @GetMapping("/combo-plan/coupons/{companyId}/{comboPlanId}")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CouponPopupDTO>> getSubscriptionComboPlanCoupons(
        @Parameter(description = "ID của doanh nghiệp", example = "1")
        @PathVariable("companyId") Long companyId,
        @Parameter(description = "ID của gói dịch vụ", example = "1")
        @PathVariable("comboPlanId") Long comboPlanId,
        @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
        @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
        @RequestParam(name = "portalType", required = false) PortalType portalType,
        @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
        @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
        @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
        @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
        @Parameter(description = Coupon.ID, example = Example.ID)
        @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        log.info("--- Execute getSubscriptionComboPlanCoupons method: Start ---");
        String classify = CouponConst.COUPON_OF_COMBO_PLAN;
        Page<CouponPopupDTO> couponsPopup = couponService.getCouponsPopup(PricingConst.DEFAULT_ID, PricingConst.DEFAULT_ID, companyId,
            customerType, comboPlanId, page, size, sort, classify, couponIds, portalType, price, quanlity);
        log.info("--- Execute getSubscriptionComboPlanCoupons method: End ---");
        return ResponseEntity.ok().body(couponsPopup);
    }

    @Operation(description = "Popup chọn CTKM cho gói combo plan")
    @PostMapping("/combo-plan/coupon-mc-promotion/{companyId}/{comboPlanId}")
    public ResponseEntity<Page<CouponMcPopupDTO>> getSubscriptionComboPlanCouponMc(
            @Valid @RequestBody CouponMcDevAdminReqDTO couponMcDevAdminReqDTO,
            @Parameter(description = "ID của doanh nghiệp", example = "1")
            @PathVariable("companyId") Long companyId,
            @Parameter(description = "ID của gói dịch vụ", example = "1")
            @PathVariable("comboPlanId") Long comboPlanId,
            @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
            @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
            @RequestParam(name = "portalType", required = false) PortalType portalType,
            @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
            @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
            @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
            @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
            @Parameter(description = Coupon.ID, example = Example.ID)
            @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        String classify = CouponConst.COUPON_OF_COMBO_PLAN;
        if (Objects.equals(PortalType.SME, portalType)) companyId = AuthUtil.getCurrentUserId();
        // Danh sách CTKM
        List<CouponPopupDTO> couponsPopup = couponService.getAllCouponsPopup(PricingConst.DEFAULT_ID, PricingConst.DEFAULT_ID, companyId,
            customerType, comboPlanId, classify, couponIds, portalType);
        // Danh sách CDQC
        McPurchasingInfoDTO mcPurchasingInfoDTO = couponMcDevAdminReqDTO.getMcPurchaseInfoDTO();
        mcPurchasingInfoDTO.setUserId(companyId);
        AtomicReference<McPurchasingItemDTO> focusItem = new AtomicReference<>();
        mcPurchasingInfoDTO.getLstPricing().forEach(item -> {
            val mainItem = item.getPricingInfo();
            if (mainItem.getType() == CalculateTypeEnum.COMBO &&
                mainItem.getItemId().equals(comboPlanId)) {
                focusItem.set(mainItem);
            }
        });
        val mcPromotionList = campaignSmeService.getListPromotion(mcPurchasingInfoDTO, focusItem.get());
        // Thông tin tổng tiền hóa đơn
        BigDecimal currentTotalPreTaxAmount = ObjectUtil.getOrDefault(couponMcDevAdminReqDTO.totalPreTaxAmount, BigDecimal.ZERO);
        // Tổng hợp và sắp xếp thông tin
        Page<CouponMcPopupDTO> promotionPage = gatheringPopupDTO(currentTotalPreTaxAmount, couponsPopup, mcPromotionList, page, size);
        return ResponseEntity.ok().body(promotionPage);
    }

    /**
     * Popup chọn CTKM cho gói dịch vụ bổ sung
     */
    @Operation(description = "Popup chọn CTKM cho gói dịch vụ bổ sung")
    @GetMapping("/addon/coupons/{companyId}/{addonId}")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CouponPopupDTO>> getSubscriptionAddonCoupons(
        @Parameter(description = "ID của doanh nghiệp", example = "1")
        @PathVariable("companyId") Long companyId,
        @Parameter(description = "ID của gói dịch vụ bổ sung", example = "1")
        @PathVariable("addonId") Long addonId,
        @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
        @Parameter(description = "ID của chu kỳ thanh toán", example = Example.ID)
        @RequestParam(name = "multiPlanId", required = false, defaultValue = "-1") Long multiPlanId,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
        @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
        @RequestParam(name = "portalType", required = false) PortalType portalType,
        @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
        @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
        @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
        @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
        @Parameter(description = Coupon.ID, example = Example.ID)
        @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        log.info("--- Execute getCouponsPopup method: Start ---");
        String classify = CouponConst.COUPON_OF_ADDON;
        Page<CouponPopupDTO> couponsPopup = couponService.getCouponsPopup(PricingConst.DEFAULT_ID, multiPlanId, companyId, customerType, addonId, page, size,
            sort, classify, couponIds, portalType, price, quanlity);
        log.info("--- Execute getCouponsPopup method: End ---");
        return ResponseEntity.ok().body(couponsPopup);
    }

    @Operation(description = "Popup chọn CTKM cho gói dịch vụ bổ sung")
    @PostMapping("/addon/coupon-mc-promotion/{companyId}/{addonId}")
    public ResponseEntity<Page<CouponMcPopupDTO>> getSubscriptionAddonCouponMc(
            @Valid @RequestBody CouponMcDevAdminReqDTO couponMcDevAdminReqDTO,
            @Parameter(description = "ID của doanh nghiệp", example = "1")
            @PathVariable("companyId") Long companyId,
            @Parameter(description = "ID của gói dịch vụ bổ sung", example = "1")
            @PathVariable("addonId") Long addonId,
            @RequestParam(name = "customerType", required = false, defaultValue = "ALL") String customerType,
            @Parameter(description = "ID của chu kỳ thanh toán", example = Example.ID)
            @RequestParam(name = "multiPlanId", required = false, defaultValue = "-1") Long multiPlanId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
            @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
            @RequestParam(name = "portalType", required = false) PortalType portalType,
            @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
            @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
            @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
            @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
            @Parameter(description = Coupon.ID, example = Example.ID)
            @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        String classify = CouponConst.COUPON_OF_ADDON;
        if (Objects.equals(PortalType.SME, portalType)) companyId = AuthUtil.getCurrentUserId();
        // Danh sách CTKM
        List<CouponPopupDTO> couponsPopup = couponService.getAllCouponsPopup(PricingConst.DEFAULT_ID, multiPlanId, companyId, customerType, addonId,
            classify, couponIds, portalType);
        // Danh sách CDQC
        McPurchasingInfoDTO mcPurchasingInfoDTO = couponMcDevAdminReqDTO.getMcPurchaseInfoDTO();
        mcPurchasingInfoDTO.setUserId(companyId);
        AtomicReference<McPurchasingItemDTO> focusItem = new AtomicReference<>();
        mcPurchasingInfoDTO.getLstPricing().forEach(item -> {
            // Tìm focusItem trong danh sách addon của các gói cước
            val addonList = item.getLstAddon() ;
            if (addonList != null && !addonList.isEmpty()) {
                addonList.forEach(addon -> {
                    if (addon.getItemId().equals(addonId)) {
                        focusItem.set(addon);
                    }
                });
            }
        });
        val mcPromotionList = campaignSmeService.getListPromotion(mcPurchasingInfoDTO, focusItem.get());
        // Thông tin tổng tiền hóa đơn
        BigDecimal currentTotalPreTaxAmount = ObjectUtil.getOrDefault(couponMcDevAdminReqDTO.totalPreTaxAmount, BigDecimal.ZERO);
        // Tổng hợp và sắp xếp thông tin
        Page<CouponMcPopupDTO> promotionPage = gatheringPopupDTO(currentTotalPreTaxAmount, couponsPopup, mcPromotionList, page, size);
        return ResponseEntity.ok().body(promotionPage);
    }

    /**
     * Popup chọn CTKM trên tổng hóa đơn khi đăng ký thuê bao gói dịch vụ
     */
    @Operation(description = "Popup chọn CTKM trên tổng hóa đơn khi đăng ký thuê bao gói dịch vụ")
    @GetMapping("/total/coupons/{companyId}/{pricingId}")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CouponPopupDTO>> getSubscriptionAddonCouponsForTotalBillWhenSubsPricing(
        @Parameter(description = "ID của doanh nghiệp", example = "1")
        @PathVariable("companyId") Long companyId,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
        @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
        @RequestParam(name = "portalType", required = false) PortalType portalType,
        @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
        @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
        @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
        @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
        @Parameter(description = Coupon.ID, example = Example.ID)
        @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        log.info("--- Execute getSubscriptionAddonCouponsForTotalBillWhenSubsPricing method: Start ---");
        String classify = CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING;
        Page<CouponPopupDTO> couponsPopup = couponService.getCouponsPopupForTotalBill(companyId, page, size, sort, classify, couponIds,
            portalType, price, quanlity, null);
        log.info("--- Execute getSubscriptionAddonCouponsForTotalBillWhenSubsPricing method: End ---");
        return ResponseEntity.ok().body(couponsPopup);
    }

    @Operation(description = "Popup chọn CTKM + CDQC trên tổng hóa đơn khi đăng ký thuê bao gói dịch vụ")
    @PostMapping("/total/coupon-mc-promotion/{companyId}/{pricingId}")
    public ResponseEntity<Page<CouponMcPopupDTO>> getSubscriptionCouponMcForTotalBillWhenSubsPricing(
            @Valid @RequestBody CouponMcDevAdminReqDTO couponMcDevAdminReqDTO,
            @Parameter(description = "ID của doanh nghiệp", example = "1")
            @PathVariable("companyId") Long companyId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
            @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
            @RequestParam(name = "portalType", required = false) PortalType portalType,
            @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
            @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
            @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
            @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
            @Parameter(description = Coupon.ID, example = Example.ID)
            @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        String classify = CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING;
        if (Objects.equals(PortalType.SME, portalType)) companyId = AuthUtil.getCurrentUserId();
        // Danh sách CTKM
        List<CouponPopupDTO> couponsPopup = couponService.getAllCouponsPopupForTotalBill(companyId, classify, couponIds, portalType,
            couponMcDevAdminReqDTO);
        // Danh sách CDQC
        McPurchasingInfoDTO mcPurchasingInfoDTO = couponMcDevAdminReqDTO.getMcPurchaseInfoDTO();
        mcPurchasingInfoDTO.setUserId(companyId);
        val mcPromotionList = campaignSmeService.getListPromotion(mcPurchasingInfoDTO, null);
        // Thông tin tổng tiền hóa đơn
        BigDecimal currentTotalPreTaxAmount = ObjectUtil.getOrDefault(couponMcDevAdminReqDTO.totalPreTaxAmount, BigDecimal.ZERO);
        // Tổng hợp và sắp xếp thông tin
        Page<CouponMcPopupDTO> promotionPage = gatheringPopupDTO(currentTotalPreTaxAmount, couponsPopup, mcPromotionList, page, size);
        return ResponseEntity.ok().body(promotionPage);
    }

    /**
     * Popup chọn CTKM trên tổng hóa đơn khi đăng ký thuê bao gói combo
     */
    @Operation(description = "Popup chọn CTKM trên tổng hóa đơn khi đăng ký thuê bao gói combo")
    @GetMapping("/combo-plan/total/coupons/{companyId}/{comboPlanId}")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CouponPopupDTO>> getSubscriptionAddonCouponsForTotalBillWhenSubsComboPlan(
        @Parameter(description = "ID của doanh nghiệp", example = "1")
        @PathVariable("companyId") Long companyId,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
        @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
        @RequestParam(name = "portalType", required = false) PortalType portalType,
        @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
        @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
        @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
        @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
        @Parameter(description = Coupon.ID, example = Example.ID)
        @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        log.info("--- Execute getSubscriptionAddonCouponsForTotalBillWhenSubsComboPlan method: Start ---");
        String classify = CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN;
        Page<CouponPopupDTO> couponsPopup = couponService.getCouponsPopupForTotalBill(companyId, page, size, sort, classify, couponIds,
            portalType, price, quanlity, null);
        log.info("--- Execute getSubscriptionAddonCouponsForTotalBillWhenSubsComboPlan method: End ---");
        return ResponseEntity.ok().body(couponsPopup);
    }

    @Operation(description = "Popup chọn CTKM + CDQC trên tổng hóa đơn khi đăng ký thuê bao gói combo")
    @PostMapping("/combo-plan/total/coupon-mc-promotion/{companyId}/{comboPlanId}")
    public ResponseEntity<Page<CouponMcPopupDTO>> getSubscriptionCouponMcForTotalBillWhenSubsComboPlan(
            @Valid @RequestBody CouponMcDevAdminReqDTO couponMcDevAdminReqDTO,
            @Parameter(description = "ID của doanh nghiệp", example = "1")
            @PathVariable("companyId") Long companyId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "promotionType,ASC") String sort,
            @Parameter(description = SwaggerConstant.PORTAL_TYPE, example = Example.PORTAL)
            @RequestParam(name = "portalType", required = false) PortalType portalType,
            @Parameter(description = "Đơn giá của gói", example = Example.AMOUNT)
            @RequestParam(name = "price", required = false, defaultValue = "0") Long price,
            @Parameter(description = "Số lượng gói đăng ký", example = Example.QUANTITY)
            @RequestParam(name = "quanlity", required = false, defaultValue = "0") Long quanlity,
            @Parameter(description = Coupon.ID, example = Example.ID)
            @RequestParam(name = "couponIds", required = false, defaultValue = "0") List<Long> couponIds) {
        String classify = CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN;
        if (Objects.equals(PortalType.SME, portalType)) companyId = AuthUtil.getCurrentUserId();
        // Danh sách CTKM
        List<CouponPopupDTO> couponsPopup = couponService.getAllCouponsPopupForTotalBill(companyId, classify, couponIds, portalType,
            couponMcDevAdminReqDTO);
        // Danh sách CDQC
        McPurchasingInfoDTO mcPurchasingInfoDTO = couponMcDevAdminReqDTO.getMcPurchaseInfoDTO();
        mcPurchasingInfoDTO.setUserId(companyId);
        val mcPromotionList = campaignSmeService.getListPromotion(mcPurchasingInfoDTO, null);
        // Thông tin tổng tiền hóa đơn
        BigDecimal currentTotalPreTaxAmount = ObjectUtil.getOrDefault(couponMcDevAdminReqDTO.totalPreTaxAmount, BigDecimal.ZERO);
        // Tổng hợp và sắp xếp thông tin
        Page<CouponMcPopupDTO> promotionPage = gatheringPopupDTO(currentTotalPreTaxAmount, couponsPopup, mcPromotionList, page, size);
        return ResponseEntity.ok().body(promotionPage);
    }

    /**
     * Tổng hợp và sắp xếp danh sách các CTKM và CDQC
     *
     * @param currentTotalPreTaxAmount Tổng tiền hóa đơn hiện tại
     * @param couponsPopup             Danh sách các CTKM
     * @param mcPromotionList          Danh sách các CDQC
     */
    private Page<CouponMcPopupDTO> gatheringPopupDTO(BigDecimal currentTotalPreTaxAmount, List<CouponPopupDTO> couponsPopup,
        List<McCampaignPromotionDTO> mcPromotionList, int page, int size) {
        List<CouponMcPopupDTO> totalList = new LinkedList<>();
        // Thu thập thông tin CTKM
        couponsPopup.forEach(item -> {
            CouponMcPopupDTO couponDTO = new CouponMcPopupDTO(item);
            if (item.getMinimumAmount() != null && currentTotalPreTaxAmount.compareTo(BigDecimal.valueOf(item.getMinimumAmount())) < 0) {
                // Các CTKM chưa đáp ứng điều kiện tổng giá trị đơn hàng được mark là applicable = false
                couponDTO.couponPopupDTO.setApplicable(false);
            }
            totalList.add(couponDTO);
        });
        // Thu thập thông tin CDQC
        if (mcPromotionList != null && !mcPromotionList.isEmpty()) {
            mcPromotionList.forEach(item -> totalList.add(new CouponMcPopupDTO(item)));
        }
        // Sắp xếp CTKM và CDQC
        val mapScore = totalList.stream().collect(Collectors.toMap(Function.identity(), item -> {
            boolean applicable;
            DiscountTypeEnum discountType;
            if (item.promotionPopupType == PromotionPopupType.COUPON) {
                applicable = item.couponPopupDTO.isApplicable();
                discountType = item.couponPopupDTO.getDiscountType();
            } else {
                applicable = item.mcPopupDTO.isApplicable();
                discountType = item.mcPopupDTO.getIconIndicator() == McIconIndicatorEnum.DISCOUNT_AMOUNT ? DiscountTypeEnum.PRICE
                    : DiscountTypeEnum.PERCENT;
            }
            return calculateOrderScore(applicable, item.promotionPopupType, discountType);
        }));
        totalList.sort((item1, item2) -> mapScore.get(item2) - mapScore.get(item1));
        // Tạo pageable đầu ra
        int startIdx = page * size;
        if (startIdx > totalList.size()) {
            startIdx = totalList.size();
        }
        int endIdx = startIdx + size;
        if (endIdx > totalList.size()) {
            endIdx = totalList.size();
        }
        return new PageImpl<>(totalList.subList(startIdx, endIdx), PageRequest.of(page, size), totalList.size());
    }

    /**
     * Lấy thông tin tiền khi đăng ký
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PostMapping("/pricing/calculate")
    @Operation(description = "Lấy thông tin tiền khi đăng ký")
    public ResponseEntity<SubscriptionCalculateDevResDTO> calculateDevSubscription(@Valid
            @RequestBody SubscriptionCalculateDevReqDTO request
    ) {
        log.info("--- Execute choosePricingSub method: Start--");
        SubscriptionCalculateDevResDTO response = subscriptionCalculateService.calculateDevSubscription(request);
        log.info("--- Execute choosePricingSub method: End--");
        return ResponseEntity.ok(response);
    }

    /**
     * Lấy thông tin tiền khi đăng ký
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PostMapping("/pricing/calculate/new")
    @Operation(description = "Lấy thông tin tiền khi đăng ký")
    public ResponseEntity<SubscriptionFormulaResDTO> calculateDevSubscriptionNew(@Valid @RequestBody SubscriptionCalculateDevReqDTO request
    ) {
        log.info("--- Execute choosePricingSub method: Start--");
        request.setInputQuantityObject(request.getPricing().getQuantity());
        if (Objects.nonNull(request.getSubscriptionId())) {
            com.entity.subscriptions.Subscription subscription = subscriptionRepository.findById(request.getSubscriptionId()).orElse(null);
            boolean isRenewSubsCalculation = Objects.equals(request.getType(), SubscriptionConstant.RENEW_SUB);
            if (Objects.nonNull(subscription)) {
                boolean isSwap = Objects.equals(subscription.getIsSwap(), 1);
                if (Objects.nonNull(subscription.getGroupCode()) && !isRenewSubsCalculation && !isSwap) {
                    request.setInputObjectPrice(request.getPricing().getPrice());
                    request.setQuantityObject(subscriptionRepository.getOriginQuantityByServiceGroupIdAndPricingId(
                            subscription.getServiceGroupId(), shoppingCartSmeService.getLstPricingMultiPlanIdByReferenceId(subscription.getPricingMultiPlanId())));
                }
            }
            if (isRenewSubsCalculation) {
                request.getPricing().setPrice(null);
                request.getPricing().setUnitLimitedList(new ArrayList<>());
            }
        }
        request.setApplyTime(new Date());
        SubscriptionFormulaResDTO response = subscriptionFormula.calculateDevAdminSubscription(request);
        log.info("--- Execute choosePricingSub method: End--");
        return ResponseEntity.ok(response);
    }

    /**
     * Lấy thông tin tiền khi đăng ký combo
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PostMapping("/combo/calculate")
    @Operation(description = "Lấy thông tin tiền khi đăng ký combo")
    public ResponseEntity<SubscriptionCalculateDevResDTO> calculateDevComboSubscription(
            @RequestBody SubscriptionCalculateDevComboReqDTO request
    ) {
        log.info("--- Execute calculateDevComboSubscription method: Start--");
        SubscriptionCalculateDevResDTO response = subscriptionCalculateService.calculateDevComboSubscription(request);
        log.info("--- Execute calculateDevComboSubscription method: End--");
        return ResponseEntity.ok(response);
    }

    /**
     * Lấy thông tin tiền khi đăng ký combo
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PostMapping("/combo/calculate/new")
    @Operation(description = "Lấy thông tin tiền khi đăng ký combo")
    public ResponseEntity<SubscriptionFormulaResDTO> calculateDevComboSubscriptionNew(
        @RequestBody SubscriptionCalculateDevComboReqDTO request
    ) {
        log.info("--- Execute calculateDevComboSubscriptionNew method: Start--");
        request.setApplyTime(new Date());
        SubscriptionFormulaResDTO response = subscriptionFormula.calculateDevAdminSubscriptionCombo(request);
        log.info("--- Execute calculateDevComboSubscriptionNew method: End--");
        return ResponseEntity.ok(response);
    }


    /**
     * Đăng ký cho SME
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PostMapping("/pricing")
    @Transactional(timeout = 120)
    @Operation(description = "Đăng ký cho SME")
    public ResponseEntity<BaseResponseDTO> registerForSME(
            @Valid @RequestBody SubscriptionRegisterDevResDTO request,
            @RequestHeader("Authorization") String token,
            @Parameter(description = Billing.IP_ADDRESS, example = Example.IP_ADDRESS)
            @RequestParam(required = false, defaultValue = "", name = "ipAddress") String ipAddress,
            @RequestParam(name = "portalType") PortalType portalType
    ) {
        log.info("--- Execute registerForSME method: Start--");
        BaseResponseDTO response = subscriptionRegisterService.registerForSME(request, token, false, null, null, ipAddress, portalType);
        log.info("--- Execute registerForSME method: End--");
        return ResponseEntity.ok(response);
    }

    /**
     * Đăng ký cho SME
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PostMapping("/combo")
    @Operation(description = "Đăng ký cho SME")
    public ResponseEntity<BaseResponseDTO> registerComboForSME(
            @Valid @RequestBody SubComboDevReqDTO request,
            @RequestHeader("Authorization") String token,
            @Parameter(description = Billing.IP_ADDRESS, example = Example.IP_ADDRESS)
            @RequestParam(required = false, defaultValue = "", name = "ipAddress") String ipAddress,
            @RequestParam(name = "portalType") PortalType portalType
    ) {
        log.info("--- Execute registerComboForSME method: Start--");
        BaseResponseDTO response = subscriptionRegisterService.registerComboForSME(request, token, false, ipAddress, portalType);
        log.info("--- Execute registerComboForSME method: End--");
        return ResponseEntity.ok(response);
    }

    /**
     * Export danh sach thue bao tao boi dev/admin
     *
     * @param searchText searchText
     * @param status     status
     * @param portalType portalType
     * @param cusName    cusName
     * @param developerName developerName
     * @param sortColumn    sortColumn
     * @param sortType      sortType
     * @return the file subscription Excel
     */
    @Operation(description = "Export danh sach thue bao tao boi dev/admin")
    @GetMapping("/export")
    public ResponseEntity ExportSubByPortalType(
        @Parameter(description = "tên gói/dịch vụ", example = "abc")
        @RequestParam(value = "searchText",defaultValue = "",required = false) String searchText,
        @Parameter(description = "Trạng thái hoạt động", example = "ACTIVE")
        @RequestParam(value = "status", required = false) SubscriptionStatusEnum status,
        @Parameter(description = "kiểu tài khoản ", example = "dev")
        @RequestParam(value = "portalType", required = false) String portalType,
        @Parameter(description = "Họ tên khách hàng", example = "Cường Lương")
        @RequestParam(value = "cusName", defaultValue = "", required = false) String cusName,
        @Parameter(description = "Ho ten khach hang ", example = "Luong Cuong")
        @RequestParam(value = "developerName", defaultValue = "", required = false) String developerName,
        @Parameter(description = " sắp xếp theo cột", example = "id")
        @RequestParam(name = "sortColumn", required = false, defaultValue = "") String sortColumn,
        @Parameter(description = "sắp xếp theo kiểu", example = "desc")
        @RequestParam(name = "sortType", required = false, defaultValue = "") String sortType)
        throws IOException {
        log.info("--- Execute findAll method: Start--");
        String filename = subscriptionService.getFileName();
        InputStreamResource file = new InputStreamResource(subscriptionService
            .ExportSubPortalType(searchText, status, portalType, cusName,developerName,sortColumn,sortType));
        log.info("--- Execute findAll method: End --");
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
            .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
            .body(file);
    }

    /**
     * Hiển thị các phương thức thanh toán của subscription
     */
    @GetMapping("/{id}/payment")
    @Operation(description = "Hiển thị phương thức thanh toán ")
    public ResponseEntity<Map<String,String>> getPaymentMethod(
        @Parameter(description = SwaggerConstant.Subscription.ID, example = SwaggerConstant.Example.ID)
        @PathVariable Long id
    ) {
        log.info("--- Execute getCustomer method: Start--");
        Map<String, String> subscriptionCustomerDTO = subscriptionDetailService.getPaymentMethod(id);
        log.info("--- Execute getCustomer method: End--");
        return ResponseEntity.ok().body(subscriptionCustomerDTO);
    }

    /**
     * Hiển thị danh sách hóa đơn của subscription
     *
     */
    @GetMapping("/{id}/list-billings")
    @Operation(description = "Hiển thị danh sách hóa đơn của subscription")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<SubscriptionBillingsResDTO>> getBillings(@PathVariable Long id,
        @Parameter(description = "Chỉ số trang", example = "1")
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "10")
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "id,DESC") String sortBy) {
        log.info("--- Execute getBillings method: Start--");
        ListRequest req = new ListRequest(size, page, sortBy);
        Page<SubscriptionBillingsResDTO> subscriptionBillingsDTOs = subscriptionService.getBillings(id, req.getPageable());
        log.info("--- Execute getBillings method: End--");
        return ResponseEntity.ok().body(subscriptionBillingsDTOs);
    }

    /**
     * Gia hạn sử dụng gói dịch vụ
     *
     */
    @PutMapping("/renew/{id}")
    @Operation(description = "Gia hạn sử dụng gói dịch vụ")
    public ResponseEntity<SubscriptionRenewResDTO> renewSubscription(
            @Parameter(description = SwaggerConstant.Subscription.ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long id,
            @Parameter(description = SwaggerConstant.Subscription.NUMBER_CYCLES, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "cycleQuantity", required = false, defaultValue = "1") Integer cycleQuantity,
            @Parameter(description = SwaggerConstant.ActionLog.PORTAL, example = SwaggerConstant.Example.PORTAL_TYPE)
            @RequestParam(name = "portalType", required = false, defaultValue = "SME") PortalType portalType,
            @Parameter(description = SwaggerConstant.Subscription.RENEW_TYPE, example = SwaggerConstant.Example.RENEW_TYPE)
            @RequestParam(name = "renewType", required = false, defaultValue = "PRICING") SubscriptionRenewTypeEnum renewType,
            @Parameter(description = SwaggerConstant.Subscription.RENEW_TYPE, example = SwaggerConstant.Example.RENEW_TYPE)
            @RequestParam(name = "paymentMethod", required = false, defaultValue = "VNPTPAY") PaymentMethodEnum paymentMethod,
            @RequestHeader("Authorization") String token,
            @RequestParam(required = false, name = "ipAddress") String ipAddress) {
        log.info("--- Execute renewSubscription method: Start--");
        SubscriptionRenewResDTO res = subscriptionService.renewSubscription(id, cycleQuantity, portalType, renewType,
                paymentMethod, null, token, ipAddress);
        log.info("--- Execute renewSubscription method: End--");
        return ResponseEntity.ok().body(res);
    }

    @PutMapping("/renew-combo/{id}")
    @Operation(description = "Gia hạn sử dụng gói dịch vụ")
    public ResponseEntity<SubscriptionRenewResDTO> renewSubscriptionCombo(
            @Parameter(description = SwaggerConstant.Subscription.ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long id,
            @Parameter(description = SwaggerConstant.Subscription.NUMBER_CYCLES, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "cycleQuantity", required = false, defaultValue = "1") Integer cycleQuantity,
            @Parameter(description = SwaggerConstant.ActionLog.PORTAL, example = SwaggerConstant.Example.PORTAL_TYPE)
            @RequestParam(name = "portalType", required = false, defaultValue = "SME") PortalType portalType,
            @Parameter(description = SwaggerConstant.Subscription.RENEW_TYPE, example = SwaggerConstant.Example.RENEW_TYPE)
            @RequestParam(name = "renewType", required = false, defaultValue = "PRICING") SubscriptionRenewTypeEnum renewType,
            @RequestHeader("Authorization") String token) {
        log.info("--- Execute renewSubscription method: Start--");
        SubscriptionRenewResDTO res = subscriptionService.renewSubscriptionCombo(id, cycleQuantity, portalType, renewType, null, token);
        log.info("--- Execute renewSubscription method: End--");
        return ResponseEntity.ok().body(res);
    }

    /**
     * check Subscription
     *
     */
    @Operation(summary = "check Subscription",
        description = " check Subscription",
        tags = {"subscription"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "check thành công",
            content = @Content(
                array = @ArraySchema(
                    schema = @Schema(implementation = SubscriptionSmeCompaniesResponseDTO.class)))),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy subscription")
    })
    @GetMapping("/{id}/general-description")
    public ResponseEntity<Map<String, String>> checkSubscription(
        @Parameter(description="id subscription")
        @PathVariable Long id) {
        log.info("--- Execute getListSmeSubscription method: Start--");
        Map<String, String> sub = subscriptionService.checkSubscriptionTrial(id);
        log.info("--- Execute getListSmeSubscription method: End--");
        return new ResponseEntity<>(sub, HttpStatus.OK);
    }

    /**
     * cancel Subscription dung thu
     *
     */
    @Operation(summary = "cancel Subscription dung thu",
        description = " hủy Subscription dung thu",
        tags = {"subscription"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "huy thành công",
            content = @Content(
                array = @ArraySchema(
                    schema = @Schema(implementation = SubscriptionSmeCompaniesResponseDTO.class)))),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy subscription")
    })
    @PutMapping("/{id}/general-description")
    public ResponseEntity<?> cancelSubscription(
        @RequestParam(name = "paymentType", required = false, defaultValue = "NOW") PricingCancelTimeActiveEnum paymentType,
        @Parameter(description="id subscription")
        @PathVariable Long id) {
        log.info("--- Execute getListSmeSubscription method: Start--");
        subscriptionService.cancelSubscription(id, paymentType);
        log.info("--- Execute getListSmeSubscription method: End--");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * get thông tin màn thông tin chung
     *
     */
    @Operation(summary = "get Subscription dung thu",
        description = " get Subscription dung thu",
        tags = {"subscription"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "get thành công",
            content = @Content(
                array = @ArraySchema(
                    schema = @Schema(implementation = SubscriptionSmeCompaniesResponseDTO.class)))),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy subscription")
    })
    @GetMapping("/{id}/general-information")
    public ResponseEntity<SubscriptionInfoDTO> getSubscription(
        @Parameter(description="id subscription")
        @PathVariable Long id) {
        log.info("--- Execute getListSmeSubscription method: Start--");
        SubscriptionInfoDTO informationDTO = subscriptionDetailService.getSubscriptionInfo(id);
        log.info("--- Execute getListSmeSubscription method: End--");
        return new ResponseEntity<>(informationDTO, HttpStatus.OK);
    }

    /**
     * update thông tin màn thông tin chung sub dùng thử
     *
     */
    @Operation(summary = "update Subscription dung thu",
        description = " update Subscription dung thu",
        tags = {"subscription"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "get thành công",
            content = @Content(
                array = @ArraySchema(
                    schema = @Schema(implementation = SubscriptionSmeCompaniesResponseDTO.class)))),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy subscription")
    })
    @PutMapping("/{id}/general-information")
    public ResponseEntity<SubscriptionInfoDTO.SubscriptionInfoUpdateDTO> updateSubscription(
        @Parameter(description = "id subscription")
        @PathVariable Long id,
        @Parameter(description = "subscriptionInfo")
        @RequestBody SubscriptionInfoDTO.SubscriptionInfoUpdateDTO subscriptionInfoDTO) {
        log.info("--- Execute getListSmeSubscription method: Start--");
        SubscriptionInfoDTO.SubscriptionInfoUpdateDTO informationDTO = subscriptionService.updateSubscriptionInfo(id, subscriptionInfoDTO);
        log.info("--- Execute getListSmeSubscription method: End--");
        return new ResponseEntity<>(informationDTO, HttpStatus.OK);
    }

    /**
     * Đăng ký cho SME từ dùng thử thành dùng chính thức
     *
     * @param request the request
     * @return the response entity
     */
    @PostMapping("/{id}/pricing")
    @Operation(description = "Đăng ký cho SME từ dùng thử thành dùng chính thức")
    public ResponseEntity<BaseResponseDTO> UpdateSubTrialForSme(
        @Parameter(description="id subscription")
        @PathVariable Long id,
        @Valid @RequestBody SubscriptionRegisterDevResDTO request,
        @RequestHeader("Authorization") String token,
        @Parameter(description = Billing.IP_ADDRESS, example = Example.IP_ADDRESS)
        @RequestParam(required = false, defaultValue = "", name = "ipAddress") String ipAddress,
        @RequestParam(name = "portalType", required = false) PortalType portalType
    ) {
        log.info("--- Execute UpdateSubTrialForSme method: Start--");
        BaseResponseDTO response = subscriptionRegisterService.updateSubTrialForSme(id, request, token, ipAddress, portalType);
        log.info("--- Execute UpdateSubTrialForSme method: End--");
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Lấy thông tin chi tiết subscription gói dịch vụ
     *
     */
    @Operation(description = "Lấy thông tin chi tiết subscription gói dịch vụ sau đăng ký")
    @GetMapping("/detail/{id}")
    public SubscriptionDetailDTO getSubscriptionDetail(@PathVariable Long id) {
        return subscriptionDetailService.getSubscriptionDetail(id, PortalType.DEV);
    }

    @Operation(description = "Lay du lieu tap tien trinh cua thue bao ON")
    @GetMapping("/{subId}/online-service/progress")
    public OnlineServiceProgressDTO getOrderOnlineServiceProgress(
        @Parameter(description = "subscriptionId")
        @PathVariable Long subId) {
        return subscriptionDetailService.getOrderOnlineServiceProgress(subId);
    }


    /**
     * Lấy thông tin mới nhất của pricing bao gồm pricingId và pricingMultiPlanId sau khi cập nhật giá tiền
     *
     * @param id id subscription
     */
    @Operation(description = "Lấy thông tin mới nhất của pricing bao gồm pricingId và pricingMultiPlanId sau khi Dev cập nhật giá tiền")
    @GetMapping("/newest-pricing/{id}")
    public ResponseEntity<NewestPricingOfSubDTO> getNewestPricingOfSubscription(
        @Parameter(description = SwaggerConstant.Subscription.ID, example = SwaggerConstant.ID)
        @PathVariable Long id
    ) {
        log.info("--- Execute getNewestPricingOfSubscription method: Start--");
        NewestPricingOfSubDTO res = subscriptionService.getNewestPricingOfSubscription(id, PortalType.SME);
        log.info("--- Execute getNewestPricingOfSubscription method: End--");
        return ResponseEntity.ok().body(res);
    }

    /**
     * Cập nhật subscription cho SME
     *
     * @param subscriptionId the subscription id
     * @param request        the request
     * @return the response entity
     */
    @PutMapping("/update/{subscriptionId}")
    @Operation(description = "Cập nhật subscription cho SME")
    public BaseResponseDTO updateSubscriptionForSME(
        @Parameter(description = "ID subscription")
        @PathVariable Long subscriptionId,
        @Valid @RequestBody SubscriptionUpdateDevResDTO request,
        @RequestParam(required = false, name = "ipAddress") String ipAddress,
        @RequestHeader("Authorization") String token,
        @RequestParam(name = "portalType") PortalType portalType) {
        return subscriptionService.updateSubDev(request, subscriptionId, token, ipAddress, portalType);
    }

    /**
     * Cập nhật subscription cho SME
     *
     * @param subscriptionId the subscription id
     * @param request        the request
     * @return the response entity
     */
    @PutMapping("/update/{subscriptionId}/pricing")
    @Operation(description = "Đổi gói cho SME")
    public BaseResponseDTO swapPricingForSME(
        @Parameter(description = "ID subscription")
        @PathVariable Long subscriptionId,
        @Valid @RequestBody SubscriptionRegisterDevResDTO request,
        @Parameter(description = Billing.IP_ADDRESS, example = Example.IP_ADDRESS)
        @RequestParam(required = true, defaultValue = "", name = "ipAddress") String ipAddress,
        @RequestParam(name = "portalType") PortalType portalType) {
        return subscriptionService.swapPricingForDev(request, subscriptionId, ipAddress, portalType);
    }

    /**
     * Đổi combo cho SME
     *
     * @param subscriptionId the subscription id
     * @param request        the request
     * @return the response entity
     */
    @PutMapping("/update/{subscriptionId}/combo")
    @Operation(description = "Đổi gói cho SME")
    public BaseResponseDTO swapComboForSME(
        @Parameter(description = "ID subscription")
        @PathVariable Long subscriptionId,
        @Valid @RequestBody SubscriptionSwapComboDevDTO request,
        @Parameter(description = Billing.IP_ADDRESS, example = Example.IP_ADDRESS)
        @RequestParam(required = true, defaultValue = "", name = "ipAddress") String ipAddress,
        @RequestParam(name = "portalType") PortalType portalType) {
        return subscriptionService.swapComboForSME(request, subscriptionId, ipAddress, portalType);
    }

    /**
     * lấy thông tin subscription chính thức màn hình chung
     *
     * @return the response entity
     */
    @GetMapping("/{id}/official-joint-screen")
    @Operation(description = "get thông tin")
    public ResponseEntity<SubscriptionOfficialJointScreenDTO> getSubDevAdmin(
        @Parameter(description = "ID subscription")
        @PathVariable Long id) {
        log.info("--- Execute get method: Start--");
        SubscriptionOfficialJointScreenDTO response = subscriptionDetailService.getSubscriptionOfficial(id);
        log.info("--- Execute get method: End--");
        return ResponseEntity.ok(response);
    }

    /**
     * hủy subscription chính thức màn hình chung
     *
     */
    @PutMapping("/{id}/official-joint-screen")
    @Operation(description = "hủy thông tin")
    public ResponseEntity<BaseResponseDTO> cancelSubscriptionOfficial(
        @Parameter(description = "ID subscription")
        @PathVariable Long id,
        @Parameter(description = "kieu cancel")
        @RequestParam String paymentType,
        @RequestParam PortalType portalType,
        @RequestHeader("Authorization") String token){
        log.info("--- Execute updateSubscriptionSME method: Start--");
        BaseResponseDTO response = subscriptionService.cancelSubscriptionOfficial(id, paymentType, portalType, token);
        log.info("--- Execute updateSubscriptionSME method: End--");
        return ResponseEntity.ok(response);
    }
    
    /**
     * Hủy multi subscription chính thức màn hình chung
     */
    @PutMapping("/official-joint-screen/cancel-multi-sub")
    @Operation(description = "hủy thông tin")
    public ResponseEntity<List<MultiSubscriptionCancelDTO>> cancelMultiSubscription(
        @Parameter(description = "cancel multi sub") @Validated @RequestBody List<MultiSubscriptionCancelDTO> subsLst,
        @Parameter(description = "trạng thái hủy")
        @RequestParam(value = "status", required = false, defaultValue = "CANCEL") CancelMultiSubscriptionTypeEnum status
    ) {
        log.info("--- Execute cancel multi subs method: Start--");
        List<MultiSubscriptionCancelDTO> subLst = subscriptionService.cancelMultiSubscriptionOfficial(subsLst, status);
        log.info("--- Execute cancel multi subs method: End--");
        return ResponseEntity.ok(subLst);
    }

    /**
     * Pop up danh sách gói combo dịch vụ
     *
     * @param comboName Tên combo
     * @param comboPlanname Tên gói combo
     * @param developerName Tên nhà phát triển
     * @param portalType Portal (DEV/ADMIN)
     * @param page page number
     * @param size size page
     * @param sortBy type of sort
     */
    @Operation(description = "Pop up danh sách gói combo dịch vụ")
    @GetMapping("/combo-plan")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<SubscriptionComboPlanPopupResDTO>> getSubscriptionComboPlan(
            @Parameter(description = SwaggerConstant.Combo.NAME, example = SwaggerConstant.Example.COMBO_NAME)
            @RequestParam(name = "name", required = false, defaultValue = "") String comboName,
            @Parameter(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
            @RequestParam(name = "comboPlan", required = false, defaultValue = "") String comboPlanname,
            @Parameter(description = SwaggerConstant.User.NAME_DEV, example = SwaggerConstant.Example.BUSINESS_SCALE)
            @RequestParam(name = "developer", required = false, defaultValue = "") String developerName,
            @Parameter(description = SwaggerConstant.Department.PORTAL_TYPE, example = SwaggerConstant.Example.PORTAL_TYPE)
            @RequestParam(name = "portalType", required = true, defaultValue = "DEV") PortalType portalType,
            @Parameter(description = SwaggerConstant.Combo.COMBO_PLAN_USING_ID, example = Example.ID)
            @RequestParam(name = "comboPlanId", required = false, defaultValue = "-1") Long comboPlanId,
            @Parameter(description = SwaggerConstant.Combo.COMBO_TYPE, example = SwaggerConstant.Example.COMBO_TYPE)
            @RequestParam(name = "comboType", defaultValue = "ALL") ComboTypeEnum comboType,
            @Parameter(description = Pricing.OS_SERVICE, example = Example.ID)
            @RequestParam(name = "osService", required = false, defaultValue = "UNSET") YesNoEnum isOsService,
            @Parameter(description = SwaggerConstant.ComboPlan.CYCLE_TYPE, example = Example.CYCLE_TYPE)
            @RequestParam(required = false, defaultValue = "UNSET") CycleTypeEnum type,
            @Parameter(description = SwaggerConstant.Pricing.PAYMENT_CYCLE, example = Example.PAYMENT_CYCLE)
            @RequestParam(required = false, defaultValue = "-1") Integer paymentCycle,
            @Parameter(description = "id", example = "1")
            @RequestParam(name = "id",required = false, defaultValue = "-1") Long removeId,
            @Parameter(description = Pricing.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
            @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "comboName,ASC") String sortBy
    ) {
        log.info("--- Execute getSubscriptionComboPlan method: Start--");
        Page<SubscriptionComboPlanPopupResDTO> subscriptionComboPlan = subscriptionDetailService.getSubscriptionComboPlan(comboName,
            comboPlanname, developerName, portalType, comboType, comboPlanId, isOsService, type, paymentCycle, removeId, customerType, size,
            page, sortBy);
        log.info("--- Execute getSubscriptionComboPlan method: End--");
        return ResponseEntity.ok().body(subscriptionComboPlan);
    }

    /**
     * Thông tin chi tiết gói combo khi dev subscription
     *
     * @param id id gói combo
     */
    @Operation(description = "Thông tin chi tiết gói combo khi dev/admin subscription")
    @GetMapping("/combo-plan/{id}")
    public ResponseEntity<SubComboPlanDetailDTO> getSubComboPlanDetail(
            @Parameter(description = SwaggerConstant.ComboPlan.ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long id,
            @Parameter(description = ActionLog.PORTAL, example = Example.PORTAL)
            @RequestParam(name = "portal", required = false, defaultValue = "DEV") PortalType portalType,
            @Parameter(description = SwaggerConstant.Subscription.ID, example = SwaggerConstant.Example.ID)
            @RequestParam(name = "subscriptionId", required = false, defaultValue = "-1") Long subscriptionId,
            @RequestParam(name = "userId", required = false, defaultValue = "-1") Long userId
    ) {
        log.info("--- Execute getSubComboPlanDetail method: Start--");
        SubComboPlanDetailDTO res = comboPlanService.getDetailComboPlan(id, subscriptionId, portalType, userId);
        log.info("--- Execute getSubComboPlanDetail method: End--");
        return ResponseEntity.ok().body(res);
    }

    /**
     * Xem thông tin dev admin dich vu khi subscription
     *
     * @param id : Id combo
     * @param comboPlanId : Id pricing
     * @return Du lieu service
     */
    @Operation(description = "Xem thông tin dich vu khi sme subscription")
    @GetMapping("/combo/{id}/basic/{comboPlanId}")
    public ResponseEntity<SubscriptionComboBasicDTO> getServiceBasic(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @PathVariable(name = "id") Long id,
        @Parameter(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
        @PathVariable(name = "comboPlanId") Long comboPlanId) {
        log.info("--- Execute getServiceBasic method: Start--");
        SubscriptionComboBasicDTO subscriptionServiceBasicDTO = subscriptionDetailService
            .getAdminOrDevSubscriptionServiceBasic(id, comboPlanId);
        log.info("--- Execute getServiceBasic method: End--");
        return ResponseEntity.ok().body(subscriptionServiceBasicDTO);
    }

    /**
     * Hiển thị danh sách hóa đơn của combo subscription
     *
     */
    @GetMapping("/{id}/combo/list-billings")
    @Operation(description = "Hiển thị danh sách hóa đơn của subscription")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<SubscriptionComboBillingsDTO>> getComboBillings(@PathVariable Long id,
        @Parameter(description = "Chỉ số trang", example = "1")
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "10")
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = "Sắp xếp", example = "name,desc")
        @RequestParam(required = false, defaultValue = "id,desc") String sort) {
        log.info("--- Execute getComboBillings method: Start--");
        ListRequest req = new ListRequest(size, page, sort);
        Page<SubscriptionComboBillingsDTO> subscriptionBillingsDTOs = billsService.getComboBillsBySubId(id, req.getPageable());
        log.info("--- Execute getComboBillings method: End--");
        return ResponseEntity.ok().body(subscriptionBillingsDTOs);
    }

    /**
     * Đăng ký cho SME từ dùng thử thành dùng chính thức
     */
    @PostMapping("/combo/{id}")
    @Operation(description = "Đăng ký cho SME từ dùng thử thành dùng chính thức")
    public ResponseEntity<BaseResponseDTO> updateSubComboTrialForSme(
        @Parameter(description="id subscriptionCombo")
        @PathVariable Long id,
        @Valid @RequestBody SubComboDevReqDTO request,
        @RequestHeader("Authorization") String token,
        @RequestParam(required = false, defaultValue = "", name = "ipAddress") String ipAddress,
        @RequestParam(name = "portalType") PortalType portalType
    ) {
        log.info("--- Execute UpdateSubComboTrialForSme method: Start--");
        BaseResponseDTO response = subscriptionService.UpdateSubComboTrialForSme(id,request, token, ipAddress, portalType);
        log.info("--- Execute UpdateSubComboTrialForSme method: End--");
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Kiểm tra sự tồn tại của coupon/ addon/ pricing khi subscription
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PutMapping("/check-exist-items")
    @Operation(description = "Kiểm tra sự tồn tại của coupon/ addon/ pricing khi subscription")
    public ResponseEntity<SubsValidResDTO> checkExistItems(@RequestBody SubsValidReqDTO request) {
        log.info("--- Execute checkExistItems method: Start--");
        SubsValidResDTO response = subscriptionService.checkExistItems(request, null);
        log.info("--- Execute checkExistItems method: End--");
        return ResponseEntity.ok().body(response);
    }

    /**
     * Kiểm tra sự tồn tại của coupon/ addon/ pricing khi subscription combo
     *
     * @param request the request
     *
     * @return the response entity
     */
    @PutMapping("/combo/check-exist-items")
    @Operation(description = "Kiểm tra sự tồn tại của coupon/ addon/ pricing khi subscription combo")
    public ResponseEntity<SubsValidResDTO> checkExistComboItems(@RequestBody SubsComboValidReqDTO request) {
        log.info("--- Execute checkExistComboItems method: Start--");
        SubsValidResDTO response = subscriptionService.checkExistComboItems(request);
        log.info("--- Execute checkExistComboItems method: End--");
        return ResponseEntity.ok().body(response);
    }

    /**
     * update sub combo dùng thử
     *
     */
    @Operation(summary = "update Subscription combo dung thu",
        description = " update Subscription combo dung thu",
        tags = {"subscription"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "update thành công",
            content = @Content(
                array = @ArraySchema(
                    schema = @Schema(implementation = SubscriptionSmeCompaniesResponseDTO.class)))),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy subscription")
    })
    @PutMapping("/{id}/update-sub-combo-try")
    public ResponseEntity<SubscriptionInfoDTO.SubComboInfoUpdateDTO> updateSubscriptionComboTry(
        @Parameter(description = "id subscription")
        @PathVariable Long id,
        @Parameter(description = "subscriptionInfo")
        @Valid @RequestBody SubscriptionInfoDTO.SubComboInfoUpdateDTO subscriptionInfoDTO) {
        log.info("--- Execute update sub method: Start--");
        SubscriptionInfoDTO.SubComboInfoUpdateDTO informationDTO = subscriptionService.updateSubComboInfo(id, subscriptionInfoDTO);
        log.info("--- Execute update sub method: End--");
        return new ResponseEntity<>(informationDTO, HttpStatus.OK);
    }

    /**
     * Lấy đơn giá trước thuế của addon
     * @return list các đơn giá trước thuế của addon
     */
    @Operation(description = "Lấy đơn giá trước thuế của addon")
    @PostMapping("/addon/price-before-tax")
    public ResponseEntity<List<UnitLimitedNewDTO>> getAddonPriceBeforeTax(
        @RequestBody @Valid List<AddonPriceBeforeTaxDTO> priceBeforeTax,
        @Parameter(description = SwaggerConstant.Subscription.ID, example = SwaggerConstant.Example.ID)
        @RequestParam(name = "subscriptionId", required = false, defaultValue = "-1") Long subscriptionId
    ) {
        log.info("--- Execute getAddonPriceBeforeTax method: Start--");
        List<UnitLimitedNewDTO> addonPriceBeforeTax = subscriptionFormula.getListAddonPriceBeforeTax(priceBeforeTax, subscriptionId);
        log.info("--- Execute getAddonPriceBeforeTax method: End--");
        return ResponseEntity.ok().body(addonPriceBeforeTax);
    }

    /**
     * Đồng bộ trạng thái sub từ DHSXKD
     */
    @Operation(description = "Đồng bộ trạng thái sub từ DHSXKD")
    @PostMapping("/sync-order-service")
    public ResponseEntity<String> syncListOrderServiceReceiveDHSXKD() {
        subscriptionOrderService.syncListOrderServiceReceiveDHSXKD(false);
        return ResponseEntity.ok().build();
    }

    /**
     * check user có đủ thông tin không
     */
    @Operation(description = "check user có đủ thông tin không")
    @GetMapping("/validate-user/{id}")
    public ResponseEntity<?> validateUser(@PathVariable Long id) {
        log.info("--- Execute validateUser method: Start--");
        subscriptionValidateService.validateInformationUser(id);
        log.info("--- Execute validateUser method: End--");
        return ResponseEntity.ok().build();
    }

    @GetMapping("/get-list-sme")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<CustomerDTO>> getListCustomer(
        @Parameter(description = "Tên khách hàng", example = "Công ty TNHH ABC")
        @RequestParam(required = false, defaultValue = "") String companyName,
        @Parameter(description = "Tên người đại diện", example = "Nguyễn An")
        @RequestParam(required = false, defaultValue = "") String adminName,
        @Parameter(description = "Mã số thuế", example = "64356143564")
        @RequestParam(required = false, defaultValue = "") String tin,
        @Parameter(description = "Tên tỉnh thành", example = "Hà Nội")
        @RequestParam(required = false, defaultValue = "") String provinceName,
        @Parameter(description = "id", example = "1")
        @RequestParam(required = false, defaultValue = "-1") Long removeId,
        @Parameter(description = "customerType", example = "KHDN")
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @Parameter(description = "repPersonalCertNumber", example = "0214536")
        @RequestParam(required = false, defaultValue = "") String repPersonalCertNumber,
        @RequestParam(required = false, defaultValue = "-1") Long userId,
        @Parameter(description = "Chỉ số trang yêu cầu", example = "0")
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @Parameter(description = "Kích thước trang", example = "0")
        @RequestParam(required = false, defaultValue = "10") Integer size,
        @Parameter(description = "Sắp xếp", example = "adminName,desc")
        @RequestParam(required = false, defaultValue = "companyName,asc") String sort,
        @RequestParam(required = false, defaultValue = "0") String searchName,
        @RequestParam(required = false, defaultValue = "0") String searchEmail,
        @RequestParam(required = false, defaultValue = "0") String searchPhone,
        @RequestParam(required = false, defaultValue = "0") String searchTin,
        @RequestParam(required = false, defaultValue = "") String value) {
        log.info("--- Execute getCustomer method: Start--");
        sort = Objects.equals(customerType, CustomerTypeEnum.PERSONAL) ? "adminName,asc" : "companyName,asc";
        ListRequest listRequest = new ListRequest(size, page, sort);
        Page<CustomerDTO> customerDTOS = subscriptionDetailService.getListCustomer(companyName, adminName, tin, provinceName, removeId,
            customerType, repPersonalCertNumber, userId, searchName, searchEmail, searchPhone, searchTin, value,
            listRequest.getPageable());
        log.info("--- Execute getCustomer method: End--");
        return ResponseEntity.ok(customerDTOS);
    }
    
    /*
        Các api combobox mới thay cho các combobox get all
     */
    @GetMapping("/combobox-sme-name")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<CustomerInfoDTO> comboboxSMEName(
        @RequestParam(required = false, defaultValue = "") String name,
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @RequestParam(required = false, defaultValue = "10") Integer size) {
        return subscriptionOrderService.comboboxSMEName(name, customerType.getValue(), PageRequest.of(0, size));
    }

    @GetMapping("/combobox-sme-rep-personal-cert-number")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<CustomerInfoDTO> comboboxSMERepPersonalCertNumber(
        @RequestParam(required = false, defaultValue = "") String repPersonalCertNumber,
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @RequestParam(required = false, defaultValue = "10") Integer size) {
        return subscriptionOrderService.comboboxSMERepPersonalCertNumber(repPersonalCertNumber, customerType.getValue(), PageRequest.of(0, size));
    }

    @GetMapping("/combobox-sme-rep-name")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<CustomerInfoDTO> comboboxSMERepName(
        @RequestParam(required = false, defaultValue = "") String repName,
        @RequestParam(required = false, defaultValue = "10") Integer size) {
        return subscriptionOrderService.comboboxSMERepName(repName, PageRequest.of(0, size));
    }

    @GetMapping("/combobox-sme-tin")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<CustomerInfoDTO> comboboxSMETin(
        @RequestParam(required = false, defaultValue = "") String tin,
        @RequestParam(required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @RequestParam(required = false, defaultValue = "10") Integer size) {
        return subscriptionOrderService.comboboxSMETin(tin, customerType.getValue(), PageRequest.of(0, size));
    }

    @GetMapping("/check-order-qr/{billId}")
    public ResponseEntity<TransactionStatusResDTO> checkOrderQr(@PathVariable Long billId) {
        return ResponseEntity.ok(paymentService.checkOrderQr(billId));
    }
}
