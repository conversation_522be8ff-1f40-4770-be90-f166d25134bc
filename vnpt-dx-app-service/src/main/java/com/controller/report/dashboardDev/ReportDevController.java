package com.controller.report.dashboardDev;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Category;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.component.BaseController;
import com.onedx.common.constants.values.DatabaseConstant;
import com.constant.enums.report.SubscriptionReportTypeEnum;
import com.dto.report.dashboardSme.EmployeeCodeReportResDTO;
import com.dto.report.dashboardSme.PricingComboPlanReportResDTO;
import com.dto.report.dashboardSme.ServiceComboReportResDTO;
import com.service.report.dashboardSme.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/dev-portal/report")
@Validated
@Slf4j
@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
public class ReportDevController {

    @Autowired
    private ReportService reportService;

    /**
     * Lấy danh sách các dịch vụ, combo tồn tại trên hệ thống
     */
    @Operation(description = "Lấy danh sách các dịch vụ, combo tồn tại trên hệ thống")
    @GetMapping("/service-combo")
    public Page<ServiceComboReportResDTO> getListServiceCombo(
        @Parameter(description = "Loại đăng ký thuê bao", example = Example.STATUS)
        @RequestParam(name = "subscriptionType", required = false) SubscriptionReportTypeEnum subscriptionType,
        @Parameter(description = Category.ID, example = Example.ID)
        @RequestParam(name = "categoryId", required = false, defaultValue = "-1") Long categoryId,
        @Parameter(description = Service.NAME, example = Example.SERVICE_NAME)
        @RequestParam(name = "serviceName", required = false, defaultValue = "") String serviceName,
        @Parameter(description = "kiểm tra nhóm sản phẩm", example = Example.STATUS)
        @RequestParam(name = "groupServiceCheck", required = false, defaultValue = "1") Integer groupServiceCheck,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "serviceName,ASC") String sort) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return reportService.getListServiceCombo(groupServiceCheck, subscriptionType, categoryId,
            serviceName, listRequest.getPageable());
    }


    /**
     * Lấy danh sách các dịch vụ, combo tồn tại trên hệ thống
     */
    @Operation(description = "Lấy danh sách các dịch vụ, combo tồn tại trên hệ thống")
    @GetMapping("/service-combo-v2")
    public Page<ServiceComboReportResDTO> getListServiceComboVer2(
        @Parameter(description = "ID của tỉnh thành", example = Example.ID)
        @RequestParam(name = "provinceId", required = false, defaultValue = "-1") Long provinceId,
        @Parameter(description = "Loại đăng ký thuê bao", example = Example.STATUS)
        @RequestParam(name = "subscriptionType", required = false) SubscriptionReportTypeEnum subscriptionType,
        @Parameter(description = Category.ID, example = Example.ID)
        @RequestParam(name = "categoryId", required = false, defaultValue = "-1") Long categoryId,
        @Parameter(description = Service.NAME, example = Example.SERVICE_NAME)
        @RequestParam(name = "serviceName", required = false, defaultValue = "") String serviceName,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "serviceName,ASC") String sort) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return reportService.getListServiceComboVer2(provinceId, subscriptionType, categoryId,
            serviceName, listRequest.getPageable());
    }

    /**
     * Lấy danh sách các gói dịch vụ, gói combo dịch vụ
     */
    @Operation(description = "Lấy danh sách các gói dịch vụ, gói combo dịch vụ")
    @GetMapping("/service-combo/plan")
    public Page<PricingComboPlanReportResDTO> getListPricingComboPlan(
        @Parameter(description = "Loại đăng ký thuê bao", example = Example.STATUS)
        @RequestParam(name = "subscriptionType", required = false) SubscriptionReportTypeEnum subscriptionType,
        @Parameter(description = "ID của service/combo", example = Example.ID)
        @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long serviceId,
        @Parameter(description = Category.ID, example = Example.ID)
        @RequestParam(name = "categoryId", required = false, defaultValue = "-1") Long categoryId,
        @Parameter(description = Pricing.NAME, example = Example.PRICING_NAME)
        @RequestParam(name = "pricingName", required = false, defaultValue = "") String pricingName,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "pricingName,ASC") String sort) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return reportService.getListPricingComboPlan(subscriptionType, serviceId,
            categoryId, pricingName, listRequest.getPageable());
    }

    /**
     * Lấy danh sách mã nhân viên giới thiệu
     */
    @Operation(description = "Lấy danh sách mã nhân viên giới thiệu")
    @GetMapping("/employee-code")
    public Page<EmployeeCodeReportResDTO> getListEmployeeCode(
        @Parameter(description = "Mã nhân viên giới thiệu", example = Example.CODE)
        @RequestParam(name = "search", required = false, defaultValue = "ALL") String search,
        @RequestParam(name = "lstCustomerType", required = false, defaultValue = "ALL") List<String> lstCustomerType,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.NUMBER)
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "employeeCode,ASC") String sort) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        return reportService.getListEmployeeCode(search, lstCustomerType, listRequest.getPageable());
    }
}
