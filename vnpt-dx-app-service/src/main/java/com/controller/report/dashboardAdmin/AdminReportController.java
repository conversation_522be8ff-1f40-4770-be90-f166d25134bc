package com.controller.report.dashboardAdmin;

import java.util.List;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.exception.MessageKeyConstant;
import com.dto.report.dashboardSme.AccountRegisterServiceByYearDTO;
import com.dto.report.dashboardSme.AccountSMERatioResponseDTO;
import com.dto.report.dashboardSme.SMERatioResponse;
import com.dto.report.dashboardSme.ServiceDevRegisReportResDTO;
import com.dto.report.dashboardSme.SmeEmployeeResDTO;
import com.dto.report.dashboardSme.SmeReleaseMonthlyResponseDTO;
import com.dto.report.dashboardSme.WidgetResponseDTO;
import com.service.report.dashboardSme.ReportAccountService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 2/3/2021
 */
@RestController
@RequestMapping("/api/admin-portal/reports")
@Validated
@Slf4j
@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
public class AdminReportController {

	@Autowired
	private ReportAccountService reportAccountService;

	/**
	 * Thong ke tong so accounr SME Employee cua tung doanh nghiep
	 *
     */
	@GetMapping("/sme")
	public List<SmeEmployeeResDTO> smeEmployee(@RequestParam(value = "smeids", required = false) List<Long> smeIds) {
		return reportAccountService.getAllSmeEmployeeByIds(smeIds);
	}

	/**
	 * Noi dung hien thi tren widget
	 *
     */
	@GetMapping("/users")
	public WidgetResponseDTO widgetReport() {
		return reportAccountService.widgetReport();
	}

	/**
	 * Lay ti le account sme da dang ky theo thang
	 *
     */
	@GetMapping("/sme-subscription/monthly")
	public SMERatioResponse getRatioReportByMonth(
			@RequestParam(required = false) @Min(value = 1, message = MessageKeyConstant.FIELD_WRONG_FORMAT)
											@Max(value = 12, message = MessageKeyConstant.FIELD_WRONG_FORMAT) Integer month,
			@RequestParam(required = false, defaultValue = "-1") Long provinceId) {
		return reportAccountService.getRatioReportByMonth(month, provinceId);
	}

	/**
	 * Lay ti le account sme da dang ky theo quy
	 *
     */
	@GetMapping("/sme-subscription/quarter")
	public SMERatioResponse getRatioReportByQuarter(
			@RequestParam(required = false) @Min(value = 1, message = MessageKeyConstant.FIELD_WRONG_FORMAT)
											@Max(value = 4, message = MessageKeyConstant.FIELD_WRONG_FORMAT) Integer quarter,
			@RequestParam(required = false, defaultValue = "-1") Long provinceId) {
		return reportAccountService.getRatioReportByQuarter(quarter, provinceId);
	}

	/**
	 * Ti le account SME da dang ky dich vu so voi account SME chua dang ky dich vu nao theo nam.
	 *
	 * @param year thong ke theo nam duoc chon. mac dinh null la thong ke theo nam hien tai
	 * @param provinceId thong ke theo tinh thanh duoc chon mac dinh se la tat ca tinh thanh.
     */
	@GetMapping("/sme-subscription/year")
	public AccountSMERatioResponseDTO getAccountSMERatioByYear(@RequestParam(value = "year", required = false) Long year,
														   @RequestParam(value = "provinceId", required = false) Long provinceId) {

		return reportAccountService.getAccountSMERatioByYear(year, provinceId);
	}

	/**
	 * Noi dung hien thi tren widget
	 *
     */
	@GetMapping("/sme-release/quarter")
	public ServiceDevRegisReportResDTO ratioAccountDevRegisterServiceByQuarter(
			@RequestParam(value = "quarter", required = false) Integer quarter,
			@RequestParam(value = "provinceId", required = false) Integer provinceId
	) {
		return reportAccountService.ratioAccountDevRegisterServiceByQuarter(quarter, provinceId);
	}


	/**
	 * Ti le account co dang ki dich vu (nha phat trien) theo nam
	 *
     */
	@GetMapping("/sme-release/year")
	public AccountRegisterServiceByYearDTO accountRegiterServiceByYear(@RequestParam(required = false) Integer year,
			@RequestParam(required = false) Long provinceId) {
		return reportAccountService.accountRegisterServiceByYear(year, provinceId);
	}

	@GetMapping("/sme-release/monthly")
	public SmeReleaseMonthlyResponseDTO getSmeReleaseByMonth(
			@RequestParam(name = "month", defaultValue = "") String month,
			@RequestParam(name = "provinceId", defaultValue = "") String provinceId) {
		return reportAccountService.getSmeReleaseByMonth(month, provinceId);
	}
}
