package com.entity.product_variant;

import com.common.converter.AttributesValueConvert;
import com.component.BaseEntity;
import com.dto.product_variant.AttributesValueConvertDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "attributes")
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class Attributes extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
    private String code;
    private String description;

    @Column(name = "attributes_value")
    @Convert(converter = AttributesValueConvert.class)
    private List<AttributesValueConvertDTO> attributesValue;

    @Column(name = "public_status")
    private Integer publicStatus;

    @Column(name = "attribute_field_type")
    private Integer attributeFieldType;

    @Column(name = "portal_type")
    private Integer portalType;

    @Column(name = "name_id")
    private String nameId;
}
