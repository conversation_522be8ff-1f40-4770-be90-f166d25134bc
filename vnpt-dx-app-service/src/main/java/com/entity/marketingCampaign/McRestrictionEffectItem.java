package com.entity.marketingCampaign;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import com.component.BaseEntity;
import com.service.utils.jsonObject.McIfConditionDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "mc_restriction_effect_item")
@NoArgsConstructor
public class McRestrictionEffectItem extends BaseEntity {

    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Integer operandCode;
    private Integer operatorCode;
    private Integer valueCode;
    private String value;
    private Long mcId;
    private Long effectItemId;

    public McRestrictionEffectItem(Long mcId, Long effectItemEntityId, McIfConditionDTO extCond) {
        this.setEffectItemId(effectItemEntityId);
        this.setMcId(mcId);
        this.setOperandCode(extCond.getOperandId());
        this.setOperatorCode(extCond.getOperator());
        this.setValue(extCond.getData().toString());
    }
}
