package com.entity.ward;

import java.time.LocalDateTime;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.PrePersist;
import javax.persistence.Table;
import com.dto.geographySync.WardInfoDTO;
import com.onedx.common.constants.enums.DeletedFlag;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "WARD")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@IdClass(WardID.class)
public class Ward {

	@Id
	@Column(name = "id")
	private Long id;

	@Column(name = "province_id")
	private Long provinceId;

	@Id
	@Column(name = "district_id")
	private Long districtId;

	@Column(name = "name")
	private String name;

	@Id
	@Column(name = "province_code")
	private String provinceCode;

	@Column(name = "code")
	private String code;

	@Column(name = "modified_at")
	private LocalDateTime modifiedAt;

	@Column(name = "deleted_flag")
	private Integer deletedFlag;

	public Ward(WardInfoDTO dto, Long districtId, Long provinceId, String provinceCode) {
		this.id = dto.getId();
		this.code = dto.getCode();
		this.name = dto.getName();
		this.districtId = districtId;
		this.provinceId = provinceId;
		this.provinceCode = provinceCode;
		this.modifiedAt = LocalDateTime.now();
		this.deletedFlag = DeletedFlag.NOT_YET_DELETED.getValue();
	}

	public boolean hasChanged(WardInfoDTO dto) {
		return !(Objects.equals(this.name, dto.getName()) &&
			Objects.equals(this.code, dto.getCode()) &&
			Objects.equals(this.deletedFlag, DeletedFlag.NOT_YET_DELETED.getValue()));
	}

	public void sync(WardInfoDTO dto) {
		this.name = dto.getName();
		this.code = dto.getCode();
		this.modifiedAt = LocalDateTime.now();
		this.deletedFlag = DeletedFlag.NOT_YET_DELETED.getValue();
	}

	public void softDelete() {
		this.modifiedAt = LocalDateTime.now();
		this.deletedFlag = DeletedFlag.DELETED.getValue();
	}

	public int compareWith(Ward other) {
		if (!Objects.equals(getProvinceId(), other.getProvinceId())) {
			return (int) (getProvinceId() - other.getProvinceId());
		}
		if (!Objects.equals(getDistrictId(), other.getDistrictId())) {
			return (int) (getDistrictId() - other.getDistrictId());
		}
		return (int) (getId() - other.getId());
	}

	@PrePersist
	public void prePersist() {
		this.modifiedAt = LocalDateTime.now();
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		Ward that = (Ward) o;
		return Objects.equals(this.id, that.getId()) &&
			Objects.equals(this.districtId, that.getDistrictId()) &&
			Objects.equals(this.provinceCode, that.getProvinceCode());
	}

	@Override
	public int hashCode() {
		return Objects.hash(29, id, districtId, provinceCode);
	}
}
