package com.entity.payment;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0 08/11/2021
 */
/**
 * Đầu mối
 */
@Data
@Entity
@Table(name = "clue")
public class Clue implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Code provin
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * Tên đầu mối
     */
    @Column(name = "clue_name")
    private String clueName;

    /**
     * Mã user
     */
    @Column(name = "user_code")
    private String userCode;

    /**
     * Tên đầy đủ
     */
    @Column(name = "full_name")
    private String fullName;

    /**
     * <PERSON><PERSON><PERSON> vụ
     */
    @Column(name = "position")
    private String position;

    /**
     * Số điện thoại
     */
    @Column(name = "phone_no")
    private String phoneNo;

    /**
     * email
     */
    @Column(name = "email")
    private String email;

    @Column(name = "merchant_service_id")
    private Long merchantServiceId;

    @Column(name = "private_key")
    private String privateKey;

    @Column(name = "api_key")
    private String apiKey;

    @Column(name = "base_url")
    private String baseUrl;

    @Column(name = "e_invoice_pass")
    private String eInvoicePass;

    @Column(name = "e_invoice_user_name")
    private String eInvoiceUserName;

    @Column(name = "e_invoice_base_url")
    private String eInvoiceBaseUrl;

    @Column(name = "e_invoice_type")
    private Integer eInvoiceType;

    @Column(name = "qr_secret_key")
    private String qrSecretKey;

    @Column(name = "qr_terminal_id")
    private String qrTerminalId;

    @Column(name = "qr_api_key")
    private String qrApiKey;

    @Column(name = "province_code_pay")
    private String provinceCodePay;
}
