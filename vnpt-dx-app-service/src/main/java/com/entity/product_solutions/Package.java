package com.entity.product_solutions;

import java.math.BigDecimal;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.enums.PurchaseVersionEnum;
import org.hibernate.annotations.Type;
import com.component.SuperBaseEntity;
import com.constant.enums.suggestions.SuggestionModeEnum;
import com.dto.product_solustions.PackageBundlingCreateDTO.ApplyConditionDTO;
import com.dto.product_solustions.PackageBundlingCreateDTO.Guidelines;
import com.enums.DisplayStatus;
import com.onedx.common.constants.enums.DiscountTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.converter.SetConverter;
import com.onedx.common.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "packages")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class Package extends SuperBaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "name")
    private String name;

    @Column(name = "provider_id")
    private Long providerId;

    @Column(name = "visibility")
    private DisplayStatus visibility;

    @Column(name = "banner_urls")
    @Convert(converter = SetConverter.class)
    private Set<String> bannerUrls;

    @Column(name = "icon_url")
    private String iconUrl;

    @Column(name = "descriptions")
    private String descriptions;

    @Enumerated(EnumType.STRING)
    @Column(name = "discount_type")
    private DiscountTypeEnum discountType;

    @Column(name = "discount_value")
    private BigDecimal discountValue;

    @Column(name = "price")
    private BigDecimal price;

    @Column(name = "price_from")
    private BigDecimal priceFrom;

    @Column(name = "recommended")
    private Boolean recommended;

    @Column(name = "seo_id")
    private Long seoId;

    @Column(name = "feature_visible")
    private Boolean featureVisible;

    @Column(name = "draft_id")
    private Long draftId;

    @Column(name = "deleted_flag")
    private Integer deletedFlag;

    @Enumerated(EnumType.STRING)
    @Column(name = "suggestion_mode")
    private SuggestionModeEnum suggestionMode;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method")
    private PaymentMethodEnum paymentMethod;

    @Column(name = "apply_condition")
    @Type(type = "jsonb")
    private ApplyConditionDTO applyCondition;

    @Column(name = "guidelines")
    @Type(type = "jsonb")
    private Guidelines guidelines;

    @Column(name = "domain_ids")
    @Type(type = "long-array")
    private Long[] domainIds;

    @Column(name = "category_ids")
    @Type(type = "long-array")
    private Long[] categoryIds;

    @Enumerated(EnumType.STRING)
    @Column(name = "purchase_version")
    private PurchaseVersionEnum purchaseVersion;

    public Long[] getCategoryIds() {
        return ObjectUtil.getOrDefault(categoryIds, new Long[0]);
    }
}

