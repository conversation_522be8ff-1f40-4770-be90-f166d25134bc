package com.entity.couponSet;

import java.util.*;

/**
 * <AUTHOR> tuannp
 * @version    : 1.0
 * 20/09/2021
 */
public class CouponSetConstant {
    public static final Integer ALPHABET_AND_NUMBER = 1;        // G<PERSON>m chữ và số
    public static final Integer NUMBER_ONLY = 2;                // Chỉ gồm số
    public static final Integer ALPHABET_ONLY = 3;              // Chỉ gồm chữ
    public static final Integer SET = 1;
    public static final Integer SINGLE = 2;
    public static final Integer AUTO = 1;
    public static final Integer MANUAL = 0;

    public static final Integer MAX_ITEM = 999999;              // Số lượng code tối đa của mỗi

    public static final Integer STATUS_INACTIVE = 0;            // coupon set dừng hoạt động
    public static final Integer STATUS_ACTIVE = 1;              // coupon set hoạt động
    public static final Integer STATUS_EXPIRED = 2;             // coupon set hết hạn

    public static final Integer CODE_NEW = 0;                   // chưa sử dụng
    public static final Integer CODE_USED = 1;                  // đã sử dụng
    public static final Integer CODE_INACTIVE = 2;              // vô hiệu hóa

    /**
     * Tạo mã code
     * @param set  the response
     *
     */
    public static String genCode(CouponSet set,Set<String> randomSet){
        StringBuilder code = new StringBuilder();
        // add prefix
        if(set.getPrefix() !=null){
            code.append(set.getPrefix());
        }
        // random string
        code.append(CouponSetConstant.random(randomSet,set.getLengthCouponCode(),set.getGenerateType()));
        // add suffix
        if(set.getSuffix() !=null){
            code.append(set.getSuffix());
        }
        return code.toString();
    }

    /**
     * Hàm tạo random code
     * @param randomSet  danh sách code đã được tạo
     * @param length  độ dài code
     * @param type  loại code
     * @return code
     */
    public static String random(Set<String> randomSet,Integer length, Integer type){
        int leftLimit;
        int rightLimit;
        switch (type){
            case 1:                     // cả số và chữ
                leftLimit = 48;         // numeral '0'
                rightLimit = 122;       // letter 'z'
                break;
            case 2:                     // chỉ có số
                leftLimit = 48;         // numeral '0'
                rightLimit = 57;        // numeral '9'
                break;
            case 3:                     // chỉ có chữ
                leftLimit = 65;         // letter 'A'
                rightLimit = 122;       // letter 'z'
                break;
            default:                    // cả số và chữ
                leftLimit = 48;         // numeral '0'
                rightLimit = 122;       // letter 'z'
                break;
        }
        return random(randomSet,leftLimit,rightLimit,length);
    }

    private static String random(Set<String> randomSet,Integer leftLimit, Integer rightLimit, Integer length){
        Random random = new Random();
        String str = random.ints(leftLimit, rightLimit + 1)
                .filter(i -> (i <= 57 || i >= 65) && (i <= 90 || i >= 97))
                .limit(length)
                .collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append)
                .toString();
        if(randomSet.contains(str)){
            str = random(randomSet,leftLimit,rightLimit,length);
        } else {
            randomSet.add(str);
        }
        return str;
    }
}
