package com.entity.logging;

import java.io.Serializable;
import java.util.Date;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "api_log") // Đặt tên collection trong MongoDB
public class ApiLog implements Serializable {

    @Id
    private String id;
    private String request;
    private String response;
    private String responseStatus;
    private Date createdAt;
    private Date requestAt;
    private Date responseAt;
    private Long responseTime;
    private Long userId;
    private String clientIp; // Field for storing client IP
    private String methodName; // Field for storing method name
    private String requestId; // request Id
    private String exception; // request Id
    // Getters and setters
    private String userAgent;
    private String referer;
    private String httpMethod;
    private String requestUrl;
    private String queryString;
    private String headers;
    private String payload;


}