package com.entity.credit_note;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR> Halt
 * @version : 1.0
 * 16/06/2021
 */
@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "change_credit_note")
@ToString(callSuper = true)
public class ChangeCreditNote {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "credit_note_id")
    private Long creditNoteId;

    @Column(name = "code")
    private String code;

    @Column(name = "name")
    private String name;

    @Column(name = "tax_id")
    private Long taxId;

    @Column(name = "tax_name")
    private String taxName;

    @Column(name = "tax_value")
    private BigDecimal taxValue;

    @Column(name = "amount_refund")
    private BigDecimal amountRefund;

    @Column(name = "remaining_amount_refund")
    private BigDecimal remainingAmountRefund;

    @Column(name = "type")
    private Integer type;

    @Column(name = "status")
    private Integer status;

    @Column(name = "billing_id")
    private Long billingId;

    @Column(name = "bill_cart_code")
    private String billCartCode;
}
