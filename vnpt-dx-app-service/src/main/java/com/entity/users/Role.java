//package com.entity.users;
//
//import java.io.Serializable;
//import java.util.HashSet;
//import java.util.Set;
//
//import javax.persistence.Column;
//import javax.persistence.Entity;
//import javax.persistence.FetchType;
//import javax.persistence.GeneratedValue;
//import javax.persistence.GenerationType;
//import javax.persistence.Id;
//import javax.persistence.JoinColumn;
//import javax.persistence.JoinTable;
//import javax.persistence.ManyToMany;
//import javax.persistence.OrderBy;
//import javax.persistence.Table;
//import javax.persistence.UniqueConstraint;
//import javax.validation.constraints.NotNull;
//import javax.validation.constraints.Size;
//
//import com.component.BaseEntity;
//
//import lombok.Getter;
//import lombok.Setter;
//
//@Entity
//@Table(name = "ROLE", uniqueConstraints = { @UniqueConstraint(columnNames = { "NAME" }) })
//@Getter
//@Setter
//public class Role extends BaseEntity implements Serializable {
//
//	/**
//	 * 
//	 */
//	private static final long serialVersionUID = 9067204826099995492L;
//
//	@Id
//	@GeneratedValue(strategy = GenerationType.IDENTITY)
//	@Column(name = "ID")
//	private Long id;
//
//	@NotNull
//	@Size(max = 50)
//	@Column(name = "NAME")
//	private String name;
//
//	@ManyToMany(fetch = FetchType.LAZY)
//	@JoinTable(name = "ROLES_PERMISSIONS", joinColumns = @JoinColumn(name = "ROLE_ID", referencedColumnName = "ID"), inverseJoinColumns = @JoinColumn(name = "PERMISSION_ID", referencedColumnName = "ID"))
//	@OrderBy
//	private Set<Permission> permissions = new HashSet<>();
//
//	private String description;
//}
