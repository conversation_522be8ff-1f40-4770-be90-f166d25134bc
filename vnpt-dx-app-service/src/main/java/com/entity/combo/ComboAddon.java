package com.entity.combo;

import java.io.Serializable;
import com.entity.addons.Addon;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 22/06/2021 - 11:57 PM
 */
@Data
@Entity
@Table(name = "combo_addon")
public class ComboAddon implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "id_combo_plan")
    private Long comboPlanId;

    @Column(name = "id_addon")
    private Long addonsId;

    @Column(name = "is_required")
    private Integer isRequired;

    @Column(name = "combo_plan_draft_id")
    private Long comboPlanDraftId;

    @Column(name = "multi_pricing_plan_id")
    private Long pricingMultiPlanId;

    @Column(name = "addon_draft_id")
    private Long addonDraftId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_addon", insertable = false, updatable = false)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Addon addon;
}
