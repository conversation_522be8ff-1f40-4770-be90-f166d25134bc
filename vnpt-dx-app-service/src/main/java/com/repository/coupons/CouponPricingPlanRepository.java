package com.repository.coupons;

import com.constant.sql.SQLCoupon;
import com.dto.coupons.CouponAddonServiceDetailDTO;
import com.dto.coupons.CouponPricingServiceDetailDTO;
import com.entity.coupons.CouponPricingPlan;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Halt3
 * @version : 1.0 18/9/2021
 */
@Repository
public interface CouponPricingPlanRepository extends JpaRepository<CouponPricingPlan, Long> {

    @Query(value = SQLCoupon.GET_ALL_COUPON_PRICING_SERVICE_DETAIL, nativeQuery = true)
    List<CouponPricingServiceDetailDTO> getCouponPricingServiceDetail(Long couponId);

    @Query(value = SQLCoupon.GET_ALL_COUPON_ADDON_SERVICE_DETAIL, nativeQuery = true)
    List<CouponAddonServiceDetailDTO> getCouponAddonServiceDetail(Long couponId);

    void deleteByCouponId(Long couponId);

    List<CouponPricingPlan> findByCouponId(Long id);

    @Query(value = SQLCoupon.FIND_ALL_BY_PRICING_MULTI_PLAN_IDS)
    List<CouponPricingPlan> findAllByPricingMultiPlanIds (@Param("pricingMultiPlanIds") List<Long> pricingMultiPlanIds);

    void deleteByPricingMultiPlanIdInAndType(List<Long> PricingMultiPlanId, int i);
}
