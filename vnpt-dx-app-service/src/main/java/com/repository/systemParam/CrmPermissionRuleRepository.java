package com.repository.systemParam;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import com.constant.sql.SQLCrmDataPartition;
import com.entity.systemParam.CrmPermissionRule;

public interface CrmPermissionRuleRepository extends JpaRepository<CrmPermissionRule, Long> {

    @Query(value = SQLCrmDataPartition.GET_RULE_CONDITION_BY_REQUIRED_PERMISSION, nativeQuery = true)
    String findConditionByRequiredPermission(Integer permissionType, Integer required);

}
