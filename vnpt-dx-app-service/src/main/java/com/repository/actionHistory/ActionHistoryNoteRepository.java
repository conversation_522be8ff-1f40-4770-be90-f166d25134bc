package com.repository.actionHistory;

import java.util.Date;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.constant.sql.SQLActionHistoryNote;
import com.dto.actionHistory.actionHistoryNote.IActionHistoryNoteDTO;
import com.entity.actionHistory.ActionHistoryNote;

@Repository
public interface ActionHistoryNoteRepository extends JpaRepository<ActionHistoryNote, Long> {

    @Query(nativeQuery = true, value = SQLActionHistoryNote.GET_LIST_ACTION_NOTE_USER_ENTERPRISE)
    Page<IActionHistoryNoteDTO> getPageUserEnterpriseActionNote(Long enterpriseId, Long userId, Date startDate, Date endDate, String search,
        Pageable pageable);

    @Query(nativeQuery = true, value = SQLActionHistoryNote.GET_LIST_ACTION_NOTE_OTHER_OBJECT)
    Page<IActionHistoryNoteDTO> getPageObjectActionNote(Integer objectType, Long objectId, Date startDate, Date endDate, String search,
        Pageable pageable);

    List<ActionHistoryNote> findByObjectTypeAndObjectId(Integer objectType, Long contactId);
}
