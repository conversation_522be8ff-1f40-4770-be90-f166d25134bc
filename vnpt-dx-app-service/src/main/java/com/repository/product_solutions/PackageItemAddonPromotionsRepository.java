package com.repository.product_solutions;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.entity.product_solutions.PackageItemAddonPromotions;

@Repository
public interface PackageItemAddonPromotionsRepository extends JpaRepository<PackageItemAddonPromotions, Long> {

    List<PackageItemAddonPromotions> findByPackageItemAddonId(Long packageItemAddonId);

    /**
     * Xóa coupon khỏi addon items dựa trên package draft ID và coupon ID
     */
    @Modifying
    @Transactional
    @Query(nativeQuery = true, value =
        "DELETE FROM package_item_addon_promotions " +
        "WHERE package_item_addon_id IN (" +
        "    SELECT pia.id FROM package_item_addons pia " +
        "    JOIN package_items pi ON pi.id = pia.package_item_id " +
        "    JOIN package_mappings pm ON pm.package_item_id = pi.id " +
        "    WHERE pm.package_draft_id = :packageDraftId" +
        ") AND coupon_id = :couponId")
    void deleteCouponFromAddonItems(@Param("packageDraftId") Long packageDraftId, @Param("couponId") Long couponId);

    /**
     * Tìm tất cả addon promotions theo addon item IDs và coupon ID
     */
    @Query("SELECT p FROM PackageItemAddonPromotions p WHERE p.packageItemAddonId IN :addonItemIds AND p.couponId = :couponId")
    List<PackageItemAddonPromotions> findByAddonItemIdsAndCouponId(@Param("addonItemIds") List<Long> addonItemIds, @Param("couponId") Long couponId);
}

