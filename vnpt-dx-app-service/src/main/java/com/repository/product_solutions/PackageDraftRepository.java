package com.repository.product_solutions;

import java.util.List;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.constant.sql.SQLPackage;
import com.constant.sql.SQLProductSolution;
import com.dto.product_solustions.IGetListCreatedBy;
import com.dto.product_solustions.IGetListPackageDraftResDTO;
import com.dto.product_solustions.IGetListProduct;
import com.dto.product_solustions.IGetListServiceProductResDTO;
import com.dto.product_solustions.IGetPackageDetailDTO;
import com.dto.product_solustions.IGetSolutionInPackageDTO;
import com.entity.product_solutions.PackageDraft;
import io.lettuce.core.dynamic.annotation.Param;

@Repository
public interface PackageDraftRepository extends JpaRepository<PackageDraft, Long> {

    boolean existsByNameAndDeletedFlagAndIdNot(String packageName, int value, Long id);

    boolean existsByProviderIdNotAndIdIn(Long providerId, List<Long> draftIds);

    @Query(nativeQuery = true, value = SQLPackage.SEARCH_PACKAGE)
    Page<IGetListPackageDraftResDTO> listPackageDraft(@Param("value") String value,
        @Param("isName") Integer isName,
        @Param("isCode") Integer isCode,
        @Param("approveStatus") Integer approveStatus,
        @Param("displayStatus") Integer displayStatus,
        @Param("customerType") String customerType,
        @Param("createdIds") List<Long> createdIds,
        @Param("productIds") List<Long> productIds,
        @Param("userId") Long userId,
        @Param("customerTypeLst") List<String> customerTypeLst,
        Pageable pageable);

    @Query(nativeQuery = true, value = SQLPackage.GET_LIST_SERVICE_PRODUCT_BY_PACKAGE_IDS)
    List<IGetListServiceProductResDTO> listServiceProduct(@Param("productIds") Set<Long> productIds);

    boolean existsByCreatedByAndIdIn(Long currentUserId, List<Long> packageIds);

    @Query(nativeQuery = true, value = SQLProductSolution.DELETE_PACKAGE_BY_IDS)
    @Modifying
    @Transactional
    void updateDeletedFlagByIds(List<Long> ids, int value, Long userId);

    @Query(nativeQuery = true, value = SQLProductSolution.UPDATE_STATE_PACKAGE_BY_IDS)
    @Modifying
    @Transactional
    void updateStateByIds(List<Long> ids, int value, Long userId);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_PACKAGE_DRAFT)
    IGetPackageDetailDTO getPackageDetailDraft(Long objectId);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_PACKAGE)
    IGetPackageDetailDTO getPackageDetail(Long objectId);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_SOLUTION_IN_PACKAGE)
    List<IGetSolutionInPackageDTO> getLstSolutionInPackage(List<Long> ids);


    @Query(nativeQuery = true, value = SQLPackage.GET_LIST_CREATED_BY_IN_SEARCH)
    List<IGetListCreatedBy> getListCreatedBy();

    @Query(nativeQuery = true, value = SQLPackage.GET_LIST_PRODUCT_IN_SEARCH)
    List<IGetListProduct> getListProduct();

    /**
     * Tìm các package draft có chứa coupon cụ thể
     * @param couponId ID của coupon
     * @return Danh sách package draft IDs
     */
    @Query(nativeQuery = true, value = SQLPackage.FIND_PACKAGE_DRAFTS_BY_COUPON_ID)
    List<Long> findPackageDraftIdsByCouponId(@Param("couponId") Long couponId);
}

