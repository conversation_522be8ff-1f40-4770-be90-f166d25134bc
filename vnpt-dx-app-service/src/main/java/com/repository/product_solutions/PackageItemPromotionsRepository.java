package com.repository.product_solutions;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.entity.product_solutions.PackageItemPromotions;

@Repository
public interface PackageItemPromotionsRepository extends JpaRepository<PackageItemPromotions, Long> {

    List<PackageItemPromotions> findByPackageItemId(Long packageItemId);

    /**
     * Xóa coupon khỏi package items dựa trên package draft ID và coupon ID
     */
    @Modifying
    @Transactional
    @Query(nativeQuery = true, value =
        "DELETE FROM package_item_promotions " +
        "WHERE package_item_id IN (" +
        "    SELECT pi.id FROM package_items pi " +
        "    JOIN package_mappings pm ON pm.package_item_id = pi.id " +
        "    WHERE pm.package_draft_id = :packageDraftId" +
        ") AND coupon_id = :couponId")
    void deleteCouponFromPackageItems(@Param("packageDraftId") Long packageDraftId, @Param("couponId") Long couponId);

    /**
     * Tìm tất cả promotions theo package item IDs và coupon ID
     */
    @Query("SELECT p FROM PackageItemPromotions p WHERE p.packageItemId IN :packageItemIds AND p.couponId = :couponId")
    List<PackageItemPromotions> findByPackageItemIdsAndCouponId(@Param("packageItemIds") List<Long> packageItemIds, @Param("couponId") Long couponId);
}

