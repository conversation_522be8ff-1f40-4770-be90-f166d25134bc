package com.repository.product_solutions;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.constant.sql.SQLPackage;
import com.constant.sql.SQLProductSolution;
import com.dto.feature.FeatureDetailDTO;
import com.dto.product_solustions.IGetFeaturePackageDTO;
import com.dto.product_solustions.IGetSPDVBundlingDTO;
import com.dto.product_solustions.IPackProductItemDTO;
import com.dto.suggestions.ISuggestionGroupDetail;
import com.dto.suggestions.ISuggestionItemDetail;
import com.entity.product_solutions.Package;

@Repository
public interface PackageRepository extends JpaRepository<Package, Long> {

    boolean existsByIdInAndProviderIdNot(List<Long> packageIds, Long providerId);

    Optional<Package> findByIdAndDeletedFlag(Long packageId, Integer deletedFlag);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_LIST_PACKAGE_PRODUCT_ITEM_BY_PACKAGE_ID)
    List<IPackProductItemDTO> getListPackageProduct(Long packageId);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_LIST_SME_PACKAGE_PRODUCT_ITEM_BY_PACKAGE_ID)
    List<IPackProductItemDTO> getListSmePackageProductByPackageId(Long packageId);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_PACKAGE_FEATURE_BY_PACKAGE_ID)
    IGetFeaturePackageDTO getPackageFeatureByPackageId(Long packageId);

    List<Package> findByDraftId(Long draftId);

    @Query(nativeQuery = true, value = SQLProductSolution.GET_SPDV_BUNDLING, countQuery = SQLProductSolution.COUNT_SPDV_BUNDLING)
    Page<IGetSPDVBundlingDTO> getSPDVBundling(String value, Integer isNameService, Integer isNamePricing, List<Long> categoryIds, Long providerId,
        String manufactureName, String customerTypes, String paymentCycle, Integer classification,
        Integer serviceType, Pageable pageable);

    @Query(nativeQuery = true, value = SQLPackage.IS_LATEST_VERSION)
    boolean isLatestVersion(Long packageId, Long draftId);

    @Query(nativeQuery = true, value = SQLPackage.GET_SUGGEST_GROUP_DETAIL)
    ISuggestionGroupDetail getSuggestionGroupDetail(Long packageDraftId);

    @Query(nativeQuery = true, value = SQLPackage.GET_SUGGEST_ITEM_DETAIL)
    ISuggestionItemDetail getSuggestionItemDetail(Long packageDraftId, Long solutionDraftId);

    @Query(nativeQuery = true, value = SQLPackage.GET_LIST_FEATURE_BY_OBJECT_ID)
    List<FeatureDetailDTO> getListFeatureByPackageId(String objectType, Long objectId);
}

