package com.repository.pricing;

import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import com.constant.sql.SQLPricing;
import com.constant.sql.SQLPricingAddon;
import com.dto.pricing.PricingAddonRes;
import com.dto.shoppingCart.IAddonInfo;
import com.entity.pricing.PricingAddon;

public interface PricingAddonRepository extends JpaRepository<PricingAddon, Long>, JpaSpecificationExecutor<PricingAddon> {

    @Query(nativeQuery = true, value = SQLPricing.FIND_PRICING_ADDON_BY_PRICING_ID)
    Set<PricingAddonRes> findByPricingId(Long pricingId, Integer addonType);

    @Query(nativeQuery = true, value = SQLPricing.FIND_PRICING_ADDON_BY_PRICING_DRAFT_ID)
    Set<PricingAddonRes> findByPricingDraftIds(Long pricingDraftId, Integer addonType);

    List<PricingAddon> findByPricingDraftId(Long pricingDraftId);

    void deleteByPricingDraftIdAndPricingIdIsNull(Long pricingDraftId);

    void deleteAllByAddonsIdIn(List<Long> addonIds);

    void deleteAllByAddonDraftIdIn(List<Long> addonIds);

    List<PricingAddon> findAllByPricingId(Long pricingId);

    List<PricingAddon> findAllByPricingIdAndAddonsIdIn(Long pricingId, Set<Long> addonsId);

    List<PricingAddon> findAllByAddonDraftIdAndAddonsIdIsNull(Long id);

    void deleteAllByAddonDraftIdAndAddonsIdIsNull(Long addonDraftId);

    @Query(nativeQuery = true, value = SQLPricingAddon.FIND_ALL_LATEST_BY_PRICING_ID)
    List<IAddonInfo> findAllLatestByPricingId(Long pricingId);

    @Query(nativeQuery = true, value = SQLPricing.GET_STATUS_PRICING_ADDON)
    List<Integer> getStatusByPricingId(Long pricingId, List<Long> addonIds);
}
