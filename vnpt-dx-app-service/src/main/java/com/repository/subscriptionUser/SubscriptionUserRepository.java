package com.repository.subscriptionUser;

import com.constant.sql.SQLSubscription;
import com.constant.sql.SQLSubscriptionUser;
import com.entity.subscriptionUser.SubscriptionUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 26/1/2021
 */
public interface SubscriptionUserRepository extends JpaRepository<SubscriptionUser, Long> {
    @Query(value = SQLSubscriptionUser.GET_SUBSCRIPTION_USERS)
    Page<SubscriptionUser> findSubscriptionUsers(Pageable pageable);

    Integer countAllBySubscriptionIdAndStatusEquals(Long subscriptionId, Integer status);

    @Query(nativeQuery = true, value = SQLSubscriptionUser.CHECK_USER_USED_OFFICIALLY_SERVICE)
    Boolean checkSubscriptionUsersWithServiceId(Long userId, Long serviceId);

    @Query(nativeQuery = true, value = SQLSubscriptionUser.CHECK_USER_USED_OFFICIALLY_SERVICE_AND_DEVICE)
    Boolean checkAllSubscriptionUsersWithServiceId(Long userId, Long serviceId);

    @Query(nativeQuery = true, value = SQLSubscriptionUser.CHECK_USER_USED_OFFICIALLY_COMBO)
    Boolean checkSubscriptionUsersWithComboId(Long userId, Long comboId);

    List<SubscriptionUser> findByUserIdAndSubscriptionIdIn(Long userId, List<Long> subscriptionIds);

    @Transactional
    @Modifying
    @Query("UPDATE SubscriptionUser SET status = :status WHERE userId IN :ids AND subscriptionId = :id AND serviceId = :serviceId")
    void updateUserSupscriptionStatus(@Param("ids") Set<Long> ids,
            @Param("status") Integer status, @Param("id") Long id, Long serviceId);

    @Query("SELECT su.userId FROM SubscriptionUser su WHERE su.subscriptionId = :id AND su.serviceId = :serviceId")
    Set<Long> getUserInSubscription(@Param("id") Long id, @Param("serviceId") Long serviceid);

    Set<SubscriptionUser> findAllBySubscriptionIdAndServiceId(Long subscriptionId, Long serviceId);

    @Query(value = SQLSubscription.GET_ID_SUB_BY_USER_ID_AND_COMBO_ID, nativeQuery = true)
    Optional<Long> getIdSubByUserIdAndComboId(Long userId, Long comboId);

}
