package com.repository.enterprise;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.constant.sql.SQLComboBoxPlace;
import com.constant.sql.SQLProvince;
import com.constant.sql.SQLRegion;
import com.dto.enterprise.ComboboxBillCodeResponseDTO;
import com.dto.enterprise.ComboboxDistrictResponseDTO;
import com.dto.enterprise.ComboboxResponseDTO;
import com.dto.enterprise.ComboboxStreetDTO;
import com.dto.enterprise.ComboboxWardResponseDTO;
import com.entity.enterprise.Regions;

@Repository
public interface RegionsRepository extends JpaRepository<Regions, Long> {

    @Query(value = SQLComboBoxPlace.GET_ALL_REGIONS, nativeQuery = true)
    List<ComboboxResponseDTO> getAllRegions(String regionName);

    @Query(value = SQLComboBoxPlace.GET_PROVINCE_BY_REGION_ID, nativeQuery = true)
    List<ComboboxResponseDTO> getProvinceByRegion(List<Long> regionIds, String provinceName);

    @Query(value = SQLComboBoxPlace.GET_DISTRICT_BY_PROVINCE, nativeQuery = true)
    Slice<ComboboxDistrictResponseDTO> getDistinctByProvince(Pageable pageable, List<Long> provinceId, String districtName);

    @Query(value = SQLComboBoxPlace.GET_WARD_BY_PROVINCE_ID_DISTRICT_ID, nativeQuery = true)
    Slice<ComboboxWardResponseDTO> getSliceWardByProvinceIdDistrictId(Pageable pageable, List<Long> districtId, List<Long> provinceId,
        String wardName);

    @Query(value = SQLComboBoxPlace.GET_BUSINESS_AREA, nativeQuery = true)
    Slice<ComboboxResponseDTO> getBusinessArea(Pageable pageable, String name, String code);

    @Query(value = SQLComboBoxPlace.GET_BUSINESS_TYPE, nativeQuery = true)
    Slice<ComboboxResponseDTO> getBusinessType(Pageable pageable, String name);

    @Query(value = SQLComboBoxPlace.GET_BUSINESS_SIZE, nativeQuery = true)
    List<ComboboxResponseDTO> getBusinessSize(String name);

    @Query(value = SQLComboBoxPlace.GET_EMAIL, nativeQuery = true)
    Slice<String> getSliceEmail(Pageable pageable, Long type, String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_PHONE_NUMBER, nativeQuery = true)
    Slice<String> getSlicePhoneNumber(Pageable pageable, Long type , String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_SERVICE, nativeQuery = true)
    Slice<ComboboxResponseDTO> getService (Pageable pageable, String name);

    @Query(value = SQLComboBoxPlace.GET_BILL_CODE, nativeQuery = true)
    Slice<ComboboxBillCodeResponseDTO> getBillCode (Pageable pageable, String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_ENTERPRISE_BY_PROVINCE_AND_STATUS, nativeQuery = true)
    Slice<ComboboxResponseDTO> getSliceEnterpriseByProvinceAndStatus(Pageable pageable, Long status, List<Long> provinceId, Long type,
        String name);

    @Query(value = SQLComboBoxPlace.GET_ENTERPRISE_BY_PROVINCE_AND_CUSTOMER_CLASSIFICATION,
        countQuery = SQLComboBoxPlace.COUNT_ENTERPRISE_BY_PROVINCE_AND_CUSTOMER_CLASSIFICATION, nativeQuery = true)
    Page<ComboboxResponseDTO> getEnterpriseByProvinceAndCustomer (Pageable pageable, Long status, List<Long> provinceId, String name);

    @Query(value = SQLComboBoxPlace.GET_ENTERPRISE_BY_PROVINCE_AND_CUSTOMER_CLASSIFICATION, nativeQuery = true)
    Slice<ComboboxResponseDTO> getSliceEnterpriseByProvinceAndCustomer(Pageable pageable, Long status, List<Long> provinceId, String name);

    @Query(value = SQLComboBoxPlace.GET_TIN_BY_ENTERPRISE_ID_AND_TYPE, nativeQuery = true)
    Slice<String> getSliceTin (Pageable pageable, List<String> enterpriseId, Long type, String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_REPRESENT_ACTIVE, nativeQuery = true)
    Slice<String> getSliceRepresentative (Pageable pageable, List<String> tins, Long type, String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_ALL_FOLK, nativeQuery = true)
    List<ComboboxResponseDTO> getFolk (String name);

    @Query(value = SQLComboBoxPlace.GET_EMPLOYEE_BY_TYPE, nativeQuery = true)
    Page<ComboboxResponseDTO> getEmployeeByType (Pageable pageable ,Long type, String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_STREET_BY_WARD_ID, nativeQuery = true)
    Page<ComboboxStreetDTO> getStreetByProvinceIdAndWardId(Pageable pageable, List<Long> lstProvinceId, List<Long> lstWardId, String name);

    @Query(value = SQLComboBoxPlace.GET_CUSTOMER_TICKET, nativeQuery = true)
    Page<String> getCustomerTicket(Pageable pageable, String name, Long adminProvinceId);

    @Query(value = SQLComboBoxPlace.GET_ACTION_HISTORY, nativeQuery = true)
    Slice<String> getActionHistory(Pageable pageable, String name);

    @Query(value = SQLRegion.FIND_NAME_BY_ID, nativeQuery = true)
    String findNameById(Long regionId);

    @Query(value = SQLRegion.FIND_FIRST_ID_BY_NAME, nativeQuery = true)
    Long findFirstIdByName(String regionName);

    @Query(value = SQLComboBoxPlace.GET_IMPORT_MIGRATION_NAME, countQuery = SQLComboBoxPlace.COUNT_IMPORT_MIGRATION_NAME, nativeQuery = true)
    Page<String> getImportName(Pageable pageable, String name, Long adminProvinceId);

    @Query(nativeQuery = true, value = SQLProvince.GET_PROVINCE_BY_ID)
    ComboboxResponseDTO getProvinceById(Long provinceId);

    @Query(nativeQuery = true, value = SQLProvince.GET_DISTRICT_BY_PROVINCE_ID_AND_ID)
    ComboboxDistrictResponseDTO getDistrictByProvinceIdAndId(Long provinceId, Long districtId);

    @Query(nativeQuery = true, value = SQLProvince.GET_WARD_BY_PROVINCE_ID_AND_DISTRICT_ID_AND_ID)
    ComboboxWardResponseDTO getWardByProvinceIdAndDistrictIdAndId(Long provinceId, Long districtId, Long wardId);

    @Query(nativeQuery = true, value = SQLProvince.GET_STREET_BY_PROVINCE_ID_AND_WARD_ID_AND_ID)
    ComboboxStreetDTO getStreetByProvinceIdAndWardIdInAndIdIn(Long provinceId, Long wardId, Long streetId);


}
