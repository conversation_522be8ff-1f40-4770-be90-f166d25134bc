package com.dto.subscriptions;

import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> nghiapt
 * @version    : 1.0
 * 30/06/2021
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComboPlanIdSubscriptionReqDTO {

    @Schema(description = SwaggerConstant.ComboPlan.ID, example = SwaggerConstant.Example.ID)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private Long comboPlanId;

    @Schema(description = Subscription.EMPLOYEE_CODE, example = Example.CODE)
    private String employeeCode;
}
