package com.dto.subscriptions;

import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @version: 1.0
 * 28/06/2021
 */

@Data
@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubscriptionComboFirstStepReqDTO {
    ComboPlan comboPlan;

    List<Addon> addons;

    List<Long> coupons;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComboPlan {

        @Schema(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
        Long id;

        @Schema(description = SwaggerConstant.ComboPlan.ESTIMATE_QUANTITY, example = SwaggerConstant.Example.QUANTITY)
        Long quantity;

        List<Long> couponIds;

    }

    //    giống với comboPlan nhưng tách ra trường hợp sau mở rộng
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Addon {

        @Schema(description = SwaggerConstant.Addon.ID, example = SwaggerConstant.Example.ID)
        Long id;

        @Schema(description = SwaggerConstant.Addon.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
        Long quantity;

        @Schema(description = SwaggerConstant.Coupon.ID, example = SwaggerConstant.Example.LIST_COUPON)
        List<Long> couponIds;
    }
}
