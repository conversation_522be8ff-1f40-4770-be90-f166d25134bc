package com.dto.subscriptions.detail;

import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 21/06/2021 - 5:41 PM
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubscriptionUnitLimitedDTO {

    @Schema(description = SwaggerConstant.UnitLimited.PRICING_ID, example = SwaggerConstant.ID)
    Long pricingId;

    @Schema(description = SwaggerConstant.UnitLimited.PRICE, example = SwaggerConstant.Example.PRICE)
    BigDecimal price;

    @Schema(description = SwaggerConstant.UnitLimited.FROM, example = SwaggerConstant.Example.RANGE_FROM_TO)
    Long unitFrom;

    @Schema(description = SwaggerConstant.UnitLimited.TO, example = SwaggerConstant.Example.RANGE_FROM_TO)
    Long unitTo;

    @Schema(description = SwaggerConstant.UnitLimited.ADDON_ID, example = SwaggerConstant.ID)
    Long addonsId;
}
