package com.dto.subscriptions;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.enums.SystemParamEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.coupons.TimeUsedTypeEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Coupon;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> nghiapt
 * @version : 1.0
 * 06/09/2021
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CouponPopupDTO {

    @Schema(description = SwaggerConstant.Coupon.ID, example = SwaggerConstant.Example.ID)
    private Long id;

    @Schema(description = SwaggerConstant.Coupon.NAME, example = SwaggerConstant.Example.COUPON_NAME)
    private String couponName;

    @Schema(description = SwaggerConstant.Coupon.CODE, example = SwaggerConstant.Example.COUPON_CODE)
    private String code;

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_VALUE, example = SwaggerConstant.Example.COUPON_DISCOUNT_AMOUNT)
    private String promotionValue;

    @Schema(description = SwaggerConstant.Coupon.MINIMUM_AMOUNT, example = SwaggerConstant.Example.AMOUNT)
    private Long minimumAmount;

    @Schema(description = SwaggerConstant.Coupon.MINIMUM, example = SwaggerConstant.Example.QUANTITY)
    private Long minimum;

    @Schema(description = SwaggerConstant.SystemParam.STATUS, example = SwaggerConstant.Example.STATUS)
    private SystemParamEnum systemParamCoupon;

    @Schema(description = SwaggerConstant.Coupon.PROMOTION_TYPE, example = SwaggerConstant.Example.PROMOTION_TYPE)
    private PromotionTypeEnum promotionType;

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_TYPE, example = SwaggerConstant.Example.COUPON_DISCOUNT_TYPE)
    private DiscountTypeEnum discountType;

    @Schema(description = SwaggerConstant.Coupon.MAX_USED, example = SwaggerConstant.Example.QUANTITY)
    private Long maxUsed;

    @Schema(description = SwaggerConstant.Coupon.START_DATE, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    private Date startDate;

    @Schema(description = SwaggerConstant.Coupon.END_DATE, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    private Date endDate;

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_AMOUNT, example = SwaggerConstant.Example.AMOUNT)
    private BigDecimal discountAmount;

    @Schema(description = SwaggerConstant.Coupon.LIMITED_QUANTITY, example = SwaggerConstant.Example.COUPON_TIMES_USED_TYPE)
    private Long limitedQuantity;

    @Schema(description = SwaggerConstant.Coupon.TYPE, example = SwaggerConstant.Example.COUPON_TYPE)
    private TimeTypeEnum timeType;

    @Schema(description = SwaggerConstant.Coupon.TIMES_USED_TYPE, example = SwaggerConstant.Example.COUPON_TIMES_USED_TYPE)
    private TimeUsedTypeEnum timesUsedType;

    @Schema(description = Coupon.CONDITIONS, example = Example.CONTENT)
    private List<String> conditions;

    @JsonIgnore
    private BigDecimal promotionValueOrder;

    private List<ProductByCoupon> listProduct;

    private boolean existCouponSet;

    private Long maximumPromotion;

    private Integer visibleStatus;

    private Long remainQuantity;

    private Long subscriptionUsedPromotion;

    private Integer objectType;

    private Long addonId;

    @JsonIgnore
    private int score = 0;
    @JsonIgnore
    BigDecimal discountValue;

    // Sử dụng để kiểm tra điều kiện số tiền tối thiểu áp dụng được CTKM
    private boolean applicable = true;

    @Schema(description = Coupon.CUSTOMER_TYPE, example = Example.CUSTOMER_TYPE)
    private String customerType;

    public interface ProductByCoupon{
        @Schema(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
        Long getProductId();

        @Schema(description = "Tên pricing/combo plan", example = SwaggerConstant.Example.PRICING_NAME)
        String getProductName();

        @Schema(description = "Tên service/combo", example = Example.SERVICE_NAME)
        String getServiceName();
    }
}
