package com.dto.orders.response;

import java.util.Date;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Order;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.constants.values.SwaggerConstant.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> HuyLD
 * @version : 1.0 02/08/2021
 */
public interface OrderServiceListResponseDTO {

    @Schema(description = Subscription.ID, example = SwaggerConstant.Example.ID)
    Long getSubscriptionId();

    @Schema(description = Order.ID, example = SwaggerConstant.Example.ID)
    Long getOrderReceiveId();

    @Schema(description = Order.ORDER_STATUS, example = Example.ORDER_STATUS)
    Integer getStatusId();

    @Schema(description = Order.ORDER_STATUS_NAME, example = Example.ORDER_STATUS_NAME)
    String getStatusName();

    @Schema(description = Order.PAYMENT_STATUS, example = Example.PAYMENT_STATUS)
    String getPaymentStatus();

    @Schema(description = Subscription.TOTAL_AMOUNT, example = Example.TOTAL_AMOUNT)
    Long getTotalAmount();

    @Schema(description = Service.NAME, example = Example.SERVICE_NAME)
    String getServiceName();

    @Schema(description = User.NAME_DEV, example = Example.NAME_DEV)
    String getCusName();

    @Schema(description = Pricing.NAME, example = Example.PRICING_NAME)
    String getPricingName();

    @Schema(description = User.NAME_DEV, example = Example.DEVELOPER_NAME)
    String getDeveloperName();

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    @Schema(description = SwaggerConstant.CreditNote.CREATED_AT, example = SwaggerConstant.Example.DATE)
    Date getCreateAt();

    @Schema(description = SwaggerConstant.Service.URL_ICON, example = SwaggerConstant.Example.FILE_PATH)
    String getIcon();

    @Schema(description = SwaggerConstant.FileAttach.EXTERNAL_LINK, example = SwaggerConstant.Example.IMAGE_URL)
    String getEmbedURL();

    @Schema(description = Order.ORDER_STATUS, example = Example.TRANSACTION_CODE)
    String getTransactionCode();

    @Schema(description = Order.ORDER_STATUS, example = Example.TRANSACTION_CODE)
    String getStatusHD();

    @Schema(description = User.TIN, example = Example.TRANSACTION_CODE)
    String getTin();

    @Schema(description = Subscription.MIGRATE_TIME, example = Example.TIME)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getMigrateTime();

    @Schema(description = Subscription.MIGRATE_CODE, example = Example.CODE)
    String getMigrateCode();

    @Schema(description = Subscription.CREATED_SOURCE_MIGRATION, example = Example.CODE)
    Integer getCreatedSource();

    @Schema(description = Subscription.ID, example = Example.ID)
    Long getMigrateId();

    Integer getInstallationStatus();

}
