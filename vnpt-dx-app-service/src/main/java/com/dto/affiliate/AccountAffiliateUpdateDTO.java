package com.dto.affiliate;

import com.onedx.common.constants.enums.affiliate.AffiliateTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.OptBoolean;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

@Getter
@Setter
public class AccountAffiliateUpdateDTO {
    private String name;
    private String firstName;
    private String lastName;
    private Long provinceId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy", lenient = OptBoolean.FALSE)
    private Date birthday;
    private String tin;
    private String email;
    private String phoneNumber;
    private AffiliateTypeEnum affiliateType;
    private String tradingAddress;
    private String parentAffiliateCode;
    private String contactName;

    @Schema(description = "Loại giấy chứng thực", example = "CMND")
    private Integer repPersonalCertTypeId;

    @Schema(description = "Số giấy chứng thực", example = "*********")
    private String repPersonalCertNumber;

    @Schema(description = "Ngày cấp giấy chứng thức", example = "Trịnh")
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date repPersonalCertDate;

    @Schema(description = "Nơi cấp giấy chứng thức", example = "Bắc Ninh")
    private String repPersonalCertPlace;

    @Schema(description = "Địa chỉ liên hệ", example = "Bắc Ninh")
    private String contactAddress;

    @Schema(description = "Địa chỉ thường trú", example = "Bắc Ninh")
    private String permanentAddress;

    @Schema(description = "Thành viên cấp trên", example = "QNT")
    private Long parentId;

    @Schema(description = "Số giấy phép ĐKKD", example = "QNT")
    private String businessLicenseNumber;

    @Schema(description = "Nơi cấp giấy phép ĐKKD", example = "QNT")
    private String businessLicenseIssuedBy;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    @Schema(description = "Ngày cấp giấy phép ĐKKD", example = "QNT")
    private Date businessLicenseIssuedDate;

    private Set<Long> businessRegistrationFileIds;
    private Set<Long> repIdentityFileIds;

    // thông tin thanh toán
    private String bankName;
    private String bankNumber;
    private String bankBranch;
    private String bankBeneficiary;

    private Integer type; // 1: admin, 0: đại lý
}
