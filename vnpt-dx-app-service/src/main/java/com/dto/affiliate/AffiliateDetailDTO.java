package com.dto.affiliate;

import com.onedx.common.entity.security.Role;
import com.onedx.common.constants.enums.affiliate.AffiliateStatusEnum;
import com.onedx.common.constants.enums.affiliate.AffiliateTypeEnum;
import com.onedx.common.entity.affiliate.AffiliateUsers;
import com.entity.file.attach.FileAttach;
import com.onedx.common.constants.enums.PortalType;
import com.model.entity.security.User;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AffiliateDetailDTO {
    // users
    String name;
    String lastName;
    String firstName;
    Date birthday;
    String email;
    String phoneNumber;
    Integer userStatus;
    String parentCode;
    AffiliateTypeEnum affiliateType;
    String corporateTaxCode;
    Integer repPersonalCertTypeId;
    String repPersonalCertNumber;
    Date repPersonalCertDate;
    Date repPersonalCertExpireDate;
    String repPersonalCertPlace;
    String businessLicenseNumber;
    String businessLicenseIssuedBy;
    Date businessLicenseIssuedDate;
    String provinceName;
    Long provinceId;
    FileAttach avatar;
    User assignee;
    List<User> lstAssignee;
    Long createdBy;
    String createdByName;
    // affiliate_users
    Long id;
    Long userId;
    String parentAffiliateCode;
    Integer affiliateLevel;
    String affiliateCode;
    String permanentAddress;
    String contactAddress;
    String tradingAddress;
    String bankName;
    String bankNumber;
    String bankBranch;
    String bankBeneficiary;
    AffiliateStatusEnum affiliateStatus;
    String approvalReason;
    String contactName;
    String referralCode;
    Boolean isApproved;
    Integer createdSource;
    String createdSourceLabel;
    // extra
    List<FileAttach> repIdentityFiles;
    List<FileAttach> businessRegistrationFiles;
    Long parentAffiliateId;
    String parentAffiliateName;
    String parentAffiliatePhone;
    String parentAffiliateEmail;
    Integer parentAffiliateLevel;
    Boolean canLoginAffiliate;
    List<AffiliateUsers> childAffiliateIds;
    Set<Role> roles = new HashSet<>();
    PortalType portalType;
    Boolean isLeakPW;
}
