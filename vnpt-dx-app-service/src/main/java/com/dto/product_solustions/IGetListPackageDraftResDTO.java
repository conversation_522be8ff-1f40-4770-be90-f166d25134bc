package com.dto.product_solustions;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import com.util.StringUtil;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public interface IGetListPackageDraftResDTO {
    Long getId();
    String getPackageName();
    String getProviderName();
    Integer getVisibility();
    Integer getState();
    Boolean getRecommended();
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getCreatedAt();
    String getProductIds(); // danh sách id sản phẩm dịch vụ thuộc gói
    String getIconUrl();

    default List<Long> getProductIdsValue(){
        String productIds = getProductIds();
        if(productIds != null){
            return StringUtil.convertStringArrToListLong(productIds);
        }
        return Collections.emptyList();
    }

}
