package com.dto.product_solustions;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;
import com.constant.enums.suggestions.SuggestionModeEnum;
import com.dto.faq.FaqServiceDetailResDTO;
import com.dto.feature.FeatureDetailDTO;
import com.dto.seo.SeoDTO;
import com.dto.suggestions.SuggestionGroupDetailDTO;
import com.enums.ApproveStatusEnum;
import com.enums.DisplayStatus;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SolutionDetailSMEResDTO {
    private Long id;

    @Schema(description = "Tên giải pháp")
    private String name;

    @Schema(description = "Đối tượng khách hàng")
    private List<CustomerTypeEnum> lstCustomerType;

    @Schema(description = "Mã giải pháp")
    private String code;

    @Schema(description = "Trạng thái duyệt")
    private ApproveStatusEnum approveStatus;

    @Schema(description = "Danh sách lĩnh vực áp dụng")
    private List<ValueDTO> lstDomain;

    @Schema(description = "Danh sách ID danh mục sản phẩm")
    private List<ValueDTO> lstCategory;

    @Schema(description = "URL hình ảnh giải pháp")
    private String avatarUrl;

    @Schema(description = "URL video giải pháp")
    private String videoUrls;

    @Schema(description = "Mô tả giới thiệu giải pháp")
    private String descriptions;

    @Schema(description = "Danh sách tính năng của giải pháp")
    private List<FeatureDetailDTO> lstFeature;

    @Schema(description = "Danh sách bố cục của giải pháp")
    private List<ISectionResDTO> lstSection;

    @Schema(description = "Danh sách topic")
    private List<FaqServiceDetailResDTO> lstTopic;

    @Schema(description = "Thông tin SEO")
    private SeoDTO seo;

    @Schema(description = "Cấu hình Multi-subscription")
    private Boolean multisubEnabled;

    @NotNull(message = "Trạng thái hoạt động không được để trống")
    private DisplayStatus visibility; // 0: INVISIBLE, 1: VISIBLE

    @Schema(description = "Chế độ gợi ý sản phẩm")
    private SuggestionModeEnum suggestionMode;

    @Schema(description = "Danh sách sản phẩm gợi ý")
    private Set<SuggestionGroupDetailDTO> suggestedProducts;

    @Schema(description = "Thông tin hỗ trợ")
    private SupportInfo supportInfo;

    @Schema(description = "Điểm đánh giá TB của giải pháp")
    private Double ratingAverage = 0.0;

    @Schema(description = "Số lượng đã bán")
    private Long soldNumber = 0L;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SupportInfo  {
        private Long id;

        @Schema(description = "Tên nhà phát hành")
        private String name;

        @Schema(description = "Email hỗ trợ")
        private String email;

        @Schema(description = "Địa chỉ")
        private String address;
    }


}
