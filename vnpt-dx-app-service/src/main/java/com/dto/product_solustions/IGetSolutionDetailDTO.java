package com.dto.product_solustions;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.dto.product_solustions.SolutionDetailResDTO.SolutionSeoDTO;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.utils.ObjectMapperUtil;

public interface IGetSolutionDetailDTO {
    Long getId();
    String getName();
    String getCode();
    String getDomains();
    String getCategories();
    String getCustomerTypes();
    String getAvatarUrl();
    String getVideoUrl();
    String getDescriptions();
    String getFeatures();
    String getSections();
    String getSeo();
    Integer getApprovalStatus();
    Boolean getMultisubEnabled();
    Integer getVisibility();
    Boolean getFeatureVisible();
    Boolean getIsApproved();

    default List<ValueDTO> getLstDomain() {
        return Objects.nonNull(this.getDomains()) ? ObjectMapperUtil.listMapper(this.getDomains(), ValueDTO.class) : new ArrayList<>();
    }

    default List<ValueDTO> getLstCategory() {
        return Objects.nonNull(this.getCategories()) ? ObjectMapperUtil.listMapper(this.getCategories(), ValueDTO.class) : new ArrayList<>();
    }

    default List<CustomerTypeEnum> getLstCustomerType() {
        return Objects.nonNull(this.getCustomerTypes()) ? ObjectMapperUtil.listMapper(this.getCustomerTypes(), CustomerTypeEnum.class) : new ArrayList<>();
    }

    default List<FeatureDetailDTO> getLstFeature() {
        return Objects.nonNull(this.getFeatures()) ? ObjectMapperUtil.listMapper(this.getFeatures(), FeatureDetailDTO.class) : new ArrayList<>();
    }

    default List<SectionDTO> getLstSection() {
        return Objects.nonNull(this.getSections()) ? ObjectMapperUtil.listMapper(this.getSections(), SectionDTO.class) : new ArrayList<>();
    }

    default List<SolutionSeoDTO> getLstSeo() {
        return Objects.nonNull(this.getSeo()) ? ObjectMapperUtil.listMapper(this.getSeo(), SolutionSeoDTO.class) : new ArrayList<>();
    }
}
