package com.dto.product_solustions;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.data.domain.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SolutionByDomainPageDTO {

    Long domainId;

    @Schema(description = "Tên lĩnh vực")
    String domainName;

    @Schema(description = "Số lượng solution thuộc lĩnh vực")
    Long sizeSolution;

    List<ProductSolutionSmeDTO> lstSolution;

    Page<ProductSolutionSmeDTO> pageSolution;

    // constructor cho list
    public static SolutionByDomainPageDTO fromList(Long domainId, String domainName, List<ProductSolutionSmeDTO> lstSolution) {
        SolutionByDomainPageDTO dto = new SolutionByDomainPageDTO();
        dto.domainId = domainId;
        dto.domainName = domainName;
        dto.lstSolution = lstSolution;
        dto.sizeSolution = lstSolution != null && !lstSolution.isEmpty() ? lstSolution.get(0).getCount() : 0L;
        return dto;
    }

    // constructor cho page
    public static SolutionByDomainPageDTO fromPage(Long domainId, String domainName, Page<ProductSolutionSmeDTO> pageSolution) {
        SolutionByDomainPageDTO dto = new SolutionByDomainPageDTO();
        dto.domainId = domainId;
        dto.domainName = domainName;
        dto.pageSolution = pageSolution;
        dto.sizeSolution = pageSolution != null ? pageSolution.getTotalElements() : 0L;
        return dto;
    }

}
