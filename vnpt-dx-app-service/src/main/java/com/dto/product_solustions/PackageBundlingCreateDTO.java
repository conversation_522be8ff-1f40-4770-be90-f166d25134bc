package com.dto.product_solustions;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.constant.enums.suggestions.SuggestionModeEnum;
import com.dto.feature.FeatureReqDTO;
import com.dto.seo.SeoReqDTO;
import com.dto.services.ServiceTopicUpdateRequest;
import com.dto.subscriptions.McApplyDTO;
import com.enums.DisplayStatus;
import com.enums.PurchaseVersionEnum;
import com.enums.product_solutions.GuidelinesTypeEnum;
import com.enums.product_solutions.SuggestionTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.OptBoolean;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DiscountTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "DTO để tạo mới gói bundling")
public class PackageBundlingCreateDTO {

    @Schema(description = "Đối tượng khách hàng (cá nhân, doanh nghiệp, hộ kinh doanh)")
    private List<CustomerTypeEnum> customerTypes;

    // THÔNG TIN CHUNG
    @Schema(description = "Danh sách ảnh banner của gói với các kích thước khác nhau")
    private Set<String> bannerUrls;

    @Schema(description = "Thông tin icon của gói")
    private String iconUrl;

    @Schema(description = "ID nhà cung cấp phát hành gói dịch vụ")
    private Long providerId;

    @Schema(description = "Tên gói dịch vụ, không được trùng với gói đã có")
    private String name;

    @Schema(description = "Mã gói")
    private String code;

    @Schema(description = "Mô tả chi tiết về gói")
    private String descriptions;

    @Schema(description = "Trạng thái gói")
    private DisplayStatus visibility;

    @Schema(description = "Đánh dấu gói có được đề xuất không")
    private Boolean recommended;

    @Schema(description = "Bật tắt tính năng")
    private Boolean featureVisible = Boolean.TRUE;

    @Schema(description = "Danh sách tính năng của giải pháp")
    private List<FeatureReqDTO> features;

    @Schema(description = "Danh sách ID của các giải pháp mà gói trực thuộc")
    private List<Long> solutionIds;

    @Schema(description = "Thông tin các thành phần trong gói")
    private List<PackagePlanCreateDTO> lstPackageItem = new ArrayList<>();

    @Schema(description = "Loại chiết khấu")
    private DiscountTypeEnum discountType;

    @Schema(description = "Chiết khấu")
    private BigDecimal discountValue;

    @Schema(description = "Điều kiện áp dụng")
    private ApplyConditionDTO applyCondition;

    @Schema(description = "Thuế đã bao gồm chưa")
    private YesNoEnum hasTax;

    @Schema(description = "Phí đã bao gồm chưa")
    private YesNoEnum hasTaxSetupFee;

    @Schema(description = "Thuế")
    private List<PackageTaxDTO> taxes;

    @Schema(description = "Phí")
    private List<SetupFee> setupFees;

    @Schema(description = "Hướng dẫn sử dụng")
    private Guidelines guidelines;

    @Schema(description = "Danh sách topic")
    private List<ServiceTopicUpdateRequest> topics;

    @Schema(description = "Cấu hình SEO")
    private SeoReqDTO seo;

    @Schema(description = "Chế độ gợi ý sản phẩm")
    private SuggestionModeEnum suggestionMode;

    @Schema(description = "Danh sách sản phẩm gợi ý")
    private List<SuggestionDTO> suggestionDTOS;

    @Schema(description = "Phương thức thanh toán được hỗ trợ")
    private PaymentMethodEnum paymentMethod;

    @Schema(description = "Danh sách ID danh mục")
    private Long[] domainIds;

    @Schema(description = "Danh sách ID lĩnh vực")
    private Long[] categoryIds;

    @Schema(description = "Thành phần gói áp dụng tại luồng mua")
    private PurchaseVersionEnum purchaseVersion;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PackagePlanCalculateDTO {
        private Long pricingMultiPlanId;
        private Long quantity;
        private BigDecimal priceUpdate;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApplyConditionDTO {
        private List<CustomerTypeEnum> customerTypes;
        private List<ValueDTO> province; // id tỉnh
        private List<ValueDTO> district; // id huyện
        private List<ValueDTO> ward; // id xã
        private List<ValueDTO> street;// id đường
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CouponCreateDTO {
        @Schema(description = "Trạng thái hiển thị mã khuyến mãi (true: hiển thị, false: ẩn)")
        private Boolean visibility;

        @Schema(description = "Đối tượng khách hàng được áp dụng mã khuyến mãi")
        private Set<CustomerTypeEnum> customerType;

        @Schema(description = "Tên chương trình khuyến mãi")
        private String name;

        @Schema(description = "Mã khuyến mãi")
        private String code;

        @Schema(description = "Hình thức khuyến mại")
        private PromotionTypeEnum promotionType;

        @Schema(description = "Banner desktop")
        private Long desktopBannerId;

        @Schema(description = "Banner mobile")
        private Long mobileBannerId;

        @Schema(description = "Loại chiết khấu")
        private com.onedx.common.constants.enums.coupons.DiscountTypeEnum discountType;

        @Schema(description = "Giá trị chiết khấu (phần trăm hoặc số tiền)")
        private BigDecimal discountValue;

        @Schema(description = "Số tiền tối đa")
        private BigDecimal discountAmount;

        @Schema(description = "Tổng số lượng km")
        private Long maxUsed;

        @Schema(description = "Thời gian bắt đầu áp dụng khuyến mãi")
        @JsonFormat(shape = Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, lenient = OptBoolean.FALSE)
        private LocalDate startDate;

        @Schema(description = "Thời gian kết thúc khuyến mãi")
        @JsonFormat(shape = Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, lenient = OptBoolean.FALSE)
        private LocalDate endDate;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SetupFee {
        private BigDecimal price;
        private String name;
        private PackageTaxDTO tax;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Guidelines {
        private GuidelinesTypeEnum type;
        private List<String> files;
        private List<String> videos;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SuggestionDTO {

        @Schema(description = "Loại đối tượng gợi ý")
        private SuggestionTypeEnum objectTypeEnum;
        @Schema(description = "ID draft của đối tượng gợi ý")
        private Long objectDraftId;
        @Schema(description = "Thông tin metadata của đối tượng gợi ý")
        private Map<String, Long> metadata;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DiscountInfo {
        private Integer type;
        private BigDecimal value;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PackagePlanCreateDTO {
        @Schema(description = "ID chiến lược định giá gói")
        private Long multiPlanId;

        @Schema(description = "ID gói")
        private Long pricingId;

        @Schema(description = "ID biến thể trong trường hợp tạo package với biến thể")
        private Long variantId;

        @Schema(description = "Thông tin addon")
        private List<PackageAddonCreateDTO> packageAddon = new ArrayList<>();

        @Schema(description = "Chiến lược định giá")
        private PricingPlanEnum pricingPlan;

        @Schema(description = "Số lượng gói")
        private Long quantity;

        @Schema(description = "Số lượng từ")
        private Long unitFrom;

        @Schema(description = "Số lượng đến")
        private Long unitTo;

        @Schema(description = "Giá cập nhật")
        private BigDecimal priceUpdate;

        @Schema(description = "Thành tiền sau thuế, phí, khuyến mại")
        private BigDecimal totalAmount;

        @Schema(description = "Tổng tiền của thành phần")
        private BigDecimal total;

        @Schema(description = "CTKM áp dụng")
        private List<Long> couponIds = new ArrayList<>();

        @Schema(description = "CTKM MC áp dụng")
        private List<McApplyDTO> mcCouponList = new ArrayList<>();

        @Schema(description = "CTKM Tao mới")
        private List<CouponCreateDTO> couponCreateDTOList = new ArrayList<>();

        @Schema(description = "Giá trước thuế, sau km")
        private BigDecimal pricePreTax;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class PackageAddonCreateDTO {
            @Schema(description = "ID addon")
            private Long addonId;

            @Schema(description = "Số lượng")
            private Long quantity;

            @Schema(description = "ID chiến lược định giá addon")
            private Long addonPlanId;

            @Schema(description = "Chiến lược định giá")
            private PricingPlanEnum pricingPlan;

            @Schema(description = "Số lượng từ")
            private Long unitFrom;

            @Schema(description = "Số lượng đến")
            private Long unitTo;

            @Schema(description = "Giá cập nhật")
            private BigDecimal priceUpdate;

            @Schema(description = "Thành tiền sau thuế, phí, khuyến mại")
            private BigDecimal totalAmount;

            @Schema(description = "Tổng tiền của addon")
            private BigDecimal total;

            @Schema(description = "CTKM áp dụng")
            private List<Long> couponIds = new ArrayList<>();

            @Schema(description = "CTKM MC áp dụng")
            private List<McApplyDTO> mcCouponList = new ArrayList<>();

            @Schema(description = "CTKM")
            private List<CouponCreateDTO> couponCreateDTOList = new ArrayList<>();

            @Schema(description = "Loại addon")
            private BonusTypeEnum addonType;

            @Schema(description = "Giá trước thuế, sau km")
            private BigDecimal pricePreTax;


        }

    }

}
