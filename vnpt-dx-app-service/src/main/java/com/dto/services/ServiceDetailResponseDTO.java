package com.dto.services;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Convert;
import org.apache.commons.lang3.StringUtils;
import com.common.converter.SetupFeeInfoConvert;
import com.onedx.common.constants.enums.services.ProductClassificationEnum;
import com.dto.categories.CategoryDTODetail;
import com.dto.categories.SeoCategoryDTO;
import com.dto.coupons.ICouponDetailDTO;
import com.dto.faq.FaqServiceDetailResDTO;
import com.dto.feature.FeatureDetailDTO;
import com.dto.marketingCampaign.smePortal.McTagDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.seo.SeoDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> VinhNT
 * @version : 1.0
 * 28/1/2021
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceDetailResponseDTO {

    @Schema(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
    private Long id;

    @Schema(description = SwaggerConstant.Service.URL_ICON, example = SwaggerConstant.Example.FILE_PATH)
    private String icon;

    private List<ServiceDetailFileAttachDTO> iconService;

    @Schema(description = SwaggerConstant.FileAttach.EXTERNAL_LINK, example = SwaggerConstant.Example.IMAGE_URL)
    private String externalLinkIcon;

    @Schema(description = Service.BANNER_URL, example = SwaggerConstant.Example.FILE_PATH)
    private String banner;

    @Schema(description = SwaggerConstant.FileAttach.EXTERNAL_LINK, example = SwaggerConstant.Example.IMAGE_URL)
    private String externalLinkBanner;

    @Schema(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
    private String name;

    @Schema(description = SwaggerConstant.Service.DEV_ID, example = SwaggerConstant.Example.ID)
    private Long developerId;

    @Schema(description = SwaggerConstant.Service.DEV_NAME, example = SwaggerConstant.Example.USER_NAME)
    private String developerName;

    @Schema(description = SwaggerConstant.Service.URL_VIDEO, example = SwaggerConstant.Example.FILE_PATH)
    private String video;

    @Schema(description = SwaggerConstant.FileAttach.EXTERNAL_LINK, example = SwaggerConstant.Example.IMAGE_URL)
    private String externalLinkVideo;

    private List<ServiceDetailFileAttachDTO> snapshots;

    private List<ServiceDetailFileAttachDTO> technologies;

    private Integer techLayout;

    private Integer techVisible;

    private Integer featureVisible;

    private List<FeatureDetailDTO> features;

    private SeoCategoryDTO seoCategoryDTOList;

    @Schema(description = SwaggerConstant.Service.DESCRIPTION, example = SwaggerConstant.Example.DESCRIPTION)
    private String description;

    @Schema(description = SwaggerConstant.Service.SAPO_DESCRIPTION, example = SwaggerConstant.Example.DESCRIPTION)
    private String shortDescription;

    @Schema(description = SwaggerConstant.Service.URL_PRE_ORDER, example = SwaggerConstant.Example.URL)
    private String urlPreOrder;

    @Schema(description = SwaggerConstant.Service.URL, example = SwaggerConstant.Example.URL)
    private String url;

    private Integer urlServiceStatus; // 0- Luôn hiển thị, 1 - Hiển thị khi khách hàng đăng nhập

    @Schema(description = SwaggerConstant.Service.LANGUAGE, example = SwaggerConstant.Example.LANGUAGE)
    private String[] language;

    @Schema(description = SwaggerConstant.Category.NAME, example = SwaggerConstant.Example.CATEGORY_NAME)
    private String category;

    private Long idCategory;

    private String planUrlCategory;

    @Convert(converter = SetupFeeInfoConvert.class)
    private SetupFeeInfoConvertDTO setupFee;

    private String serviceCode;

    private Boolean reaction;

    private String onOsType;

    @Schema(description = SwaggerConstant.Service.EMAIL, example = SwaggerConstant.Example.EMAIL)
    private String email;

    @Schema(description = SwaggerConstant.Service.PHONE, example = SwaggerConstant.Example.PHONE)
    private String phoneNumber;

    @Schema(description = Service.PRODUCT_ID, example = Example.PROVINCE)
    private String provinceId;
    private String provinceName;
    private String wardName;
    private String districtName;
    private String streetName;
    private String address;
    private String provider;

    private String couponDiscount;
    private Long couponDiscountTime;
    private String couponFree;
    private Long couponFreeTime;

    @Schema(description = "Số lượng đã bán")
    private Integer soldNumber;

    private McTagDTO discountInfo;
    private McTagDTO giftInfo;

    private Integer isOneTime;

    private List<ServiceDetailPlanAdminDTO> plans;

    @Schema(description = SwaggerConstant.Service.SERVICE_OWNER, example = SwaggerConstant.Example.SERVICE_OWNER)
    private String serviceOwner;

    @Schema(description = SwaggerConstant.Service.URL_VIDEO, example = SwaggerConstant.Example.FILE_PATH)
    private List<ServiceDetailFileAttachDTO> videoGuide;

    @Schema(description = SwaggerConstant.Service.URL_VIDEO, example = SwaggerConstant.Example.FILE_PATH)
    private List<ServiceDetailFileAttachDTO> docGuide;

    @Schema(description = SwaggerConstant.Service.SERVICE_OWNER, example = SwaggerConstant.Example.SERVICE_OWNER)
    SeoDTO seoDTO;

    List<PricingSaaSResDTO> pricing;

    Map<Long, List<Long>> variantIdToListPricingId;

    ICouponDetailDTO couponFlashSaleDetailDTO; //  Thông tin flash sale

    // private List<ServiceSuggestionResDTO> serviceSuggestions;

    @Schema(description = SwaggerConstant.Service.UPDATE_BASE_INFO_REASON, example = SwaggerConstant.Example.DESCRIPTION)
    private String updateBaseInfoReason;

    @Schema(description = SwaggerConstant.Service.UPDATE_FEATURE_REASON, example = SwaggerConstant.Example.DESCRIPTION)
    private String updateFeatureReason;

    @Schema(description = SwaggerConstant.Service.UPDATE_SNAP_REASON, example = SwaggerConstant.Example.DESCRIPTION)
    private String updateSnapReason;

    @Schema(description = SwaggerConstant.Service.UPDATE_TECH_REASON, example = SwaggerConstant.Example.DESCRIPTION)
    private String updateTechReason;

    @Schema(description = SwaggerConstant.Service.CUSTOMER_TYPE_CODE, example = SwaggerConstant.Example.CUSTOMER_TYPE_NAME)
    private Set<CustomerTypeEnum> customerType;

    private Integer allowMultiSub;

    private Integer numSub; // numSub only current login user register sub in this service

    private Long numOfAllSub; // numSub all user register sub in this service

    private Double avgRating;

    private String userLatestRating;

    private Integer serviceOwnerPartner;

    List<FaqServiceDetailResDTO> serviceTopics;

    private List<Long> permissionIds;
    private List<Long> categoriesApp;
    private Integer serviceTypeApplication;
    Long creationLayoutId;
    List<CustomFieldValueDTO> lstCustomField;

    private Integer suggestionType; //0 - tu dong. 1 - chọn
    private Integer registerEcontract; //1: có; 0: không
    private Integer serviceOwnerVNPT; //0: VNPT; 1: PARTNER; 2: NONE_VNPT

    private ProductClassificationEnum classification;
    private Long serviceDraftId;
    private Integer productType;
    private BigDecimal priceService;
    private List<FeatureDetailDTO> featureList; // Lấy danh sách all tính năng của dịch vụ
    private PaymentMethodEnum paymentMethod;
    private Boolean attributeVariant;
    private String specifications;
    private Boolean isCancel;
    private List<CategoryDTODetail> lstCategory;

    public ServiceDetailResponseDTO(Long id, String icon, String externalLinkIcon, String banner,
            String externalLinkBanner, String name, Long developerId, String developerName, String video,
            String externalLinkVideo, String description, String shortDescription, String urlPreOrder, String url, Integer urlServiceStatus,
            String language, String category, Long idCategory, String email, String phoneNumber, Integer serviceOwner,
            String updateBaseInfoReason, String updateFeatureReason, String updateSnapReason, String updateTechReason, Integer allowMultiSub,
            Integer serviceOwnerPartner, Integer suggestionType, Integer registerEContract, Integer serviceOwnerVNPT,
        Boolean attributeVariant, String specifications, Boolean reaction, String onOsType, Integer serviceTypeApplication,
        ProductClassificationEnum classification) {
        this.id = id;
        this.icon = icon;
        this.externalLinkIcon = externalLinkIcon;
        this.banner = banner;
        this.externalLinkBanner = externalLinkBanner;
        this.name = name;
        this.developerId = developerId;
        this.developerName = developerName;
        this.video = video;
        this.externalLinkVideo = externalLinkVideo;
        this.description = description;
        this.shortDescription = shortDescription;
        this.url = url;
        this.urlServiceStatus = urlServiceStatus;
        this.urlPreOrder = urlPreOrder;
        this.language = StringUtils.stripAll(StringUtils.split(
                StringUtils.defaultIfEmpty(language, CharacterConstant.BLANK), CharacterConstant.COMMA)
        );
        this.category = category;
        this.idCategory = idCategory;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.serviceOwner = Objects.nonNull(serviceOwner) ? ServiceOwnerEnum.valueOf(serviceOwner).name() : null;
        this.updateBaseInfoReason = updateBaseInfoReason;
        this.updateFeatureReason = updateFeatureReason;
        this.updateSnapReason = updateSnapReason;
        this.updateTechReason = updateTechReason;
        this.allowMultiSub = allowMultiSub;
        this.serviceOwnerPartner = serviceOwnerPartner;
        this.suggestionType = suggestionType;
        this.registerEcontract = registerEContract;
        this.serviceOwnerVNPT = serviceOwnerVNPT;
        this.attributeVariant= attributeVariant;
        this.specifications= specifications;
        this.classification = classification;
        this.reaction = reaction;
        this.onOsType = onOsType;
        this.serviceTypeApplication= serviceTypeApplication;
    }

    public ServiceDetailResponseDTO(Long id, String icon, String externalLinkIcon, String banner,
        String externalLinkBanner, String name, Long developerId, String developerName, String video,
        String externalLinkVideo, String description, String shortDescription, String urlPreOrder, String url,
        String language, String category, Long idCategory, String email, String phoneNumber, Integer serviceOwner,
        String updateBaseInfoReason, String updateFeatureReason, String updateSnapReason, String updateTechReason, Integer allowMultiSub,
        Integer numSub, Integer serviceOwnerPartner, Integer suggestionType, Integer registerEContract, Integer serviceOwnerVNPT,Boolean attributeVariant) {
        this.id = id;
        this.icon = icon;
        this.externalLinkIcon = externalLinkIcon;
        this.banner = banner;
        this.externalLinkBanner = externalLinkBanner;
        this.name = name;
        this.developerId = developerId;
        this.developerName = developerName;
        this.video = video;
        this.externalLinkVideo = externalLinkVideo;
        this.description = description;
        this.shortDescription = shortDescription;
        this.url = url;
        this.urlPreOrder = urlPreOrder;
        this.language = StringUtils.stripAll(StringUtils.split(
            StringUtils.defaultIfEmpty(language, CharacterConstant.BLANK), CharacterConstant.COMMA)
        );
        this.category = category;
        this.idCategory = idCategory;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.serviceOwner = Objects.nonNull(serviceOwner) ? ServiceOwnerEnum.valueOf(serviceOwner).name() : null;
        this.updateBaseInfoReason = updateBaseInfoReason;
        this.updateFeatureReason = updateFeatureReason;
        this.updateSnapReason = updateSnapReason;
        this.updateTechReason = updateTechReason;
        this.allowMultiSub = allowMultiSub;
        this.numSub = numSub;
        this.serviceOwnerPartner = serviceOwnerPartner;
        this.suggestionType = suggestionType;
        this.registerEcontract = registerEContract;
        this.serviceOwnerVNPT = serviceOwnerVNPT;
        this.attributeVariant= attributeVariant;
    }
}
