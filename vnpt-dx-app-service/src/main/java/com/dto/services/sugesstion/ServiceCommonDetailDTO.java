package com.dto.services.sugesstion;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.springframework.beans.BeanUtils;
import com.dto.TaxCommonDetailDTO;
import com.dto.feature.FeatureDetailDTO;
import com.dto.pricing.PricingCommonDetailDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.AttributeSPDVDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.VariantSPDVDTO;
import com.dto.product_solustions.MetadataDTO;
import com.dto.services.ServiceSuggestionDTO;
import com.enums.product_solutions.SuggestionTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DiscountTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.values.CharacterConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceCommonDetailDTO {

    @Schema(description = "ID sản phẩm dịch vụ liên quan")
    Long id;

    @Schema(description = "Tên sản phẩm dịch vụ liên quan")
    String name;

    @Schema(description = "Mô tả sản phẩm dịch vụ liên quan")
    String description;

    @Schema(description = "Loại sản phẩm dịch vụ liên quan")
    SuggestionTypeEnum suggestionType;

    @Schema(description = "Url ảnh spdv liên quan")
    String imageUrl;

    @Schema(description = "Số lượng gói")
    Integer numPricing;

    @Schema(description = "Giá gốc")
    BigDecimal price;

    @Schema(description = "Giá chỉ từ")
    BigDecimal priceFrom;

    @Schema(description = "ID draft sản phẩm dịch vụ liên quan")
    Long draftId;

    @Schema(description = "Thông tin nhà cung cấp")
    String providerName;

    @Schema(description = "Tên danh mục SPDV liên quan")
    String categoryName;

    @Schema(description = "Tổng số lượt đã mua mà khách hàng đã đăng ký/ đặt hàng")
    Long numSub;

    @Schema(description = "Số điểm đánh giá trung bình")
    Double avgRating;

    @Schema(description = "Mô tả giới thiệu Sản phẩm")
    String descriptions;

    @Schema(description = "Đối tượng khách hàng")
    Set<CustomerTypeEnum> customerTypes;

    BigDecimal pricePreTax;

    @Schema(description = "Phương thức thanh toán")
    PaymentMethodEnum paymentMethod;

    @Schema(description = "Loại khuyến mại")
    DiscountTypeEnum discountType;

    @Schema(description = "Giá trị khuyến mại")
    BigDecimal discountValue;

    MetadataDTO suggestMetadata;

    @Schema(description = "Danh sách tên tính năng ")
    List<String> features;

    @JsonIgnore
    List<TaxCommonDetailDTO> taxes = new ArrayList<>();

    @Schema(description = "Danh sách gói spdv liên quan")
    List<PricingCommonDetailDTO> lstPricing;

    @Schema(description = "Danh sách các biến thể")
    List<VariantSPDVDTO> lstVariantSPDV;

    @Schema(description = "Danh sách thuộc tính biến thể")
    List<AttributeSPDVDTO> lstAttributeSPDV;

    @Schema(description = "Danh sách các tính năng")
    List<FeatureDetailDTO> lstFeature;

    @Schema(description = "Số lượng thiết bị trong gói giải pháp (các item có biến thể)")
    Long numOfDevice = 0L;

    @Schema(description = "Số lượng sản phẩm dịch vụ dạng SAAS trong gói giải pháp")
    Long numOfService = 0L;
    
    public ServiceCommonDetailDTO(ServiceSuggestionDTO serviceSuggestionDTO) {
        BeanUtils.copyProperties(serviceSuggestionDTO, this);
        this.features = Objects.nonNull(serviceSuggestionDTO.getFeatures()) ?
            Arrays.asList(serviceSuggestionDTO.getFeatures().split(CharacterConstant.SEMICOLON)) : null;
        this.taxes = serviceSuggestionDTO.getTaxes();
    }

    public ServiceCommonDetailDTO(ServiceSuggestionDTO serviceSuggestionDTO, List<PricingCommonDetailDTO> lstPricing) {
        BeanUtils.copyProperties(serviceSuggestionDTO, this);
        this.features = Objects.nonNull(serviceSuggestionDTO.getFeatures()) ?
            Arrays.asList(serviceSuggestionDTO.getFeatures().split(CharacterConstant.SEMICOLON)) : null;
        this.taxes = serviceSuggestionDTO.getTaxes();
        this.lstPricing = lstPricing;
    }

}
