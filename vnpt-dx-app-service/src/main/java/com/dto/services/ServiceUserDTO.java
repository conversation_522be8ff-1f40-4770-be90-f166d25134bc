package com.dto.services;

import com.onedx.common.dto.integration.backend.IntegrationAuthorizationDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> TinhNX
 * @version    : 1.0
 * 4/2/2021
 */
@Data
@Builder
public class ServiceUserDTO {

    @Schema(description = "ID của user")
    private Long id;

    @Schema(description = "Tên user")
    private String name;

    @Schema(description = "Email user")
    private String email;

    @Schema(description = "URL ảnh avatar của user đăng nhập")
    private String avatar;

    @Schema(description = "ID của SME admin")
    private Long smeId;

    @Schema(description = "Tên doanh nghiệp SME")
    private String smeName;

    @Schema(description = "Website doanh nghiệp")
    private String website;

    @Schema(description = "Số điện thoại doanh nghiệp")
    private String phoneNumber;

    @Schema(description = "Địa chỉ doanh nghiệp")
    private String address;

    @Schema(description = "Access token dùng để xác thực các dev APIs")
    private String accessToken;

    private IntegrationAuthorizationDTO authorization;
}
