package com.dto.shoppingCart;

import java.util.Date;
import com.onedx.common.constants.enums.migration.MigrationServiceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;

public interface ISubscriptionsSaaSResDTO {

    String getSubCode();

    String getReferCode();

    String getCustomerName();

    String getCustomerIcon();

    String getCustomerPhone();

    String getCustomerEmail();

    String getIdentityNo();

    String getUserCode();

    String getCustomerType();

    String getServiceName();

    String getServiceId();

    String getFileServiceId();

    String getServiceOwner();

    String getProvider();

    Integer getActiveStatus();

    Boolean getIsCombo();

    String getPaymentCycle();

    MigrationServiceTypeEnum getMigrationServiceTypeEnum();

    String getSimSubCode();

    String getServiceCode();

    Integer getNumCycle();

    Integer getSubStatus();

    String getOrderStatus();

    Integer getQuantity();

    Integer getQuantityVariant();

    Integer getFreeQuantity();

    String getPricingName();

    Long getFileAttachId();

    String getFileName();

    String getFilePath();

    Long getUserId();

    Long serviceId();

    Integer getObjectType();

    Integer getPriority();

    Integer getAccessType();

    Integer getFileSize();

    String getExtLink();

    Long getSubId();

    Double getTotalAmount();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getMigrateTime();

    String getCreateSource();

    String getMigrateCode();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getCreatedAt();

    Long getMigrateId();

    Integer getCurrentCycle();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getNextPaymentTime();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getReactiveDate();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getStartCurrentCycle();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getEndCurrentCycle();

    Integer getNumOfCycleReactive();

    Integer getReactiveStatus();

    Integer getIsOneTime();

    String getAssigneeName();

    Integer getInstallationStatus();

    Double getPriceVariant();

    String getNameVariant();

    Boolean getIsOnlyService();

    String getServiceGroupName();

    String getServiceGroupAvatarUrl();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getEndCurrentCycleContract();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    Date getCancelledTime();

    Integer getHasRenew();

    Integer getActiveDate();

    String getAmName();

    String getAmCode();
}
