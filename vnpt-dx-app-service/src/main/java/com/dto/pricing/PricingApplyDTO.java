package com.dto.pricing;

import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Coupon;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;

/**
 * * Pricing Apply DTO
 *
 * <AUTHOR>
 *  5/21/2021 4:20 PM
 */

public interface PricingApplyDTO {

    @Schema(description = SwaggerConstant.Coupon.ID, example = SwaggerConstant.Example.ID)
    Long getId();

    @Schema(description = SwaggerConstant.Coupon.NAME, example = SwaggerConstant.Example.COUPON_NAME)
    String getName();

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_TYPE, example = SwaggerConstant.Example.COUPON_DISCOUNT_TYPE)
    Integer getDiscountType();

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_VALUE, example = SwaggerConstant.Example.NUMBER)
    BigDecimal getDiscountValue();

    @Schema(description = SwaggerConstant.Coupon.ENTERPRISE_TYPE, example = SwaggerConstant.Example.YES_NO)
    Integer getEnterpriseType();

    @Schema(description = SwaggerConstant.Coupon.PRICING_TYPE, example = SwaggerConstant.Example.YES_NO)
    Integer getPricingType();

    @Schema(description = SwaggerConstant.Coupon.ADDONS_TYPE, example = SwaggerConstant.Example.YES_NO)
    Integer getAddonsType();

    @Schema(description = SwaggerConstant.Coupon.TOTAL_BILL_TYPE, example = SwaggerConstant.Example.YES_NO)
    Integer getTotalBillType();

    @Schema(description = Coupon.PROMOTION_TYPE, example = Example.PROMOTION_TYPE)
    Integer getPromotionType();

    @Schema(description = Coupon.START_DATE, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    Date getStartDate();

    @Schema(description = Coupon.END_DATE, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    Date getEndDate();

    Date getCreatedAt();

    Integer getVisibleStatus();
    String getCouponCustomerTypeCode();
}
