package com.dto.pricing;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.dto.combo.detail.IComboPlanDetailDTO;
import com.dto.pricing.multiplePeriod.PricingMultiPlanDetailDTO;
import com.dto.product_solustions.IGetPackItemDTO;
import com.dto.product_solustions.PackageListResDTO.ProductItemDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PricingCommonDetailDTO {

    @Schema(description = "Id gói")
    Long id;
    @Schema(description = "Id draft gói")
    Long draftId;
    @Schema(description = "Tên gói")
    String name;
    @Schema(description = "Chu kỳ gói")
    Long paymentCycle;
    @Schema(description = "Loại chu kỳ gói(-1: Không giới hạn,0:ngày,1:tuần,2:tháng, 3:năm,4:qúy)")
    Integer cycleType;
    @Schema(description = "Giá gốc")
    BigDecimal price;
    @Schema(description = "Giá chỉ từ")
    BigDecimal priceFrom;
    @Schema(description = "Danh sách chu kỳ của gói")
    List<PricingMultiPlanDetailDTO> lstMultiPlan = new ArrayList<>();
    @Schema(description = "Gói 1 lần hay định kỳ")
    Integer isOneTime;
    @Schema(description = "Gói mặc định hay không")
    Boolean isDefault;
    @Schema(description = "Url ảnh gói")
    String imageUrl;
    @Schema(description = "Số lượng tối thiểu")
    Long minQuantity;
    @Schema(description = "Số lượng tối đa")
    Long maxQuantity;
    @Schema(description = "Tên nhà cung cấp")
    String providerName;

    // Mapping từ các gói trong bundling
    public PricingCommonDetailDTO(ProductItemDTO productItemDTO) {
        this.id = productItemDTO.getId();
        this.name = productItemDTO.getPricingName();
        this.paymentCycle = productItemDTO.getPaymentCycle();
        this.cycleType = productItemDTO.getCycleType();
        this.price = productItemDTO.getOriginPrice();
        this.priceFrom = productItemDTO.getOriginPrice();
        this.providerName = productItemDTO.getProviderName();
    }

    // Map từ các gói bundling
    public PricingCommonDetailDTO(IGetPackItemDTO packageDetail) {
        this.id = packageDetail.getId();
        this.draftId = packageDetail.getDraftId();
        this.name = packageDetail.getPackageName();
        this.price = packageDetail.getPrice();
        this.providerName = packageDetail.getProviderName();
    }

    // Mapping từ gói cước trong dịch vụ
    public PricingCommonDetailDTO(IPricingCommonDetailDTO pricingDetail) {
        this.id = pricingDetail.getId();
        this.draftId = pricingDetail.getDraftId();
        this.name = pricingDetail.getName();
        this.paymentCycle = Objects.nonNull(pricingDetail.getPaymentCycle()) ? pricingDetail.getPaymentCycle() : null;
        this.cycleType = pricingDetail.getCycleType();
        this.price = pricingDetail.getPrice();
        this.priceFrom = pricingDetail.getPrice();
        this.lstMultiPlan = pricingDetail.getLstMultiPlan();
    }

    // Mapping từ gói trong comboPlan
    public PricingCommonDetailDTO(IComboPlanDetailDTO comboPlanDetail) {
        this.id = comboPlanDetail.getId();
        this.draftId = comboPlanDetail.getDraftId();
        this.name = comboPlanDetail.getName();
        this.paymentCycle = comboPlanDetail.getPaymentCycle();
        this.cycleType = comboPlanDetail.getCycleType();
        this.isOneTime = comboPlanDetail.getIsOneTime();
        this.price = comboPlanDetail.getPrice();
        this.isDefault = comboPlanDetail.getIsDefault();
    }


}
