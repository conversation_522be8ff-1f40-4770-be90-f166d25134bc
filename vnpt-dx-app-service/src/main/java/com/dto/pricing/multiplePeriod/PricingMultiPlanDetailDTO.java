package com.dto.pricing.multiplePeriod;

import java.math.BigDecimal;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PricingMultiPlanDetailDTO {

    @Schema(description = "Id chu kỳ thanh toán")
    Long pricingMultiPlanId;
    @Schema(description = "Chu kỳ thanh toán")
    Long paymentCycle;
    @Schema(description = "Loại chu kỳ thanh toán")
    Integer cycleType;
    @Schema(description = "<PERSON><PERSON><PERSON> gói cước ứng với chu kỳ thanh toán")
    BigDecimal price;
    @Schema(description = "Giá chỉ từ")
    BigDecimal priceFrom;
    @Schema(description = "Số lượng tối thiểu")
    Long minQuantity;
    @Schema(description = "Số lượng tối đa")
    Long maxQuantity;
    @Schema(description = "Chu kỳ mặc định hay không")
    Boolean isDefaultPMP;

    public PricingMultiPlanDetailDTO(PricingMultiplePeriodResDTO planDetail) {
        this.pricingMultiPlanId = planDetail.getIdPricingPeriod();
        this.paymentCycle = planDetail.getPaymentCycle();
        this.cycleType = Objects.nonNull(planDetail.getCycleType()) ? planDetail.getCycleType().value : null;
        this.price = planDetail.getPrice();
        this.minQuantity = planDetail.getMinimumQuantity();
        this.maxQuantity = planDetail.getMaximumQuantity();
    }
}
