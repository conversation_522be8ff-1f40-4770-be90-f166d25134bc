package com.dto.pricing;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Range;
import com.constant.PricingConst;
import com.constant.enums.coupon.ApproveTypeEnum;
import com.constant.enums.pricing.PricingCancelTimeActiveEnum;
import com.constant.enums.pricing.PricingDurationTypeEnum;
import com.dto.file.attach.FileAttachDTO;
import com.dto.seo.SeoDTO;
import com.enums.DisplayStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.onedx.common.constants.enums.BigDecimalSerializer;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeContinueEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeQuantityEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * * Pricing Detail Res DTO
 *
 * <AUTHOR> 5/14/2021 4:30 PM
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PricingDetailResDTO {

    @Schema(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
    private Long id;

    @Schema(description = SwaggerConstant.Pricing.NAME, example = SwaggerConstant.Example.NUMBER)
    private String pricingName;

    @Schema(description = SwaggerConstant.Pricing.CODE, example = SwaggerConstant.Example.ADDON_CODE)
    private String pricingCode;

    @Schema(description = SwaggerConstant.Pricing.HAS_APPROVE, example = SwaggerConstant.Example.YES_NO)
    private String hasApproved;

    @Schema(description = SwaggerConstant.Pricing.APPROVED, example = SwaggerConstant.Example.APPROVED)
    private ApproveTypeEnum approveStatus;

    @Schema(description = SwaggerConstant.Pricing.DESCRIPTION, example = SwaggerConstant.Example.DESCRIPTION)
    private String description;

    @Schema(description = SwaggerConstant.Pricing.SETUP_FEE, example = SwaggerConstant.Example.PRICE)
    @Range(max = PricingConst.SETUP_FEE_MAX, message = MessageKeyConstant.Validation.RANGE)
    private List<PricingReqDTO.SetupFee> setupFees;

    @Schema(description = SwaggerConstant.Pricing.CAUSE, example = SwaggerConstant.Example.CONTENT)
    private String updateReason;

    @Schema(description = SwaggerConstant.Currency.ID, example = SwaggerConstant.Example.ID)
    private Long currencyId;

    @Schema(description = SwaggerConstant.Currency.TYPE, example = SwaggerConstant.Example.CURRENCY)
    private String currencyName;

    List<PricingStrategy> pricingStrategies;

    private List<AddOn> addonList;

    @Schema(description = SwaggerConstant.Pricing.PRICING_TYPE, example = SwaggerConstant.Example.PRICING_TYPE_PREPAY)
    private PricingTypeEnum pricingType;

    @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal price;

    // 09/06/2021 thay đổi hasTax áp dụng cho tất cả các loại thuế. thay vì áp dụng cho từng loại thuế riêng
    @Schema(description = SwaggerConstant.Tax.HAS_TAX, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasTax;

    @Schema(description = SwaggerConstant.Pricing.HAS_CHANGE_PRICE, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasChangePrice;

    @Schema(description = SwaggerConstant.Pricing.HAS_CHANGE_QUANTITY, example = SwaggerConstant.Example.NONE)
    private ChangeQuantityEnum hasChangeQuantity;

    @Schema(description = SwaggerConstant.Pricing.HAS_REFUND, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasRefund;

    @Schema(description = Pricing.HAS_RENEW, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasRenew;

    @Schema(description = SwaggerConstant.Pricing.CANCEL_DATE, example = SwaggerConstant.Example.NOW)
    private PricingCancelTimeActiveEnum cancelDate;

    @Schema(description = SwaggerConstant.Pricing.ACTIVE_DATE, example = SwaggerConstant.Example.NUMBER)
    private Integer activeDate;

    @Schema(description = SwaggerConstant.Pricing.UPDATE_PRICING_DATE, example = SwaggerConstant.Example.NOW)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private PricingCancelTimeActiveEnum updateSubscriptionDate;

    @Schema(description = SwaggerConstant.Pricing.CHANGE_PRICING_DATE, example = SwaggerConstant.Example.NOW)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private PricingCancelTimeActiveEnum changePricingDate;

    @Schema(description = SwaggerConstant.Pricing.FREE_QUANTITY, example = SwaggerConstant.Example.NUMBER)
    private Integer freeQuantity;

    @Schema(description = SwaggerConstant.Pricing.DURATION_TYPE, example = SwaggerConstant.Example.PERIOD_TYPE)
    private PricingDurationTypeEnum durationType;

    private List<Tax> taxList;

    private List<Tax> setupFeeTaxList;

    private YesNoEnum hasTaxSetupFee;

    @Schema(description = SwaggerConstant.Pricing.FEATURES, example = SwaggerConstant.Example.LIST_FEATURE)
    private List<Feature> featureList;

    @JsonIgnore
    private String listFeatureId;

    @Schema(description = Pricing.CREATED_BY, example = Example.ID)
    private Long createdBy;

    private List<SeoDTO> seoList;

    @Schema(description = Pricing.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
    private Set<CustomerTypeEnum> customerTypeCode;

    private Integer isOneTime;

    private YesNoEnum paymentRequest;

    private ChangeContinueEnum typeActiveInPaymentType;

    private Long creationLayoutId;

    private List<CustomFieldValueDTO> lstCustomFields;

    private Integer changePricingPaymentTime = 0;

    private FileAttachDTO pricingImage;

    // thông tin thêm cho gói của hàng hóa vật lý
    private PricingConfigResDTO pricingConfig;

    private PricingCommitmentTimeDTO pricingCommitmentTime;

    private PricingPromotionResDTO pricingPromotion;

    @Data
    public static class Feature {

        @Schema(description = SwaggerConstant.Feature.ID, example = SwaggerConstant.Example.ID)
        private Long id;

        @Schema(description = SwaggerConstant.Feature.NAME, example = SwaggerConstant.Example.NAME_FEATURE)
        private String name;

        private String icon;

        private String FilePath;

        @Schema(description = "Loại tính năng  0: Không hiển thị trên trang tổng quan, 1: có hiển thị trên trang tổng quan")
        private Integer type;

        private String description;
    }

    @Data
    @NoArgsConstructor
    public static class AddOn {

        @Schema(description = SwaggerConstant.Addon.ID, example = SwaggerConstant.Example.ID)
        private Long id;

        @Schema(description = SwaggerConstant.Addon.NAME, example = SwaggerConstant.Example.ADDON_NAME)
        private String name;

        @Schema(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
        private String serviceName;

        @Schema(description = SwaggerConstant.Addon.CODE, example = SwaggerConstant.Example.FEATURE_CODE)
        private String code;

        @Schema(description = SwaggerConstant.Addon.DISPLAYED, example = SwaggerConstant.Example.DISPLAY)
        private DisplayStatus displayed;

        @Schema(description = SwaggerConstant.Addon.BONUS_VALUE, example = SwaggerConstant.Example.NUMBER)
        @JsonProperty("paymentCycle")
        private Integer bonusValue;

        @Schema(description = SwaggerConstant.Addon.TYPE, example = SwaggerConstant.Example.ADDON_TYPE)
        private CycleTypeEnum type;

        @Schema(description = SwaggerConstant.Addon.BONUS_TYPE, example = SwaggerConstant.Example.ADDON_BONUS_TYPE)
        private BonusTypeEnum bonusType;

        @Schema(description = SwaggerConstant.Addon.IS_REQUIRED, example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum isRequired;

        private Long pricingMultiPlanId;

        public AddOn(Long id, String name, String serviceName, String code, Integer displayed, Integer bonusValue, Integer type,
            Integer bonusType, Integer isRequired, Long pricingMultiPlanId) {
            this.id = id;
            this.name = name;
            this.serviceName = serviceName;
            this.code = code;
            this.displayed = Objects.nonNull(displayed) ? DisplayStatus.valueOf(displayed) : null;
            this.bonusValue = bonusValue;
            this.type = Objects.nonNull(type) ? CycleTypeEnum.valueOf(type) : null;
            this.bonusType = Objects.nonNull(bonusType) ? BonusTypeEnum.valueOf(bonusType) : null;
            this.isRequired = Objects.nonNull(isRequired) ? YesNoEnum.valueOf(isRequired) : null;
            this.pricingMultiPlanId = pricingMultiPlanId;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Tax {

        @Schema(description = SwaggerConstant.Tax.ID, example = SwaggerConstant.Example.ID)
        private Long taxId;

        @Schema(description = SwaggerConstant.Tax.NAME, example = SwaggerConstant.Example.TAX_NAME)
        private String taxName;

        @Schema(description = SwaggerConstant.Tax.PERCENT, example = SwaggerConstant.Example.NUMBER)
        private Double percent;

        @Schema(description = SwaggerConstant.Tax.PERCENT, example = SwaggerConstant.Example.NUMBER)
        private YesNoEnum hasTax;
    }

    @Data
    public static class UnitLimited implements Serializable {

        @Schema(description = SwaggerConstant.UnitLimited.FROM, example = SwaggerConstant.Example.NUMBER)
        private Long unitFrom;

        @Schema(description = SwaggerConstant.UnitLimited.TO, example = SwaggerConstant.Example.NUMBER)
        private Long unitTo;

        @Schema(description = SwaggerConstant.UnitLimited.PRICE, example = SwaggerConstant.Example.PRICE)
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal price;
    }

    @Data
    public static class PricingStrategy {

        @Schema(description = SwaggerConstant.Pricing.PLAN_ID, example = SwaggerConstant.Example.ID)
        private Long id;

        @Schema(description = SwaggerConstant.Pricing.PLAN_NAME, example = SwaggerConstant.Example.PRICING_NAME)
        private String planName;

        @Schema(description = SwaggerConstant.Pricing.PAYMENT_CYCLE, example = SwaggerConstant.Example.SIZE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        @Range(max = PricingConst.PAYMENT_CYCLE_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Long paymentCycle;

        @Schema(description = SwaggerConstant.Pricing.CYCLE_TYPE, example = SwaggerConstant.Example.CYCLE_TYPE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private CycleTypeEnum cycleType;

        @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_CYCLES, example = SwaggerConstant.Example.SIZE)
        @Range(min = -1, max = PricingConst.NUMBER_OF_CYCLES, message = MessageKeyConstant.Validation.RANGE)
        private Integer numberOfCycles;

        @Schema(description = SwaggerConstant.Pricing.PRICING_PLAN, example = SwaggerConstant.Example.PRICING_PLAN)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private PricingPlanEnum pricingPlan;

        @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_TRIAL, example = SwaggerConstant.Example.SIZE)
        @Range(max = PricingConst.NUMBER_OF_TRIAL_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Integer numberOfTrial;

        @Schema(description = SwaggerConstant.Pricing.TRIAL_TYPE, example = SwaggerConstant.Example.CYCLE_TYPE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private TimeTypeEnum trialType;

        @Schema(description = SwaggerConstant.Unit.ID, example = SwaggerConstant.Example.ID)
        private Long unitId;

        @Schema(description = SwaggerConstant.Unit.NAME, example = SwaggerConstant.Example.ID)
        private String unitName;

        @Schema(description = SwaggerConstant.Pricing.FREE_QUANTITY, example = SwaggerConstant.Example.QUANTITY)
        @Range(max = PricingConst.FREE_QUANTITY_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Long freeQuantity;

        @Schema(description = SwaggerConstant.Pricing.DEFAULT_CIRCLE, example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum defaultCircle;

        @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE)
        private BigDecimal price;

        private String cycleCode;

        private List<@Valid UnitLimited> unitLimitedList;

        private List<@Valid AddOn> addonList;

        private Set<CustomerTypeEnum> customerTypeCode;

        private Integer displayStatus;
        private Long referenceId;
        private Long pricingId;

//        private Long pricingId;

        private ApproveTypeEnum approveStatus;
        private Integer priority;
        private Set<Long> exchangedPricingStrategy;
        private Timestamp createdAt;

        private Long minimumQuantity;

        private Long maximumQuantity;
    }

}
