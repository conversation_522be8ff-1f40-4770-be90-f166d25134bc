package com.dto.pricing;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.dto.services.VariantApplyDTO;
import com.onedx.common.constants.enums.*;
import com.onedx.common.constants.enums.migration.RepeatPeriodicEnum;
import org.hibernate.validator.constraints.Range;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.constant.PricingConst;
import com.constant.ServicesConstant.SubDescription;
import com.constant.enums.pricing.PricingCancelTimeActiveEnum;
import com.constant.enums.pricing.PricingDurationTypeEnum;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.dto.seo.SeoReqDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.onedx.common.annotation.TrimString;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeContinueEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeQuantityEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Seo;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@TrimString(fieldNames = {"pricingName", "pricingCode", "description", "updateReason"})
public class PricingReqDTO {

    @Schema(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
    private Long id;

    @Schema(description = SwaggerConstant.Pricing.NAME, example = SwaggerConstant.Example.PRICING_NAME)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    @Size(max = 200, message = MessageKeyConstant.Validation.SIZE)
    private String pricingName;

    @Schema(description = SwaggerConstant.Pricing.CODE, example = SwaggerConstant.Example.PRICING_CODE)
    private String pricingCode;

    @Schema(description = SwaggerConstant.Pricing.DESCRIPTION, example = SwaggerConstant.Example.DESCRIPTION)
    @Size(max = SubDescription.MAX_LENGTH_SORT_DESCRIPTION, message = MessageKeyConstant.Validation.SIZE)
    private String description;

    @Schema(description = SwaggerConstant.Currency.ID, example = SwaggerConstant.Example.ID)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private Long currencyId;

    //1: gói cước 1 lần
    //0: gói bình thường
    private Integer isOneTime = 0;

    List<PricingStrategy> pricingStrategies;

    private LinkedHashSet<@Valid RowAddon> addonList = new LinkedHashSet<>();

    @Schema(description = SwaggerConstant.Pricing.PRICING_TYPE, example = SwaggerConstant.Example.PRICING_TYPE_PREPAY)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private PricingTypeEnum pricingType;

    @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE)
    @Range(max = PricingConst.PRICE_MAX, message = MessageKeyConstant.Validation.RANGE)
    private BigDecimal price;

    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    @Schema(description = SwaggerConstant.Tax.HAS_TAX, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasTax = YesNoEnum.NO;

    @Schema(description = SwaggerConstant.Pricing.HAS_CHANGE_PRICE, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasChangePrice;

    @Schema(description = SwaggerConstant.Pricing.HAS_REFUND, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasRefund;

    @Schema(description = Pricing.HAS_RENEW, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasRenew;

    @Schema(description = SwaggerConstant.Pricing.CANCEL_DATE, example = SwaggerConstant.Example.NOW)
    private PricingCancelTimeActiveEnum cancelDate;

    @Schema(description = SwaggerConstant.Pricing.ACTIVE_DATE, example = SwaggerConstant.Example.NUMBER)
    private Integer activeDate;

    @Schema(description = SwaggerConstant.Pricing.UPDATE_PRICING_DATE, example = SwaggerConstant.Example.NOW)
    private PricingCancelTimeActiveEnum updateSubscriptionDate;

    @Schema(description = SwaggerConstant.Pricing.CHANGE_PRICING_DATE, example = SwaggerConstant.Example.NOW)
    private PricingCancelTimeActiveEnum changePricingDate;

    @Schema(description = SwaggerConstant.Pricing.SETUP_FEE, example = SwaggerConstant.Example.PRICE)
    private List<@Valid SetupFee> setupFees = new ArrayList<>();

    @Schema(description = SwaggerConstant.Pricing.CAUSE, example = SwaggerConstant.Example.CAUSE)
    @Size(max = PricingConst.UPDATE_REASON, message = MessageKeyConstant.Validation.SIZE)
    private String updateReason;

    @Schema(description = Seo.ID, example = Example.ID)
    private Long seoId;

    private LinkedHashSet<@Valid RowTax> taxList = new LinkedHashSet<>();

    @Schema(description = SwaggerConstant.Pricing.HAS_TAX_SETUP_FEE, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasTaxSetupFee;

    @Schema(description = SwaggerConstant.Pricing.FEATURES, example = SwaggerConstant.Example.LIST_FEATURE)
    private LinkedHashSet<@Valid @NotNull(message = MessageKeyConstant.Validation.NOT_NULL) Long> featureList;

    @Schema(description = SwaggerConstant.Pricing.HAS_CHANGE_QUANTITY, example = SwaggerConstant.Example.NONE)
    private ChangeQuantityEnum hasChangeQuantity;

    @Schema(description = SwaggerConstant.Pricing.DURATION_TYPE, example = SwaggerConstant.Example.PERIOD_TYPE)
    private PricingDurationTypeEnum durationType;

    @Schema(description = SwaggerConstant.Pricing.DURATION_TYPE, example = SwaggerConstant.Example.PERIOD_TYPE)
    private SeoReqDTO seoReqDTO;

    @Schema(description = Service.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_NAME)
    private Set<CustomerTypeEnum> customerTypeCode;

    @JsonIgnore
    private Long serviceId;

    private YesNoEnum paymentRequest;

    private ChangeContinueEnum typeActiveInPaymentType;

    private Integer changePricingPaymentTime = 0;

    private Integer recommendedStatus;

    private VariantApplyDTO variantApply;

    private PricingConfigDTO pricingConfigDTO;

    private PricingCommitmentTimeDTO pricingCommitmentTimeDTO;

    private PricingPromotionDTO pricingPromotionDTO;

    private PortalType portalType;

    private Integer priority;

    @Setter
    @Getter
    public static class RowLimited {

        @Schema(description = SwaggerConstant.UnitLimited.FROM, example = SwaggerConstant.Example.RANGE_FROM_TO)
        private Long unitFrom;

        @Schema(description = SwaggerConstant.UnitLimited.TO, example = SwaggerConstant.Example.RANGE_FROM_TO)
        private Long unitTo;

        @Schema(description = SwaggerConstant.UnitLimited.PRICE, example = SwaggerConstant.Example.PRICE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        @Range(max = PricingConst.UNIT_LIMITED_MAX, message = MessageKeyConstant.Validation.RANGE)
        private BigDecimal price;
    }

    @Setter
    @Getter
    public static class SetupFee {
        @Schema(description = SwaggerConstant.Tax.ID, example = SwaggerConstant.Example.NAME_TAX)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private Long currencyId;

        @Schema(description = SwaggerConstant.Tax.NAME, example = SwaggerConstant.Example.NAME_TAX)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        @Size(max = 200, message = MessageKeyConstant.Validation.SIZE)
        private String name;

        @Schema(description = SwaggerConstant.Tax.PRICE, example = SwaggerConstant.Example.PRICE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        @Range(max = PricingConst.UNIT_LIMITED_MAX, message = MessageKeyConstant.Validation.RANGE)
        private BigDecimal price;

        @Schema(description = SwaggerConstant.Tax.PERCENT, example = SwaggerConstant.Example.SIZE)
        private Double percent;

        @Schema(description = SwaggerConstant.Tax.HAS_TAX, example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum hasTax;

        private RowTax tax;
    }
    @Setter
    @Getter
    public static class RowTax {

        @Schema(description = SwaggerConstant.Tax.ID, example = SwaggerConstant.Example.NAME_TAX)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private Long taxId;

        @Schema(description = SwaggerConstant.Tax.PERCENT, example = SwaggerConstant.Example.SIZE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        @Range(max = PricingConst.TAX_PERCENT_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Double percent;

        @Schema(description = SwaggerConstant.Tax.HAS_TAX, example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum hasTax;
    }

    @Setter
    @Getter
    public static class RowAddon {

        @Schema(description = SwaggerConstant.Addon.ID, example = SwaggerConstant.Example.ID)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private Long id;

        @Schema(description = SwaggerConstant.Addon.IS_REQUIRED, example = SwaggerConstant.Example.YES_NO)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private YesNoEnum isRequired;

        Long pricingMultiPlanId;
    }

    @Setter
    @Getter
    public static class PricingStrategy {

        @Schema(description = SwaggerConstant.Pricing.PLAN_ID, example = SwaggerConstant.Example.ID)
        private Long id;

        @Schema(description = SwaggerConstant.Pricing.PLAN_NAME, example = SwaggerConstant.Example.PRICING_NAME)
        private String planName;

        @Schema(description = SwaggerConstant.Pricing.PAYMENT_CYCLE, example = SwaggerConstant.Example.SIZE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        @Range(max = PricingConst.PAYMENT_CYCLE_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Long paymentCycle;

        @Schema(description = SwaggerConstant.Pricing.CYCLE_TYPE, example = SwaggerConstant.Example.CYCLE_TYPE)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private CycleTypeEnum cycleType;

        @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_CYCLES, example = SwaggerConstant.Example.SIZE)
        @Range(min = -1, max = PricingConst.NUMBER_OF_CYCLES, message = MessageKeyConstant.Validation.RANGE)
        private Integer numberOfCycles;

        @Schema(description = SwaggerConstant.Pricing.PRICING_PLAN, example = SwaggerConstant.Example.PRICING_PLAN)
        @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
        private PricingPlanEnum pricingPlan;

        @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_TRIAL, example = SwaggerConstant.Example.SIZE)
        @Range(max = PricingConst.NUMBER_OF_TRIAL_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Integer numberOfTrial;

        @Schema(description = SwaggerConstant.Pricing.TRIAL_TYPE, example = SwaggerConstant.Example.CYCLE_TYPE)
        private TimeTypeEnum trialType;

        @Schema(description = SwaggerConstant.Unit.ID, example = SwaggerConstant.Example.ID)
        private Long unitId;

        @Schema(description = SwaggerConstant.Pricing.FREE_QUANTITY, example = SwaggerConstant.Example.QUANTITY)
        @Range(max = PricingConst.FREE_QUANTITY_MAX, message = MessageKeyConstant.Validation.RANGE)
        private Long freeQuantity;

        @Schema(description = SwaggerConstant.Pricing.DEFAULT_CIRCLE, example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum defaultCircle;

        @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE)
        private BigDecimal price;

        private String cycleCode;

        private Set<@Valid RowLimited> unitLimitedList;

        private LinkedHashSet<@Valid RowAddon> addonList = new LinkedHashSet<>();

        @Schema(description = Service.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_NAME)
        private Set<CustomerTypeEnum> customerTypeCode;

        @Schema(description = SwaggerConstant.Pricing.STATUS, example = SwaggerConstant.Example.YES_NO)
        private Integer displayStatus;

        private Long minimumQuantity;

        private Long maximumQuantity;
    }

    private Long creationLayoutId;
    private List<CustomFieldValueDTO> lstCustomField = new ArrayList<>();
    private String type;
    private Long pricingImageId; // ID ảnh gói cước
}
