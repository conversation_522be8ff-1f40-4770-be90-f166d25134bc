package com.dto.pricing;

import com.dto.enterprise.ComboboxDistrictResponseDTO;
import com.dto.enterprise.ComboboxResponseDTO;
import com.dto.enterprise.ComboboxStreetDTO;
import com.dto.enterprise.ComboboxWardResponseDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PricingConfigResDTO {
    private ComboboxResponseDTO province; // thông tin tỉnh
    private ComboboxDistrictResponseDTO district; // thông huyện
    private ComboboxWardResponseDTO ward; // thông tin xã
    private ComboboxStreetDTO street;// thông tin đường/phố

}
