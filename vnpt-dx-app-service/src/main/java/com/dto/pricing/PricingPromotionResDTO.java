package com.dto.pricing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import com.constant.enums.product_variant.ExtraPriceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PricingPromotionResDTO {

    @Schema(description = "Trạng thái bật tắt khuyến mại")
    private YesNoEnum enabled;

    @Schema(description = "Bật tắt áp dụng chung quy tắc với biến thể")
    private YesNoEnum sameVariant;

    @Schema(description = "Giá trị chiết khấu")
    private BigDecimal discountValue;

    @Schema(description = "Loại chiết khấu (Số tiền/%)")
    private ExtraPriceTypeEnum discountType;

    @Schema(description = "Thời gian bắt đầu áp dụng")
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date discountFrom;

    @Schema(description = "Thời gian kết thúc")
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date discountTo;

    @Schema(description = "Tổng chiết khấu theo số tiền")
    private BigDecimal discountAmount;

    public PricingPromotionResDTO(PricingPromotionDTO savedDTO) {
        this.enabled = Objects.nonNull(savedDTO) ? YesNoEnum.YES : YesNoEnum.NO;
        if (this.enabled == YesNoEnum.YES) {
            BeanUtils.copyProperties(savedDTO, this);
        }
    }

}
