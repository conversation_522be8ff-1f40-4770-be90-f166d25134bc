package com.dto.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Metadata cho các event liên quan đến Coupon
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CouponEventMetadata {
    
    /**
     * ID của coupon
     */
    private Long couponId;
    
    /**
     * ID của coupon draft (chỉ có trong COUPON_UPGRADED)
     */
    private Long couponDraftId;
    
    /**
     * Trạng thái (chỉ có trong một số event)
     */
    private String status;
    
    // Constructor cho COUPON_EXPIRED và COUPON_APPLY_EXCEED
    public CouponEventMetadata(Long couponId) {
        this.couponId = couponId;
    }
    
    // Constructor cho COUPON_UPGRADED
    public CouponEventMetadata(Long couponId, Long couponDraftId) {
        this.couponId = couponId;
        this.couponDraftId = couponDraftId;
    }
}
