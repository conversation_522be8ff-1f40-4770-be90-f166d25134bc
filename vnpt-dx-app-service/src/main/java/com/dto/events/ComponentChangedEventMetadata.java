package com.dto.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Metadata cho event khi component thay đổi
 * Đơn giản chỉ chứa ID và type để gửi cho package service xử lý
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComponentChangedEventMetadata {
    
    /**
     * ID của component bị thay đổi (coupon, addon, pricing, variant)
     */
    private Long componentId;
    
    /**
     * Loại component thay đổi: COUPON, ADDON, PRICING, VARIANT
     */
    private String type;
    
    /**
     * <PERSON><PERSON> tả thay đổi (optional)
     */
    private String description;
    
    /**
     * Thời gian thay đổi
     */
    private Long timestamp;
    
    public ComponentChangedEventMetadata(Long componentId, String type) {
        this.componentId = componentId;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
        this.description = String.format("Component %s với ID %d đã thay đổi", type, componentId);
    }
}
