package com.dto.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Metadata cho các event liên quan đến Product (Pricing, Variant, Addon)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductEventMetadata {
    
    /**
     * ID của pricing (cho PRICING events)
     */
    private Long pricingId;
    
    /**
     * ID của pricing draft (cho PRICING events)
     */
    private Long pricingDraftId;
    
    /**
     * ID của variant (cho VARIANT events)
     */
    private Long variantId;
    
    /**
     * ID của variant draft (cho VARIANT events)
     */
    private Long variantDraftId;
    
    /**
     * ID của addon (cho ADDON events)
     */
    private Long addonId;
    
    /**
     * ID của addon draft (cho ADDON events)
     */
    private Long addonDraftId;
    
    /**
     * Trạng thái (cho STATUS_CHANGED events)
     */
    private String status;
    
    // Constructor cho PRICING events
    public ProductEventMetadata(Long pricingId, Long pricingDraftId, String status) {
        this.pricingId = pricingId;
        this.pricingDraftId = pricingDraftId;
        this.status = status;
    }
    
    // Constructor cho VARIANT events với status
    public static ProductEventMetadata forVariantStatus(Long pricingId, Long pricingDraftId, String status) {
        ProductEventMetadata metadata = new ProductEventMetadata();
        metadata.setPricingId(pricingId);
        metadata.setPricingDraftId(pricingDraftId);
        metadata.setStatus(status);
        return metadata;
    }
    
    // Constructor cho VARIANT_UPGRADED
    public static ProductEventMetadata forVariantUpgraded(Long variantId, Long variantDraftId) {
        ProductEventMetadata metadata = new ProductEventMetadata();
        metadata.setVariantId(variantId);
        metadata.setVariantDraftId(variantDraftId);
        return metadata;
    }
    
    // Constructor cho ADDON events
    public static ProductEventMetadata forAddon(Long addonId, Long addonDraftId, String status) {
        ProductEventMetadata metadata = new ProductEventMetadata();
        metadata.setAddonId(addonId);
        metadata.setAddonDraftId(addonDraftId);
        metadata.setStatus(status);
        return metadata;
    }
}
