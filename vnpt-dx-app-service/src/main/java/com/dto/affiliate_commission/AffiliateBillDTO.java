package com.dto.affiliate_commission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AffiliateBillDTO {

    private String content;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    private Date endTime;

    private Integer timeType;

    private List<AffiliateBillItemDTO> listItem = new ArrayList<>();

    private String type;
}
