package com.dto.openapis.v1.products;

import java.util.List;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionPlanDTO {
    private Long planId; // ID của thuê bao
    private String name; // Tên gói thuê bao
    private String image; // Hình ảnh đại diện của gói thuê bao
    private String shortDescription; // Mô tả ngắn gọn về gói thuê bao
    private String descriptions; // Mô tả chi tiết về gói thuê bao
    private String allowMultiSubs; // Cho phép mua nhiều lần: YES/NO
    private String recommended; // Gói khuyên dùng: YES/NO
    private List<BillingCycle> billingCycles; // Danh sách chu kỳ thanh toán
    private PricingPlanEnum pricingModel; // Mô hình định giá (FLAT_RATE, UNIT, TIER, VOLUME, STAIR_STEP)
    private List<PriceDetail> priceDetails; // Danh sách giá cho từng chu kỳ thuê bao

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BillingCycle {
        private Long cycleId; // ID chu kỳ thuê bao
        private String duration; // Thời gian của chu kỳ
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PriceDetail {
        private Long cycleId; // ID chu kỳ thuê bao tương ứng
        private Double price; // Giá cố định cho gói thuê bao (áp dụng khi pricing_model là FLAT_RATE, UNIT)
        private Integer fromQuantity; // Số lượng tối thiểu (áp dụng cho pricing_model TIER, VOLUME, STAIR_STEP)
        private Integer toQuantity; // Số lượng tối đa (áp dụng cho pricing_model TIER, VOLUME, STAIR_STEP)
        private Double pricePerUnit; // Giá theo đơn vị (áp dụng cho pricing_model tiered, volume, stairstep)
        private Boolean priceIncludesTax; // Giá đã bao gồm thuế: true/false
    }
}


