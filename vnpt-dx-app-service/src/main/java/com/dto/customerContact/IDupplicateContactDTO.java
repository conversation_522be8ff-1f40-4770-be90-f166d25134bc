package com.dto.customerContact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.onedx.common.constants.enums.CustomerTypeEnum;

public interface IDupplicateContactDTO {

    Long getId();

    String getName();

    String getEmails();

    String getPhones();

    String getAssignee();

    String getLstPartitionName();

    String getProvinceName();

    @JsonIgnore
    String getCustomerType();

    default CustomerTypeEnum getCustomerTypeEnum() {
        return CustomerTypeEnum.fromValue(getCustomerType());
    }
}
