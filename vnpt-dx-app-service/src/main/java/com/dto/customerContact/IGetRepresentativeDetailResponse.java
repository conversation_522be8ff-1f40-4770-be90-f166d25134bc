package com.dto.customerContact;

import java.util.Date;

public interface IGetRepresentativeDetailResponse {
    String getRepresentative(); // Tên người đại diện

    Integer getSex(); // Giới tính

    String getPosition(); // Chức danh

    Date getBirthday(); // Ngày sinh

    Long getNationalityId(); // ID quốc tịch

    String getNationality(); // Quốc tịch

    Long getFolkId(); // ID dân tộc

    String getFolk(); // Dân tộc

    Long getIdentityType(); // ID loại giấy chứng thực

    String getIdentityTypeName(); // Loại giấy chứng thức

    String getIdentityNo(); // Số chứng thực cá nhân

    Date getIdentityDate(); // Ngày cấp

    String getIdentityAddress(); // Nơi cấp

    String getPermanentResidence(); // Nơi đăng ký hộ khẩu

    String getCurrentResident(); // Chỗ ở hiện tại

    String getGeneralDesc(); // Giới thiệu chung
}