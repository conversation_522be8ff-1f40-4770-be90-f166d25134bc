package com.dto.crm.assignmentRule;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import com.constant.enums.crm.assignmentRule.AssignmentMethodEnum;
import com.dto.crm.assignmentRule.actionAssignment.AssignmentWarningPolicyResDTO;
import com.dto.crm.assignmentRule.actionAssignment.NotificationPolicyDTO;
import com.dto.crm.assignmentRule.actionAssignment.ReassignPolicyResDTO;
import lombok.Data;

@Data
public class RuleActionAssignmentDetailDTO {

    Long id;
    Set<Long> lstAssigneeId;
    List<AssigneeInfo> lstAssignee = new ArrayList<>();
    Set<Long> lstWatcherId;
    List<AssigneeInfo> lstWatcher;
    AssignmentMethodEnum assignmentMethod;
    private Long[] lstAssigneesId;
    private ReassignPolicyResDTO reassignPolicy;
    private NotificationPolicyDTO notificationPolicy;
    private AssignmentWarningPolicyResDTO warningPolicy;

}
