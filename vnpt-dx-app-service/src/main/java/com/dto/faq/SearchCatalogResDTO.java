package com.dto.faq;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Data
public class SearchCatalogResDTO {
    private Long id;
    private String name;
    private Date modifiedAt;
    private Integer status;
    private List<QuestionDTO> questionDTOS;
    private List<TopicDTO> topicDTOS;
    private Integer haveAwatingApproval;
    private Integer haveTopicVisible;
    private String answer;
    private Integer type; // 0: catalog, 1: question

    @Getter
    @Setter
    @NoArgsConstructor
    public static class QuestionDTO {
        private Long id;
        private String name;
        private String answer;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class TopicDTO {
        private Long id;
        private String name;
    }
}
