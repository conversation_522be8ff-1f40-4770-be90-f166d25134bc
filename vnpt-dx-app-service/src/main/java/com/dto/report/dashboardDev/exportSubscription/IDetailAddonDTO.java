package com.dto.report.dashboardDev.exportSubscription;

import java.math.BigDecimal;
import org.springframework.context.annotation.Description;

@Description("Đầu ra của truy vấn preview và export dữ liệu chi tiết dịch vụ bổ sung dashboard dev portal")
public interface IDetailAddonDTO {

    @Description("Tên khách hàng")
    String getSmeName();

    String getTaxCode();

    String getAddress();

    String getProductName();

    @Description("Tên gói cước")
    String getPricingName();

    String getSubType();

    String getAddonName();

    String getAddonCode();

    String getAddonType();

    Long getAddonQuantity();

    BigDecimal getAddonPrice();

    @Description("Tên đơn vị tính addon")
    String getAddonUnit();

    BigDecimal getAmount();

    String getCouponName();

    BigDecimal getCouponAmount();

    String getCreatedAt();

    String getStartedAt();

    String getUpdatedAt();

    String getCanceledAt();

    @Description("Đối tượng khách hàng")
    String getCustomerType();

    @Description("Số chứng thực cá nhân")
    String getIdentityNumber();

}
