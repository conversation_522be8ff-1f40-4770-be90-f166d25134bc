package com.dto.rating.response;

import com.onedx.common.constants.values.SwaggerConstant.Evaluation;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> HienNT6
 * @version : 1.0 19/12/2021
 */
public interface EvalRatingDetailDTO {

    Long getUserId();

    Long getRatingId();
    @Schema(description = "Ten tieu chi danh gia", example = "Tốc độ xử lý nhanh")
    String getCriteria();
    @Schema(description = Evaluation.AVG_RATING, example = Example.AVG_RATE)
    Double getAvgRating();

    @Schema(description = "PREDEFINED: Đánh giá dạng rating sao mặc định, CUSTOM: Đánh giá chi tiết bằng bình luận")
    String getType();

}
