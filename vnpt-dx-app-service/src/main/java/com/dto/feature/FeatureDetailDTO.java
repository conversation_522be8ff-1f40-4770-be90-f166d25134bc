package com.dto.feature;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;

@JsonInclude(value = Include.NON_NULL)
public interface FeatureDetailDTO {
    @Schema(description = "Thứ tự hiển thị tính năng")
    Integer getIdx();

    @Schema(description = "ID tính năng")
    Long getId();

    @Schema(description = "Tên tính năng")
    String getName();

    @Schema(description = "Mô tả tính năng")
    String getDescription();
    Integer getPriorityOrder();
    String getFileUrl();

    @Schema(description = "Icon tính năng")
    String getIcon();
    Integer getType();
}
