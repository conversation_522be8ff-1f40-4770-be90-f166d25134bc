package com.dto.serviceGroup;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;

public interface ListServiceGroupResDTO {

    Long getGroupDraftId();

    String getGroupName();

    Integer getApprove();

    String getIconUrl();

    Integer getCountGroupPricing();

    String getGroupCode();

    Long getCreatorId();

    String getCreatorName();

    String getCreatorPortal();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getModifiedAt();
}
