package com.dto.payment;

import com.onedx.common.exception.MessageKeyConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 04/06/2021 - 5:04 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentInitReqDTO {

    @JsonProperty("ACTION")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String action = "INIT";

    @JsonProperty("VERSION")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String version = "1.0.5";

    @JsonProperty("MERCHANT_SERVICE_ID")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String merchant_service_id;

    @JsonProperty("MERCHANT_ORDER_ID")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String merchant_order_id;

    @JsonProperty("AMOUNT")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private BigDecimal amount;

    @JsonProperty("AMOUNT_DETAIL")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String amount_detail;

    @JsonProperty("SERVICE_ID")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private Integer service_id = 1;

    @JsonProperty("SERVICE_CATEGORY")
    @Value("1")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private Integer service_category = 1;

    @JsonProperty("DEVICE")
    @Value("1")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private Integer device = 1;

    @JsonProperty("LOCALE")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String locale = "vi-VN";

    @JsonProperty("CURRENCY_CODE")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String currency_code = "VND";

    @JsonProperty("PAYMENT_METHOD")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String payment_method = "VNPTPAY";

    @JsonProperty("DESCRIPTION")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String description = "Thanhtoan";

    @JsonProperty("CREATE_DATE")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String created_date;

    @JsonProperty("CLIENT_IP")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String client_ip;

    @JsonProperty("SECURE_CODE")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String secure_code;

    @JsonProperty("MERCHANT_DATA")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String merchantData;
}
