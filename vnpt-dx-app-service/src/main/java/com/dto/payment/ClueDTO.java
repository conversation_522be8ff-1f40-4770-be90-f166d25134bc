package com.dto.payment;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0 08/11/2021
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ClueDTO {

    Long merchantServiceId;

    String privateKey;

    String apiKey;

    String baseUrl;

    String qrSecretKey;

    String qrTerminalId;

    String qrApiKey;

    String provinceCodePay;

}
