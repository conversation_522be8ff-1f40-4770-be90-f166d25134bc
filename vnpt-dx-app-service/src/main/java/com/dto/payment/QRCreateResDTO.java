package com.dto.payment;

import com.onedx.common.exception.MessageKeyConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QRCreateResDTO {

    @JsonProperty("responseCode")
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String responseCode;

    @JsonProperty("DESCRIPTION")
    private String description;

    @JsonProperty("qrcodeData")
    private String qrcodeData;

    @JsonProperty("qrcodeId")
    private String qrcodeId;

    @JsonProperty("totalAmount")
    private String totalAmount;

    @JsonProperty("originalAmount")
    private String originalAmount;

    @JsonProperty("fee")
    private String fee;

    @JsonProperty("createDate")
    private String createDate;

    @JsonProperty("checksum")
    private String checksum;

    @JsonProperty("qrcodeImage")
    private String qrcodeImage;

    @JsonProperty("billDetail")
    private String billDetail;

    @JsonProperty("provinceCode")
    private String provinceCode;
}
