package com.constant.quotation;

public class QuotationExportPdfTemplate {

    public static final String HEADER =
            "<!DOCTYPE html>\n" +
                    "<html lang=\"en\">\n" +
                    "	<head>\n" +
                    "		<meta charset=\"UTF-8\" />\n" +
                    "		<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n" +
                    "		<link\n" +
                    "			rel=\"stylesheet\"\n" +
                    "			href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap\"\n" +
                    "		/>\n" +
                    "		<title>Document</title>\n" +
                    "	</head>\n" +
                    "	<body>\n" +
                    "		<!-- header -->\n" +
                    "		<section class=\"header-email\">\n" +
                    "			<table class=\"table-header\">\n" +
                    "				<tbody>\n" +
                    "					<tr>\n" +
                    "						<td style=\"width: 28%\">\n" +
                    "							<div><img src=\"/upload/file/mail/images/logo_bos.png\" width=\"140\" height=\"20\"></div>\n" +
                    "						</td>\n" +
                    "						<td style=\"width: 44%; text-align: center; font-weight: 500; font-size: 8px; line-height: 16px; width: 300px; height: 32px\">\n" +
                    "							<div style=\"margin-top: 10px\">CÔNG TY CỔ PHẦN CÔNG NGHỆ CÔNG NGHIỆP BƯU CHÍNH VIỄN THÔNG</div>\n" +
                    "							<div>Số 124 Hoàng Quốc Việt, Phường Nghĩa Tân, Quận Cầu Giấy, Hà Nội</div>\n" +
                    "						</td>\n" +
                    "						<td style=\"width: 28%; text-align: right; font-weight: 400; font-size: 8px; line-height: 16px; width: 164px; height: 32px\">\n" +
                    "							<div style=\"margin-top: 10px\">Website: <a href=\"http://www.vnpt-technology.vn/\">http://www.vnpt-technology.vn/</a></div>\n" +
                    "							<div>Tel: (84-4) 37480921, Fax:(84-4) 3748 0925</div>\n" +
                    "						</td>\n" +
                    "					</tr>\n" +
                    "				</tbody>\n" +
                    "			</table>\n" +
                    "		</section>\n" +
                    "		<style>\n" +
                    "			body {\n" +
                    "				font-family: \"Montserrat\", sans-serif;\n" +
                    "				font-size: 10px;\n" +
                    "			}\n" +
                    "			.header-email {\n" +
                    "				border-bottom: 2px solid rgb(207, 207, 207);\n" +
                    "			}\n" +
                    "			.table-header {\n" +
                    "				width: 100%;\n" +
                    "			}\n" +
                    "			.table-header td {\n" +
                    "			}\n" +
                    "		</style>\n" +
                    "	</body>\n" +
                    "</html>";

    public static final String FOOTER =
        "<!DOCTYPE html>\n" +
            "<html lang=\"en\">\n" +
            "  <head>\n" +
            "    <meta charset=\"UTF-8\" />\n" +
            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n" +
            "    <link\n" +
            "      rel=\"stylesheet\"\n" +
            "      href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap\"\n" +
            "    />\n" +
            "    <title>Document</title>\n" +
            "  </head>\n" +
            "  <body>\n" +
            "    <!-- footer -->\n" +
            "    <section class=\"footer-email\">\n" +
            "      <table class=\"table-footer\">\n" +
            "        <tbody>\n" +
            "          <tr>\n" +
            "            <td style=\"font-weight: 400; font-size: 10px; line-height: 16px\">Make life easier!</td>\n" +
            "            <td style=\"text-align: right; font-weight: 500; font-size: 12px; line-height: 20px\">$PAGE</td>\n" +
            "          </tr>\n" +
            "        </tbody>\n" +
            "      </table>\n" +
            "    </section>\n" +
            " \n" +
            "    <style>\n" +
            "      body {\n" +
            "        font-family: \"Montserrat\", sans-serif;\n" +
            "        font-size: 10px;\n" +
            "      }\n" +
            "      .footer-email {\n" +
            "        border-top: 2px solid rgb(207, 207, 207);\n" +
            "      }\n" +
            "      .table-footer {\n" +
            "        width: 100%;\n" +
            "      }\n" +
            "      .table-footer td {\n" +
            "        width: 25%;\n" +
            "      }\n" +
            "    </style>\n" +
            "  </body>\n" +
            "</html>";

    public static final String EXPORT_TABLE_TEMPLATE =
        "<!DOCTYPE html>\n" +
            "<html lang=\"en\">\n" +
            "  <head>\n" +
            "    <meta charset=\"UTF-8\" />\n" +
            "    <meta name=\"viewport\" content=\"width=595px, initial-scale=1.0\" />\n" +
            "    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap\">\n" +
            "     <title>Document</title>\n" +
            "  </head>\n" +
            "  <body>\n" +
            "    <!-- hiển thị thông tin -->\n" +
            "    <section class=\"product-price\">\n" +
            "      <h1>Báo giá $QUOTATION_CODE</h1>\n" +
            "      <table >\n" +
            "        <thead>\n" +
            "          <tr >\n" +
            "            <th style=\"text-align: start; padding: 8px; font-weight: 400; border-radius: 6px;\" colspan=\"35\">Khách hàng:\n" +
            "              <b style=\"margin-left: 5px;\">$SME_NAME - $EMAIL</b></th>\n" +
            "          </tr>\n" +
            "        </thead>\n" +
            "        <tbody >\n" +
            "          <tr >\n" +
            "            <td style=\"padding-left: 10px; padding-top: 20px;\">Nhân sự phụ trách </td>\n" +
            "            <td style=\"padding-top: 20px;\">Email</td>\n" +
            "            <td style=\"padding-top: 20px;\">Số điện thoại</td>\n" +
            "            <td style=\"padding-top: 20px;\">Ngày hiệu lực báo giá</td>\n" +
            "            <td style=\"padding-top: 20px;\">Ngày hết hạn báo giá</td>\n" +
            "          </tr>\n" +
            "          <tr>\n" +
            "            <td style=\"padding-left: 10px; padding-top: 5px;\">$ASSIGNEE_NAME</td>\n" +
            "            <td style=\"padding-top: 5px;\">$ASSIGNEE_EMAIL</td>\n" +
            "            <td style=\"padding-top: 5px;\">$PHONE_NUM</td>\n" +
            "            <td style=\"padding-top: 5px;\">$START_TIME</td>\n" +
            "            <td style=\"padding-top: 5px;\">$END_TIME</td>\n" +
            "          </tr>\n" +
            "         </tbody>\n" +
            "      </table>\n" +
            "      <table class=\"custom-table\">\n" +
            "        <thead>\n" +
            "          <tr>\n" +
            "            <th>Dịch vụ</th>\n" +
            "            <th>Số lượng</th>\n" +
            "            <th>Đơn giá(đ)</th>\n" +
            "            <th>Tổng tạm tính(đ)</th>\n" +
            "            <th>Khuyến mại(đ)</th>\n" +
            "            <th>Thuế(đ)</th>\n" +
            "            <th>Phí(đ)</th>\n" +
            "            <th>Giá sau thuế(đ)</th>\n" +
            "          </tr>\n" +
            "        </thead>\n" +
            "        <tbody>\n" +
            "$SERVICE_TABLE_ROW\n" +
            "        </tbody>\n" +
            "      </table>\n" +
            "       <!-- Bản thông tin khuyến mại -->\n" +
            "       <section>\n" +
            "      <table class=\"detail-price\">\n" +
            "        <tbody>\n" +
            "          <tr>\n" +
            "            <td style=\"width: 60%\"></td>\n" +
            "            <td>Tổng tạm tính</td>\n" +
            "            <td >\n" +
            "              $AMOUNT_PRE_TAX<u>đ</u>\n" +
            "            </td>\n" +
            "          </tr>\n" +
            "          <tr>\n" +
            "            <td></td>\n" +
            "            <td>Khuyến mại</td>\n" +
            "            <td >\n" +
            "              $COUPON_AMOUNT<u>đ</u>\n" +
            "            </td>\n" +
            "          </tr>\n" +
            "          <tr>\n" +
            "            <td></td>\n" +
            "            <td>Thuế</td>\n" +
            "            <td >\n" +
            "              $TAX_AMOUNT<u>đ</u>\n" +
            "            </td>\n" +
            "          </tr>\n" +
            "          <tr>\n" +
            "            <td></td>\n" +
            "            <td>Phí</td>\n" +
            "            <td >\n" +
            "              $FEE_AMOUNT<u>đ</u>\n" +
            "            </td>\n" +
            "          </tr>\n" +
            "          <tr>\n" +
            "            <td></td>\n" +
            "            <td><b>Tổng sau thuế</b></td>\n" +
            "            <td >\n" +
            "              <b>$AMOUNT_AFTER_TAX<u>đ</u></b>\n" +
            "            </td>\n" +
            "          </tr>\n" +
            "        </tbody>\n" +
            "      </table>\n" +
            "      $NOTE\n" +
            "      $APPENDIX\n" +
            "    </section>\n" +
            "     <style>\n" +
            "       body {\n" +
            "        font-family: 'Montserrat', sans-serif;\n" +
            "        font-size: 10px;\n" +
            "      }\n" +
            "      /* css cho table */\n" +
            "      table{\n" +
            "        margin-bottom: 30px;\n" +
            "        width: 100%;\n" +
            "      }\n" +
            "            table.custom-table {\n" +
            "        border-collapse: collapse;\n" +
            "        width: 100%;\n" +
            "      }\n" +
            "      .custom-table th{\n" +
            "        font-weight: 500;\n" +
            "        color: black;\n" +
            "      }\n" +
            "           .custom-table th,\n" +
            "      .custom-table td {\n" +
            "        border-bottom: 1px solid #dddddd;\n" +
            "        border-top: none;\n" +
            "        border-left: none;\n" +
            "        border-right: none;\n" +
            "        text-align: left;\n" +
            "        padding: 8px;\n" +
            "      }\n" +
            "     th {\n" +
            "        background-color: #e6e6e6;\n" +
            "        color: black;\n" +
            "      }\n" +
            "      .custom-table  tr:nth-child(even) {\n" +
            "        background-color: #f2f2f2;\n" +
            "      }\n" +
            "       /* css bảng chi tiết báo giá */\n" +
            "      .detail-price tr td:last-child{\n" +
            "        text-align: end;\n" +
            "      }\n" +
            "      .detail-price tr td{\n" +
            "        padding-top: 5px;\n" +
            "      }\n" +
            "               </style>\n" +
            "  </body>\n" +
            "</html>";

    public static final String EXPORT_TABLE_TEMPLATE_BOS =
            "<!DOCTYPE html>\n" +
                    "            <html lang=\"en\">\n" +
                    "              <head>\n" +
                    "                <meta charset=\"UTF-8\" />\n" +
                    "                <meta name=\"viewport\" content=\"width=595px, initial-scale=1.0\" />\n" +
                    "                <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap\">\n" +
                    "                 <title>Document</title>\n" +
                    "              </head>\n" +
                    "              <body>\n" +
                    "                <!-- hiển thị thông tin -->\n" +
                    "                <section class=\"product-price\">\n" +
                    "                  <h1 style=\"margin-top:20px\">Báo giá $QUOTATION_CODE</h1>\n" +
                    "                  <table style=\"margin-bottom: 30px;\">\n" +
                    "                  <tbody>\n" +
                    "                  <tr>\n" +
                    "                  	<th style=\"width: 45%; background-color: transparent; text-align: start; align-content: start; vertical-align: text-top; font-weight: 400;\" colspan=\"35\">\n" +
                    "                      <div style=\"border: 2px solid transparent; padding: 10px; background-color: #F2F2F2; border-radius: 6px;\" >\n" +
                    "                      	<b>Bên bán</b>\n" +
                    "                      </div>\n" +
                    "                      <table style=\"font-weight: 500; padding: 8px;\">\n" +
                    "                          <tbody>\n" +
                    "                          	<tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Công ty:</p></td>\n" +
                    "                              <td style=\"width: 5%\"></td>\n" +
                    "                              <td style=\"text-align: left\"><p>CÔNG TY CỔ PHẦN CÔNG NGHỆ CÔNG NGHIỆP BƯU CHÍNH VIỄN THÔNG</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>GCNĐKDN số:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>0105140413</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Đại diện bởi:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>Ông: Tô Mạnh Cường</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Điện thoại:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>0123456789</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\">Địa chị:</td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\">Khu công nghiệp công nghệ cao I, khu Công nghệ cao Hòa Lạc, xã Hạ Bằng, huyện Thạch Thất, Hà Nội</td>\n" +
                    "                            </tr>\n" +
                    "                          </tbody>\n" +
                    "                        </table>\n" +
                    "                    </th>\n" +
                    "                    <th style=\"width: 2.5%; background-color: transparent; border-right: 1px solid #E6E6E6;\"></th>\n" +
                    "                    <th style=\"width: 2%; background-color: transparent\"></th>\n" +
                    "                    <th style=\"width: 45%; background-color: transparent; text-align: start; align-content: start; vertical-align: text-top; font-weight: 400;\" colspan=\"35\">\n" +
                    "                      <div style=\"border: 2px solid transparent; padding: 10px; background-color: #F2F2F2; border-radius: 6px;\" >\n" +
                    "                      	<b>Bên mua</b>\n" +
                    "                      </div>\n" +
                    "                      <table style=\"font-weight: 500; padding: 8px;\">\n" +
                    "                          $USER_INFOR\n" +
                    "                      </table>\n" +
                    "                    </th>\n" +
                    "                  </tr>\n" +
                    "                  </tbody>\n" +
                    "                  </table>\n" +
                    "                  <table class=\"custom-table\">\n" +
                    "                    <thead>\n" +
                    "                      <tr>\n" +
                    "                        <th style=\"width: 13%\">Mã sản phẩm</th>\n" +
                    "                        <th style=\"width: 30%\">Tên sản phẩm</th>\n" +
                    "                        <th style=\"width: 9%; text-align: center\">Số lượng</th>\n" +
                    "                        <th style=\"width: 11%; text-align: center\">Đơn giá (₫)</th>\n" +
                    "                        <th style=\"width: 9%; text-align: center\">Thuế (₫)</th>\n" +
                    "                        <th style=\"width: 13%; text-align: center\">Chiết khấu (₫)</th>\n" +
                    "                        <th style=\"width: 17%; text-align: center\">Tổng tạm tính (₫)</th>\n" +
                    "                      </tr>\n" +
                    "                    </thead>\n" +
                    "                    <tbody>\n" +
                    "            $SERVICE_TABLE_ROW\n" +
                    "                    </tbody>\n" +
                    "                  </table>\n" +
                    "                   <!-- Bản thông tin khuyến mại -->\n" +
                    "                   <section>\n" +
                    "                  <table class=\"detail-price\">\n" +
                    "                    <tbody>\n" +
                    "                      <tr>\n" +
                    "                        <td>Tổng tạm tính</td>\n" +
                    "                        <td style=\"width: 60%\"></td>\n" +
                    "                        <td >\n" +
                    "                          $AMOUNT_PRE_TAX <u>đ</u>\n" +
                    "                        </td>\n" +
                    "                      </tr>\n" +
                    "                      <tr>\n" +
                    "                        <td>Khuyến mại</td>\n" +
                    "                        <td></td>\n" +
                    "                        <td >\n" +
                    "                          $COUPON_AMOUNT <u>đ</u>\n" +
                    "                        </td>\n" +
                    "                      </tr>\n" +
                    "                      <tr>\n" +
                    "                        <td>Thuế</td>\n" +
                    "                        <td></td>\n" +
                    "                        <td >\n" +
                    "                          $TAX_AMOUNT <u>đ</u>\n" +
                    "                        </td>\n" +
                    "                      </tr>\n" +
                    "                      <tr>\n" +
                    "                        <td>Phí</td>\n" +
                    "                        <td></td>\n" +
                    "                        <td >\n" +
                    "                          $FEE_AMOUNT <u>đ</u>\n" +
                    "                        </td>\n" +
                    "                      </tr>\n" +
                    "                      <tr>\n" +
                    "                        <td><b>Tổng sau thuế</b></td>\n" +
                    "                        <td></td>\n" +
                    "                        <td >\n" +
                    "                          <b>$AMOUNT_AFTER_TAX <u>đ</u></b>\n" +
                    "                        </td>\n" +
                    "                      </tr>\n" +
                    "                    </tbody>\n" +
                    "                  </table>\n" +
                    "                  $NOTE\n" +
                    "                  $APPENDIX\n" +
                    "                </section>\n" +
                    "                 <style>\n" +
                    "                   body {\n" +
                    "                    font-family: 'Montserrat', sans-serif;\n" +
                    "                    font-size: 10px;\n" +
                    "                  }\n" +
                    "                  /* css cho table */\n" +
                    "                  table{\n" +
                    "                    width: 100%;\n" +
                    "                  }\n" +
                    "                  table.custom-table {\n" +
                    "                    border-collapse: collapse;\n" +
                    "                    width: 100%;\n" +
                    "                  }\n" +
                    "                  .custom-table th{\n" +
                    "                    font-weight: 500;\n" +
                    "                    color: black;\n" +
                    "                  }\n" +
                    "                  .custom-table th,\n" +
                    "                  .custom-table td {\n" +
                    "                    border-bottom: 1px solid #dddddd;\n" +
                    "                    border-top: none;\n" +
                    "                    border-left: none;\n" +
                    "                    border-right: none;\n" +
                    "                    text-align: left;\n" +
                    "                    padding: 8px;\n" +
                    "                    font-weight: 500;\n" +
                    "                  }\n" +
                    "                 th {\n" +
                    "                    background-color: #e6e6e6;\n" +
                    "                    color: black;\n" +
                    "                  }\n" +
                    "                  .custom-table  tr:nth-child(even) {\n" +
                    "                    background-color: #f2f2f2;\n" +
                    "                  }\n" +
                    "                   /* css bảng chi tiết báo giá */\n" +
                    "                  .detail-price tr td:last-child{\n" +
                    "                    text-align: end;\n" +
                    "                    text-align: right;\n" +
                    "                  }\n" +
                    "                  .detail-price tr td{\n" +
                    "                    padding-top: 5px;\n" +
                    "                  }\n" +
                    "                           </style>\n" +
                    "              </body>\n" +
                    "            </html>";

    public static final String PERSONAL_INFOR_TEMPLATE =
            "                          <tbody>\n" +
                    "                          	<tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Ông/Bà:</p></td>\n" +
                    "                              <td style=\"width: 5%\"></td>\n" +
                    "                              <td style=\"text-align: left\"><p>$SME_NAME</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>CCCD số:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>$SME_PERSONAL_NUMBER</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Điện thoại:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>$SME_PHONE</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\">Địa chị:</td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\">$SME_ADDRESS</td>\n" +
                    "                            </tr>\n" +
                    "                          </tbody>";

    public static final String BUSINESS_INFOR_TEMPLATE =
            "                          <tbody>\n" +
                    "                          	<tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Công ty:</p></td>\n" +
                    "                              <td style=\"width: 5%\"></td>\n" +
                    "                              <td style=\"text-align: left\"><p>$SME_NAME</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>GCNĐKDN số:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>$SME_LICENSE_NUMBER</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Đại diện bởi:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>Ông/Bà: $REP_NAME</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\"><p>Điện thoại:</p></td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\"><p>$SME_PHONE</p></td>\n" +
                    "                            </tr>\n" +
                    "                            <tr>\n" +
                    "                              <td style=\"width: 25%\">Địa chị:</td>\n" +
                    "                              <td></td>\n" +
                    "                              <td style=\"text-align: left\">$SME_ADDRESS</td>\n" +
                    "                            </tr>\n" +
                    "                          </tbody>";

    public static final String NOTE_TEMPLATE =
        " <!-- Mô tả thêm -->\n" +
            "      <section style=\"margin-top: 50px; font-weight: 500; font-size: 8px; line-height: 14px; font-style: italic\">\n" +
            "       <dt>Lưu ý:</dt>\n" +
            "       <dt>- Khối lượng thiết bị làm tạm tính, trong quá trình thi công do điều kiện công trường và các phát sinh có thể mua sắm thêm theo đơn giá đã được có trong hợp đồng.</dt>\n" +
            "       <dt>- Với những vật tư phát sinh nằm ngoài bảng giá trên, sẽ thỏa thuận và thống nhất giữa 2 bên trước khi tiến hành thi công.</dt>\n" +
            "      </section>";

    public static final String APPENDIX_TEMPLATE =
        "<div style=\"page-break-before: always;\"></div>\n" +
            "      <!-- Phụ lục -->\n" +
            "      <h2 style=\"margin-top:30px\">Phụ lục</h2>\n" +
            "      $DESCRIPTION\n" +
            "      <br> \n" +
            "      $DETAIL_PRICING_PLAN_TABLE\n" +
            "      <br> \n" +
            "      $COUPON_TABLE\n" +
            "      <br> \n" +
            "      $DETAIL_FEE_TABLE_TEMPLATE";

    public static final String APPENDIX_TEMPLATE_BOS =
            "<div style=\"page-break-before: always;\"></div>\n" +
                    "<!-- Phụ lục -->\n" +
                    "<h2 style=\"margin-top:20px\">Phụ lục</h2>\n" +
                    "<b>1. Chi phí lắp đặt</b>\n" +
                    "<div>\n" +
                    "  <p><li style=\"margin-left: 4px;\">Chi phí lắp đặt tiêu chuẩn: 10% giá trị hợp đồng</li></p>\n" +
                    "  <p><li style=\"margin-left: 4px;\">(Chi phí lắp đặt tiêu chuẩn chưa bao gồm các chi phí nhân công phát sinh khác  trong quá trình thi công, nếu phát sinh thêm sẽ được thỏa thuận và thống nhất giữa 2 bên trước khi tiến hành thi công)</li></p>\n" +
                    "</div>\n" +
                    "<b>2. Thông tin khuyến mại</b>\n" +
                    "<p style=\"margin-left: 4px\">Toàn bộ thiết bị còn mới 100%</p>\n" +
                    "<b>3. Thông tin phí</b>\n" +
                    "<table>\n" +
                    "  <tbody>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%\"><p>Giá:</p></td>\n" +
                    "        <td style=\"width: 5%\"></td>\n" +
                    "        <td style=\"text-align: left\"><p>Giá đã bao gồm thuế VAT 10% cho các thiết bị</p></td>\n" +
                    "    </tr>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%\"><p>Thời hạn thanh toán:</p></td>\n" +
                    "        <td></td>\n" +
                    "        <td><p>Thanh toán 100% giá trị hàng hóa trước khi giao hàng</p></td>\n" +
                    "    </tr>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%\"><p>Hình thức:</p></td>\n" +
                    "        <td></td>\n" +
                    "        <td><p>Thanh toán bằng tiền đồng Việt Nam, bằng  chuyển khoản</p></td>\n" +
                    "    </tr>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%; align-content: center\"><p>Thông tin tài khoản:</p></td>\n" +
                    "        <td></td>\n" +
                    "        <td>\n" +
                    "          <p>\n" +
                    "             <div style=\" margin-bottom: 10px;\">124 1900 1525 - Ngân hàng TMCP Tiên Phong Việt Nam - Chi nhánh Hà Nội</div>\n" +
                    "             <div>CÔNG TY CỔ PHẦN CÔNG NGHỆ CÔNG NGHIỆP BƯU CHÍNH VIỄN THÔNG</div>\n" +
                    "          </p>\n" +
                    "        </td>\n" +
                    "    </tr>\n" +
                    "  </tbody>\n" +
                    "</table>\n" +
                    "<b>4. Giao hàng & Bảo hành</b>\n" +
                    "<table>\n" +
                    "  <tbody>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%\"><p>Địa điểm giao hàng:</p></td>\n" +
                    "        <td style=\"width: 5%\"></td>\n" +
                    "        <td style=\"text-align: left\"><p>Giá đã bao gồm thuế VAT 10% cho các thiết bị</p></td>\n" +
                    "    </tr>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%\"><p>Thời gian giao:</p></td>\n" +
                    "        <td></td>\n" +
                    "        <td><p>Bên B cam kết thực hiện công việc và hoàn thiện theo tiến độ chung của công trình</p></td>\n" +
                    "    </tr>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%; align-content: center\"><p>Vận chuyển:</p></td>\n" +
                    "        <td></td>\n" +
                    "        <td>\n" +
                    "          <p>Miễn phí vận chuyển trong vòng bán kính 15km các khu vực nội thành Hà Nội</p>\n" +
                    "          <p>Tính phí vận chuyển hàng đến các khu vực khác ngoài các khu vực đã nêu.</p>\n" +
                    "        </td>\n" +
                    "    </tr>\n" +
                    "    <tr>\n" +
                    "        <td style=\"width: 20%\"><p>Bảo hành:</p></td>\n" +
                    "        <td></td>\n" +
                    "        <td><p>Theo chính sách của nhà sản xuất</p></td>\n" +
                    "    </tr>\n" +
                    "  </tbody>\n" +
                    "</table>\n" +
                    "<b>5. Trách nhiệm của bên mua</b>\n" +
                    "<div>\n" +
                    "  <p><li style=\"margin-left: 4px;\">Nhận hàng và thanh toán cho Bên Bán đầy đủ và đúng hạn theo nội dung Đơn đặt hàng này;</li></p>\n" +
                    "  <p><li style=\"margin-left: 4px;\">Kiểm tra và chịu mọi rủi ro đối với hàng hóa kể từ thời điểm nhận hàng từ Bên Bán;</li></p>\n" +
                    "</div>\n" +
                    "<b>6. Hiệu lực & Giải quyết tranh chấp</b>\n" +
                    "<div>\n" +
                    "  <p><li style=\"margin-left: 4px;\">Bảng báo giá có giá trị và hiệu lực tương đương với Đơn đặt hàng và Hợp đồng mua bán nếu được ký bởi người đại diện có thẩm quyền của hai bên.</li></p>\n" +
                    "  <p><li style=\"margin-left: 4px;\">Mọi sự bổ sung, điều chỉnh nội dung liên quan đến đơn hàng này phải được lập thành văn bản và có xác nhận của đại diện có thẩm quyền của hai bên.</li></p>\n" +
                    "  <p><li style=\"margin-left: 4px;\">Các bên ưu tiên thương lượng, hòa giải trong suốt quá trình thực hiện. Trong trường hợp phát sinh tranh chấp mà không thể thương lượng, hòa giải; mỗi bên đều có quyền yêu cầu Trung tâm Trọng tài Quốc tế Việt Nam (VIAC) để giải quyết theo thủ tục tố tụng của Trung tâm này. Địa điểm giải quyết tranh chấp là Thành phố Hà Nội, số trọng tài tham gia giải quyết vụ việc là 01, luật áp dụng giải quyết tranh chấp là luật Việt Nam.</li></p>\n" +
                    "</div>";

    public static final String DESCRIPTION_TEMPLATE =
        "<b>$INDEX. Mô tả thêm</b>\n" +
            "      <p style=\"margin-bottom: 30px;\">$DESCRIPTION_CONTENT</p>";

    public static final String DETAIL_FEE_TABLE_TEMPLATE =
        "<b>$INDEX. Thông tin phí</b>\n" +
            "      <table style=\"margin-top: 20px; width: 45%;\" class=\"custom-table\">\n" +
            "        <thead>\n" +
            "          <tr>\n" +
            "            <th>Tên phí </th>\n" +
            "            <th>Giá tiền(đ)</th>\n" +
            "          </tr>\n" +
            "        </thead>\n" +
            "        <tbody>\n" +
            "          $DETAIL_FEE_ROW\n" +
            "        </tbody>\n" +
            "      </table>";

    public static final String COUPON_TABLE_TEMPLATE =
        "<b>$INDEX. Thông tin khuyến mại</b>\n" +
            "      <table style=\"margin-top: 20px;\" class=\"custom-table\">\n" +
            "        <thead>\n" +
            "          <tr>\n" +
            "            <th style=\"width: 30%;\">Tên dịch vụ</th>\n" +
            "            <th style=\"width: 50%;\">Khuyến mại áp dụng</th>\n" +
            "            <th style=\"width: 20%;\">Giá tiền(đ)</th>\n" +
            "          </tr>\n" +
            "        </thead>\n" +
            "        <tbody>\n" +
            "        $COUPON_TABLE_ROW\n" +
            "        </tbody>\n" +
            "      </table>";

    public static final String DETAIL_PRICING_PLAN_TABLE_TEMPLATE =
        "<b>$INDEX. Chi tiết cách tính</b>\n" +
            "      <table style=\"margin-top: 20px;\" class=\"custom-table\">\n" +
            "        <thead>\n" +
            "          <tr>\n" +
            "            <th style=\"width: 50%;\">Tên dịch vụ</th>\n" +
            "            <th style=\"width: 10%;\">Số lượng</th>\n" +
            "            <th style=\"width: 40%;\" colspan=\"2\">Chi tiết cách tính</th>\n" +
            "          </tr>\n" +
            "        </thead>\n" +
            "        <tbody>\n" +
            "          $DETAIL_PRICING_PLAN_TABLE_ROW\n" +
            "        </tbody>\n" +
            "      </table>";

    public static final String COUPON_TABLE_ROW_TEMPLATE =
        "            <tr>\n" +
            "              <td>%s</td>\n" +
            "              <td>%s</td>\n" +
            "              <td>%s</td>\n" +
            "            </tr>\n";

    public static final String SERVICE_TABLE_ROW_TEMPLATE =
            "          <tr style=\"background-color: transparent;\">\n" +
                    "            <td>%s</td>\n" +
                    "            <td style=\"padding: 5px;\">\n" +
                    "               <table>\n" +
                    "                <tbody>\n" +
                    "                  <tr>\n" +
                    "                   <td style=\"width: 25px; border-bottom: 0px solid transparent; padding: 0px;\">\n" +
                    "                   	<img src=\"%s\" width=\"24\" height=\"24\">\n" +
                    "                   </td>\n" +
                    "                   <td style=\"border-bottom: 0px solid transparent; padding: 0px;\">\n" +
                    "                   	<div style=\"align-content: center; margin-left: 12px;\">%s</div>\n" +
                    "                   </td>\n" +
                    "                  </tr>\n" +
                    "                </tbody>\n" +
                    "              </table>\n" +
                    "            </td>\n" +
                    "            <td style=\"text-align: center\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "          </tr>";

    public static final String OTHER_TABLE_ROW_TEMPLATE =
            "          <tr style=\"background-color: #F0F8FF;\">\n" +
                    "            <td>%s</td>\n" +
                    "            <td>%s</td>\n" +
                    "            <td style=\"text-align: center\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "            <td style=\"text-align: end; text-align: right\">%s</td>\n" +
                    "          </tr>";

    public static final String DETAIL_PRICING_PLAN_FIRST_ROW_TEMPLATE =
        "          <tr>\n" +
            "            <td rowspan=\"%s\">%s</td>\n" +
            "            <td rowspan=\"%s\">%s</td>\n" +
            "            <td>%s</td>\n" +
            "            <td>%sđ</td>\n" +
            "          </tr>";

    public static final String DETAIL_PRICING_PLAN_ROW_TEMPLATE =
        "          <tr>\n" +
            "            <td>%s</td>\n" +
            "            <td>%sđ</td>\n" +
            "          </tr>";

    public static final String DETAIL_FEE_ROW_TEMPLATE =
        "          <tr>\n" +
            "            <td>%s</td>\n" +
            "            <td>%s</td>\n" +
            "          </tr>";
}
