package com.constant.sql;

/**
 * * SQLSubscription User
 *
 * <AUTHOR>
 *  4/19/2021 1:52 PM
 */
public final class SQLSubscriptionUser {
    private SQLSubscriptionUser() {
    }


    public static final String GET_SUBSCRIPTION_USERS = "select s from SubscriptionUser s where s.deletedFlag = 1 and s.status = 1";

    public static final String CHECK_USER_USED_OFFICIALLY_SERVICE =
            "SELECT "
            + "    count(us) > 0  "
            + "FROM "
            + "    {h-schema}user_subscription us "
            + "JOIN {h-schema}subscriptions s ON "
            + "    us.subscription_id = s.id "
            + "JOIN {h-schema}services s2 ON "
            + "    s.service_id = s2.id "
            + "WHERE "
            + "    us.user_id = :userId "
            + "    AND s2.id = :serviceId "
            + "    AND ((s.cancelled_time IS NULL "
            + "        AND s.from_date < now()) "
            + "    OR (s.cancelled_time IS NOT NULL "
            + "        AND s.from_date < s.cancelled_time))";

    public static final String CHECK_USER_USED_OFFICIALLY_SERVICE_AND_DEVICE =
            "SELECT "
                    + "    count(s) > 0  "
                    + "FROM {h-schema}subscriptions s "
                    + "JOIN {h-schema}services s2 ON "
                    + "    s.service_id = s2.id "
                    + "WHERE "
                    + "    s.user_id = :userId "
                    + "    AND s2.id = :serviceId "
                    + "    AND ((s.cancelled_time IS NULL "
                    + "        AND s.from_date < now()) "
                    + "    OR (s.cancelled_time IS NOT NULL "
                    + "        AND s.from_date < s.cancelled_time))";

    public static final String CHECK_USER_USED_OFFICIALLY_COMBO =
        "SELECT "
            + "    count(us) > 0  "
            + "FROM "
            + "    {h-schema}user_subscription us "
            + "JOIN {h-schema}subscriptions s ON "
            + "    us.subscription_id = s.id "
            + "JOIN {h-schema}combo_plan cp ON "
            + "    s.combo_plan_id = cp.id "
            + "JOIN {h-schema}combo c ON "
            + "    cp.combo_id = c.id "
            + "WHERE "
            + "    us.user_id = :userId "
            + "    AND c.id = :comboId "
            + "    AND ((s.cancelled_time IS NULL "
            + "        AND s.from_date < now()) "
            + "    OR (s.cancelled_time IS NOT NULL "
            + "        AND s.from_date < s.cancelled_time))";
}
