package com.constant.sql;

public final class SQLProductSolution {
    public static final String SEARCH_PRODUCT_SOLUTION =
                    // đếm tổng số khuyến mãi của giải pháp
                    " WITH all_promotions AS (\n" +
                            "SELECT psd.id,coupon_id\n" +
                            "FROM product_solution_draft psd \n" +
                            "JOIN solution_packages sp ON sp.solution_draft_id = psd.id\n" +
                            "JOIN package_draft p ON p.id = sp.package_draft_id\n" +
                            "JOIN package_mappings pm ON pm.package_draft_id = p.id\n" +
                            "JOIN package_items pi ON pm.package_item_id = pi.id\n" +
                            "JOIN package_item_promotions pip ON pip.package_item_id = pi.id\n" +
                            "UNION ALL\n" +
                            "SELECT psd.id,coupon_id\n" +
                            "FROM product_solution_draft psd \n" +
                            "JOIN solution_packages sp ON sp.solution_draft_id = psd.id\n" +
                            "JOIN package_draft p ON p.id = sp.package_draft_id\n" +
                            "JOIN package_mappings pm ON pm.package_draft_id = p.id\n" +
                            "JOIN package_items pi ON pm.package_item_id = pi.id\n" +
                            "JOIN package_item_addons pia ON pia.package_item_id = pi.id\n" +
                            "JOIN package_item_addon_promotions piap ON piap.package_item_addon_id = pia.id" +
                            "),\n" +
                            "promotion_count_cte AS (\n" +
                            "   SELECT id as solution_id, COUNT(*) AS promotion_count\n" +
                            "   FROM all_promotions\n" +
                            "   GROUP BY solution_id\n" +
                            " )," +
                    // doanh thu của giải pháp
                    " revenue_cte AS (\n" +
                        "    SELECT m.solution_id, SUM(i.amount_after_tax) AS revenue\n" +
                        "    FROM {h-schema}subscription_metadata m\n" +
                        "    JOIN subscriptions s ON s.id = m.subscription_id\n" +
                        "    JOIN billings b ON s.id = b.subscriptions_id\n" +
                        "    JOIN bill_item i ON i.billing_id = b.id\n" +
                        "    WHERE b.status = 2\n" +
                        "    GROUP BY m.solution_id\n" +
                        ")," +
                    // danh sách ngành nghề áp dụng
                    " solution_domains_cte AS (\n" +
                            "SELECT psd.id AS solution_id,\n" +
                            "   string_agg(sd.name, ',') AS solution_domains\n" +
                            "FROM {h-schema}product_solution_draft psd\n" +
                            "JOIN LATERAL unnest(psd.domain_ids) AS domain_id ON TRUE\n" +
                            "JOIN {h-schema}solution_domains sd ON sd.id = domain_id\n" +
                            "GROUP BY psd.id\n" +
                        "),\n" +
                    // danh sách danh mục
                    "categories_cte AS (\n" +
                            "SELECT psd.id AS solution_id,\n" +
                            "    string_agg(c.name, ',') AS categories\n" +
                            "FROM {h-schema}product_solution_draft psd\n" +
                            "JOIN LATERAL unnest(psd.category_ids) AS category_id ON TRUE\n" +
                            "JOIN {h-schema}categories c ON c.id = category_id\n" +
                            "WHERE ((ARRAY[-1]::::bigint[] && (:categoryIds)::::bigint[]) OR psd.category_ids && (:categoryIds)::::bigint[])\n" +
                            "GROUP BY psd.id\n" +
                        "),\n" +
                    // danh sách id của gói thuộc giải pháp
                    " package_ids_cte AS (\n" +
                        "    SELECT sp.solution_draft_id AS solution_id,\n" +
                        "           string_agg(DISTINCT p.id::::text, ',') AS package_ids\n" +
                        "    FROM {h-schema}solution_packages sp\n" +
                        "    JOIN {h-schema}package_draft p ON sp.package_draft_id = p.id\n" +
                        "    WHERE -1 IN :packageIds OR p.id IN :packageIds\n" +
                        "    GROUP BY sp.solution_draft_id)\n" +
                "SELECT psd.id AS id," +
                        "psd.name AS name, " +
                        "psd.customer_types AS customerTypes," +
                        "psd.state AS approvalStatus," +
                        "psd.visibility AS visibility," +
                        "psd.modified_at AS modifiedAt, " +
                        "psd.avatar_url AS avatarUrl," +
                        "COALESCE(pc.promotion_count, 0) AS promotionCount," +
                        "COALESCE(r.revenue, 0) AS revenue," +
                        "COALESCE(sd.solution_domains, '') AS solutionDomains," +
                        "COALESCE(c.categories, '') AS categories," +
                        "COALESCE(pk.package_ids, '') AS packageIds"+
                " FROM {h-schema}product_solution_draft psd " +
                        "LEFT JOIN promotion_count_cte pc ON psd.id = pc.solution_id\n" +
                        "LEFT JOIN revenue_cte r ON psd.id = r.solution_id\n" +
                        "LEFT JOIN solution_domains_cte sd ON psd.id = sd.solution_id\n" +
                        "JOIN categories_cte c ON psd.id = c.solution_id\n" +
                        "LEFT JOIN package_ids_cte pk ON psd.id = pk.solution_id" +
                    " WHERE (:value = '' " +
                    " OR (:isName = 1 AND psd.name ILIKE ('%' || :value || '%'))\n" +
                    " OR (:isCode = 1 AND psd.code ILIKE ('%' || :value || '%')))\n" +
                    " AND (:approveStatus = -1 OR psd.state = :approveStatus )  \n" +
                    " AND (:displayStatus = -1 OR psd.visibility = :displayStatus) \n" +
                    " AND (-1 IN :createdIds OR psd.created_by IN :createdIds)\n" +
                    " AND (:customerType = 'ALL' OR LOWER(psd.customer_types) ILIKE ('%' || LOWER(:customerType) || '%'))\n" +
                    " AND (-1 = :userId OR psd.provider_id = :userId)\n" +
                    " AND (-1 IN :ids OR psd.id IN :ids)\n" +
                    " AND (psd.deleted_flag = 1)";

    public static final String COUNT_SEARCH_PRODUCT_SOLUTION =
            "select count(1) from (\n"  + SEARCH_PRODUCT_SOLUTION + ") as query" ;
    public static final String GET_LIST_PACKAGE_BY_SOLUTION_ID =
            "SELECT \n" +
                    "    pd.id AS id,\n" +
                    "    pd.name AS name,\n" +
                    "    pd.state AS approvalStatus,\n" +
                    "    pd.visibility AS visibility,\n" +
                    "    pd.modified_at AS modifiedAt,\n" +
                    "    pd.icon_url AS iconUrl,\n" +
                    "    sp.solution_draft_id AS solutionDraftId,\n" +
                    "    COALESCE(SUM(pi.total_amount), 0) + COALESCE(SUM(pia.total_amount), 0) AS finalPrice,\n" +
                    "    COUNT(DISTINCT pip.id) + COUNT(DISTINCT piad.id) AS promotionCount\n" +
                    "FROM {h-schema}solution_packages sp\n" +
                    "    LEFT JOIN {h-schema}package_draft pd ON pd.id = sp.package_draft_id\n" +
                    "    LEFT JOIN {h-schema}package_mappings pm ON pm.package_draft_id = pd.id\n" +
                    "    LEFT JOIN {h-schema}package_items pi ON pi.id = pm.package_item_id\n" +
                    "    LEFT JOIN {h-schema}package_item_addons pia ON pia.package_item_id = pi.id\n" +
                    "    LEFT JOIN {h-schema}package_item_promotions pip ON pip.package_item_id = pi.id\n" +
                    "    LEFT JOIN {h-schema}package_item_addon_promotions piad ON piad.package_item_addon_id = pia.id\n" +
                    "WHERE (-1 IN :packageIds OR pd.id IN :packageIds)" +
                    "    GROUP BY pd.id, pd.name, pd.state, pd.visibility, pd.modified_at, sp.solution_draft_id";

    public static final String GET_LIST_SOLUTION_POP_UP =
            "SELECT DISTINCT psd.id as id,psd.name as name,psd.avatar_url as avatarUrl\n" +
            "FROM {h-schema}product_solution_draft psd \n" +
                    "LEFT JOIN {h-schema}solution_packages sp ON psd.id = sp.solution_draft_id\n" +
                    "LEFT JOIN {h-schema}package_draft p ON p.id = sp.package_draft_id\n" +
            "WHERE  (('-1' IN :customerTypeCondition) OR (psd.customer_types IS NOT NULL AND EXISTS (SELECT 1 FROM jsonb_array_elements_text(psd.customer_types::::JSONB) as cusType WHERE cusType IN :customerTypeCondition )))\n" +
                    "AND (:value = ''  OR (:isSolution = 1 AND psd.name ILIKE ('%' || :value || '%')) \n" +
                    "OR (:isPackage = 1 AND p.name ILIKE ('%' || :value || '%')))\n" +
                    "AND (:displayStatus = -1 OR psd.visibility = :displayStatus)\n" +
                    "AND (:customerTypeSearch = 'ALL' OR LOWER(psd.customer_types) ILIKE ('%' || LOWER(:customerTypeSearch) || '%'))\n" +
                    "AND ((ARRAY[-1]::::bigint[] && (:categoryIds)::::bigint[]) OR psd.category_ids && (:categoryIds)::::bigint[])\n" +
                    "AND ((ARRAY[-1]::::bigint[] && (:domainIds)::::bigint[]) OR psd.domain_ids && (:domainIds)::::bigint[])" ;


    public static final String DELETE_PACKAGE_BY_IDS =
        "update {h-schema}package_draft set deleted_flag = :value, modified_by = :userId where id IN (:ids)";

    public static final String DELETE_SOLUTION_BY_IDS =
        "update {h-schema}product_solution_draft set deleted_flag = :value, modified_by = :userId where id IN (:ids)";

    public static final String UPDATE_STATE_PACKAGE_BY_IDS =
        "update {h-schema}package_draft set state = :value, modified_by = :userId where id IN (:ids)";

    public static final String UPDATE_STATE_SOLUTION_BY_IDS =
        "update {h-schema}product_solution_draft set state = :value, modified_by = :userId where id IN (:ids)";

    public static final String COMMON_LIST_LATEST_PRODUCT_SOLUTION =
        "WITH latestSolution AS (\n" +
            "    SELECT MAX(id) AS latest_solution_id, draft_id \n" +
            "    FROM {h-schema}product_solutions\n" +
            "    WHERE deleted_flag = 1 AND visibility = 1\n" +
            "    GROUP BY draft_id\n" +
            "),\n" +
            "solutionPackage AS (\n" +
            "    SELECT \n" +
            "        sp.solution_id,\n" +
            "        sp.solution_draft_id,\n" +
            "        sp.package_draft_id,\n" +
            "        sp.default_package\n" +
            "    FROM {h-schema}solution_packages sp\n" +
            "    WHERE sp.solution_id IS NOT NULL \n" +
            "      AND sp.solution_draft_id IS NOT NULL\n" +
            "),\n" +
            "defaultPackage AS (\n" +
            "    SELECT \n" +
            "        p.*,\n" +
            "        sp.solution_id,\n" +
            "        sp.solution_draft_id\n" +
            "    FROM solutionPackage sp\n" +
            "    JOIN {h-schema}packages p \n" +
            "      ON p.draft_id = sp.package_draft_id\n" +
            "    WHERE sp.default_package = TRUE\n" +
            "),\n" +
            "serviceViewCount AS (\n" +
            "    SELECT \n" +
            "        service_id,\n" +
            "        COUNT(*) AS view_count\n" +
            "    FROM {h-schema}service_view\n" +
            "    WHERE type = 5 \n" +
            "    GROUP BY service_id\n" +
            ")\n" +
            "SELECT \n" +
            "    ps.id,\n" +
            "    ps.draft_id AS solutionDraftId,\n" +
            "    ps.name AS solutionName,\n" +
            "    ps.domain_ids::::text AS domainIds,\n" +
            "    ps.customer_types AS customerTypeStr,\n" +
            "    ps.avatar_url AS avatarUrl,\n" +
            "    ps.descriptions,\n" +
            "    ps.multisub_enabled as multisubEnabled,\n" +
            "    ps.modified_at AS modifiedAt,\n" +
            "    null AS numSub,\n" +
            "    users.name AS provider,\n" +
            "    pkg.id AS packageDefaultId,\n" +
            "    pkg.draft_id AS packageDefaultDraftId,\n" +
            "    pkg.name AS packageName,\n" +
            "    pkg.price_from AS priceFrom,\n" +
            "    pkg.price,\n" +
            "    pkg.payment_method AS paymentMethod,\n" +
            "    CAST(pkg.apply_condition AS text) AS applyCondition, \n" +
            "    COALESCE(sv.view_count, 0) AS viewCount \n" +
            "FROM {h-schema}product_solutions ps \n" +
            "JOIN latestSolution ls ON ps.id = ls.latest_solution_id \n" +
            "    LEFT JOIN defaultPackage pkg ON pkg.solution_id = ps.id AND pkg.solution_draft_id = ps.draft_id \n" +
            "    LEFT JOIN serviceViewCount sv ON sv.service_id = ps.id \n" +
            "    LEFT JOIN {h-schema}users ON ps.provider_id = users.id \n";

    public static final String GET_LIST_LATEST_PRODUCT_SOLUTION =
        COMMON_LIST_LATEST_PRODUCT_SOLUTION +
            "WHERE \n" +
            "    (:customerType = 'ALL' OR ps.customer_types  ILIKE ('%' || :customerType || '%')) \n" +
            "LIMIT :limit";

    public static final String GET_LIST_VIEWED_PRODUCT_SOLUTION =
        COMMON_LIST_LATEST_PRODUCT_SOLUTION +
            "WHERE \n" +
            "    (:customerType = 'ALL' OR ps.customer_types  ILIKE ('%' || :customerType || '%')) \n" +
            "AND ps.id IN (:solutionIds)";

    public static final String GET_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN_ID =
        COMMON_LIST_LATEST_PRODUCT_SOLUTION +
            "WHERE \n" +
            "    (:customerType = 'ALL' OR ps.customer_types  ILIKE ('%' || :customerType || '%')) \n" +
            "    AND (:search = '' OR ps.name ILIKE ('%' || :search || '%')) \n" +
            "    AND (\n" +
            "      :domainId = '-1'\n" +
            "      OR (:domainId)::::bigint = ANY(ps.domain_ids)\n" +
            "    )\n";

    public static final String COUNT_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN_ID =
        "select count(1) from ( " + GET_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN_ID + ") as query ";

    public static final String COMMON_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN =
        "WITH latestSolution AS (\n" +
            "    SELECT MAX(id) AS latest_solution_id, draft_id \n" +
            "    FROM {h-schema}product_solutions\n" +
            "    WHERE deleted_flag = 1 AND visibility = 1\n" +
            "    GROUP BY draft_id\n" +
            "),\n" +
            "solutionPackage AS (\n" +
            "    SELECT \n" +
            "        sp.solution_id,\n" +
            "        sp.solution_draft_id,\n" +
            "        sp.package_draft_id,\n" +
            "        sp.default_package\n" +
            "    FROM {h-schema}solution_packages sp\n" +
            "    WHERE sp.solution_id IS NOT NULL \n" +
            "      AND sp.solution_draft_id IS NOT NULL\n" +
            "),\n" +
            "defaultPackage AS (\n" +
            "    SELECT \n" +
            "        p.*,\n" +
            "        sp.solution_id,\n" +
            "        sp.solution_draft_id\n" +
            "    FROM solutionPackage sp\n" +
            "    JOIN {h-schema}packages p \n" +
            "      ON p.draft_id = sp.package_draft_id\n" +
            "    WHERE sp.default_package = TRUE\n" +
            "),\n" +
            "serviceViewCount AS (\n" +
            "    SELECT \n" +
            "        service_id,\n" +
            "        COUNT(*) AS view_count\n" +
            "    FROM {h-schema}service_view\n" +
            "    WHERE type = 5 \n" +
            "    GROUP BY service_id\n" +
            "),\n" +
            "mSolutionDetail as (\n" +
            "SELECT \n" +
            "    ps.id,\n" +
            "    ps.draft_id AS solutionDraftId,\n" +
            "    ps.name AS solutionName,\n" +
            "    ps.domain_ids::::text AS domainIds,\n" +
            "    ps.customer_types AS customerTypeStr,\n" +
            "    ps.avatar_url AS avatarUrl,\n" +
            "    ps.descriptions,\n" +
            "    ps.multisub_enabled as multisubEnabled,\n" +
            "    ps.modified_at AS modifiedAt,\n" +
            "    null AS numSub,\n" +
            "    users.name AS provider,\n" +
            "    pkg.id AS packageDefaultId,\n" +
            "    pkg.draft_id AS packageDefaultDraftId,\n" +
            "    pkg.name AS packageName,\n" +
            "    pkg.price_from AS priceFrom,\n" +
            "    pkg.price,\n" +
            "    pkg.payment_method AS paymentMethod,\n" +
            "    unnest(ps.domain_ids) as domainId,\n" +
            "    CAST(pkg.apply_condition AS text) AS applyCondition, \n" +
            "    COALESCE(sv.view_count, 0) AS viewCount \n" +
            "FROM {h-schema}product_solutions ps \n" +
            "JOIN latestSolution ls ON ps.id = ls.latest_solution_id \n" +
            "    LEFT JOIN defaultPackage pkg ON pkg.solution_id = ps.id AND pkg.solution_draft_id = ps.draft_id \n" +
            "    LEFT JOIN serviceViewCount sv ON sv.service_id = ps.id \n" +
            "    LEFT JOIN {h-schema}users ON ps.provider_id = users.id\n" +
            "WHERE \n" +
            "    (:customerType = 'ALL' OR ps.customer_types  ILIKE ('%' || :customerType || '%')) \n" +
            "    AND (:search = '' OR ps.name ILIKE ('%' || :search || '%')) \n" +
            "), \n";

    public static final String GET_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN_NEWEST =
        COMMON_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN +
            "solutionForDomain AS (\n" +
            "    select \n" +
            "        *,\n" +
            "        COUNT(*) OVER (PARTITION BY domainId) AS count,\n" +
            "        rank() over(partition by domainId order by modifiedAt desc) as RANK   \n" +
            "    from mSolutionDetail\n" +
            "    WHERE \n" +
            "     (:domainId = '-1' or domainId = ANY(cast(string_to_array(:domainId, ',') as bigint[])))\n" +
            ")\n" +
            "SELECT \n" +
            "    *\n" +
            "FROM solutionForDomain\n" +
            "WHERE RANK <= :rank ";

    public static final String GET_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN_TRENDING =
        COMMON_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN +
            "solutionForDomain AS (\n" +
            "    select \n" +
            "        *,\n" +
            "        COUNT(*) OVER (PARTITION BY domainId) AS count,\n" +
            "        rank() over(partition by domainId order by viewCount desc) as RANK   \n" +
            "    from mSolutionDetail\n" +
            "    WHERE \n" +
            "     (:domainId = '-1' or domainId = ANY(cast(string_to_array(:domainId, ',') as bigint[])))\n" +
            ")\n" +
            "SELECT \n" +
            "    *\n" +
            "FROM solutionForDomain\n" +
            "WHERE RANK <= :rank ";

    public static final String GET_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN_TOP_SELLING =
        COMMON_LIST_LATEST_PRODUCT_SOLUTION_BY_DOMAIN +
            "solutionForDomain AS (\n" +
            "    select \n" +
            "        *,\n" +
            "        COUNT(*) OVER (PARTITION BY domainId) AS count,\n" +
            "        rank() over(partition by domainId order by numSub desc) as RANK   \n" +
            "    from mSolutionDetail\n" +
            "    WHERE \n" +
            "     (:domainId = '-1' or domainId = ANY(cast(string_to_array(:domainId, ',') as bigint[])))\n" +
            ")\n" +
            "SELECT \n" +
            "    *\n" +
            "FROM solutionForDomain\n" +
            "WHERE RANK <= :rank ";

    public static final String GET_SOLUTION =
        "with newest_solution as (  \n" +
            "	select max (id) as solution_id, draft_id from product_solutions \n" +
            "	group by draft_id  \n" +
            "),\n" +
            "solution_feature as ( \n" +
            "		select feature_mappings.object_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', features.id,  \n" +
            "		'name', features.name, \n" +
            "		'code', features.code, \n" +
            "		'description', features.description, \n" +
            "		'icon', features.icon, \n" +
            "		'idx', feature_mappings.idx,  \n" +
            "		'isDisplay', features.type = 1, \n" +
            "		'fileUrl', COALESCE(file_attach.file_path, file_attach.ext_link) )) as text) AS features \n" +
            "		from {h-schema}feature_mappings   \n" +
            "		left join {h-schema}features  ON  feature_mappings.feature_id = features.id \n" +
            "		LEFT JOIN {h-schema}file_attach  ON features.id = file_attach.object_id  \n" +
            "		AND file_attach.object_type = 19 AND file_attach.file_type = 1 \n" +
            "		where feature_mappings.object_type = 'SOLUTION' and feature_mappings.object_id is not null\n" +
            "		group by feature_mappings.object_id \n" +
            "	), \n" +
            "	solution_section as ( \n" +
            "		select section_mappings.object_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', sections.id,  \n" +
            "		'idx', section_mappings.idx, \n" +
            "		'imageUrl', sections.image_url, \n" +
            "		'videoUrl', sections.video_url, \n" +
            "		'description', sections.description,  \n" +
            "		'name', sections.name)) as text ) AS sections \n" +
            "		from {h-schema}section_mappings  \n" +
            "		left join {h-schema}sections ON  section_mappings.section_id = sections.id \n" +
            "		where {h-schema}section_mappings.object_type = 'SOLUTION'  and section_mappings.object_id is not null\n" +
            "		group by section_mappings.object_id \n" +
            "	), \n" +
            "	solution_seo as ( \n" +
            "		select product_solutions.id as object_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', seo.id,  \n" +
            "		'titlePage', seo.title_page, \n" +
            "		'planUrl', seo.plan_url, \n" +
            "		'description', seo.meta_description, \n" +
            "		'filePath', product_solutions.avatar_url, \n" +
            "		'descriptionShare', seo.description, \n" +
            "		'titleShare', case when seo.is_available_title = true then null  \n" +
            "										else seo.title \n" +
            "									end,  \n" +
            "		'filePathShare', case when seo.is_image = true then product_solutions.avatar_url \n" +
            "											else COALESCE(file_attach.file_path, file_attach.ext_link) \n" +
            "										end)) as text ) AS seo  \n" +
            "		from {h-schema}product_solutions  \n" +
            "		left join {h-schema}seo ON seo.id = product_solutions.seo_id \n" +
            "		left join {h-schema}file_attach ON file_attach.id = seo.id \n" +
            "		group by product_solutions.id \n" +
            "	), \n" +
            "	solution_domain as ( \n" +
            "		select product_solutions.id as object_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', solution_domains.id, \n" +
            "		'name', solution_domains.name)) as text ) AS domains \n" +
            "		 \n" +
            "		FROM {h-schema}product_solutions \n" +
            "		JOIN {h-schema}solution_domains ON solution_domains.id = any(product_solutions.domain_ids) \n" +
            "		group by product_solutions.id \n" +
            "	), \n" +
            "	solution_category as ( \n" +
            "		select product_solutions.id as object_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', categories.id, \n" +
            "		'name', categories.name)) as text ) AS categories \n" +
            "		 \n" +
            "		FROM {h-schema}product_solutions \n" +
            "		JOIN {h-schema}categories ON categories.id = any(product_solutions.category_ids)\n" +
            "		group by product_solutions.id \n" +
            "	) \n" +
            "	select ps.id, ps.customer_types as customerTypes, ps.name,  \n" +
            "	ps.code, sd.domains, ps.feature_visible as featureVisible, sc.categories, ps.avatar_url as avatarUrl,  \n" +
            "	ps.video_urls as videoUrl, ps.descriptions, sf.features,  \n" +
            "	ss.sections, sseo.seo, ps.multisub_enabled as multisubEnabled, ps.visibility  \n" +
            "	from {h-schema}product_solution_draft psd \n" +
            "	join newest_solution ON newest_solution.draft_id = psd.id\n" +
            "	join product_solutions ps ON ps.id = newest_solution.solution_id\n" +
            "	left join solution_feature sf ON sf.object_id = ps.id  \n" +
            "	left join solution_section ss ON ss.object_id = ps.id \n" +
            "	left join solution_seo sseo ON sseo.object_id = ps.id \n" +
            "	left join solution_domain sd ON sd.object_id = ps.id \n" +
            "	left join solution_category sc ON sc.object_id = ps.id \n" +
            "	where psd.id = :objectId";

    public static final String GET_SOLUTION_DRAFT =
        "with solution_feature as (\n" +
            "	select feature_mappings.object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', features.id, \n" +
            "	'name', features.name,\n" +
            "	'code', features.code,\n" +
            "	'description', features.description,\n" +
            "	'icon', features.icon,\n" +
            "	'idx', feature_mappings.idx, \n" +
            "	'isDisplay', features.type = 1,\n" +
            "	'fileUrl', COALESCE(file_attach.file_path, file_attach.ext_link) )) as text) AS features\n" +
            "	from {h-schema}feature_mappings  \n" +
            "	left join {h-schema}features  ON  feature_mappings.feature_id = features.id\n" +
            "	LEFT JOIN {h-schema}file_attach  ON features.id = file_attach.object_id \n" +
            "	AND file_attach.object_type = 19 AND file_attach.file_type = 1\n" +
            "	where feature_mappings.object_type = 'SOLUTION'\n" +
            "	group by feature_mappings.object_draft_id\n" +
            "),\n" +
            "solution_section as (\n" +
            "	select section_mappings.object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', sections.id, \n" +
            "	'idx', section_mappings.idx,\n" +
            "	'imageUrl', sections.image_url,\n" +
            "	'videoUrl', sections.video_url,\n" +
            "	'description', sections.description, \n" +
            "	'name', sections.name)) as text ) AS sections\n" +
            "	from {h-schema}section_mappings \n" +
            "	left join {h-schema}sections ON  section_mappings.section_id = sections.id\n" +
            "	where {h-schema}section_mappings.object_type = 'SOLUTION'\n" +
            "	group by section_mappings.object_draft_id\n" +
            "),\n" +
            "solution_seo as (\n" +
            "	select product_solution_draft.id as object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', seo.id, \n" +
            "	'titlePage', seo.title_page,\n" +
            "	'planUrl', seo.plan_url,\n" +
            "	'description', seo.meta_description,\n" +
            "	'filePath', product_solution_draft.avatar_url,\n" +
            "	'descriptionShare', seo.description,\n" +
            "	'titleShare', case when seo.is_available_title = true then null \n" +
            "									else seo.title\n" +
            "								end, \n" +
            "	'filePathShare', case when seo.is_image = true then product_solution_draft.avatar_url\n" +
            "										else COALESCE(file_attach.file_path, file_attach.ext_link)\n" +
            "									end)) as text ) AS seo \n" +
            "	from {h-schema}product_solution_draft \n" +
            "	left join {h-schema}seo ON seo.id = product_solution_draft.seo_id\n" +
            "	left join {h-schema}file_attach ON file_attach.id = seo.id\n" +
            "	group by product_solution_draft.id\n" +
            "),\n" +
            "solution_domain as (\n" +
            "	select product_solution_draft.id as object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', solution_domains.id,\n" +
            "	'name', solution_domains.name)) as text ) AS domains\n" +
            "	\n" +
            "	FROM {h-schema}product_solution_draft\n" +
            "	JOIN {h-schema}solution_domains ON solution_domains.id = any(product_solution_draft.domain_ids)	\n" +
            "	group by product_solution_draft.id\n" +
            "),\n" +
            "solution_category as (\n" +
            "	select product_solution_draft.id as object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', categories.id,\n" +
            "	'name', categories.name)) as text ) AS categories\n" +
            "	\n" +
            "	FROM {h-schema}product_solution_draft\n" +
            "	JOIN {h-schema}categories ON categories.id = any(product_solution_draft.category_ids)\n" +
            "	group by product_solution_draft.id\n" +
            ")\n" +
            "select psd.id, psd.state as approvalStatus, psd.customer_types as customerTypes, psd.name, \n" +
            "psd.code, sd.domains, psd.feature_visible as featureVisible, sc.categories, psd.avatar_url as avatarUrl, \n" +
            "psd.video_urls as videoUrl, psd.descriptions, sf.features, \n" +
            "ss.sections, sseo.seo, psd.multisub_enabled as multisubEnabled, psd.visibility, \n" +
            "(select EXISTS (select id from {h-schema}product_solutions where draft_id = :objectId)) as isApproved \n" +
            "from {h-schema}product_solution_draft psd\n" +
            "left join solution_feature sf ON sf.object_draft_id = psd.id \n" +
            "left join solution_section ss ON ss.object_draft_id = psd.id\n" +
            "left join solution_seo sseo ON sseo.object_draft_id = psd.id\n" +
            "left join solution_domain sd ON sd.object_draft_id = psd.id\n" +
            "left join solution_category sc ON sc.object_draft_id = psd.id\n" +
            "where psd.id = :objectId";


    public static final String GET_PACKAGE_IN_SOLUTION =
        "with packageItemAddon as (\n" +
            "	select package_items.id, \n" +
            "	jsonb_agg(json_build_object(\n" +
            "	'id', package_item_addons.id, \n" +
            "	'totalAmount', package_item_addons.total_amount))  AS addon\n" +
            "	from {h-schema}package_items\n" +
            "	join {h-schema}package_item_addons ON package_items.id = package_item_addons.package_item_id\n" +
            "	group by package_items.id\n" +
            "),\n" +
            "packageItem as (\n" +
            "	select package_mappings.package_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', pricing.id, \n" +
            "	'pricingName', pricing.pricing_name,\n" +
            "	'providerName', users.name,\n" +
            "	'status', pricing.status,\n" +
            "   'cycleType', pricing_multi_plan.circle_type,\n" +
            "	'isOneTime', pricing.is_one_time,\n" +
            "	'payment_cycle', pricing_multi_plan.payment_cycle," +
            "	'createdAt', TO_CHAR(pricing.created_at, 'DD/MM/YYYY'), \n" +
            "	'totalAmount', package_items.total_amount, \n" +
            "	'addons', COALESCE(packageItemAddon.addon, '[]'::::jsonb))) as text ) AS pricing\n" +
            "	from {h-schema}package_mappings  \n" +
            "	left join {h-schema}package_items ON  package_mappings.package_item_id = package_items.id\n" +
            "	left join {h-schema}pricing_multi_plan ON pricing_multi_plan.id = package_items.id\n" +
            "  left join {h-schema}pricing ON pricing_multi_plan.pricing_id = pricing.id\n" +
            "  left join {h-schema}users ON pricing.created_by = users.id\n" +
            "	left join packageItemAddon ON packageItemAddon.id = package_items.id\n" +
            "	where package_id is null\n" +
            "	group by package_mappings.package_draft_id\n" +
            ")\n" +
            "select sp.package_draft_id as id, p.name as name, sp.default_package as defaultPackage, u.name as providerName, \n" +
            "CASE WHEN p.visibility = 1 then 'VISIBLE'\n" +
            "			ELSE 'INVISIBLE'\n" +
            "		 END AS visibility,\n" +
            "p.price as price, p.icon_url as iconUrl, p.created_at as createdAt, p.recommended, packageItem.pricing as products\n" +
            "from {h-schema}product_solution_draft psd\n" +
            "left join {h-schema}solution_packages sp ON psd.id = sp.solution_draft_id\n" +
            "left join {h-schema}package_draft p ON sp.package_draft_id = p.id\n" +
            "left join {h-schema}users u ON u.id = p.created_by \n" +
            "left join packageItem ON packageItem.package_draft_id = p.id\n" +
            "where psd.id = :objectId and sp.solution_id is null and sp.package_id is null";

    public static final String GET_PACKAGE_DRAFT =
        "with package_feature as ( \n" +
            "		select feature_mappings.object_draft_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', features.id,  \n" +
            "		'name', features.name, \n" +
            "		'code', features.code, \n" +
            "		'description', features.description, \n" +
            "		'icon', features.icon, \n" +
            "		'idx', feature_mappings.idx,  \n" +
            "		'isDisplay', features.type = 1, \n" +
            "		'fileUrl', COALESCE(file_attach.file_path, file_attach.ext_link) )) as text) AS features \n" +
            "		from {h-schema}feature_mappings   \n" +
            "		left join {h-schema}features  ON  feature_mappings.feature_id = features.id \n" +
            "		LEFT JOIN {h-schema}file_attach  ON features.file_id = file_attach.id  \n" +
            "		where feature_mappings.object_type = 'PACKAGE' and feature_mappings.object_id is null \n" +
            "		group by feature_mappings.object_draft_id \n" +
            "	), \n" +
            "	packageItemAddon as (\n" +
            "		select package_items.id, \n" +
            "		jsonb_agg(json_build_object(\n" +
            "		'id', package_item_addons.id, \n" +
            "		'couponIds', couponItem.couponIds, \n" +
            "		'couponMcIds', couponItemMc.couponMcIds, \n" +
            "		'quantity', package_item_addons.quantity, \n" +
            "		'multiPlanId', pricing_multi_plan.id, \n" +
            "		'cycleType', pricing_multi_plan.circle_type, \n" +
            "		'addonName', addons.name, \n" +
            "		'isOneTime', addons.bonus_type, \n" +
            "		'paymentCycle', pricing_multi_plan.payment_cycle, \n" +
            "		'originPrice', package_item_addon_prices.price, \n" +
            "		'priceUpdate', package_item_addon_prices.price, \n" +
            "		'totalAmount', package_item_addons.total_amount))  AS addon\n" +
            "		from {h-schema}package_items\n" +
            "		join {h-schema}package_item_addons ON package_items.id = package_item_addons.package_item_id\n" +
            "		left join {h-schema}addons ON addons.id = package_item_addons.addon_id\n" +
            "		left join {h-schema}pricing_multi_plan ON pricing_multi_plan.id = package_item_addons.addon_plan_id\n" +
            "		left join {h-schema}package_item_addon_prices ON package_item_addon_prices.package_item_addon_id = package_item_addons.id\n" +
            "       left join (\n" +
            "			SELECT\n" +
            "				package_item_addon_id,\n" +
            "				ARRAY_AGG(coupon_id) AS couponIds\n" +
            "			FROM\n" +
            "				{h-schema}package_item_addon_promotions\n" +
            "			WHERE\n" +
            "				package_item_addon_id is not null and coupon_id is not null\n" +
            "			GROUP BY\n" +
            "				package_item_addon_id) couponItem ON couponItem.package_item_addon_id = package_item_addons.id\n" +
            "       left join (\n" +
            "			SELECT\n" +
            "				package_item_addon_id,\n" +
            "				ARRAY_AGG(concat(mc_id, activity_idx)) as couponMcIds\n" +
            "			FROM\n" +
            "				{h-schema}package_item_addon_promotions\n" +
            "			WHERE\n" +
            "				package_item_addon_id is not null and mc_id is not null and activity_idx is not null \n" +
            "			GROUP BY\n" +
            "				package_item_addon_id) couponItemMc ON couponItemMc.package_item_addon_id = package_item_addons.id\n" +
            "       group by package_items.id \n" +
            "	),\n" +
            "	packageItem as (\n" +
            "			select package_mappings.package_draft_id, \n" +
            "			cast(jsonb_agg(json_build_object(\n" +
            "			'id', pricing.id, \n" +
            "			'serviceName', services.service_name, \n" +
            "			'variantName', variant.full_name, \n" +
            "			'variantId', variant.id, \n" +
            "			'pricingName', pricing.pricing_name,\n" +
            "			'providerName', users.name,\n" +
            "			'manufactureName', manufacturer.name,\n" +
            "			'status', pricing.status,\n" +
            "			'multiPlanId', pricing_multi_plan.id,\n" +
            "			'cycleType', pricing_multi_plan.circle_type,\n" +
            "			'isOneTime', pricing.is_one_time,\n" +
            "			'couponIds', couponItem.couponIds,\n" +
            "			'couponMcIds', couponItemMc.couponMcIds,\n" +
            "			'quantity', package_items.quantity,\n" +
            "			'priceUpdate', package_item_prices.price,\n" +
            "			'originPrice', package_item_prices.price,\n" +
            "			'paymentCycle', pricing_multi_plan.payment_cycle,\n" +
            "			'createdAt', TO_CHAR(pricing.created_at, 'DD/MM/YYYY'), \n" +
            "			'totalAmount', package_items.total_amount, \n" +
            "			'addons', COALESCE(packageItemAddon.addon, '[]'::::jsonb))) as text) AS pricings\n" +
            "			from {h-schema}package_mappings  \n" +
            "			join {h-schema}package_items ON  package_mappings.package_item_id = package_items.id\n" +
            "			left join {h-schema}pricing_multi_plan ON pricing_multi_plan.id = package_items.plan_id\n" +
            "			left join {h-schema}pricing ON pricing_multi_plan.pricing_id = pricing.id\n" +
            "			left join {h-schema}services ON services.id = pricing.service_id\n" +
            "			left join {h-schema}users ON pricing.created_by = users.id\n" +
            "			left join {h-schema}manufacturer ON services.manufacture = manufacturer.id\n" +
            "			left join {h-schema}variant ON (package_items.metadata ->> 'variantId')::::bigint = variant.id\n" +
            "			left join packageItemAddon ON packageItemAddon.id = package_items.id\n" +
            "           left join {h-schema}package_item_prices ON package_item_prices.package_item_id = package_items.id \n" +
            "	        left join (\n" +
            "		        SELECT\n" +
            "				    package_item_id,\n" +
            "				    ARRAY_AGG(coupon_id) AS couponIds\n" +
            "		        FROM\n" +
            "				    {h-schema}package_item_promotions\n" +
            "		        WHERE\n" +
            "				    package_item_id IS NOT NULL and coupon_id is not null\n" +
            "		        GROUP BY\n" +
            "				    package_item_id) couponItem ON couponItem.package_item_id = package_items.id\n" +
            "	        left join (\n" +
            "		        SELECT\n" +
            "				    package_item_id,\n" +
            "				    ARRAY_AGG(concat(mc_id, activity_idx)) as couponMcIds\n" +
            "		        FROM\n" +
            "				    {h-schema}package_item_promotions\n" +
            "		        WHERE\n" +
            "				    package_item_id is not null and mc_id is not null and activity_idx is not null \n" +
            "		        GROUP BY\n" +
            "				    package_item_id) couponItemMc ON couponItemMc.package_item_id = package_items.id\n" +
            "			where package_id is not null \n" +
            "			group by package_mappings.package_draft_id\n" +
            "		), \n" +
            "	package_seo as ( \n" +
            "		select package_draft.id as object_draft_id,  \n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "		'id', seo.id,  \n" +
            "		'titlePage', seo.title_page, \n" +
            "		'planUrl', seo.plan_url, \n" +
            "		'description', seo.description, \n" +
            "		'filePath', package_draft.icon_url, \n" +
            "		'descriptionShare', seo.description, \n" +
            "		'titleShare', case when seo.is_available_title = true then null  \n" +
            "						else seo.title \n" +
            "					  end,  \n" +
            "		'filePathShare', case when seo.is_image = true then package_draft.icon_url \n" +
            "							else COALESCE(file_attach.file_path, file_attach.ext_link) \n" +
            "						 end)) as text ) AS seo  \n" +
            "		from {h-schema}package_draft  \n" +
            "		left join {h-schema}seo ON seo.id = package_draft.seo_id \n" +
            "		left join {h-schema}file_attach ON file_attach.id = seo.id \n" +
            "		group by package_draft.id \n" +
            "	),\n" +
            "package_domain as (\n" +
            "	select package_draft.id as object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', solution_domains.id,\n" +
            "	'name', solution_domains.name)) as text ) AS domains\n" +
            "	\n" +
            "	FROM {h-schema}package_draft\n" +
            "	JOIN {h-schema}solution_domains ON solution_domains.id = any(package_draft.domain_ids) \n" +
            "	group by package_draft.id\n" +
            "),\n" +
            "package_category as (\n" +
            "	select package_draft.id as object_draft_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', categories.id,\n" +
            "	'name', categories.name)) as text ) AS categories\n" +
            "	\n" +
            "	FROM {h-schema}package_draft\n" +
            "	JOIN {h-schema}categories ON categories.id = any(package_draft.category_ids) \n" +
            "	group by package_draft.id\n" +
            "), \n" +
            "   groupPackageDraft as (SELECT\n" +
            "    package_draft_id,\n" +
            "    ARRAY_AGG(distinct solution_draft_id) AS solutionIds\n" +
            "   FROM\n" +
            "    {h-schema}solution_packages\n" +
            "   WHERE\n" +
            "    package_draft_id IS NOT NULL\n" +
            "   GROUP BY\n" +
            "    package_draft_id)\n" +
            "	select pd.id, u.name as providerName, pd.name, pd.price, pd.code, \n" +
            "	pd.recommended, pd.discount_type as discountType, pd.discount_value as discountValue, pd.feature_visible as featureVisible, pd.state as approvalStatus, pd.icon_url as iconUrl, pd.banner_urls as bannerUrls, pf.features,\n" +
            "	pd.descriptions, pd.apply_condition::::text as condition, pd.guidelines::::text as guidelinesString, \n" +
            "	ps.seo, packageItem.pricings, pd.payment_method as paymentMethod, groupPackageDraft.solutionIds::::text as solutionIds,\n" +
            "	CASE WHEN pd.visibility = 1 then 'VISIBLE'\n" +
            "				ELSE 'INVISIBLE'\n" +
            "			 END AS visibility,  \n" +
            "   package_domain.domains, package_category.categories, \n" +
            "	(select EXISTS (select id from {h-schema}packages where draft_id = :objectId)) as isApproved  \n" +
            "	from {h-schema}package_draft pd \n" +
            "	left join {h-schema}users u ON pd.created_by = u.id\n" +
            "	left join package_feature pf ON pf.object_draft_id = pd.id  \n" +
            "	left join package_seo ps ON ps.object_draft_id = pd.id \n" +
            "	left join packageItem ON packageItem.package_draft_id = pd.id \n" +
            "	left join groupPackageDraft ON groupPackageDraft.package_draft_id = pd.id \n" +
            "   left join package_domain on package_domain.object_draft_id = pd.id\n" +
            "   left join package_category on package_category.object_draft_id = pd.id\n" +
            "	where pd.id = :objectId";

    public static final String GET_PACKAGE =
        "with newest_package as (  \n" +
            "	select max (id) as package_id, draft_id from {h-schema}packages \n" +
            "	group by draft_id  \n" +
            "),\n" +
            "package_feature as (  \n" +
            "			select feature_mappings.object_id,   \n" +
            "			cast(jsonb_agg(json_build_object(  \n" +
            "			'id', features.id,   \n" +
            "			'name', features.name,  \n" +
            "			'code', features.code,  \n" +
            "			'description', features.description,  \n" +
            "			'icon', features.icon,  \n" +
            "			'idx', feature_mappings.idx,   \n" +
            "			'isDisplay', features.type = 1,  \n" +
            "			'fileUrl', COALESCE(file_attach.file_path, file_attach.ext_link) )) as text) AS features  \n" +
            "			from {h-schema}feature_mappings    \n" +
            "			left join {h-schema}features  ON  feature_mappings.feature_id = features.id  \n" +
            "			LEFT JOIN {h-schema}file_attach  ON features.file_id = file_attach.id   \n" +
            "			where feature_mappings.object_type = 'PACKAGE' and feature_mappings.object_id is not null\n" +
            "			group by feature_mappings.object_id  \n" +
            "		),  \n" +
            "		packageItemAddon as ( \n" +
            "			select package_items.id,  \n" +
            "			jsonb_agg(json_build_object( \n" +
            "			'id', package_item_addons.id,  \n" +
            "			'couponIds', couponItem.couponIds,  \n" +
            "		    'couponMcIds', couponItemMc.couponMcIds, \n" +
            "			'quantity', package_item_addons.quantity,  \n" +
            "			'cycleType', pricing_multi_plan.circle_type,  \n" +
            "			'multiPlanId', pricing_multi_plan.id,  \n" +
            "			'addonName', addons.name,  \n" +
            "			'isOneTime', addons.bonus_type,  \n" +
            "			'paymentCycle', pricing_multi_plan.payment_cycle,  \n" +
            "			'originPrice', package_item_addon_prices.price,  \n" +
            "			'priceUpdate', package_item_addon_prices.price,  \n" +
            "			'totalAmount', package_item_addons.total_amount))  AS addon \n" +
            "			from {h-schema}package_items \n" +
            "			left join {h-schema}package_item_addons ON package_items.id = package_item_addons.package_item_id \n" +
            "			left join {h-schema}addons ON addons.id = package_item_addons.addon_id \n" +
            "			left join {h-schema}pricing_multi_plan ON pricing_multi_plan.id = package_item_addons.addon_plan_id \n" +
            "			left join {h-schema}package_item_addon_prices ON package_item_addon_prices.package_item_addon_id = package_item_addons.id \n" +
            "			left join ( \n" +
            "				SELECT \n" +
            "					package_item_addon_id, \n" +
            "					ARRAY_AGG(coupon_id) AS couponIds \n" +
            "				FROM \n" +
            "					{h-schema}package_item_addon_promotions \n" +
            "				WHERE \n" +
            "					package_item_addon_id IS NOT NULL \n" +
            "				GROUP BY \n" +
            "					package_item_addon_id) couponItem ON couponItem.package_item_addon_id = package_item_addons.id \n" +
            "           left join (\n" +
            "			    SELECT\n" +
            "				    package_item_addon_id,\n" +
            "				    ARRAY_AGG(concat(mc_id, activity_idx)) as couponMcIds\n" +
            "			    FROM\n" +
            "				    {h-schema}package_item_addon_promotions\n" +
            "			    WHERE\n" +
            "				    package_item_addon_id is not null and mc_id is not null and activity_idx is not null \n" +
            "			    GROUP BY\n" +
            "				    package_item_addon_id) couponItemMc ON couponItemMc.package_item_addon_id = package_item_addons.id\n" +
            "				 group by package_items.id  \n" +
            "		), \n" +
            "		packageItem as ( \n" +
            "				select package_mappings.package_id,  \n" +
            "				cast(jsonb_agg(json_build_object( \n" +
            "				'id', pricing.id,  \n" +
            "				'serviceName', services.service_name,  \n" +
            "				'variantName', variant.full_name,  \n" +
            "				'variantId', variant.id,  \n" +
            "				'pricingName', pricing.pricing_name, \n" +
            "				'manufactureName', manufacturer.name, \n" +
            "				'multiPlanId', pricing_multi_plan.id, \n" +
            "				'providerName', users.name, \n" +
            "				'status', pricing.status, \n" +
            "				'cycleType', pricing_multi_plan.circle_type, \n" +
            "				'isOneTime', pricing.is_one_time, \n" +
            "				'couponIds', couponItem.couponIds, \n" +
            "			    'couponMcIds', couponItemMc.couponMcIds,\n" +
            "				'quantity', package_items.quantity, \n" +
            "				'priceUpdate', package_item_prices.price, \n" +
            "				'originPrice', package_item_prices.price, \n" +
            "				'paymentCycle', pricing_multi_plan.payment_cycle, \n" +
            "				'createdAt', TO_CHAR(pricing.created_at, 'DD/MM/YYYY'),  \n" +
            "				'totalAmount', package_items.total_amount,  \n" +
            "				'addons', COALESCE(packageItemAddon.addon, '[]'::::jsonb))) as text) AS pricings \n" +
            "				from {h-schema}package_mappings   \n" +
            "				left join {h-schema}package_items ON  package_mappings.package_item_id = package_items.id \n" +
            "				left join {h-schema}pricing_multi_plan ON pricing_multi_plan.id = package_items.plan_id \n" +
            "				left join {h-schema}pricing ON pricing_multi_plan.pricing_id = pricing.id \n" +
            "				left join {h-schema}services ON services.id = pricing.service_id \n" +
            "			    left join {h-schema}variant ON (package_items.metadata ->> 'variantId')::::bigint = variant.id\n" +
            "			    left join {h-schema}manufacturer ON services.manufacture = manufacturer.id\n" +
            "				left join {h-schema}users ON pricing.created_by = users.id \n" +
            "				left join packageItemAddon ON packageItemAddon.id = package_items.id \n" +
            "						 left join {h-schema}package_item_prices ON package_item_prices.package_item_id = package_items.id  \n" +
            "						left join ( \n" +
            "							SELECT \n" +
            "							package_item_id, \n" +
            "							ARRAY_AGG(coupon_id) AS couponIds \n" +
            "							FROM \n" +
            "							{h-schema}package_item_promotions \n" +
            "							WHERE \n" +
            "							package_item_id IS NOT NULL \n" +
            "							GROUP BY \n" +
            "							package_item_id) couponItem ON couponItem.package_item_id = package_items.id \n" +
            "	            left join (\n" +
            "		            SELECT\n" +
            "				        package_item_id,\n" +
            "				        ARRAY_AGG(concat(mc_id, activity_idx)) as couponMcIds\n" +
            "		            FROM\n" +
            "				        {h-schema}package_item_promotions\n" +
            "		            WHERE\n" +
            "				        package_item_id is not null and mc_id is not null and activity_idx is not null \n" +
            "		            GROUP BY\n" +
            "				        package_item_id) couponItemMc ON couponItemMc.package_item_id = package_items.id\n" +
            "				where package_id is not null \n" +
            "				group by package_mappings.package_id \n" +
            "			),  \n" +
            "		package_seo as (  \n" +
            "			select packages.id as object_id,   \n" +
            "			cast(jsonb_agg(json_build_object(  \n" +
            "			'id', seo.id,   \n" +
            "			'titlePage', seo.title_page,  \n" +
            "			'planUrl', seo.plan_url,  \n" +
            "			'description', seo.description,  \n" +
            "			'filePath', packages.icon_url,  \n" +
            "			'descriptionShare', seo.description,  \n" +
            "			'titleShare', case when seo.is_available_title = true then null   \n" +
            "							else seo.title  \n" +
            "							end,   \n" +
            "			'filePathShare', case when seo.is_image = true then packages.icon_url  \n" +
            "								else COALESCE(file_attach.file_path, file_attach.ext_link)  \n" +
            "							 end)) as text ) AS seo   \n" +
            "			from {h-schema}packages   \n" +
            "			left join {h-schema}seo ON seo.id = packages.seo_id  \n" +
            "			left join {h-schema}file_attach ON file_attach.id = seo.id  \n" +
            "			group by packages.id  \n" +
            "		), \n" +
            "package_domain as (\n" +
            "	select packages.id as object_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', solution_domains.id,\n" +
            "	'name', solution_domains.name)) as text ) AS domains\n" +
            "	\n" +
            "	FROM {h-schema}packages\n" +
            "	JOIN {h-schema}solution_domains ON solution_domains.id = any(packages.domain_ids) \n" +
            "	group by packages.id\n" +
            "),\n" +
            "package_category as (\n" +
            "	select packages.id as object_id, \n" +
            "	cast(jsonb_agg(json_build_object(\n" +
            "	'id', categories.id,\n" +
            "	'name', categories.name)) as text ) AS categories\n" +
            "	\n" +
            "	FROM {h-schema}packages\n" +
            "	JOIN {h-schema}categories ON categories.id = any(packages.category_ids) \n" +
            "	group by packages.id\n" +
            "), \n" +
            "		 groupPackage as (SELECT \n" +
            "			package_id, \n" +
            "			ARRAY_AGG(distinct solution_draft_id) AS solutionIds \n" +
            "		 FROM \n" +
            "			{h-schema}solution_packages \n" +
            "		 WHERE \n" +
            "			package_draft_id IS NOT NULL and package_id is not null\n" +
            "		 GROUP BY \n" +
            "			package_id) \n" +
            "		select p.id, u.name as providerName, p.name, p.price, p.code,  \n" +
            "		p.recommended,  p.discount_type as discountType, p.discount_value as discountValue, p.feature_visible as featureVisible,  p.icon_url as iconUrl, p.banner_urls as bannerUrls, pf.features, \n" +
            "		p.descriptions, p.apply_condition::::text as condition, p.guidelines::::text as guidelinesString,  \n" +
            "		ps.seo, packageItem.pricings, p.payment_method as paymentMethod, groupPackage.solutionIds::::text as solutionIds, \n" +
            "       package_domain.domains, package_category.categories, \n" +
            "		CASE WHEN p.visibility = 1 then 'VISIBLE' \n" +
            "					ELSE 'INVISIBLE' \n" +
            "				 END AS visibility   \n" +
            "		from {h-schema}package_draft pd  \n" +
            "		join newest_package ON pd.id = newest_package.draft_id\n" +
            "		join {h-schema}packages p ON p.id = newest_package.package_id\n" +
            "		left join {h-schema}users u ON p.created_by = u.id \n" +
            "		left join package_feature pf ON pf.object_id = p.id   \n" +
            "		left join package_seo ps ON ps.object_id = p.id  \n" +
            "		left join packageItem ON packageItem.package_id = p.id  \n" +
            "		left join groupPackage ON groupPackage.package_id = p.id  \n" +
            "		left join package_domain on package_domain.object_id = p.id\n" +
            "		left join package_category on package_category.object_id = p.id\n" +
            "		where pd.id = :objectId";

    public static final String GET_SOLUTION_IN_PACKAGE =
        "with packageSolution as (\n" +
            "			select solution_packages.solution_draft_id,\n" +
            "			cast(jsonb_agg(json_build_object(\n" +
            "			'id', package_draft.id,\n" +
            "			'price', package_draft.price,\n" +
            "			'countSub', tbl.countSub,\n" +
            "			'visibility',CASE WHEN package_draft.visibility = 1 then 'VISIBLE'\n" +
            "					ELSE 'INVISIBLE'\n" +
            "				 END,\n" +
            "			'name', package_draft.name)) as text ) AS package\n" +
            "			from {h-schema}solution_packages \n" +
            "			left join {h-schema}package_draft ON solution_packages.package_draft_id = package_draft.id\n" +
            "			left join (\n" +
            "				select package_draft.id, count(subscription_id) as countSub from subscription_metadata \n" +
            "				left join {h-schema}packages ON packages.id = subscription_metadata.package_id\n" +
            "				left join {h-schema}package_draft ON package_draft.id = packages.draft_id\n" +
            "				where subscription_metadata.package_id is not null\n" +
            "				group by package_draft.id \n" +
            "			) tbl ON tbl.id = package_draft.id\n" +
            "			group by solution_packages.solution_draft_id\n" +
            "		),\n" +
            "		solution_domain as (\n" +
            "			select product_solution_draft.id as object_draft_id, \n" +
            "			cast(jsonb_agg(json_build_object(\n" +
            "			'id', solution_domains.id,\n" +
            "			'name', solution_domains.name)) as text ) AS domains\n" +
            "			\n" +
            "			FROM {h-schema}product_solution_draft\n" +
            "			JOIN {h-schema}solution_domains ON solution_domains.id = any(product_solution_draft.domain_ids)	\n" +
            "			group by product_solution_draft.id\n" +
            "		),\n" +
            "		solution_category as (\n" +
            "			select product_solution_draft.id as object_draft_id, \n" +
            "			cast(jsonb_agg(json_build_object(\n" +
            "			'id', categories.id,\n" +
            "			'name', categories.name)) as text ) AS categories\n" +
            "			\n" +
            "			FROM {h-schema}product_solution_draft\n" +
            "			JOIN {h-schema}categories ON categories.id = any(product_solution_draft.category_ids)\n" +
            "			group by product_solution_draft.id\n" +
            "		)\n" +
            "		select psd.id, psd.name, \n" +
            "		CASE WHEN psd.visibility = 1 then 'VISIBLE'\n" +
            "					ELSE 'INVISIBLE'\n" +
            "				 END AS visibility,\n" +
            "		psd.modified_at as modifiedAt, solution_domain.domains, solution_category.categories,\n" +
            "	  	packageSolution.package\n" +
            "		from {h-schema}product_solution_draft psd\n" +
            "		left join solution_domain ON solution_domain.object_draft_id = psd.id\n" +
            "		left join solution_category ON solution_category.object_draft_id = psd.id\n" +
            "		left join packageSolution ON packageSolution.solution_draft_id = psd.id\n" +
            "		where psd.id IN (:ids)";

    public static final String GET_LIST_PACKAGE_BASE_INFO_IN_SOLUTION =
        "with packFeatures as ( \n" +
            "    select \n" +
            "            featMapping.object_id as packageId, \n" +
            "            jsonb_agg(json_build_object( \n" +
            "                'id', features.id, \n" +
            "                'name', features.name \n" +
            "            )) as featureJson \n" +
            "        from {h-schema}feature_mappings as featMapping \n" +
            "            left join {h-schema}features on featMapping.feature_id = features.id \n" +
            "        where featMapping.object_type = 'PACKAGE' and features.deleted_flag = 1 and features.status = 1 \n" +
            "        group by featMapping.object_id \n" +
            ") \n" +
        "select \n" +
            "    packages.id as id, \n" +
            "    packages.draft_id as draftId, \n" +
            "    packages.name as packageName, \n" +
            "    case \n" +
            "        when packages.visibility = 1 then 'VISIBLE' \n" +
            "        else 'INVISIBLE' \n" +
            "    end as visibility, \n" +
            "    packages.price_from as price, \n" +
            "    packages.created_at as createdAt, \n" +
            "    packages.descriptions as descriptions, \n" +
            "    packages.recommended, \n" +
            "    provider.name as providerName, \n" +
            "    text(coalesce(packFeatures.featureJson, cast('[]' as jsonb))) as lstFeatureJson, \n" +
            "    solPackage.solution_id \n" +
            "from {h-schema}solution_packages as solPackage \n" +
            "    join {h-schema}product_solutions as solution on solution.id = solPackage.solution_id \n" +
            "    join ( \n" +
            "        select draft_id, max(id) as latest_id from {h-schema}packages where deleted_flag = 1 and visibility = 1 group by draft_id \n" +
            "    ) as latestPackage on latestPackage.draft_id = solPackage.package_draft_id \n" +
            "    join {h-schema}packages on packages.id = latestPackage.latest_id \n" +
            "    left join packFeatures on packFeatures.packageId = packages.id \n" +
            "    left join {h-schema}users as provider on provider.id = packages.created_by \n" +
            "where \n" +
            "    solPackage.solution_id is not null and \n" +
            "    solution.id = :solutionId and \n" +
            "    packages.deleted_flag = 1 and \n" +
            "    packages.visibility = 1 ";

    public static final String GET_LIST_PACKAGE_BY_IDS =
        "with packFeatures as ( \n" +
            "    select \n" +
            "        featMapping.object_id as packageId, \n" +
            "        jsonb_agg(json_build_object( \n" +
            "            'id', features.id, \n" +
            "            'name', features.name \n" +
            "        )) as featureJson \n" +
            "    from {h-schema}feature_mappings as featMapping \n" +
            "        left join {h-schema}features on featMapping.feature_id = features.id \n" +
            "    where featMapping.object_type = 'PACKAGE' and features.deleted_flag = 1 and features.status = 1 \n" +
            "    group by featMapping.object_id \n" +
            "), \n" +
            "latestPackages as ( \n" +
            "    select \n" +
            "        draft_id, \n" +
            "        max(id) as latestId \n" +
            "    from {h-schema}packages \n" +
            "    where deleted_flag = 1 and visibility = 1 \n" +
            "    group by draft_id \n" +
            ") \n" +
            "select \n" +
            "    packages.id, \n" +
            "    packages.draft_id as draftId, \n" +
            "    packages.name as name, \n" +
            "    case \n" +
            "        when packages.visibility = 1 then 'VISIBLE' \n" +
            "        else 'INVISIBLE' \n" +
            "    end as visibility, \n" +
            "    packages.price_from as price, \n" +
            "    packages.created_at as createdAt, \n" +
            "    packages.descriptions as descriptions, \n" +
            "    packages.recommended, \n" +
            "    provider.name as providerName, \n" +
            "    text(coalesce(packFeatures.featureJson, cast('[]' as jsonb))) as lstFeatureJson \n" +
            "from {h-schema}packages as packages \n" +
            "    join latestPackages on packages.id = latestPackages.latestId \n" +
            "    left join packFeatures on packFeatures.packageId = packages.id \n" +
            "    left join {h-schema}users as provider on provider.id = packages.created_by \n" +
            "where \n" +
            "    packages.id in (:packageIds) and \n" +
            "    packages.deleted_flag = 1 and \n" +
            "    packages.visibility = 1 ";


    public static final String GET_PACKAGE_FEATURE_BY_PACKAGE_ID =
        "select \n" +
            "    feature_mappings.object_id,  \n" +
            "    cast(jsonb_agg(json_build_object( \n" +
            "    'id', features.id,  \n" +
            "    'name', features.name, \n" +
            "    'code', features.code, \n" +
            "    'description', features.description, \n" +
            "    'icon', features.icon, \n" +
            "    'idx', feature_mappings.idx,  \n" +
            "    'isDisplay', features.type = 1, \n" +
            "    'fileUrl', COALESCE(file_attach.file_path, file_attach.ext_link) )) as text) AS featureJson \n" +
            "from \n" +
            "    {h-schema}feature_mappings   \n" +
            "    join {h-schema}features  ON  feature_mappings.feature_id = features.id \n" +
            "    LEFT JOIN {h-schema}file_attach  ON features.id = file_attach.object_id AND file_attach.object_type = 19 AND file_attach.file_type = 1 \n" +
            "where \n" +
            "   feature_mappings.object_type = 'PACKAGE' \n" +
            "   and feature_mappings.object_id = :packageId\n" +
            "group by \n" +
            "    feature_mappings.object_id ";

    public static final String GET_LIST_SME_PACKAGE_PRODUCT_ITEM_BY_PACKAGE_ID =
        "    with packItemAddon as ( \n" +
            "        select \n" +
            "            package_items.id, \n" +
            "            jsonb_agg(json_build_object(\n" +
            "                'id', package_item_addons.id, \n" +
            "                'couponIds', couponItem.couponIds, \n" +
            "                'quantity', package_item_addons.quantity, \n" +
            "                'cycleType', pricing_multi_plan.circle_type, \n" +
            "                'addonName', addons.name, \n" +
            "                'isOneTime', addons.bonus_type, \n" +
            "                'paymentCycle', pricing_multi_plan.payment_cycle, \n" +
            "                'originPrice', COALESCE(pricing_multi_plan.price, addons.price), \n" +
            "                'priceUpdate', package_item_addon_prices.price, \n" +
            "                'totalAmount', package_item_addons.total_amount))  AS addonJson\n" +
            "        from \n" +
            "            {h-schema}package_mappings\n" +
            "            join {h-schema}package_items on package_mappings.package_item_id = package_items.id \n" +
            "            join {h-schema}package_item_addons ON package_items.id = package_item_addons.package_item_id\n" +
            "            join {h-schema}addons ON addons.id = package_item_addons.addon_id\n" +
            "            left join {h-schema}pricing_multi_plan ON pricing_multi_plan.id = package_item_addons.addon_plan_id\n" +
            "            left join {h-schema}package_item_addon_prices ON package_item_addon_prices.package_item_addon_id = package_item_addons.id\n" +
            "            left join (\n" +
            "                SELECT\n" +
            "                    package_item_addon_id,\n" +
            "                    ARRAY_AGG(coupon_id) AS couponIds\n" +
            "                FROM\n" +
            "                    {h-schema}package_item_addon_promotions\n" +
            "                WHERE\n" +
            "                    package_item_addon_id IS NOT NULL\n" +
            "                GROUP BY\n" +
            "                    package_item_addon_id) couponItem ON couponItem.package_item_addon_id = package_item_addons.id\n" +
            "        where \n" +
            "            package_mappings.package_id = :packageId\n" +
            "        group by package_items.id \n" +
            "    ),\n" +
            "    packageVariantId as (\n" +
            "        select \n" +
            "            package_items.id as package_item_id,\n" +
            "            cast((package_items.metadata ->> 'variantId') as bigint) AS variantId\n" +
            "        from {h-schema}package_mappings\n" +
            "            join {h-schema}package_items on package_mappings.package_item_id = package_items.id \n" +
            "        where \n" +
            "            package_mappings.package_id = :packageId\n" +
            "    )\n" +
            "select \n" +
            "    packItem.id as packageItemId, \n" +
            "    pricing.id as pricingId, \n" +
            "    pricing.pricing_name as pricingName, \n" +
            "    pricing.is_one_time as isOneTime,  \n" +
            "    services.id as serviceId, \n" +
            "    services.service_name as serviceName, \n" +
            "    pmp.id as multiPlanId, \n" +
            "    pmp.circle_type as cycleType, \n" +
            "    pmp.payment_cycle as paymentCycle, \n" +
            "    provider.name as providerName, \n" +
            "    packItem.total_amount as originPrice, \n" +
            "    case \n" +
            "       when packItemPrice.id is not null then packItemPrice.price \n" +
            "    end as priceUpdate, \n" +
            "    categories.categories_id_migration as categoriesIdMigration,\n" +
            "    variant.id as variantId,\n" +
            "    variant.full_name as variantName,\n" +
            "    text(coalesce(packItemAddon.addonJson, cast('[]' as jsonb))) as lstAddonJson \n" +
            "from {h-schema}package_mappings as packMapping \n" +
            "    join {h-schema}package_items as packItem on packMapping.package_item_id = packItem.id \n" +
            "    join {h-schema}packages on packages.id = packMapping.package_id \n" +
            "    left join {h-schema}package_item_prices as packItemPrice on packItemPrice.package_item_id = packItem.id \n" +
            "    left join {h-schema}pricing_multi_plan as pmp on pmp.id = packItem.plan_id \n" +
            "    left join {h-schema}pricing on pricing.id = pmp.pricing_id \n" +
            "    left join {h-schema}services on pricing.service_id = services.id \n" +
            "    left join packageVariantId on packageVariantId.package_item_id = packItem.id \n" +
            "    left join {h-schema}variant on packageVariantId.variantId = variant.id\n" +
            "    left join {h-schema}categories on services.categories_id = categories.id\n" +
            "    left join {h-schema}users as provider on provider.id = pricing.created_by \n" +
            "    left join packItemAddon on packItemAddon.id = packItem.id \n" +
            "where \n" +
            "    packMapping.package_id is not null \n" +
            "    and packages.deleted_flag = 1 and packages.visibility = 1 \n" +
            "    and pricing.deleted_flag = 1 and pricing.approve = 1 and pricing.status = 1 \n" +
            "    and packMapping.package_id = :packageId";

    public static final String GET_LIST_PACKAGE_PRODUCT_ITEM_BY_PACKAGE_ID =
        "with packItemAddon as ( \n" +
            "select package_items.id, \n" +
            "    jsonb_agg(json_build_object( \n" +
            "    'id', package_item_addons.id, \n" +
            "    'totalAmount', package_item_addons.total_amount))  AS addonJson \n" +
            "    from {h-schema}package_items \n" +
            "    left join {h-schema}package_item_addons ON package_items.id = package_item_addons.package_item_id \n" +
            "    group by package_items.id \n" +
            ") \n" +
        "select \n" +
            "    packItem.id as packageItemId, \n" +
            "    variant.id as variantId, \n" +
            "    variant.full_name as variantName, \n" +
            "    pricing.id as pricingId, \n" +
            "    pricing.pricing_name as pricingName, \n" +
            "    pricing.is_one_time as isOneTime,  \n" +
            "    services.id as serviceId, \n" +
            "    services.service_name as serviceName, \n" +
            "    pmp.id as multiPlanId, \n" +
            "    pmp.circle_type as cycleType, \n" +
            "    pmp.payment_cycle as paymentCycle, \n" +
            "    provider.name as providerName, \n" +
            "    packItem.total_amount as originPrice, \n" +
            "    case \n" +
            "       when packItemPrice.id is not null then packItemPrice.price \n" +
            "    end as priceUpdate, \n" +
            "    categories.categories_id_migration as categoriesIdMigration,\n" +
            "    text(coalesce(packItemAddon.addonJson, cast('[]' as jsonb))) as lstAddonJson \n" +
            "from {h-schema}package_mappings as packMapping \n" +
            "    join {h-schema}package_items as packItem on packMapping.package_item_id = packItem.id \n" +
            "    join {h-schema}packages on packages.id = packMapping.package_id \n" +
            "    left join {h-schema}package_item_prices as packItemPrice on packItemPrice.package_item_id = packItem.id \n" +
            "    left join {h-schema}variant on variant.id = cast((packItem.metadata->>'variantId') as int8) \n" +
            "    left join {h-schema}pricing_multi_plan as pmp on pmp.id = packItem.plan_id \n" +
            "    left join {h-schema}pricing on pricing.id = pmp.pricing_id \n" +
            "    left join {h-schema}services on pricing.service_id = services.id \n" +
            "    left join {h-schema}categories on services.categories_id = categories.id\n" +
            "    left join {h-schema}users as provider on provider.id = pricing.created_by \n" +
            "    left join packItemAddon on packItemAddon.id = packItem.id \n" +
            "where \n" +
            "    packMapping.package_id is not null \n" +
            "    and packages.deleted_flag = 1 and packages.visibility = 1 \n" +
            "    and pricing.deleted_flag = 1 and pricing.approve = 1 and pricing.status = 1 \n" +
            "    and packMapping.package_id = :packageId";

    public static final String GET_SPDV_BUNDLING =
        "with filtered_services as (\n" +
            "  select id from services s\n" +
            "  where status = 1 and deleted_flag = 1\n" +
            "   and ('ALL' similar to (replace(replace(BTRIM(:customerTypes, '[]'), ',', '|'), '\\', ''))\n" +
            "	    or s.customer_type_code similar to '%('|| replace(replace(BTRIM(:customerTypes, '[]'),',', '|'),'\\' , '' )||')%')\n" +
            "	and (:classification = 0 or s.classification = :classification)	\n" +
            "	and (-1 = :serviceType or s.on_os_type = :serviceType)\n" +
            "  and EXISTS (\n" +
            "	select 1\n" +
            "		from pricing p\n" +
            "		where p.service_id = s.id\n" +
            "			and p.deleted_flag = 1\n" +
            "			and p.status = 1\n" +
            "			and (\n" +
            "						:value = '' \n" +
            "						or (:isNameService = 1 AND s.service_name ILIKE ('%' || :value || '%'))\n" +
            "						or (:isNamePricing = 1 AND p.pricing_name ILIKE ('%' || :value || '%'))\n" +
            "					)\n" +
            "	)\n" +
            "),\n" +
            "service_categories as (\n" +
            "  select\n" +
            "    service_id,\n" +
            "    jsonb_agg(categories_id) as category_ids\n" +
            "  from {h-schema}mapping_services_categories\n" +
            "  where service_id is not null\n" +
            "	group by service_id \n" +
            "),\n" +
            "newest_addon as (  \n" +
            "		select max (id) as addon_id from {h-schema}addons \n" +
            "		where deleted_flag = 1 \n" +
            "		and status = 1 \n" +
            "		and approve = 1 \n" +
            "		group by addon_draft_id  \n" +
            "	),  \n" +
            "	newest_pricing as (\n" +
            "		select max (id) as pricing_id from {h-schema}pricing\n" +
            "		where deleted_flag = 1\n" +
            "		and status = 1\n" +
            "		group by pricing_draft_id\n" +
            "	),\n" +
            "	newest_variant as (\n" +
            "		select max (id) as variant_id from {h-schema}variant\n" +
            "		where deleted_flag = 1\n" +
            "		and status = 0\n" +
            "		group by variant_draft_id\n" +
            "	),\n" +
            "	pricing_addon_price AS (  \n" +
            "		SELECT addons_id, \n" +
            "			jsonb_agg(json_build_object( \n" +
            "			'unitTo', unit_limited.unit_to,  \n" +
            "			'unitFrom', unit_limited.unit_from, \n" +
            "			'price', unit_limited.price ) ORDER BY unit_limited.unit_from) AS plan 	\n" +
            "		FROM {h-schema}unit_limited  \n" +
            "		WHERE addons_id IS NOT NULL AND  \n" +
            "				subscription_setup_fee_id IS NULL   \n" +
            "			GROUP BY addons_id\n" +
            "	), \n" +
            "	pricing_multiplan_price AS (  \n" +
            "		SELECT pricing_multi_plan_id, \n" +
            "			jsonb_agg(json_build_object( \n" +
            "			'unitTo', pricing_plan_detail.unit_to,  \n" +
            "			'unitFrom', pricing_plan_detail.unit_from, \n" +
            "			'price', pricing_plan_detail.price ) ORDER BY pricing_plan_detail.unit_from) AS plan   \n" +
            "		FROM {h-schema}pricing_plan_detail  \n" +
            "		WHERE subscription_setup_fee_id IS NULL  \n" +
            "		GROUP BY pricing_multi_plan_id \n" +
            "	),\n" +
            "	pricingMultPlanAddonItem as (\n" +
            "		select tbl.addon_id,\n" +
            "		jsonb_agg( json_build_object( \n" +
            "			'id', tbl.id,  \n" +
            "			'paymentCycle', tbl.payment_cycle,\n" +
            "			'pricingPlan', tbl.pricing_plan,\n" +
            "			'cycleType', tbl.circle_type,\n" +
            "			'numberOfCycle', tbl.number_of_cycles,\n" +
            "			'price', tbl.price,\n" +
            "			'unitLimiteds', tbl.plan\n" +
            "			 )) AS multiPlans \n" +
            "			 from newest_addon\n" +
            "			 left join (\n" +
            "					select distinct \n" +
            "					tmpAddon.id as addon_id, \n" +
            "					pricing_multi_plan.id, \n" +
            "					pricing_multi_plan.payment_cycle,	\n" +
            "					pricing_multi_plan.circle_type,	\n" +
            "					pricing_multi_plan.pricing_plan,	\n" +
            "					pricing_multi_plan.number_of_cycles,	\n" +
            "					pricing_multi_plan.price,\n" +
            "					pricing_multiplan_price.plan	\n" +
            "					from \n" +
            "					(\n" +
            "						select addons.id, pricing_multi_plan.id as multi_plan_id from {h-schema}addons \n" +
            "						left join {h-schema}pricing_multi_plan_addon ON  pricing_multi_plan_addon.addon_id = addons.id\n" +
            "						left join {h-schema}pricing_multi_plan addon_multi_plan on addon_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_addon_id \n" +
            "						left join {h-schema}pricing_multi_plan pricing_multi_plan on pricing_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_id\n" +
            "						where addon_multi_plan.id is null and addons.bonus_type = 1\n" +
            "						union \n" +
            "						select addons.id, pricing_multi_plan.id as multi_plan_id from {h-schema}addons \n" +
            "						left join {h-schema}pricing_multi_plan_addon ON  pricing_multi_plan_addon.addon_id = addons.id\n" +
            "						left join {h-schema}pricing_multi_plan addon_multi_plan on addon_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_addon_id \n" +
            "						left join {h-schema}pricing_multi_plan pricing_multi_plan on pricing_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_id\n" +
            "						where addon_multi_plan.id is not null and addons.bonus_type = 1\n" +
            "					) tmpAddon\n" +
            "					left join {h-schema}pricing_multi_plan ON tmpAddon.multi_plan_id = pricing_multi_plan.id\n" +
            "					left join pricing_multiplan_price ON pricing_multiplan_price.pricing_multi_plan_id = pricing_multi_plan.id\n" +
            "			) tbl ON newest_addon.addon_id = tbl.addon_id \n" +
            "			group by tbl.addon_id\n" +
            "	),\n" +
            "	addonItem as ( \n" +
            "		select tbl.pricing_id,  \n" +
            "			jsonb_agg(json_build_object( \n" +
            "			'id', tbl.id,  \n" +
            "			'addonName', tbl.name,\n" +
            "			'isOneTime', tbl.bonus_type,\n" +
            "			'price', tbl.price,\n" +
            "			'isRequired', tbl.is_required = 1,\n" +
            "			'addonPlan', tbl.pricing_plan,\n" +
            "			'unitLimiteds', case when tbl.bonus_type = 0 then tbl.multiPlans\n" +
            "											else null\n" +
            "										end,\n" +
            "			'multiPlans', case when tbl.bonus_type = 1 then tbl.multiPlans\n" +
            "											else null\n" +
            "										end\n" +
            "			 )) AS addon_item \n" +
            "			 from newest_addon\n" +
            "			left join (\n" +
            "				select addons.id, addons.name, pricing_addons.is_required, addons.bonus_type, addons.pricing_plan, pricing_addons.pricing_id,\n" +
            "					pricing_addon_price.plan as multiPlans, addons.price	from {h-schema}addons \n" +
            "				left join {h-schema}pricing_addons ON  addons.id = pricing_addons.addons_id\n" +
            "				left join pricing_addon_price on pricing_addon_price.addons_id = addons.id\n" +
            "				union\n" +
            "				select addons.id, addons.name, pricing_multi_plan_addon.is_require as is_required, addons.bonus_type, null as pricing_plan, pricing_multi_plan.pricing_id,\n" +
            "					pricingMultPlanAddonItem.multiPlans, addons.price from {h-schema}addons \n" +
            "				left join {h-schema}pricing_multi_plan_addon ON  pricing_multi_plan_addon.addon_id = addons.id\n" +
            "				left join {h-schema}pricing_multi_plan addon_multi_plan on addon_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_addon_id \n" +
            "				left join {h-schema}pricing_multi_plan pricing_multi_plan on pricing_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_id\n" +
            "				left join pricingMultPlanAddonItem on pricingMultPlanAddonItem.addon_id = addons.id\n" +
            "				where addon_multi_plan.id is null and addons.bonus_type = 1\n" +
            "				union \n" +
            "				select addons.id, addons.name, pricing_multi_plan_addon.is_require as is_required, addons.bonus_type, null as pricing_plan, pricing_multi_plan.pricing_id,\n" +
            "					pricingMultPlanAddonItem.multiPlans, addons.price from {h-schema}addons \n" +
            "				left join {h-schema}pricing_multi_plan_addon ON  pricing_multi_plan_addon.addon_id = addons.id\n" +
            "				left join {h-schema}pricing_multi_plan addon_multi_plan on addon_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_addon_id \n" +
            "				left join {h-schema}pricing_multi_plan pricing_multi_plan on pricing_multi_plan.id = pricing_multi_plan_addon.pricing_multi_plan_id\n" +
            "				left join pricingMultPlanAddonItem on pricingMultPlanAddonItem.addon_id = addons.id\n" +
            "				where addon_multi_plan.id is not null and addons.bonus_type = 1\n" +
            "			) tbl ON newest_addon.addon_id = tbl.id \n" +
            "			where tbl.pricing_id is not null\n" +
            "		group by tbl.pricing_id \n" +
            "	),\n" +
            "	pricingMultPlanItem as (\n" +
            "		select tbl.pricing_id,\n" +
            "		jsonb_agg( json_build_object( \n" +
            "			'id', tbl.id,  \n" +
            "			'paymentCycle', tbl.payment_cycle,\n" +
            "			'pricingPlan', tbl.pricing_plan,\n" +
            "			'cycleType', tbl.circle_type,\n" +
            "			'numberOfCycles', tbl.number_of_cycles,\n" +
            "			'price', tbl.price,\n" +
            "			'isDefault', tbl.default_circle = 1,\n" +
            "			'unitLimiteds', tbl.plan\n" +
            "			 )) AS multiPlans \n" +
            "			 from newest_pricing\n" +
            "			 join (\n" +
            "					select distinct \n" +
            "					pricing.id as pricing_id, \n" +
            "					pricing_multi_plan.id, \n" +
            "					pricing_multi_plan.payment_cycle,	\n" +
            "					pricing_multi_plan.circle_type,	\n" +
            "					pricing_multi_plan.pricing_plan,	\n" +
            "					pricing_multi_plan.number_of_cycles,\n" +
            "					pricing_multi_plan.default_circle,\n" +
            "					pricing_multi_plan.price,\n" +
            "						pricing_multiplan_price.plan,	\n" +
            "								 CASE\n" +
            "										 WHEN (pricing_multi_plan.circle_type = 0) THEN concat(pricing_multi_plan.payment_cycle, ' ngày')\n" +
            "										 WHEN (pricing_multi_plan.circle_type = 1) THEN concat(pricing_multi_plan.payment_cycle, ' tuần')\n" +
            "										 WHEN (pricing_multi_plan.circle_type = 2) THEN concat(pricing_multi_plan.payment_cycle, ' tháng')\n" +
            "										 WHEN (pricing_multi_plan.circle_type = 3) THEN concat(pricing_multi_plan.payment_cycle, ' năm')\n" +
            "					ELSE NULL::::text\n" +
            "								 END AS payment_cycle_text\n" +
            "					from {h-schema}pricing \n" +
            "					left join {h-schema}pricing_multi_plan ON pricing.id = pricing_multi_plan.pricing_id\n" +
            "					left join pricing_multiplan_price ON pricing_multiplan_price.pricing_multi_plan_id = pricing_multi_plan.id\n" +
            "					\n" +
            "			) tbl ON newest_pricing.pricing_id = tbl.pricing_id \n" +
            "			where (:paymentCycle = 'ALL' or :paymentCycle = tbl.payment_cycle_text) \n" +
            "			group by tbl.pricing_id\n" +
            "	),\n" +
            "	pricingItemService as (\n" +
            "		select tbl.service_id,\n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "			'id', tbl.id,  \n" +
            "			'pricingName', tbl.pricing_name,\n" +
            "			'isOneTime', tbl.is_one_time,\n" +
            "			'multiPlans', tbl.multiPlans,\n" +
            "			'lstAddon', COALESCE(tbl.addon_item, '[]'::::jsonb)\n" +
            "			 )) as text) AS pricing_item \n" +
            "			 from newest_pricing\n" +
            "			join (\n" +
            "				select pricing.id, \n" +
            "					pricing.pricing_name, \n" +
            "					pricing.service_id,\n" +
            "					pricing.is_one_time,\n" +
            "					pricingMultPlanItem.multiPlans,\n" +
            "					addonItem.addon_item\n" +
            "				from {h-schema}pricing \n" +
            "				left join pricingMultPlanItem ON pricing.id = pricingMultPlanItem.pricing_id\n" +
            "				left join addonItem ON addonItem.pricing_id = pricing.id\n" +
            "			) tbl ON newest_pricing.pricing_id = tbl.id \n" +
            "			group by tbl.service_id\n" +
            "	),\n" +
            "	pricingItemVariant as (\n" +
            "		select tbl.variant_id,\n" +
            "		jsonb_agg( json_build_object( \n" +
            "			'id', tbl.id,  \n" +
            "			'name', tbl.pricing_name,\n" +
            "			'isOneTime', tbl.is_one_time,\n" +
            "			'multiPlans', tbl.multiPlans,\n" +
            "			'lstAddon', COALESCE(tbl.addon_item, '[]'::::jsonb)\n" +
            "			 )) AS pricing_item \n" +
            "			 from newest_pricing\n" +
            "			 join (\n" +
            "					select distinct \n" +
            "					pricing.id, \n" +
            "					pricing.pricing_name, \n" +
            "					variant.id as variant_id,\n" +
            "					pricing.is_one_time,\n" +
            "					pricingMultPlanItem.multiPlans,\n" +
            "					addonItem.addon_item\n" +
            "					from {h-schema}variant \n" +
            "					left join filtered_services ON filtered_services.id = variant.service_id\n" +
            "					join {h-schema}pricing ON filtered_services.id = pricing.service_id\n" +
            "					left join {h-schema}pricing_variant ON pricing.id = pricing_variant.pricing_id \n" +
            "					left join pricingMultPlanItem ON pricing.id = pricingMultPlanItem.pricing_id\n" +
            "					left join addonItem ON addonItem.pricing_id = pricing.id\n" +
            "					where pricing_variant.pricing_id is not null or pricing.variant_apply = 1\n" +
            "			) tbl ON newest_pricing.pricing_id = tbl.id \n" +
            "			where tbl.variant_id is not null\n" +
            "			group by tbl.variant_id\n" +
            "	),\n" +
            "	attributeService as (\n" +
            "		SELECT\n" +
            "			mas.service_id,\n" +
            "			jsonb_agg(\n" +
            "				jsonb_build_object(\n" +
            "				'id', a.id,\n" +
            "				'attributeName', a.name,\n" +
            "				'attributeValues', a.attributes_value::::jsonb\n" +
            "			)) AS attributes\n" +
            "		FROM {h-schema}mapping_attributes_service mas\n" +
            "		JOIN {h-schema}attributes a ON mas.attributes_id = a.id\n" +
            "		GROUP BY mas.service_id\n" +
            "	),\n" +
            "	variantItem as (\n" +
            "		select tbl.service_id,\n" +
            "		cast(jsonb_agg(json_build_object( \n" +
            "			'id', tbl.id,  \n" +
            "			'variantName', tbl.full_name,\n" +
            "			'variantDefault', tbl.variant_default = 1,\n" +
            "				 'attributeValues', tbl.attributes_value::::jsonb,\n" +
            "			'pricingItems', COALESCE(tbl.pricing_item, '[]'::::jsonb) \n" +
            "			 )) as text) AS variant_item \n" +
            "			 from newest_variant\n" +
            "			left join (\n" +
            "				select variant.id, variant.full_name, variant.variant_default,\n" +
            "					pricingItemVariant.pricing_item, variant.attributes_value, variant.service_id from variant \n" +
            "				left join pricingItemVariant ON pricingItemVariant.variant_id = variant.id \n" +
            "			) tbl ON newest_variant.variant_id = tbl.id \n" +
            "			where tbl.pricing_item is not null\n" +
            "			group by tbl.service_id\n" +
            "	)\n" +
            "	select \n" +
            "	s.id,\n" +
            "	s.service_name as serviceName,\n" +
            "	u.name as providerName,\n" +
            "	maf.name as manufacturerName,\n" +
            "	variantItem.variant_item as variants,\n" +
            "	s.created_at as createdAt,\n" +
            "	attributeService.attributes::::text as attributes,\n" +
            "	pricingItemService.pricing_item as pricingItems,\n" +
            "	coalesce(fa.file_path, fa.ext_link) as icon\n" +
            "	from filtered_services\n" +
            "	JOIN {h-schema}services s ON filtered_services.id = s.id\n" +
            "	LEFT JOIN {h-schema}file_attach fa ON s.id = fa.service_id AND fa.object_type = 0\n" +
            "	left join {h-schema}manufacturer maf ON maf.id = s.manufacture\n" +
            "	left join {h-schema}users u ON s.user_id = u.id\n" +
            "	LEFT JOIN service_categories sc ON sc.service_id = s.id \n" +
            "	left join variantItem ON s.id = variantItem.service_id\n" +
            "	left join pricingItemService ON s.id = pricingItemService.service_id\n" +
            "	left join attributeService ON s.id = attributeService.service_id\n" +
            "	where\n" +
            "	s.status = 1 \n" +
            "	and s.deleted_flag = 1\n" +
            "	and (variantItem.variant_item is not null or pricingItemService.pricing_item is not null )\n" +
            "	and (-1 = :providerId or u.id = :providerId)\n" +
            "	and (:manufactureName = '' OR maf.name ILIKE ('%' || :manufactureName || '%'))\n" +
            "	and (-1 IN (:categoryIds) OR EXISTS (\n" +
            "				SELECT 1 FROM jsonb_array_elements_text(sc.category_ids) AS cat(id)\n" +
            "				WHERE cat.id::::bigint IN (:categoryIds)\n" +
            "))";

    public static final String COUNT_SPDV_BUNDLING =
        "select count(*) from ( " +  GET_SPDV_BUNDLING + " ) a";

    public static final String IS_LATEST_VERSION =
        "SELECT :solutionId = max(id) FROM {h-schema}product_solutions WHERE draft_id = :draftId GROUP BY draft_id";

    public static final String GET_SEARCHED_COMBINED_SOLUTION_PACKAGE_BY_UNIQUE_IDS =
       "with solutionFeatures as ( \n" +
            "   select \n" +
            "       object_id as solution_id, \n" +
            "       string_agg(features.name, ';') as feature_name, \n" +
            "       string_agg(features.description, ';') as feature_desc \n" +
            "    from {h-schema}feature_mappings as featMapping \n" +
            "             left join {h-schema}features on featMapping.feature_id = features.id \n" +
            "    where object_type = 'SOLUTION' and object_id is not null \n" +
            "    group by featMapping.object_id), \n" +
            "packageFeatures as ( \n" +
            "   select object_id as package_id, \n" +
            "          string_agg(features.name, ';') as feature_name, \n" +
            "          string_agg(features.description, ';') as feature_desc \n" +
            "   from {h-schema}feature_mappings as featMapping \n" +
            "            left join {h-schema}features on featMapping.feature_id = features.id \n" +
            "   where object_type = 'PACKAGE' and object_id is not null \n" +
            "   group by featMapping.object_id) \n" +
       "select queryPack.*, originalIdOrdering.original_order as related \n" +
            "from ( \n" +
            "-- trường hợp giải pháp thì thông tin giá/chiết khấu là của gói mặc định --\n" +
            "select \n" +
            "      solution.id as id, \n" +
            "      (solution.id * 10000 + 5) as uniqueId, \n" +
            "      solution.name as name, \n" +
            "      solution.draft_id as draftId, \n" +
            "      replace(replace(replace(solution.customer_types, 'ENTERPRISE', 'KHDN'), 'HOUSE_HOLD', 'HKD'), 'PERSONAL', 'CN') as customer_types, \n" +
            "      packages.id as defaultPricingId, \n" +
            "      packages.name as defaultPricingName, \n" +
            "      coalesce(packages.price_from, 0.0) as price, \n" +
            "      provider.id as provider_id, \n" +
            "      coalesce(provider.name, concat_ws(' ', provider.last_name, provider.first_name)) as providerName, \n" +
            "      -- bố cục giải pháp -- \n" +
            "      solution.descriptions as description, \n" +
            "      -- mô tả tính năng giải pháp -- \n" +
            "      solutionFeatures.feature_name as features, \n" +
            "      -- danh mục giải pháp -- \n" +
            "      solutionCategories.category as categories,  \n" +
            "      'SOLUTION' as suggestionType,  \n" +
            "      solution.avatar_url as imageUrl,  \n" +
            "      solution.created_at as createdAt \n" +
            "from {h-schema}product_solutions as solution \n" +
            "    join (select draft_id, max(id) as latest_id \n" +
            "          from {h-schema}product_solutions \n" +
            "          where deleted_flag = 1 and visibility = 1 \n" +
            "          group by draft_id \n" +
            "     ) latestSolution on latestSolution.draft_id = solution.draft_id and latestSolution.latest_id = solution.id \n" +
            "    left join {h-schema}solution_packages as solPack on solPack.solution_id = solution.id \n" +
            "    left join {h-schema}package_draft as packDraft on packDraft.id = solPack.package_draft_id \n" +
            "    left join ( select draft_id, max(id) as latest_id \n" +
            "               from {h-schema}packages \n" +
            "               where deleted_flag = 1 and visibility = 1 \n" +
            "               group by draft_id \n" +
            "    ) as latestPackage on packDraft.id = latestPackage.draft_id \n" +
            "    left join {h-schema}packages on packages.draft_id = latestPackage.draft_id and packages.id = latestPackage.latest_id \n" +
            "    left join {h-schema}users as provider on provider.id = solution.created_by \n" +
            "   -- tính năng giải pháp -- \n" +
            "    left join solutionFeatures on solutionFeatures.solution_id = solution.id \n" +
            "   -- danh mục giải pháp -- \n" +
            "        left join lateral ( \n" +
            "           select solution.id, string_agg(categories.name, ';') as category \n" +
            "           from {h-schema}categories \n" +
            "           where id = any(solution.category_ids)\n" +
            "           group by solution.id \n" +
            "   ) as solutionCategories on solutionCategories.id = solution.id \n" +
            "where " +
            "   solution.deleted_flag = 1 and \n" +
            "   solution.visibility = 1 and \n" +
            "   (solPack.id is null or solPack.default_package is true) and -- view cả các solution có/không có package default -- \n" +
            "   (solution.id * 10000 + 5) = any(:solutionPackageUnqIdsArr) \n" +
            "union \n" +
            "-- trường hợp gói không thuộc giải pháp nào -- \n" +
            "select \n" +
            "      packages.id as id, \n" +
            "      (packages.id * 10000 + 6) as uniqueId," +
            "      packages.name as name, \n" +
            "      packages.draft_id as draftId, \n" +
            "      '[\"KHDN\",\"HKD\", \"CN\"]' as customerType, \n" +
            "      null as pricingDefaultId, \n" +
            "      null as pricingDefaultName, \n" +
            "      coalesce(packages.price_from, 0.0) as price, \n" +
            "      provider.id as provider_id, \n" +
            "      coalesce(provider.name, \n" +
            "      concat_ws(' ', provider.last_name, provider.first_name)) as providerName, \n" +
            "      -- mô tả gói bundling \n" +
            "      packages.descriptions as description, \n" +
            "      -- tính năng gói bundling \n" +
            "      packageFeatures.feature_name as features, \n" +
            "      -- danh mục giải pháp -- \n" +
            "      null as categories,  \n" +
            "      'PACKAGE' as suggestionType,  \n" +
            "      packages.icon_url as imageUrl,  \n" +
            "      packages.created_at as createdAt \n" +
            "from {h-schema}packages \n" +
            "      join (select draft_id, max(id) as latest_id \n" +
            "            from {h-schema}packages \n" +
            "            where deleted_flag = 1 and visibility = 1 \n" +
            "            group by draft_id" +
            "      ) as latestPackage on latestPackage.draft_id = packages.draft_id and latestPackage.latest_id = packages.id \n" +
            "      left join {h-schema}solution_packages as solPack on solPack.package_id = packages.id \n" +
            "      left join {h-schema}users as provider on provider.id = packages.created_by \n" +
            "       -- tính năng gói bundling -- \n" +
            "      left join packageFeatures on packageFeatures.package_id = packages.id \n" +
            "where packages.deleted_flag = 1 \n" +
            "   and packages.visibility = 1 \n" +
            "   and solPack.id is null \n" +
            "   and (packages.id * 10000 + 6) = any(:solutionPackageUnqIdsArr) \n" +
            ") as queryPack \n" +
            "    left join ( \n" +
            "       select res.id, res.original_order \n" +
            "           from unnest(cast(('{' || text(:solutionPackageUnqIdsArr) || '}') as int8[])) with ordinality as res(id, original_order) \n" +
            "    ) as originalIdOrdering on originalIdOrdering.id = queryPack.uniqueId ";

    public static final String GET_SUGGEST_GROUP_DETAIL =
        "select \n" +
            "    solution.id, \n" +
            "    solution.draft_id as draftId, \n" +
            "    solution.name as name, \n" +
            "    provider.name as providerName, \n" +
            "    cast(array_agg(categories.name) as varchar) as categoryNames \n" +
            "from {h-schema}product_solutions solution \n" +
            "    left join {h-schema}users provider on provider.id = solution.provider_id \n" +
            "    left join {h-schema}categories on categories.id = any(solution.category_ids) \n" +
            "where solution.draft_id = :solutionDraftId and solution.deleted_flag = 1 and solution.visibility = 1 \n" +
            "group by solution.id, solution.draft_id, solution.name, provider.name \n" +
            "order by solution.id desc limit 1";
}
