package com.constant.sql;

/**
 * * SQLProvince
 *
 * <AUTHOR>
 *  11/11/2021
 */
public class SQLProvince {
    public static final String GET_LIST_PROVINCE_FILTER_INFO =
        "SELECT "
            + " p.id, p.name "
            + "FROM "
            + " {h-schema}province p "
            + " WHERE "
            + " (-1 IN (:provinceIds) OR p.id IN (:provinceIds)) "
            + " AND (:provinceName = '' OR p.name ILIKE ('%' || :provinceName || '%')) "
            + " AND p.id NOT IN (:provinceIdsNotIn) "
            + " ORDER BY p.display_order ";

    public static final String JDBC_SYNC_ADDRESS_DETAIL =
        "select vnpt_dev.func_update_full_address(?)";

    public static final String GET_PROVINCE_BY_ID =
        "select id, name, code from {h-schema}province where id = :provinceId and deleted_flag = 1";

    public static final String GET_DISTRICT_BY_PROVINCE_ID_AND_ID =
        "select \n" +
            "    province_id as provinceId, \n" +
            "    id as districtId, \n" +
            "    name as districtName, \n" +
            "    code as code \n" +
            "from {h-schema}district \n" +
            "where \n" +
            "    province_id = :provinceId and \n" +
            "    id = :districtId and \n" +
            "    deleted_flag = 1 ";

    public static final String GET_WARD_BY_PROVINCE_ID_AND_DISTRICT_ID_AND_ID =
        "select \n" +
            "    province_id as provinceId, \n" +
            "    district_id as districtId, \n" +
            "    id as wardId, \n" +
            "    name as wardName, \n" +
            "    code as code \n" +
            "from {h-schema}ward \n" +
            "where " +
            "    province_id = :provinceId and \n" +
            "    district_id = :districtId and \n" +
            "    id = :wardId and \n" +
            "    deleted_flag = 1 \n";

    public static final String GET_STREET_BY_PROVINCE_ID_AND_WARD_ID_AND_ID =
        "SELECT \n" +
            "    id AS id, \n" +
            "    name AS name, \n" +
            "    province_id AS provinceId, \n" +
            "    ward_id AS wardId \n" +
            "FROM \n" +
            "    {h-schema}street \n" +
            "WHERE \n" +
            "    province_id = :provinceId and \n" +
            "    ward_id = :wardId and \n" +
            "    id = :streetId AND \n" +
            "    deleted_flag = 1 ";
}
