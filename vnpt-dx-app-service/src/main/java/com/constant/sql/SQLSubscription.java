package com.constant.sql;

/**
 * * SQLSubscription
 *
 * <AUTHOR>
 *  4/19/2021 1:52 PM
 */
public final class SQLSubscription {

    public static final String GET_COUPON_IN_PRICING =
        "select coupon_id from {h-schema}subscription_pricing_coupon where subscription_id = :subscriptionId";

    public static final String GET_COUPON_MC_IN_PRICING =
        "select mc_id as mcId, acitivity_idx as activityIdx  from {h-schema}subscription_pricing_mc where subscription_id = :subscriptionId";

    public static final String GET_COUPON_MC_IN_TOTAL =
        "select mc_id as mcId, acitivity_idx as activityIdx  from {h-schema}subscription_mc where subscription_id = :subscriptionId";

    public static final String GET_COUPON_IN_COMBO =
        "select coupon_id from {h-schema}subscription_combo_coupon where subscription_id = :subscriptionId";

    public static final String GET_COUPON_MC_IN_COMBO =
        "select mc_id as mcId, acitivity_idx as activityIdx from {h-schema}subscription_combo_mc where subscription_id = :subscriptionId";

    public static final String GET_COUPON_MC_IN_ADDON =
        "select mc_id as mcId, acitivity_idx as activityIdx, addon_id as addonId  from {h-schema}subscription_addon_mc where subscription_id = :subscriptionId";

    public static final String GET_ADDON_IN_SUB =
        "select  \n" +
            "		sac.coupon_id as couponId,  \n" +
            "		sa.addons_id as id,  \n" +
            "		COALESCE(pmpa.pricing_plan, a.pricing_plan) as pricingPlan,  \n" +
            "		COALESCE(pmpa.price, a.price) as price,  \n" +
            "		pmpa.payment_cycle as paymentCycle,  \n" +
            "       CASE" +
            "           WHEN pmpa.circle_type = 0 THEN 'DAILY'" +
            "           WHEN pmpa.circle_type = 1 THEN 'WEEKLY' " +
            "           WHEN pmpa.circle_type = 2 THEN 'MONTHLY' " +
            "           WHEN pmpa.circle_type = 3 THEN 'YEARLY' " +
            "       END AS cycleType, " +
            "		COALESCE(pmpa.free_quantity,a.free_quantity) as freeQuantity,\n" +
            "		COALESCE(upmp.name, u.name) as unitName,\n" +
            "		sa.pricing_multi_plan_id as addonMultiPlanId,  \n" +
            "		sa.used_quantity as quantity  \n" +
            "		from {h-schema}subscription_addons sa  \n" +
            "		left join {h-schema}subscription_addon_coupon sac ON sa.id = sac.subscription_addon_id \n" +
            "		left join {h-schema}addons a ON a.id = sa.addons_id\n" +
            "		left join {h-schema}units u ON u.id = a.unit_id\n" +
            "		left join {h-schema}pricing_multi_plan pmpa ON pmpa.id = sa.pricing_multi_plan_id\n" +
            "		left join {h-schema}units upmp ON upmp.id = pmpa.unit_id\n" +
            "		where sa.subscription_id = :subscriptionId" +
            " UNION\n" +
            "select  \n" +
            "		sac.coupon_id as couponId,  \n" +
            "		sa.addon_id as id,  \n" +
            "		COALESCE(pmpa.pricing_plan, a.pricing_plan) as pricingPlan,  \n" +
            "		COALESCE(pmpa.price, a.price) as price,  \n" +
            "		pmpa.payment_cycle as paymentCycle,  \n" +
            "			 CASE \n" +
            "					 WHEN pmpa.circle_type = 0 THEN 'DAILY' \n" +
            "					 WHEN pmpa.circle_type = 1 THEN 'WEEKLY'  \n" +
            "					 WHEN pmpa.circle_type = 2 THEN 'MONTHLY'  \n" +
            "					 WHEN pmpa.circle_type = 3 THEN 'YEARLY'  \n" +
            "			 END AS cycleType,  \n" +
            "		COALESCE(pmpa.free_quantity,a.free_quantity) as freeQuantity,\n" +
            "		COALESCE(upmp.name, u.name) as unitName,\n" +
            "		sa.pricing_multi_plan_id as addonMultiPlanId,  \n" +
            "		sa.quantity as quantity  \n" +
            "		from {h-schema}subscription_combo_addon sa  \n" +
            "		left join {h-schema}subscription_combo_addon_coupon sac ON sa.id = sac.subscription_combo_addon_id \n" +
            "		left join {h-schema}addons a ON a.id = sa.addon_id\n" +
            "		left join {h-schema}units u ON u.id = a.unit_id\n" +
            "		left join {h-schema}pricing_multi_plan pmpa ON pmpa.id = sa.pricing_multi_plan_id\n" +
            "		left join {h-schema}units upmp ON upmp.id = pmpa.unit_id\n" +
            "		where sa.subscription_id = :subscriptionId";

    public static final String GET_SUB_HISTORY_BY_ID =
        "select \n" +
            "	subscription_id as subscriptionId,\n" +
            "	created_at as createdAt,\n" +
            "	content as content,\n" +
            "	content_type as contentType\n" +
            "from \n" +
            "	{h-schema}subscription_history\n" +
            "where \n" +
            "   content_type in (50,51,52,53,54,55,56,60,61) and\n" +
            "	subscription_id = :subId order by id desc";

    public static final String GET_SUB_HISTORY_BY_CART =
        "select \n" +
            "	subscription_id as subscriptionId,\n" +
            "	created_at as createdAt,\n" +
            "	content as content,\n" +
            "	content_type as contentType\n" +
            "from \n" +
            "	{h-schema}subscription_history\n" +
            "where \n" +
            "   content_type in (50,51,52,53,54,55,57,59,60,61) and\n" +
            "	cart_code = :cartCode \n" +
            "   order by id desc";

    public static final String GET_SUB_HISTORY_BY_CART_DEV =
        "select \n" +
            "	subscription_id as subscriptionId,\n" +
            "	created_at as createdAt,\n" +
            "	content as content,\n" +
            "	content_type as contentType\n" +
            "from \n" +
            "	{h-schema}subscription_history\n" +
            "where \n" +
            "   (content_type in (50,51,61) OR (content_type in (52,53,54,55,56,58,60) AND created_by in (:viewableDevIds))) and \n" +
            "	cart_code = :cartCode \n" +
            "order by id desc";

    public static final String UPDATE_SUB_CALLED_TRANS =
        "UPDATE \n" +
            "    Subscription subscription \n" +
            "SET \n" +
            "    subscription.calledTrans = :calledTransValue, \n" +
            "    subscription.modifiedAt = cast(:modifiedAt as timestamp) \n" +
            "WHERE \n" +
            "    subscription.id = :id ";

    public static final String GET_QUERY_SERVICE_DETAIL_SUB =
        "	select \n" +
            "	sub.id as id,\n" +
            "	b.id as billingId,\n" +
            "   true as isService, \n" +
            "   ser.product_type as productType, " +
            "	op.code as processStatus,\n" +
            "	op.id as processId,\n" +
            "   COALESCE(pmp.payment_cycle, p.payment_cycle) as paymentCycle, \n" +
            "   CASE" +
            "       WHEN COALESCE(pmp.circle_type, p.cycle_type) = 0 THEN 'DAILY'" +
            "       WHEN COALESCE(pmp.circle_type, p.cycle_type) = 1 THEN 'WEEKLY' " +
            "       WHEN COALESCE(pmp.circle_type, p.cycle_type) = 2 THEN 'MONTHLY' " +
            "       WHEN COALESCE(pmp.circle_type, p.cycle_type) = 3 THEN 'YEARLY' " +
            "   END AS cycleType, " +
            "   COALESCE(pmp.number_of_cycles, p.number_of_cycles) as numberOfCycles," +
            "   pmp.pricing_plan as pricingPlan," +
            "   p.pricing_type as pricingType," +
            "   b.status as billingStatus," +
            "   pmp.free_quantity as freeQuantity," +
            "   ui.name as unitName," +
            "    case when u.customer_type = 'CN' then concat(u.last_name,' ',u.first_name) \n" +
            "    else u.name end as customerName,\n" +
            "	u.phone_number AS phone,\n" +
            "	b.billing_code AS billingCode,\n" +
            "	sub.pricing_id AS pricingId,\n" +
            "	sub.pricing_multi_plan_id AS pricingMultiPlanId,\n" +
            "	sub.quantity AS quantity,\n" +
            "	sub.variant_draft_id AS variantDraftId,\n" +
            "	sub.variant_id AS variantId,\n" +
            "	sub.setup_fee_device AS setupFeeDevice,\n" +
            "	sub.service_draft_id AS serviceDraftId,\n" +
            "	sub.service_id AS serviceId,\n" +
            "	sub.os_3rd_status AS Os3rdStatus,\n" +
            "	sub.is_one_time AS isOneTime,\n" +
            "	sub.is_buy_service AS isBuyService,\n" +
            "	sub.is_only_service AS isOnlyService,\n" +
            "	sub.quantity_variant AS quantityVariant,\n" +
            "	sub.combo_plan_id AS comboPlanId,\n" +
            "	ser.installation_configuration AS installationConfiguration,\n" +
            "	sub.address, \n" +
            "	sub.payment_method as paymentMethod, \n" +
            "	ser.service_name as serviceName, \n" +
            "   CASE " +
            "       WHEN sub.status = 0 THEN 'FUTURE' " +
            "       WHEN sub.status = 1 THEN 'IN_TRIAL' " +
            "       WHEN sub.status = 2 THEN 'ACTIVE' " +
            "       WHEN sub.status = 3 THEN 'CANCELLED' " +
            "       WHEN sub.status = 4 THEN 'FINISHED' " +
            "   END as status, " +
            "	CASE\n" +
            "		WHEN provider.name IS NOT NULL THEN provider.name \n" +
            "		ELSE concat ( provider.last_name, ' ', provider.first_name ) \n" +
            "	END providerName,\n" +
            "	provider.id as providerId,\n" +
            "	u.id as userId,\n" +
            "	sub.created_at::::date + 15 as timeDelivery,\n" +
            "	sub.cancelled_time as cancelTime,\n" +
            "	sub.update_reason as updateReason,\n" +
            "	sub.message as message,\n" +
            "	CASE\n" +
            "		WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number \n" +
            "		ELSE u.tin \n" +
            "	END identityNo,\n" +
            "	ser.service_owner as owner \n" +
            "  from {h-schema}subscriptions sub \n" +
            "	left join {h-schema}billings b ON b.subscriptions_id = sub.id \n" +
            "	left join {h-schema}services ser ON sub.service_id = ser.id \n" +
            "	left join {h-schema}pricing p ON sub.pricing_id = p.id  \n" +
            "	left join {h-schema}pricing_multi_plan pmp ON sub.pricing_multi_plan_id = pmp.id  \n" +
            "	left join {h-schema}units ui ON ui.id = pmp.unit_id  \n" +
            "	left join {h-schema}users provider ON ser.user_id = provider.id\n" +
            "   left join {h-schema}users u ON u.ID = sub.user_id\n" +
            "	left join {h-schema}order_service_device_status osd ON osd.id = sub.os_3rd_status\n" +
            "	left join {h-schema}order_progress op ON op.id = osd.order_progress_id\n";
    public static final String GET_QUERY_COMBO_DETAIL_SUB =
        "	select \n" +
            "	sub.id as id,\n" +
            "	b.id as billingId,\n" +
            "   false as isService, \n" +
            "   null::::int as productType, " +
            "	op.code as processStatus,\n" +
            "	op.id as processId,\n" +
            "   cp.payment_cycle as paymentCycle, \n" +
            "   CASE" +
            "       WHEN cp.cycle_type = 0 THEN 'DAILY'" +
            "       WHEN cp.cycle_type = 1 THEN 'WEEKLY' " +
            "       WHEN cp.cycle_type = 2 THEN 'MONTHLY' " +
            "       WHEN cp.cycle_type = 3 THEN 'YEARLY' " +
            "   END AS cycleType, " +
            "	cp.number_of_cycles as numberOfCycles, " +
            "	-1 as pricingPlan, " +
            "	cp.combo_plan_type as pricingType, " +
            "   b.status as billingStatus," +
            "	cp.free_quantity as freeQuantity, " +
            "   ui.name as unitName," +
            "    case when u.customer_type = 'CN' then concat(u.last_name,' ',u.first_name) \n" +
            "    else u.name end as customerName,\n" +
            "	u.phone_number AS phone,\n" +
            "	b.billing_code AS billingCode,\n" +
            "	sub.pricing_id AS pricingId,\n" +
            "	sub.pricing_multi_plan_id AS pricingMultiPlanId,\n" +
            "	sub.quantity AS quantity,\n" +
            "	sub.variant_draft_id AS variantDraftId,\n" +
            "	sub.variant_id AS variantId,\n" +
            "	sub.setup_fee_device AS setupFeeDevice,\n" +
            "	sub.service_draft_id AS serviceDraftId,\n" +
            "	c.id AS serviceId,\n" +
            "	sub.os_3rd_status AS Os3rdStatus,\n" +
            "	sub.is_one_time AS isOneTime,\n" +
            "	sub.is_buy_service AS isBuyService,\n" +
            "	sub.is_only_service AS isOnlyService,\n" +
            "	sub.quantity_variant AS quantityVariant,\n" +
            "	sub.combo_plan_id AS comboPlanId,\n" +
            "	cp.installation_configuration AS installationConfiguration,\n" +
            "	sub.address, \n" +
            "	sub.payment_method as paymentMethod, \n" +
            "	c.combo_name as serviceName, \n" +
            "   CASE " +
            "       WHEN sub.status = 0 THEN 'FUTURE' " +
            "       WHEN sub.status = 1 THEN 'IN_TRIAL' " +
            "       WHEN sub.status = 2 THEN 'ACTIVE' " +
            "       WHEN sub.status = 3 THEN 'CANCELLED' " +
            "       WHEN sub.status = 4 THEN 'FINISHED' " +
            "   END as status, " +
            "	CASE\n" +
            "		WHEN c.portal_type = 1 THEN ''\n" +
            "		WHEN provider.name IS NOT NULL THEN u.name \n" +
            "		ELSE concat ( provider.last_name, ' ', provider.first_name ) \n" +
            "	END providerName,\n" +
            "	provider.id as providerId,\n" +
            "	u.id as userId,\n" +
            "	sub.created_at::::date + 15 as timeDelivery,\n" +
            "	sub.cancelled_time as cancelTime,\n" +
            "	sub.update_reason as updateReason,\n" +
            "	sub.message as message,\n" +
            "	CASE\n" +
            "		WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number \n" +
            "		ELSE u.tin \n" +
            "	END identityNo,\n" +
            "   c.combo_owner as owner \n" +
            "  from {h-schema}subscriptions sub \n" +
            "	left join {h-schema}billings b ON b.subscriptions_id = sub.id \n" +
            "	left join {h-schema}combo_plan cp ON sub.combo_plan_id = cp.id \n" +
            "	left join {h-schema}combo c ON c.id = cp.combo_id\n" +
            "	left join {h-schema}units ui ON ui.id = cp.unit_id  \n" +
            "	left join {h-schema}users provider ON c.user_id = provider.id\n" +
            "   left join {h-schema}users u ON u.ID = sub.user_id\n" +
            "	left join {h-schema}order_service_device_status osd ON osd.id = sub.os_3rd_status\n" +
            "	left join {h-schema}order_progress op ON op.id = osd.order_progress_id\n";
    public static final String GET_DETAIL_SUB_ORDER_BY_CART =
        GET_QUERY_SERVICE_DETAIL_SUB +
            "	where " +
            "   sub.cart_code = :cartCode and " +
            "   sub.combo_plan_id is null\n" +
            "	union\n" +
            GET_QUERY_COMBO_DETAIL_SUB +
            "	where " +
            "   sub.cart_code = :cartCode and\n" +
            "   sub.combo_plan_id is not null";

    public static final String GET_DETAIL_SUB_ORDER_BY_ID =
        GET_QUERY_SERVICE_DETAIL_SUB +
            "	where " +
            "   sub.id = :subId and " +
            "   sub.combo_plan_id is null\n" +
            "	union\n" +
            GET_QUERY_COMBO_DETAIL_SUB +
            "	where " +
            "   sub.id = :subId and\n" +
            "   sub.combo_plan_id is not null";

    public static final String GET_ECONTRACT_BY_ID =
        "	select \n" +
            "	b.id as billingId,\n" +
            "	b.billing_code as billingCode,\n" +
            "	CASE\n" +
            "		WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number \n" +
            "		ELSE u.tin \n" +
            "	END identityNo\n" +
            "  from {h-schema}subscriptions sub \n" +
            "	left join {h-schema}billings b ON b.subscriptions_id = sub.id \n" +
            "   left join {h-schema}users u ON u.ID = sub.user_id\n" +
            "   where sub.id = :id";

    public static final String GET_ECONTRACT_BY_CART =
        "with ids as (\n" +
            "	select id from {h-schema}subscriptions where cart_code = :cartCode\n" +
            ")" +
            "	select \n" +
            "	array_to_string(ARRAY(select * from ids), ',') as lstSubId,\n" +
            "	b.id as billingId,\n" +
            "	b.billing_code as billingCode,\n" +
            "	CASE\n" +
            "		WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number \n" +
            "		ELSE u.tin \n" +
            "	END identityNo\n" +
            "  from {h-schema}subscriptions sub \n" +
            "	left join {h-schema}billings b ON b.subscriptions_id = sub.id \n" +
            "   left join {h-schema}users u ON u.ID = sub.user_id\n" +
            "   where sub.cart_code = :cartCode limit 1";

    public static final String UPDATE_SUB_INSTALLED_TIME =
        "UPDATE \n" +
            "    Subscription subscription \n" +
            "SET \n" +
            "    subscription.installedTime = cast(:date as date), \n" +
            "    subscription.modifiedAt = cast(:date as timestamp) \n" +
            "WHERE \n" +
            "    subscription.id = :id";

    public static final String UPDATE_SUB_MESSAGE_SETUP =
        "UPDATE \n" +
            "    Subscription subscription \n" +
            "SET \n" +
            "    subscription.messageSetup = :message, \n" +
            "    subscription.modifiedAt = cast(:modifiedAt as timestamp) \n" +
            "WHERE \n" +
            "    subscription.id = :id";


    public static final String GET_USERS_BY_SUBSCRIPTION_ID =
        "SELECT "
            + "u.id, "
            + "CONCAT(u.last_name, ' ', u.first_name) AS name, "
            + "fa.file_path AS avatar, "
            + "us2.modified_at AS updatedTime, "
            + "CASE "
            + "WHEN us2.status = 1 THEN 'ACTIVE' "
            + "ELSE 'INACTIVE' "
            + "END status, "
            + "u.email AS email "
            + "FROM "
            + "{h-schema}users u "
            + "LEFT JOIN {h-schema}user_subscription us2 ON "
            + "u.id = us2.user_id "
            + "AND us2.subscription_id = :id "
            + "AND (us2.service_id IS NULL or us2.service_id = :serviceId) "
            + "LEFT JOIN {h-schema}file_attach fa ON "
            + "u.id = fa.user_id "
            + "AND fa.object_type = 5 "
            + "WHERE "
            + " (CONCAT(u.first_name, ' ', u.last_name) ILIKE ('%' || :name || '%') "
            + "OR CONCAT(u.last_name, ' ', u.first_name) ILIKE ('%' || :name || '%')) "
            + "AND (-1 = :status "
            + "OR ((0 = :status "
            + "AND us2.status IS NULL) "
            + "OR us2.status = :status)) "
            + "AND u.status <> 0 "
            + "AND "
            + "(u.parent_id = :userId "
            + "   or u.id = :userId ) "
            + "UNION "
            + "SELECT "
            + "us2.user_id , "
            + "CONCAT(u2.last_name, ' ', u2.first_name) AS name, "
            + "fa.file_path AS avatar, "
            + "us2.modified_at AS updatedTime, "
            + "CASE "
            + "WHEN us2.status = 1 THEN 'ACTIVE' "
            + "else 'INACTIVE' "
            + "END status , "
            + "u2.email AS email "
            + "FROM "
            + "{h-schema}user_subscription us2 "
            + "LEFT JOIN {h-schema}users u2 ON "
            + "us2.user_id = u2.id "
            + "LEFT JOIN {h-schema}file_attach fa ON "
            + "u2.id = fa.user_id "
            + "AND fa.object_type = 5 "
            + "WHERE "
            + "us2.user_id = :userId "
            + "AND us2.subscription_id = :id "
            + "AND (CONCAT(u2.first_name, ' ', u2.last_name) ILIKE ('%' || :name || '%') "
            + "OR CONCAT(u2.last_name, ' ', u2.first_name) ILIKE ('%' || :name || '%')) "
            + "AND (-1 = :status "
            + "OR ((0 = :status "
            + "AND us2.status IS NULL) "
            + "OR us2.status = :status)) "
            + "AND (:serviceId = -1 "
            + "OR us2.service_id IS NULL OR us2.service_id = :serviceId ) "
            + "AND u2.status <> 0 ";

    public static final String CHECK_IS_ON_SUB =
        "select \n" +
            "    case \n" +
            "        when mService.service_owner_partner = 1 or coalesce(mService.service_owner, mCombo.combo_owner) in (0, 1) \n" +
            "            then true \n" +
            "        else false \n" +
            "    end \n" +
            "from {h-schema}subscriptions mSub \n" +
            "    left join {h-schema}pricing mPricing on mPricing.id = mSub.pricing_id \n" +
            "    left join {h-schema}services mService on mService.id = mPricing.service_id \n" +
            "    left join {h-schema}combo_plan mComboPlan on mComboPlan.id = mSub.combo_plan_id \n" +
            "    left join {h-schema}combo mCombo on mCombo.id = mComboPlan.combo_id \n" +
            "where \n" +
            "    mSub.id = :subId";

    public static final String CHECK_IS_THIRD_PARTY_SUB =
        "select \n" +
            "    case \n" +
            "        when coalesce(mService.provider_type, mCombo.provider_type) = 2 \n" +
            "            then true \n" +
            "        else false \n" +
            "    end \n" +
            "from {h-schema}subscriptions mSub \n" +
            "    left join {h-schema}pricing mPricing on mPricing.id = mSub.pricing_id \n" +
            "    left join {h-schema}services mService on mService.id = mPricing.service_id \n" +
            "    left join {h-schema}combo_plan mComboPlan on mComboPlan.id = mSub.combo_plan_id \n" +
            "    left join {h-schema}combo mCombo on mCombo.id = mComboPlan.combo_id \n" +
            "where \n" +
            "    mSub.id = :subId";

    public static final String SEARCH_ACTIVE_SERVICES = "SELECT sub.id AS id, s.service_name AS serviceName, fa.file_path AS icon, " +
            "sub.modified_at AS updatedTime, u.id AS developerId, u.name AS developerName, s.url_service AS url, s.api_key AS apiKey " +
            "FROM {h-schema}services s JOIN {h-schema}subscriptions sub ON s.id = sub.service_id AND sub.status = 1 " +
            "JOIN {h-schema}users u ON u.id = s.user_id JOIN {h-schema}user_subscription us ON " +
            "sub.id = us.subscription_id AND us.status = 1 LEFT JOIN " +
            "{h-schema}file_attach fa ON fa.service_id = s.id AND fa.object_type = 0 " +
            "WHERE us.user_id = :userId AND " +
            "('' = :apiKey OR s.api_key = :apiKey) AND " +
            "('' = :name OR s.service_name ILIKE '%' || :name || '%') AND " +
            "(-1 = :categoryId OR s.categories_id = :categoryId) AND " +
            "(-1 = :developerId OR u.id = :developerId) AND " +
            "('' = :developerName OR u.name ILIKE '%' || :developerName || '%') AND " +
            "(''=:searchText OR (s.service_name ILIKE '%' || :searchText || '%' OR u.name ILIKE '%' || :searchText || '%'))";

    public static final String GET_COMPANY_LIST = "SELECT " +
            "   s.id, " +
            "   u.id AS companyId, " +
            "   u.name AS companyName " +
            "FROM " +
            "   {h-schema}services sv " +
            "JOIN    {h-schema}subscriptions s ON " +
            "   sv.id = s.service_id " +
            "JOIN    {h-schema}users u ON " +
            "   sv.user_id = u.id " +
            "JOIN    {h-schema}pricing sp ON " +
            "   s.pricing_id = sp.id " +
            "JOIN    {h-schema}user_subscription us ON " +
            "   us.subscription_id = s.id " +
            "WHERE " +
            "   us.user_id = :id " +
            "   AND ('' = :name " +
            "   OR u.name ILIKE '%' || :name || '%') " +
            "   AND (-1 = :status " +
            "   OR s.status = :status) " +
            "   AND (-1 = :userStatus " +
            "   OR us.status = :userStatus)";

    public static final String GET_PLAN_LIST = "SELECT " +
            "   s.id, " +
            "   sp.id AS planId, " +
            "   sp.plan_name AS planName " +
            "FROM " +
            "   {h-schema}services sv " +
            "JOIN    {h-schema}subscriptions s ON " +
            "   sv.id = s.service_id " +
            "JOIN    {h-schema}users u ON " +
            "   sv.user_id = u.id " +
            "JOIN    {h-schema}sub_plan_service sps ON " +
            "   s.sub_plan_service_id = sps.id " +
            "JOIN    {h-schema}subscription_plan sp ON " +
            "   sps.subscription_plan_id = sp.id " +
            "JOIN    {h-schema}user_subscription us ON " +
            "   us.subscription_id = s.id " +
            "WHERE " +
            "   us.user_id = :id " +
            "   AND ('' = :name " +
            "   OR sp.plan_name ILIKE '%' || :name || '%') " +
            "   AND (-1 = :status " +
            "   OR s.status = :status) " +
            "   AND (-1 = :userStatus " +
            "   OR us.status = :userStatus)";

    public static final String GET_SERVICE_LIST = "SELECT " +
            "   s.id, " +
            "   sv.id AS serviceId, " +
            "   sv.service_name AS serviceName " +
            "FROM " +
            "   {h-schema}services sv " +
            "JOIN    {h-schema}subscriptions s ON " +
            "   sv.id = s.service_id AND sv.approve = 1 " +
            "JOIN    {h-schema}users u ON " +
            "   sv.user_id = u.id " +
            "JOIN    {h-schema}pricing sp ON " +
            "   s.pricing_id = sp.id " +
            "JOIN    {h-schema}user_subscription us ON " +
            "   us.subscription_id = s.id " +
            "WHERE " +
            "   us.user_id = :id " +
            "   AND ('' = :name " +
            "   OR sv.service_name ILIKE '%' || :name || '%') " +
            "   AND (-1 = :status " +
            "   OR s.status = :status) " +
            "   AND (-1 = :userStatus " +
            "   OR us.status = :userStatus)";

    public static final String GET_LIST_SME_SUBSCRIPTION = " SELECT " +
            " s.user_id as companyId," +
            " CASE " +
            "    WHEN sme.customer_type = 'CN' THEN concat(sme.last_name, ' ',  sme.first_name)   " +
            "    ELSE sme.name   " +
            "    END AS companyName " +
            " FROM" +
            " {h-schema}subscriptions s" +
            " JOIN {h-schema}services s2 on" +
            " s2.id = s.service_id" +
            " JOIN {h-schema}users sme on" +
            " sme.id = s.user_id" +
            " WHERE" +
            " s2.user_id = :devId" +
            " AND (:companyName = '' OR (sme.customer_type IN ('HKD', 'KHDN') AND  lower(sme.name) LIKE ('%' || lower(:companyName) || '%')) OR (sme.customer_type = 'CN' AND lower(concat(sme.last_name , ' ', sme.first_name)) LIKE ('%' || lower(:companyName) || '%')) ) " +
            " AND ( -1 = :status OR s.status = :status)" +
            " GROUP BY s.user_id, sme.name,companyName";
    public static final String GET_LIST_SUBSCRIPTION = " select " +
            "         s.id subscriptionId, " +
            "         s.total_amount totalAmount, " +
            "         to_date(to_char(s.from_date, 'dd/MM/yyyy'), 'dd/MM/yyyy') as fromDate, " +
            "         to_date(to_char(s.cancelled_time, 'dd/MM/yyyy'), 'dd/MM/yyyy') as cancelledTime, " +
            "         s.max_person as maxPerson, " +
            "         b.id as billingId, " +
            "         s.user_id as userId," +
            "         s.quantity as quantity," +
            "         b.billing_type as billingType" +
            " from " +
            "         {h-schema}subscriptions s " +
            " join {h-schema}billings b on " +
            "         s.id = b.subscriptions_id " +
            "         and b.phase = 1 " +
            "         and b.status = 0 " +
            "         and b.billing_type <> 1 " +
            " where " +
            "         from_date >= to_date(:fromDate, 'dd/MM/yyyy') " +
            "         and from_date < to_date(:toDate, 'dd/MM/yyyy') + 1 " +
            "        and now() >= from_date" +
            "         and extract (month from        now()) = extract (month from b.created_at) " +
            " group by s.id, s.total_amount, max_person, b.id";

    public static final String GET_RANGE_UNIT_PRICE =
            "SELECT * FROM(SELECT "
                + "ul.unit_from AS unitFrom, "
                + "ul.unit_to AS unitTo, "
                + "COALESCE(ul.price, ssf.price) AS price, "
                + "CASE "
                + "     WHEN ssf.id IS NOT NULL THEN TRUE "
                + "     ELSE FALSE "
                + "END AS isEdited "
                + "FROM {h-schema}unit_limited ul "
                + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
                + "   ssf.id = ul.subscription_setup_fee_id "
                + "   AND ssf.subscription_id = :subscriptionId "
                + "WHERE (ul.pricing_id = :pricingId OR ssf.pricing_id = :pricingId) "
                + "UNION "
                + "SELECT "
                + "ppd.unit_from AS unitFrom, "
                + "ppd.unit_to AS unitTo, "
                + "COALESCE(ppd.price, ssf.price) AS price, "
                + "CASE "
                + "     WHEN ssf.id IS NOT NULL THEN TRUE "
                + "     ELSE FALSE "
                + "END AS isEdited "
                + "FROM {h-schema}pricing_plan_detail ppd "
                + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
                + "   ssf.id = ppd.subscription_setup_fee_id "
                + "   AND ssf.subscription_id = :subscriptionId "
                + "WHERE (:pricingMultiPlanId <> -1 "
                + "        AND ((ppd.pricing_multi_plan_id = :pricingMultiPlanId AND ppd.subscription_setup_fee_id IS NULL) OR "
                + "       ssf.pricing_multi_plan_id = :pricingMultiPlanId))) as ul2 "
                + "ORDER BY ul2.unitFrom ASC";

    public static final String GET_ADDON_RANGE_UNIT_PRICE =
                  "SELECT "
                + "    DISTINCT ul.unit_from AS unitFrom, "
                + "    ul.unit_to AS unitTo, "
                + "    COALESCE(ul.price, ssf.price) AS price, "
                + "    CASE "
                + "        WHEN ssf.id IS NOT NULL THEN TRUE "
                + "        ELSE FALSE "
                + "    END AS isEdited "
                + "FROM "
                + "    {h-schema}unit_limited ul "
                + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
                + "    ssf.id = ul.subscription_setup_fee_id "
                + "    AND ssf.subscription_id = :subscriptionId "
                + "WHERE "
                + "    (ul.addons_id = :addonId AND subscription_setup_fee_id IS NULL) "
                + "    OR ssf.addon_id = :addonId "
                + "ORDER BY "
                + "    ul.unit_from ASC";

    public static final String GET_ADDON_UNIT_PRICE =
        "SELECT "
            + "    DISTINCT ul.unit_from AS unitFrom, "
            + "    ul.unit_to AS unitTo, "
            + "    COALESCE(ul.price, ssf.price) AS price, "
            + "    CASE "
            + "        WHEN ssf.id IS NOT NULL THEN TRUE "
            + "        ELSE FALSE "
            + "    END AS isEdited "
            + "FROM "
            + "    {h-schema}unit_limited ul "
            + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
            + "    ssf.id = ul.subscription_setup_fee_id "
            + "WHERE "
            + "    (ul.addons_id = :addonId "
            + "    OR ssf.addon_id = :addonId)"
            + "    AND (ul.subscription_setup_fee_id is null or ssf.subscription_id = :subscriptionId) "
            + "ORDER BY "
            + "    ul.unit_from ASC";

    public static final String GET_ADDON_UNIT_PRICE_CUSTOM =
        "SELECT "
            + "    DISTINCT ul.unit_from AS unitFrom, "
            + "    ul.unit_to AS unitTo, "
            + "    COALESCE(ul.price, ssf.price) AS price, "
            + "    CASE "
            + "        WHEN ssf.id IS NOT NULL THEN TRUE "
            + "        ELSE FALSE "
            + "    END AS isEdited "
            + "FROM "
            + "    {h-schema}unit_limited ul "
            + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
            + "    ssf.id = ul.subscription_setup_fee_id "
            + "WHERE "
            + "    (ul.subscription_setup_fee_id = :subscriptionSetupFeeId) "
            + "ORDER BY "
            + "    ul.unit_from ASC";

    public static final String GET_ADDON_MULTI_PLAN_RANGE_UNIT_PRICE =
                 "SELECT "
               + "    DISTINCT ppd.unit_from AS unitFrom, "
               + "    ppd.unit_to AS unitTo, "
               + "    ppd.price AS price, "
               + "    CASE "
               + "        WHEN ssf.id IS NOT NULL THEN TRUE "
               + "        ELSE FALSE "
               + "    END AS isEdited "
               + "FROM "
               + "    {h-schema}pricing_plan_detail ppd "
               + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
               + "    ssf.id = ppd.subscription_setup_fee_id "
               + "    AND ssf.subscription_id = :subscriptionId "
               + "WHERE "
               + "    ppd.pricing_multi_plan_id = :multiPlanId "
               + "    AND (ppd.subscription_setup_fee_id IS NULL "
               + "      OR ssf.pricing_multi_plan_id = :multiPlanId) "
               + "ORDER BY "
               + "    ppd.unit_from ASC";

    public static final String FIND_ALL_PRICING_BY_SERVICE =
        "SELECT distinct new com.dto.pricing.PricingSaaSResDTO(" + //HiepNT thêm distinct loại bỏ bản ghi duplicate
            "    p.id, " +
            "    p.pricingDraftId, " +
            "    p.pricingName, " +
            "    p.pricingCode, " +
            "    p.description, " +
            "    p.price AS priceValue, " +
            "    p.pricingPlan, " +
            "    p.currencyId, " +
            "    c.currencyType, " +
            "    p.unitId, " +
            "    u.name, " +
            "    p.cycleType, " +
            "    p.paymentCycle, " +
            "    p.recommendedStatus, " +
            "    p.listFeatureId, " +
            "    p.trialType, " +
            "    p.numberOfTrial, " +
            "    p.pricingOrder, " +
            "    p.numberOfCycles, " +
            "    p.priority, " +
            "    p.createdAt, " +
            "    p.customerTypeCode, " +
            "    p.serviceId, " +
            "    p.isOneTime ) " +
            "FROM " +
            "    Pricing p " +
            "LEFT JOIN Currency c ON " +
            "    c.id = p.currencyId " +
            "LEFT JOIN Unit u ON " +
            "    u.id = p.unitId " +
            "WHERE " +
            "    p.id IN (:ids) " +
            "ORDER BY p.pricingOrder ASC";

    public static final String GET_PRICING_DRAFT_BY_PRICING_ID =
        "SELECT distinct new com.dto.pricing.PricingSaaSResDTO(" + //HiepNT thêm distinct loại bỏ bản ghi duplicate
            "    p.id, " +
            "    p.pricingDraftId, " +
            "    p.pricingName, " +
            "    p.pricingCode, " +
            "    p.description, " +
            "    p.price AS priceValue, " +
            "    p.pricingPlan, " +
            "    p.currencyId, " +
            "    c.currencyType, " +
            "    p.unitId, " +
            "    u.name, " +
            "    p.cycleType, " +
            "    p.paymentCycle, " +
            "    p.recommendedStatus, " +
            "    p.listFeatureId, " +
            "    p.trialType, " +
            "    p.numberOfTrial, " +
            "    p.pricingOrder, " +
            "    p.numberOfCycles, " +
            "    p.priority, " +
            "    p.createdAt, " +
            "    p.customerTypeCode, " +
            "    p.serviceId, " +
            "    p.isOneTime ) " +
            "FROM " +
            "    Pricing p " +
            "LEFT JOIN Currency c ON " +
            "    c.id = p.currencyId " +
            "LEFT JOIN Unit u ON " +
            "    u.id = p.unitId " +
            "WHERE " +
            "    p.id = :id " +
            "ORDER BY p.pricingOrder ASC";

    public static final String FIND_ALL_PRICING_SORT_BY_SERVICE =
            " SELECT  " +
                    "   p.id as id, " +
                    "   p.customer_type_code as customerType, " +
                    "   p.pricing_draft_id as pricingDraftId, " +
                    "   p.pricing_name as pricingName,  " +
                    "   p.description as description, " +
                    "   mul.id as pricingMultiPlanId," +
                    "   s2.plan_url as urlName," +
                    "   p.list_feature_id as listFeatureId," +
                    "   COALESCE (p.pricing_order, 1) " +
                    " FROM     " +
                    "   {h-schema}pricing p     " +
                    "  LEFT JOIN {h-schema}currency c ON     " +
                    "   c.id = p.currency_id     " +
                    " LEFT JOIN {h-schema}units u ON     " +
                    "   u.id = p.unit_id " +
                    " LEFT JOIN {h-schema}seo s2 ON s2.id = p.seo_id AND s2.seo_type_code = 'CAU_HINH_GOI_DICH_VU'" +
                    " LEFT JOIN {h-schema}pricing_multi_plan mul ON p.id = mul.pricing_id" +
                    "  WHERE   (:customerType = '' OR p.customer_type_code ILIKE ('%' || :customerType || '%')) AND  " +
                    "   p.id IN (" +
                    "             SELECT     " +
                    "                 p.id     " +
                    "             FROM     " +
                    "                  {h-schema}pricing p     " +
                    "             WHERE     " +
                    "                 p.status = 1     " +
                    "                 AND p.deleted_flag = 1     " +
                    "                 AND p.approve = 1     " +
                    "                 AND id IN (     " +
                    "                 SELECT     " +
                    "                     MAX(p.id)     " +
                    "                 FROM     " +
                    "                      {h-schema}pricing_draft pd     " +
                    "                 JOIN  {h-schema}pricing p ON     " +
                    "                     pd.id = p.pricing_draft_id     " +
                    "                     AND pd.deleted_flag = 1     " +
                    "                 LEFT JOIN  {h-schema}services s ON     " +
                    "                     pd.service_id = s.id     " +
                    "                 WHERE     " +
                    "                     s.deleted_flag = 1     " +
                    "                     AND s.status = 1     " +
                    "                     AND s.approve = 1     " +
                    "                     AND s.id = :serviceId     " +
                    "                 GROUP BY     " +
                    "                     pd.id ) ) ORDER BY pricing_order    ";
    public static final String FIND_MAX_VER_IDS_BY_SERVICE_ID =
            "SELECT " +
            "    p.id " +
            "FROM " +
            "    {h-schema}pricing p " +
            "WHERE " +
            "    p.status = 1 " +
            "    AND p.deleted_flag = 1 " +
            "    AND p.approve = 1 " +
            "    AND id IN ( " +
            "    SELECT " +
            "        MAX(p.id) " +
            "    FROM " +
            "        {h-schema}pricing_draft pd " +
            "    JOIN {h-schema}pricing p ON " +
            "        pd.id = p.pricing_draft_id " +
            "        AND pd.deleted_flag = 1 " +
            "    LEFT JOIN {h-schema}pricing_variant pv ON pv.pricing_id = p.id " +
            "    LEFT JOIN {h-schema}services s ON " +
            "        pd.service_id = s.id " +
            "    WHERE " +
            "        s.deleted_flag = 1 " +
            "        AND s.status = 1 " +
            "        AND s.approve = 1 " +
            "        AND s.id = :serviceId " +
            "        AND (:variantId = -1 or pv.variant_id = :variantId or p.variant_apply = 1) " +
            "    GROUP BY " +
            "        pd.id )";
    public static final String GET_CHANGE_SUBSCRIPTION_BY_SUBSCRIPTION =
        "SELECT  "
            + "  cs.*  "
            + "FROM  "
            + " {h-schema}change_subscription cs  "
            + "JOIN {h-schema}subscriptions s ON  "
            + "  s.id = cs.subscription_id  "
            + "  AND s.deleted_flag = 1  "
            + "  AND s.confirm_status = 1  "
            + "JOIN {h-schema}pricing p ON  "
            + "  p.id = s.pricing_id  "
            + "WHERE  "
            + "  cs.subscription_id = :id  "
            + "  AND cs.status = :status  "
            + "  AND cs.action = :action  "
            + "  AND p.change_pricing_payment_time = 1  "
            + "ORDER BY  "
            + "  cs.id DESC  "
            + "LIMIT 1";
    public static final String UPDATE_END_CURRENT_CYCLE_CONTRACT =
        "update {h-schema}subscriptions set end_current_cycle_contract = :endContractDate where id =:id ";

    public static final String GET_SUB_DETAILS_BY_SUB_ID =
        "select \n" +
            "    id,\n" +
            "    user_id as userId,\n" +
            "    dhsxkd_sub_code as subCodeDHSXKD,\n" +
            "    service_id as serviceId,\n" +
            "    province_id_setup as provinceIdSetup,\n" +
            "    created_source_migration as createdSourceMigration,\n" +
            "    current_cycle as currentCycle,\n" +
            "    pricing_multi_plan_id as pricingMultiPlanId,\n" +
            "    number_of_cycles as numberOfCycles,\n" +
            "    end_current_cycle as endCurrentCycle,\n" +
            "    started_at as startedAt,\n" +
            "    created_at as createdAt,\n" +
            "    reg_type as regType,\n" +
            "    change_date as changeDate,\n" +
            "    change_status as changeStatus,\n" +
            "    update_date as updateDate,\n" +
            "    update_status as updateStatus,\n" +
            "    current_cycle_swap as currentCycleSwap,\n" +
            "    current_cycle_non_extend as currentCycleNonExtend,\n" +
            "    started_at_swap as startedAtSwap,\n" +
            "    current_cycle_renew as currentCycleRenew,\n" +
            "    reactive_date as reactiveDate,\n" +
            "    is_one_time as isOneTime,\n" +
            "    next_payment_time as nextPaymentTime,\n" +
            "    next_payment_amount as nextPaymentAmount,\n" +
            "    type_reactive as typeReactive,\n" +
            "    reactive_status as reactiveStatus,\n" +
            "    status as status,\n" +
            "    installed as installed \n" +
            "from \n" +
            "    {h-schema}subscriptions\n" +
            "where \n" +
            "    id = :subId and deleted_flag = 1";

    public static final String FIND_MY_SERVICE_SUBSCRIPTION_COMBO =
        "SELECT "
            + "    DISTINCT  "
            + "    service.url_setup AS urlSetup, "
            + "    service.token_spdv AS tokenSPDV, "
            + "    service.service_owner AS serviceOwner, "
            + "    service.topic_name AS topicName, "
            + "    service.service_code AS serviceType "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}combo_plan cpl ON "
            + "    s.combo_plan_id = cpl.id "
            + "JOIN {h-schema}combo_pricing cp ON "
            + "    cp.id_combo_plan = cpl.id "
            + "JOIN {h-schema}pricing p ON "
            + "    p.id = cp.object_id AND cp.object_type = 'PRICING' "
            + "JOIN {h-schema}services service ON "
            + "    service.id = p.service_id "
            + "    AND service.deleted_flag = 1 "
            + "WHERE "
            + "    s.id = :id";

    public static final String GET_SUBSCRIPTION_INTEGRATION_RES_DTO =
            "SELECT " +
                    "    new com.dto.subscriptions.SubscriptionIntegrationResDTO (  " +
                    "    s.pricingId, " +
                    "    p.pricingCode, " +
                    "    p.pricingName, " +
                    "    p.paymentCycle, " +
                    "    CASE " +
                    "        WHEN p.cycleType = 0 THEN 'DAILY' " +
                    "        WHEN p.cycleType = 1 THEN 'WEEKLY' " +
                    "        WHEN p.cycleType = 2 THEN 'MONTHLY' " +
                    "        WHEN p.cycleType = 3 THEN 'YEARLY' " +
                    "    END, " +
                    "    p.numberOfCycles, " +
                    "    s.quantity, " +
                    "    p.price, " +
                    "    p.unitId, " +
                    "    u.name, " +
                    "    p.currencyId, " +
                    "    c.currencyType, " +
                    "    p.setupFee, " +
                    "    p.numberOfTrial, " +
                    "    CASE " +
                    "        WHEN p.trialType = 0 THEN 'DAILY' " +
                    "        WHEN p.trialType = 1 THEN 'WEEKLY' " +
                    "        WHEN p.trialType = 2 THEN 'MONTHLY' " +
                    "        WHEN p.trialType = 3 THEN 'YEARLY' " +
                    "    END, " +
                    "    s.fromDate, " +
                    "    s.totalAmount, " +
                    "    CASE " +
                    "        WHEN s.status = 1 THEN 'ACTIVE' " +
                    "        ELSE 'INACTIVE' " +
                    "    END ) " +
                    "FROM " +
                    "    Subscription s " +
                    "JOIN Pricing p ON " +
                    "    s.pricingId = p.id " +
                    "    AND p.deletedFlag = 1 " +
                    "LEFT JOIN Unit u ON " +
                    "    u.id = p.unitId " +
                    "LEFT JOIN Currency c ON " +
                    "    c.id = p.currencyId " +
                    "WHERE " +
                    "    s.deletedFlag = 1 " +
                    "    AND s.id = :subscriptionId ";

    public static final String GET_ALLOW_SUBSCRIPT =
            "SELECT " +
            "    DISTINCT us.user_id " +
            "FROM " +
            "    {h-schema}subscriptions sub " +
            "LEFT JOIN {h-schema}pricing p2 ON " +
            "    sub.pricing_id = p2.id " +
            "LEFT JOIN {h-schema}user_subscription us ON " +
            "    us.subscription_id = sub.id " +
            "WHERE " +
            "    sub.confirm_status = 1 " +
            "    AND sub.deleted_flag = 1 " +//HiepNT thêm dk subscription chưa bị xóa
            "    AND p2.service_id = :serviceId " +
            "    AND sub.reg_type = 1  " +
            "    AND us.user_id = :userId";

    public static final String GET_SUB_PROGRESS_DETAIL_BY_SUB_ID =
        "   with service_avatar_path as \n" +
            "    ( \n" +
            "         select \n" +
            "            distinct on (service_id) service_id, \n" +
            "            file_path \n" +
            "        from  \n" +
            "            {h-schema}file_attach \n" +
            "        where \n" +
            "            file_attach.object_type = 0 -- lay avatar \n" +
            "        order by \n" +
            "            service_id, id desc \n" +
            "    )\n" +
            "select \n" +
            "    subscriptions.id as subId, \n" +
            "    coalesce(subscriptions.cart_code, concat('ID', to_char(subscriptions.id, 'FM09999999'::::text))::::character varying) AS subscriptionCode, \n" +
            "    subscriptions.installed as statusOrderId, \n" +
            "    subscriptions.service_id, \n" +
            "    services.service_name as serviceName, \n" +
            "    pricing.pricing_name as pricingName, \n" +
            "    service_avatar_path.file_path as icon, \n" +
            "    subscriptions.created_at as createdAt, \n" +
            "    subscriptions.registed_by as registedBy, \n" +
            "    subscriptions.portal_type as portalType, \n" +
            "    users.email as email \n" +
            "from \n" +
            "    {h-schema}subscriptions \n" +
            "    join {h-schema}services on subscriptions.service_id = services.id \n" +
            "    left join {h-schema}pricing on subscriptions.pricing_id = pricing.id \n" +
            "    left join {h-schema}users on subscriptions.registed_by = users.id \n" +
            "    left join service_avatar_path on services.id = service_avatar_path.service_id \n" +
            "where \n" +
            "    subscriptions.id = :subId \n" +
            "    and coalesce(services.on_os_type, 1) = 0 -- ON \n" +
            "    and coalesce(subscriptions.is_only_service, false) is false --  ko lấy thiết bị ko gói ";

    public static final String GET_LIST_SAAS =
         "SELECT * " +
            "FROM ( " +
                 "SELECT DISTINCT " +
            "    s.id, " +
            "    service.id AS serviceId, " +
            "    service.service_name AS serviceName, " +
            "    CASE  " +
            "        WHEN service.service_owner = 0 THEN 'SAAS'  " +
            "        WHEN service.service_owner = 1 THEN 'VNPT'  " +
            "        WHEN service.service_owner = 2 THEN 'NONE'  " +
            "    END AS serviceOwner, " +
            "    fa.file_path AS icon, " +
            "    fa.ext_link AS embedUrl, " +
            "    p.id AS pricingId, " +
            "    p.pricing_name AS pricingName, " +
            "    CASE  " +
//            "        WHEN ( ( p.customer_type_code ILIKE  ('%' || u.customer_type || '%') )AND ( (p.pricing_type = 0 AND s.next_payment_time < now()) or (p.pricing_type = 1 AND s.current_payment_date < now()) ) ) THEN 'NON_RENEWING' " +
            "        WHEN s.status = 0 THEN 'FUTURE'  " +
            "        WHEN s.status = 1 THEN 'IN_TRIAL'  " +
            "        WHEN s.status = 2 THEN 'ACTIVE'  " +
            "        WHEN s.status = 3 THEN 'CANCELLED'  " +
            "        WHEN s.status = 4 AND service.service_owner in (0,1) AND s.expired_time < DATE(NOW()) AND p.has_renew = 1  \n" +
            "             AND s.expired_time + cast ((SELECT payment_date_fail_on FROM {h-schema}system_params WHERE param_type = 'COUPON') as INTEGER ) - DATE(NOW()) > 0 THEN 'ACTIVE' \n" +
            "        WHEN s.status = 4 AND service.service_owner in (2,3) AND s.expired_time < DATE(NOW()) AND p.has_renew = 1 \n" +
            "              AND s.expired_time + cast ((SELECT payment_date_fail_off FROM {h-schema}system_params WHERE param_type = 'COUPON') as INTEGER ) - DATE(NOW()) > 0 THEN 'ACTIVE' \n" +
            "      ELSE 'NON_RENEWING' \n" +
            "    END AS status, " +
            "    p.payment_cycle AS paymentCycle, " +
            "    dev.name AS developerName, " +
            "    currency.currency_type AS currencyName, " +
            "    units.name AS unitName, " +
            "    s.total_amount AS amount, " +
            "   CASE" +
            "       WHEN p.pricing_type = 0 THEN s.next_payment_time" +
            "       WHEN p.pricing_type = 1 THEN s.current_payment_date" +
            "   END AS nextPaymentDate," +
            "    s.created_at AS createdAt, " +
            "    CASE  " +
            "         WHEN s.quantity = -1 THEN 'UNLIMITED'  " +
            "         WHEN s.quantity <> 1 THEN (COALESCE(s.used_quantity, '0') || ' / ' || COALESCE(s.quantity, '0')) " +
            "    END AS currentStatus, " +
            "    CASE  " +
            "        WHEN p.cycle_type = 0 THEN 'DAILY'  " +
            "        WHEN p.cycle_type = 1 THEN 'WEEKLY'  " +
            "        WHEN p.cycle_type = 2 THEN 'MONTHLY'  " +
            "        WHEN p.cycle_type = 3 THEN 'YEARLY'  " +
            "    END AS cycleType, " +
            "    CASE " +
            "       WHEN s.created_source_migration = 1 then 5 " +
            "       WHEN s.traffic_id is not null then 3" +
            "       WHEN s.employee_code is not null then 2" +
            "       WHEN s.portal_type = 1 then 4" +
            "       WHEN s.portal_type = 2 then 4" +
            "       ELSE 1 " +
            "   END AS createdSource," +
            "    s.migrate_time AS migrateTime, " +
            "    s.migrate_code AS migrateCode, " +
            " CASE " +
            " WHEN service.service_owner = 1 OR service.service_owner = 0 THEN s.installed " +
                 "ELSE null END as installationStatus," +
            " e.confirm_status as confirmStatus, e.response_contract_id as responseContractId " +
            "FROM " +
            "    {h-schema}user_subscription us " +
            "JOIN {h-schema}subscriptions s ON " +
            "    us.subscription_id = s.id " +
            "LEFT JOIN {h-schema}pricing p ON " +
            "    s.pricing_id = p.id " +
            "LEFT JOIN {h-schema}services service ON " +
            "    p.service_id = service.id and service.service_owner <> 3  " +
            "LEFT JOIN {h-schema}users dev ON " +
            "    dev.id = service.user_id " +
//            "LEFT JOIN {h-schema}users u ON " +
//            "    s.user_id = u.id " +
            "LEFT JOIN {h-schema}currency currency ON " +
            "    p.currency_id = currency.id " +
            "LEFT JOIN {h-schema}units units ON " +
            "    p.unit_id = units.id " +
            "LEFT JOIN {h-schema}file_attach fa ON " +
            "    fa.service_id = service.id " +
            "    AND fa.object_type = 0 " +
            "LEFT JOIN {h-schema}e_contract e ON e.id_subscription = s.id " +
            "WHERE " +
            "    s.user_id = :uId AND " +
            "    (:devName = '' OR CONCAT (dev.last_name, ' ', dev.first_name) ILIKE  ('%' ||:devName || '%')) AND " +
            "    (:status = -1 OR s.status = :status) AND " +
            "    s.deleted_flag = 1 AND " +
            "    s.confirm_status = 1 AND " +
            "    service.service_name ILIKE ('%' ||:search || '%') ) tmp " +
            "WHERE (:createdSource = -1 OR tmp.createdSource = :createdSource ) ";

    public static final String COUNT_BY_COUPON_ID =
            "SELECT " +
            "    count(sc.id) " +
            "FROM " +
            "    {h-schema}subscription_coupons sc " +
            "LEFT JOIN {h-schema}subscriptions s ON " +
            "    s.id = sc.subscription_id " +
            "WHERE " +
            "    s.status = 1 " +
            "    AND sc.coupon_id = :couponId";

    public static final String GET_CUSTOMER =
        "SELECT DISTINCT "
            + "    u.id, "
            + "    CASE  "
            + "        WHEN u.name IS NOT NULL THEN u.name "
            + "        ELSE concat(u.last_name,' ' ,u.first_name) "
            + "    END AS companyName,  "
            + "    CASE  "
            + "        WHEN u.rep_fullname IS NOT NULL THEN u.rep_fullname "
            + "        WHEN u.customer_type = 'CN' THEN concat(u.last_name,' ' ,u.first_name) "
            + "    END AS adminName,"
            + "    u.last_name AS lastName, "
            + "    u.first_name AS firstName, "
            + "    u.tin, "
            + "    u.nation_id as countryId, n.name as countryName, "
            + "    u.province_id as provinceId, COALESCE(u.province_code,p.code) as provinceCode, p.name as provinceName, "
            + "    u.district_id as districtId, d.name as districtName, "
            + "    u.ward_id as wardId, w.name as wardName, "
            + "    u.street_id as streetId, str.name as streetName, "
            + "    u.address as address, "
            + "    u.email as email, "
            + "    u.phone_number as phoneNumber, "
            + "    u.birthday as birthday, "
            + "    u.business_area_id as businessAreasId, ba.name as businessAreasName, "
            + "    u.business_size_id as businessScaleId, bs.name as businessScaleName, "
            + "    u.social_insurance_number as socialInsuranceNumber, "
            + "    u.description as description, "
            + "    u.home_number as apartmentNumber, "
            + "    u.rep_personal_cert_number as repPersonalCertNumber, "
            + "    u.rep_personal_cert_type_id as repPersonalCertType, "
            + "    u.rep_personal_cert_date as identityCreatedDate, "
            + "    CASE WHEN u.provider_type = 0 THEN 'LOCAL' "
            + "         WHEN u.provider_type = 1 THEN 'GOOGLE' "
            + "         WHEN u.provider_type = 2 THEN 'FACEBOOK' "
            + "         WHEN u.provider_type = 3 THEN 'PHONE' "
            + "         WHEN u.provider_type = 4 THEN 'APPLE' "
            + "    END AS userProviderType, "
            + "    ad0.address as customerAddress,"
            + "    ad0.tin as customerTin,"
            + "    ad0.sme_name as customerSmeName,"
            + "    ad1.address as setupAddress, "
            + "    u.home_number as homeNumber, "
            + "    u.rep_fullname as repFullName, "
            + "    coalesce(u.customer_type, 'KHDN') as customerType, "
            + "    u.employee_code as employeeCode "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN {h-schema}users_roles ur ON "
            + "    u.id = ur.user_id "
            + " LEFT JOIN {h-schema}province p ON "
            + "     p.id = u.province_id "
                + " LEFT JOIN {h-schema}nation n ON n.id = u.nation_id "
                + " LEFT join {h-schema}district d ON u.district_id = d.id AND u.province_code = d.province_code "
                + " LEFT join {h-schema}ward w ON u.ward_id = w.id AND u.province_code = w.province_code "
            +
            "    LEFT JOIN {h-schema}street str ON str.id = u.street_id AND str.ward_id = u.ward_id AND str.district_id = u.district_id AND str.province_code = u.province_code "
                + " LEFT join {h-schema}business_area ba ON u.business_area_id = ba.id  "
                + " LEFT join {h-schema}business_size bs ON u.business_size_id = bs.id  "
                + " LEFT join {h-schema}address ad0 on u.id = ad0.user_id and ad0.type = 0 and ad0.default_location = 1 "
                + " LEFT join {h-schema}address ad1 on u.id = ad1.user_id and ad1.type = 1 and ad1.default_location = 1 "
            + "WHERE "
            + "    (ur.role_id = 3) "
            + "    AND u.status = 1 "
            + "    AND u.deleted_flag <> 0 "
            + "    AND (:customerType = 'CN' OR u.parent_id = -1) "
            + "    AND (:searchProvinceStatus = -1 OR :provinceId = u.province_id) "
            + "    AND (:companyName = '' OR u.name ILIKE ('%' || :companyName || '%')) "
            + "    AND (:adminName = '' or (u.rep_fullname ILIKE ('%' || :adminName || '%') and u.rep_fullname is not null) or "
            + "    (concat(u.last_name,' ' ,u.first_name) ILIKE ('%' || :adminName || '%') and u.rep_fullname is null and u.customer_type = 'CN')) "
            + "    AND (:tin = '' OR u.tin ILIKE ('%' || :tin || '%') ) "
            + "    AND (:provinceName = '' OR p.name ILIKE ('%' || :provinceName || '%') ) "
            + "    AND u.id <> :removeId "
            + "    AND (:customerType = '' OR u.customer_type = :customerType) "
            + "    AND (:repPersonalCertNumber = '' OR u.rep_personal_cert_number = :repPersonalCertNumber)"
            + "    AND (:userId = -1 OR u.id = :userId) ";


    public static final String GET_ALL_ADDONS_PRICING =
        "WITH NewestAddon AS ( "
            + "SELECT "
            + "     a.addon_draft_id, "
            + "     max(a.id) newest "
            + "FROM "
            + "     {h-schema}addons a "
            + " LEFT JOIN {h-schema}addon_draft ad ON  "
            + "  ad.id = a.addon_draft_id  "
            + "  WHERE ad.deleted_flag = 1 "
            + "GROUP BY "
            + "     a.addon_draft_id ) "
            + "SELECT DISTINCT "
            + "    a.name as name, "
            + "    a.id as id, "
            + "    a.setup_fee as setupFee, "
            + "    COALESCE (s.service_name, c2.combo_name) as objectName, "
            + "    CASE "
            + "        WHEN pa.is_required = 0 THEN false "
            + "        WHEN pa.is_required = 1 THEN  true "
            + "    END AS isRequire, "
            + "    0 as paymentCycle, "
            + "    CASE "
            + "        WHEN a.type = 0 THEN 'DAILY' "
            + "        WHEN a.type = 1 THEN 'WEEKLY' "
            + "        WHEN a.type = 2 THEN 'MONTHLY' "
            + "        WHEN a.type = 3 THEN 'YEARLY' "
            + "    END AS TYPE, "
            + "   -1 AS periodId, "
            + "    CASE "
            + "        WHEN a.pricing_plan = 0 THEN 'FLAT_RATE' "
            + "        WHEN a.pricing_plan = 1 THEN 'UNIT' "
            + "        WHEN a.pricing_plan = 2 THEN 'TIER' "
            + "        WHEN a.pricing_plan = 3 THEN 'VOLUME' "
            + "        WHEN a.pricing_plan = 4 THEN 'STAIR_STEP' "
            + "    END AS pricingPlan, "
            + "    u.name AS companyName, "
            + "    a.bonus_value AS bonusValue, "
            + "    CASE "
            + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
            + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
            + "    END AS bonusType, "
            + "    a.allow_price_change AS hasChangePrice, "
            + "    a.allow_change_quantity as hasChangeQuantity, "
            + "    c.currency_type as currencyType, "
            + "    a.free_quantity as freeQuantity, "
            + "    a.price AS price, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
            + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
            + "     ELSE pmp.maximum_quantity END as maximumQuantity "
            + "FROM "
            + "    {h-schema}addons a "
            + "INNER JOIN NewestAddon na ON "
            + "    a.id = na.newest "
            + "    AND a.addon_draft_id = na.addon_draft_id "
            + "LEFT JOIN {h-schema}pricing_addons pa ON "
            + "    a.id = pa.addons_id "
            + "INNER JOIN {h-schema}currency c ON "
            + "    a.currency_id = c.id "
            + " JOIN {h-schema}services s ON "
            + "    a.service_id = s.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND s.approve = 1 "
            + " LEFT JOIN {h-schema}combo c2 ON "
            + " c2.id = a.combo_id "
            + " AND c2.deleted_flag = 1 "
            + " AND c2.approve = 1 "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = a.user_id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + " LEFT JOIN {h-schema}departments d2 ON d2.id = u.department_id "
            + "    AND d2.deleted_flag = 1 "
            + " LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id = a.id "
            + " LEFT JOIN {h-schema}pricing p ON "
            + "    p.id = pa.pricing_id "
            + "    AND p.deleted_flag = 1 "
            + "WHERE "
            + "    a.status = 1 "
            + "    AND a.deleted_flag = 1 "
            + "    AND a.approve = 1 "
            + "    AND pa.pricing_id = :pricingId "
            + "    AND (:statusSearchAddonIdsNot = 0 OR a.id NOT IN (:addonIdsNot)) "
            + "    AND ((:periodId = -2 AND a.bonus_type = 0) OR :periodId = -1 AND (a.bonus_type = 0 OR (a.bonus_type = 1 AND a.bonus_value NOTNULL)))  "
            + "    AND (:userId = 0 OR a.user_id = :userId OR a.portal = 1) "
            + "    AND (:provinceId = -2 "
            + "        OR (:provinceId = -1 "
            + "            AND (d2.province_id IS NULL)) "
            + "            OR :provinceId = d2.province_id "
            + "            OR d2.province_id is null "
            + "            OR a.portal = 2) "
            + "    AND (:serviceId = -1 OR s.id = :serviceId) "
            + "    AND (:addonId = -1 OR a.id = :addonId) "
            + "    AND (:developerId = -1 OR u.id = :developerId) "
            + "    AND :periodPlanId = -1"
            + "    AND pmp.id is null "
            + "UNION  "
            + "SELECT  "
            + "    a.name as name, "
            + "    a.id as id, "
            + "    a.setup_fee as setupFee, "
            + "    COALESCE (s.service_name, c2.combo_name) as objectName, "
            + "    CASE "
            + "        WHEN pa.is_required = 0 THEN false "
            + "        WHEN pa.is_required = 1 THEN  true "
            + "    END AS isRequire, "
            + "    pmp.payment_cycle as paymentCycle, "
            + "      CASE "
            + "       WHEN pmp.circle_type = 0 THEN 'DAILY' "
            + "       WHEN pmp.circle_type = 1 THEN 'WEEKLY' "
            + "       WHEN pmp.circle_type = 2 THEN 'MONTHLY' "
            + "       WHEN pmp.circle_type = 3 THEN 'YEARLY' "
            + "   END AS type, "
            + "    pmp.id AS periodId, "
            + "    CASE "
            + "        WHEN pmp.pricing_plan = 0 THEN 'FLAT_RATE' "
            + "        WHEN pmp.pricing_plan = 1 THEN 'UNIT' "
            + "        WHEN pmp.pricing_plan = 2 THEN 'TIER' "
            + "        WHEN pmp.pricing_plan = 3 THEN 'VOLUME' "
            + "        WHEN pmp.pricing_plan = 4 THEN 'STAIR_STEP' "
            + "    END AS pricingPlan, "
            + "    u.name AS companyName, "
            + "    a.bonus_value AS bonusValue, "
            + "    CASE "
            + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
            + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
            + "    END AS bonusType, "
            + "    a.allow_price_change AS hasChangePrice, "
            + "    a.allow_change_quantity as hasChangeQuantity, "
            + "    c.currency_type as currencyType, "
            + "    pmp.free_quantity as freeQuantity, "
            + "    pmp.price AS price, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
            + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
            + "     ELSE pmp.maximum_quantity END as maximumQuantity "
            + "FROM "
            + "    {h-schema}addons a "
            + "INNER JOIN NewestAddon na ON "
            + "    a.id = na.newest "
            + "    AND a.addon_draft_id = na.addon_draft_id "
            + "LEFT JOIN {h-schema}pricing_addons pa ON "
            + "    a.id = pa.addons_id "
            + "INNER JOIN {h-schema}currency c ON "
            + "    a.currency_id = c.id "
            + " JOIN {h-schema}services s ON "
            + "    a.service_id = s.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND s.approve = 1 "
            + " LEFT JOIN {h-schema}combo c2 ON "
            + " c2.id = a.combo_id "
            + " AND c2.deleted_flag = 1 "
            + " AND c2.approve = 1 "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = a.user_id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + " LEFT JOIN {h-schema}departments d2 ON d2.id = u.department_id "
            + " AND d2.deleted_flag = 1 "
            + " LEFT JOIN {h-schema}pricing p ON "
            + "    p.id = pa.pricing_id "
            + "    AND p.deleted_flag = 1 "
            + " JOIN {h-schema}pricing_multi_plan pmp ON "
            + "   pmp.addon_id = a.id "
            + "   AND pmp.deleted_flag = 1 "
            + "WHERE "
            + "    a.status = 1 "
            + "    AND a.deleted_flag = 1 "
            + "    AND a.approve = 1 "
            + "    AND pa.pricing_id = :pricingId "
            + "    AND (:statusSearchAddonIdsNot = 0 OR a.id NOT IN (:addonIdsNot)) "
            + "    AND (:periodId <> -2 AND (pmp.payment_cycle = :paymentCycle AND pmp.circle_type = :circleType)) "
            + "    AND (:userId = 0 OR a.user_id = :userId OR a.portal = 1) "
            + "    AND (:provinceId = -2 "
            + "        OR (:provinceId = -1 "
            + "            AND (d2.province_id IS NULL)) "
            + "            OR :provinceId = d2.province_id "
            + "            OR d2.province_id is null "
            + "            OR a.portal = 2) "
            + "    AND (:serviceId = -1 OR s.id = :serviceId) "
            + "    AND (:addonId = -1 OR a.id = :addonId) "
            + "    AND (:developerId = -1 OR u.id = :developerId) "
            + "UNION "
            + "SELECT  "
            + "    a.name as name, "
            + "    a.id as id, "
            + "    a.setup_fee as setupFee, "
            + "    COALESCE (s.service_name, c2.combo_name) as objectName, "
            + "    CASE "
            + "        WHEN pmpa.is_require = 0 THEN false "
            + "        WHEN pmpa.is_require = 1 THEN  true "
            + "    END AS isRequire, "
            + "    0 as paymentCycle, "
            + "    CASE "
            + "        WHEN a.type = 0 THEN 'DAILY' "
            + "        WHEN a.type = 1 THEN 'WEEKLY' "
            + "        WHEN a.type = 2 THEN 'MONTHLY' "
            + "        WHEN a.type = 3 THEN 'YEARLY' "
            + "    END AS TYPE, "
            + "    -1 AS periodId, "
            + "    CASE "
            + "        WHEN a.pricing_plan = 0 THEN 'FLAT_RATE' "
            + "        WHEN a.pricing_plan = 1 THEN 'UNIT' "
            + "        WHEN a.pricing_plan = 2 THEN 'TIER' "
            + "        WHEN a.pricing_plan = 3 THEN 'VOLUME' "
            + "        WHEN a.pricing_plan = 4 THEN 'STAIR_STEP' "
            + "    END AS pricingPlan, "
            + "    u.name AS companyName, "
            + "    a.bonus_value AS bonusValue, "
            + "    CASE "
            + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
            + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
            + "    END AS bonusType, "
            + "    a.allow_price_change AS hasChangePrice, "
            + "    a.allow_change_quantity as hasChangeQuantity, "
            + "    c.currency_type as currencyType, "
            + "    a.free_quantity as freeQuantity, "
            + "    a.price AS price, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
            + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
            + "     ELSE pmp.maximum_quantity END as maximumQuantity "
            + "FROM "
            + "    {h-schema}addons a "
            + "INNER JOIN NewestAddon na ON "
            + "    a.id = na.newest "
            + "    AND a.addon_draft_id = na.addon_draft_id "
            + " LEFT JOIN {h-schema}pricing_multi_plan pmp ON "
            + " pmp.addon_id = a.id"
            + " LEFT JOIN {h-schema}pricing_multi_plan_addon pmpa ON pmpa.addon_id  = a.id "
            + " AND pmpa.pricing_multi_plan_addon_id IS NULL "
            + " LEFT JOIN {h-schema}pricing_multi_plan_addon pmpa1 ON "
            + " pmpa1.pricing_multi_plan_addon_id = pmp.id "
            + " AND pmpa1.pricing_multi_plan_id IS NULL "
            + "INNER JOIN {h-schema}currency c ON "
            + "    a.currency_id = c.id "
            + " JOIN {h-schema}services s ON "
            + "    a.service_id = s.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND s.approve = 1 "
            + " LEFT JOIN {h-schema}combo c2 ON "
            + " c2.id = a.combo_id "
            + " AND c2.deleted_flag = 1 "
            + " AND c2.approve = 1 "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = a.user_id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + "LEFT JOIN {h-schema}departments d2 ON d2.id = u.department_id "
            + " AND d2.deleted_flag = 1 "
            + "WHERE "
            + "    a.status = 1 "
            + "    AND a.deleted_flag = 1 "
            + "    AND a.approve = 1 "
            + "    AND ((pmpa.pricing_multi_plan_id = :periodPlanId AND pmp.id IS NULL) OR pmpa1.pricing_id = :pricingId ) "
            + "    AND (:statusSearchAddonIdsNot = 0 OR a.id NOT IN (:addonIdsNot)) "
            + "    AND ((:periodId = -2 AND a.bonus_type = 0) OR :periodId = -1 AND (a.bonus_type = 0 OR (a.bonus_type = 1 AND a.bonus_value NOTNULL)))  "
            + "    AND (:userId = 0 OR a.user_id = :userId OR a.portal = 1) "
            + "    AND (:provinceId = -2 "
            + "        OR (:provinceId = -1 "
            + "            AND (d2.province_id IS NULL)) "
            + "            OR :provinceId = d2.province_id "
            + "            OR d2.province_id is null "
            + "            OR a.portal = 2) "
            + "    AND (:serviceId = -1 OR s.id = :serviceId) "
            + "    AND (:addonId = -1 OR a.id = :addonId) "
            + "    AND (:developerId = -1 OR u.id = :developerId) "
            + "UNION "
            + "SELECT  "
            + "    a.name as name, "
            + "    a.id as id, "
            + "    a.setup_fee as setupFee, "
            + "    COALESCE (s.service_name, c2.combo_name) as objectName, "
            + "    CASE "
            + "        WHEN pmpa.is_require = 0 THEN false "
            + "        WHEN pmpa.is_require = 1 THEN  true "
            + "    END AS isRequire, "
            + "    pmp.payment_cycle as paymentCycle, "
            + "      CASE "
            + "       WHEN pmp.circle_type = 0 THEN 'DAILY' "
            + "       WHEN pmp.circle_type = 1 THEN 'WEEKLY' "
            + "       WHEN pmp.circle_type = 2 THEN 'MONTHLY' "
            + "       WHEN pmp.circle_type = 3 THEN 'YEARLY' "
            + "   END AS type, "
            + "    pmp.id AS periodId, "
            + "    CASE "
            + "        WHEN pmp.pricing_plan = 0 THEN 'FLAT_RATE' "
            + "        WHEN pmp.pricing_plan = 1 THEN 'UNIT' "
            + "        WHEN pmp.pricing_plan = 2 THEN 'TIER' "
            + "        WHEN pmp.pricing_plan = 3 THEN 'VOLUME' "
            + "        WHEN pmp.pricing_plan = 4 THEN 'STAIR_STEP' "
            + "    END AS pricingPlan, "
            + "    u.name AS companyName, "
            + "    a.bonus_value AS bonusValue, "
            + "    CASE "
            + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
            + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
            + "    END AS bonusType, "
            + "    a.allow_price_change AS hasChangePrice, "
            + "    a.allow_change_quantity as hasChangeQuantity, "
            + "    c.currency_type as currencyType, "
            + "    pmp.free_quantity as freeQuantity, "
            + "    pmp.price AS price, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
            + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
            + "     ELSE pmp.maximum_quantity END as maximumQuantity "
            + "FROM "
            + "    {h-schema}addons a "
            + "INNER JOIN NewestAddon na ON "
            + "    a.id = na.newest "
            + "    AND a.addon_draft_id = na.addon_draft_id "
            + "INNER JOIN {h-schema}currency c ON "
            + "    a.currency_id = c.id "
            + " JOIN {h-schema}services s ON "
            + "    a.service_id = s.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND s.approve = 1 "
            + " LEFT JOIN {h-schema}combo c2 ON "
            + " c2.id = a.combo_id "
            + " AND c2.deleted_flag = 1 "
            + " AND c2.approve = 1 "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = a.user_id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + " LEFT JOIN {h-schema}departments d2 ON d2.id = u.department_id "
            + " AND d2.deleted_flag = 1 "
            + " JOIN {h-schema}pricing_multi_plan pmp ON "
            + "   pmp.addon_id = a.id "
            + "   AND pmp.deleted_flag = 1 "
            + "JOIN {h-schema}pricing_multi_plan_addon pmpa ON pmpa.addon_id = a.id  "
            + "WHERE "
            + "    a.status = 1 "
            + "    AND a.deleted_flag = 1 "
            + "    AND a.approve = 1 "
            + "    AND (pmpa.pricing_multi_plan_id = :periodPlanId)"
            + "    AND (:statusSearchAddonIdsNot = 0 OR a.id NOT IN (:addonIdsNot)) "
            + "    AND (:periodId = -1 AND ((pmp.payment_cycle = :paymentCycle AND pmp.circle_type = :circleType) OR pmp.id  IS NULL)) "
            + "    AND (:userId = 0 OR a.user_id = :userId OR a.portal = 1) "
            + "    AND (:provinceId = -2 "
            + "        OR (:provinceId = -1 "
            + "            AND (d2.province_id IS NULL)) "
            + "            OR :provinceId = d2.province_id "
            + "            OR d2.province_id is null "
            + "            OR a.portal = 2) "
            + "    AND (:serviceId = -1 OR s.id = :serviceId) "
            + "    AND (:addonId = -1 OR a.id = :addonId) "
            + "    AND (:developerId = -1 OR u.id = :developerId) " 
            + "UNION  "
            + "SELECT "
            + "    a.name AS name, "
            + "    a.id AS id, "
            + "    a.setup_fee AS setupFee, "
            + "    COALESCE (s.service_name, c2.combo_name) AS objectName, "
            + "    CASE "
            + "        WHEN pmpa.is_require = 0 THEN false "
            + "        WHEN pmpa.is_require = 1 THEN  true "
            + "    END AS isRequire, "
            + "    pmp.payment_cycle AS paymentCycle, "
            + "    CASE "
            + "        WHEN pmp.circle_type = 0 THEN 'DAILY' "
            + "        WHEN pmp.circle_type = 1 THEN 'WEEKLY' "
            + "        WHEN pmp.circle_type = 2 THEN 'MONTHLY' "
            + "        WHEN pmp.circle_type = 3 THEN 'YEARLY' "
            + "    END AS TYPE, "
            + "    pmp.id AS periodId, "
            + "    CASE "
            + "        WHEN pmp.pricing_plan = 0 THEN 'FLAT_RATE' "
            + "        WHEN pmp.pricing_plan = 1 THEN 'UNIT' "
            + "        WHEN pmp.pricing_plan = 2 THEN 'TIER' "
            + "        WHEN pmp.pricing_plan = 3 THEN 'VOLUME' "
            + "        WHEN pmp.pricing_plan = 4 THEN 'STAIR_STEP' "
            + "    END AS pricingPlan, "
            + "    u.name AS companyName, "
            + "    a.bonus_value AS bonusValue, "
            + "    CASE "
            + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
            + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
            + "    END AS bonusType, "
            + "    a.allow_price_change AS hasChangePrice, "
            + "    a.allow_change_quantity AS hasChangeQuantity, "
            + "    c.currency_type AS currencyType, "
            + "    pmp.free_quantity AS freeQuantity, "
            + "    pmp.price AS price, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
            + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
            + " CASE "
            + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
            + "     ELSE pmp.maximum_quantity END as maximumQuantity "
            + "FROM "
            + "    {h-schema}addons a "
            + "INNER JOIN NewestAddon na ON "
            + "    a.id = na.newest "
            + "    AND a.addon_draft_id = na.addon_draft_id "
            + "INNER JOIN {h-schema}currency c ON "
            + "    a.currency_id = c.id "
            + " JOIN {h-schema}services s ON "
            + "    a.service_id = s.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND s.approve = 1 "
            + " LEFT JOIN {h-schema}combo c2 ON "
            + " c2.id = a.combo_id "
            + " AND c2.deleted_flag = 1 "
            + " AND c2.approve = 1 "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = a.user_id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + "LEFT JOIN {h-schema}departments d2 ON "
            + "    d2.id = u.department_id "
            + "    AND d2.deleted_flag = 1 "
            + "JOIN {h-schema}pricing_multi_plan pmp ON "
            + "    pmp.addon_id = a.id "
            + "    AND pmp.deleted_flag = 1 "
            + "JOIN {h-schema}pricing_multi_plan_addon pmpa ON "
            + "    pmpa.pricing_multi_plan_addon_id = pmp.id "
            + "    AND pmpa.pricing_multi_plan_id IS NULL  "
            + "WHERE "
            + "    a.status = 1 "
            + "    AND a.deleted_flag = 1 "
            + "    AND a.approve = 1 "
            + "    AND (pmpa.pricing_id = :pricingId) "
            + "    AND (:statusSearchAddonIdsNot = 0 "
            + "        OR a.id NOT IN (:addonIdsNot)) "
            + "    AND (:periodId <> -2 "
            + "        AND (pmp.payment_cycle = :paymentCycle "
            + "            AND pmp.circle_type = :circleType)) "
            + "    AND (:userId = 0 "
            + "        OR a.user_id = :userId "
            + "        OR a.portal = 1) "
            + "    AND (:provinceId = -2 "
            + "        OR (:provinceId = -1 "
            + "            AND (d2.province_id IS NULL)) "
            + "            OR :provinceId = d2.province_id "
            + "            OR d2.province_id IS NULL "
            + "            OR a.portal = 2) "
            + "    AND (:serviceId = -1 "
            + "        OR s.id = :serviceId) "
            + "    AND (:addonId = -1 "
            + "        OR a.id = :addonId) "
            + "    AND (:developerId = -1 "
            + "        OR u.id = :developerId)";


    public static final String GET_ALL_ADDONS_COMBO =
              "WITH NewestAddon AS ( "
                      + "SELECT "
                      + "     a.addon_draft_id, "
                      + "     max(a.id) newest "
                      + "FROM "
                      + "     {h-schema}addons a "
                      + " LEFT JOIN {h-schema}addon_draft ad ON  "
                      + "  ad.id = a.addon_draft_id  "
                      + "  WHERE ad.deleted_flag = 1 "
                      + "GROUP BY "
                      + "     a.addon_draft_id ) "
                      + "SELECT   "
                      + "    a.name AS name, "
                      + "    a.id AS id, "
                      + "    a.setup_fee AS setupFee, "
                      + "    s.service_name as objectName, "
                      + "    CASE "
                      + "        WHEN ca.is_required = 0 THEN false "
                      + "        WHEN ca.is_required = 1 THEN  true "
                      + "    END AS isRequire, "
                      + "    -2 as paymentCycle, "
                      + "     CASE "
                      + "        WHEN a.type = 0 THEN 'DAILY' "
                      + "        WHEN a.type = 1 THEN 'WEEKLY' "
                      + "        WHEN a.type = 2 THEN 'MONTHLY' "
                      + "        WHEN a.type = 3 THEN 'YEARLY' "
                      + "    END AS TYPE, "
                      + "    CAST (NULL AS int2) AS periodId, "
                      + "    CASE "
                      + "        WHEN a.pricing_plan = 0 THEN 'FLAT_RATE' "
                      + "        WHEN a.pricing_plan = 1 THEN 'UNIT' "
                      + "        WHEN a.pricing_plan = 2 THEN 'TIER' "
                      + "        WHEN a.pricing_plan = 3 THEN 'VOLUME' "
                      + "        WHEN a.pricing_plan = 4 THEN 'STAIR_STEP' "
                      + "    END AS pricingPlan, "
                      + "    u.name AS companyName, "
                      + "    a.bonus_value AS bonusValue, "
                      + "    CASE "
                      + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
                      + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
                      + "    END AS bonusType, "
                      + "    a.allow_price_change AS hasChangePrice, "
                      + "    a.allow_change_quantity as hasChangeQuantity, "
                      + "    cu.currency_type as currencyType, "
                      + "    a.free_quantity AS freeQuantity, "
                      + "    a.price AS price, "
                      + " CASE "
                      + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
                      + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
                      + " CASE "
                      + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
                      + "     ELSE pmp.maximum_quantity END as maximumQuantity "
                      + "FROM "
                      + "     {h-schema}addons a "
                      + "INNER JOIN NewestAddon na ON "
                      + "    a.id = na.newest "
                      + "    AND a.addon_draft_id = na.addon_draft_id "
                      + "INNER JOIN {h-schema}combo_addon ca ON "
                      + "    a.id = ca.id_addon "
                      + "INNER JOIN {h-schema}currency cu ON "
                      + "    a.currency_id = cu.id "
                      + "INNER JOIN {h-schema}combo_plan cp ON "
                      + "    ca.id_combo_plan = cp.id "
                      + "    AND cp.deleted_flag = 1 "
                      + "    AND cp.status = 1 "
                      + "LEFT JOIN {h-schema}combo c ON "
                      + "    cp.combo_id = c.id "
                      + "    AND c.deleted_flag = 1 "
                      + "    AND c.status = 1 "
                      + "INNER JOIN {h-schema}users u ON "
                      + "    u.id = a.user_id "
                      + "    AND u.deleted_flag = 1 "
                      + "    AND u.status = 1 "
                      + " LEFT JOIN {h-schema}departments d2 ON d2.id = u.department_id "
                      + "    AND d2.deleted_flag = 1 "
                      + "LEFT JOIN {h-schema}services s ON "
                      + "    a.service_id = s.id "
                      + "    AND s.deleted_flag = 1 "
                      + "    AND s.approve = 1 "
                      + "LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id  = a.id AND pmp.deleted_flag <> 0 "
                      + "WHERE "
                      + "        a.deleted_flag = 1 "
                      + "    AND a.approve = 1 "
                      + "    AND a.status = 1 "
                      + "    AND ('' = :customerType OR a.customer_type_code SIMILAR TO '%('|| :customerType ||')%') "
                      + "    AND pmp.payment_cycle IS NULL "
                      + "    AND ((:periodId = -2 AND a.bonus_type = 0) OR :periodId = -1 AND (a.bonus_type = 0 OR (a.bonus_type = 1 AND a.bonus_value NOTNULL)))  "
                      + "    AND ca.id_combo_plan = :comboPlanId "
                      + "    AND (:statusSearchAddonIdsNot = 0 OR a.id NOT IN (:addonIdsNot)) "
                      + "    AND (:userId = 0 OR s.user_id = :userId) "
                      + "    AND (:provinceId = -2 "
                      + "        OR (:provinceId = -1 "
                      + "            AND (d2.province_id IS NULL)) "
                      + "            OR :provinceId = d2.province_id "
                      + "            OR d2.province_id is null "
                      + "            OR a.portal = 2) "
                      + "    AND (:addonId = -1 OR a.id = :addonId) "
                      + "    AND (:serviceId = -1 OR s.id = :serviceId) "
                      + "    AND (:developerId = -1 OR u.id = :developerId) "
                      + "UNION "
                      + "SELECT  "
                      + "    a.name AS name, "
                      + "    a.id AS id, "
                      + "    a.setup_fee AS setupFee, "
                      + "    s.service_name as objectName, "
                      + "    CASE "
                      + "        WHEN ca.is_required = 0 THEN false "
                      + "        WHEN ca.is_required = 1 THEN  true "
                      + "    END AS isRequire, "
                      + "    pmp.payment_cycle as paymentCycle, "
                      + "      CASE "
                      + "       WHEN pmp.circle_type = 0 THEN 'DAILY' "
                      + "       WHEN pmp.circle_type = 1 THEN 'WEEKLY' "
                      + "       WHEN pmp.circle_type = 2 THEN 'MONTHLY' "
                      + "       WHEN pmp.circle_type = 3 THEN 'YEARLY' "
                      + "   END AS type, "
                      + "    pmp.id AS periodId, "
                      + "     CASE "
                      + "        WHEN pmp.pricing_plan = 0 THEN 'FLAT_RATE' "
                      + "        WHEN pmp.pricing_plan = 1 THEN 'UNIT' "
                      + "        WHEN pmp.pricing_plan = 2 THEN 'TIER' "
                      + "        WHEN pmp.pricing_plan = 3 THEN 'VOLUME' "
                      + "        WHEN pmp.pricing_plan = 4 THEN 'STAIR_STEP' "
                      + "    END AS pricingPlan, "
                      + "    u.name AS companyName, "
                      + "    pmp.payment_cycle AS bonusValue, "
                      + "    CASE "
                      + "        WHEN a.bonus_type = 0 THEN 'ONCE' "
                      + "        WHEN a.bonus_type = 1 THEN 'PERIODIC' "
                      + "    END AS bonusType, "
                      + "    a.allow_price_change AS hasChangePrice, "
                      + "    a.allow_change_quantity as hasChangeQuantity, "
                      + "    cu.currency_type as currencyType, "
                      + "    a.free_quantity AS freeQuantity, "
                      + "    pmp.price AS price, "
                      + " CASE "
                      + "     WHEN a.bonus_type = 0 THEN a.minimum_quantity "
                      + "     ELSE  pmp.minimum_quantity END as minimumQuantity, "
                      + " CASE "
                      + "     WHEN a.bonus_type = 0 THEN a.maximum_quantity "
                      + "     ELSE pmp.maximum_quantity END as maximumQuantity "
                      + "FROM "
                      + "     {h-schema}addons a "
                      + "INNER JOIN NewestAddon na ON "
                      + "    a.id = na.newest "
                      + "    AND a.addon_draft_id = na.addon_draft_id "
                      + "INNER JOIN {h-schema}combo_addon ca ON "
                      + "    a.id = ca.id_addon "
                      + "LEFT JOIN {h-schema}pricing_multi_plan pmp "
                      + "    ON pmp.id = ca.multi_pricing_plan_id "
                      + "INNER JOIN {h-schema}currency cu ON "
                      + "    a.currency_id = cu.id "
                      + "INNER JOIN {h-schema}combo_plan cp ON "
                      + "    ca.id_combo_plan = cp.id "
                      + "    AND cp.deleted_flag = 1 "
                      + "    AND cp.status = 1 "
                      + "LEFT JOIN {h-schema}combo c ON "
                      + "    cp.combo_id = c.id "
                      + "    AND c.deleted_flag = 1 "
                      + "    AND c.status = 1 "
                      + "INNER JOIN {h-schema}users u ON "
                      + "    u.id = a.user_id "
                      + "    AND u.deleted_flag = 1 "
                      + "    AND u.status = 1 "
                      + "LEFT JOIN {h-schema}departments d2 ON d2.id = u.department_id "
                      + "    AND d2.deleted_flag = 1 "
                      + "LEFT JOIN {h-schema}services s ON "
                      + "    a.service_id = s.id "
                      + "    AND s.deleted_flag = 1 "
                      + "    AND s.approve = 1 "
                      + "WHERE "
                      + "        a.deleted_flag = 1 "
                      + "    AND a.approve = 1 "
                      + "    AND a.status = 1 "
                      + "    AND ((:periodId = -2 AND a.bonus_type = 0) "
                      + "    OR pmp.payment_cycle = -1 "
                      + "    OR (pmp.circle_type = :circleType and pmp.payment_cycle = :paymentCycle)) "
                      + "    AND ca.id_combo_plan = :comboPlanId "
                      + "    AND (:statusSearchAddonIdsNot = 0 OR a.id NOT IN (:addonIdsNot)) "
                      + "    AND (:userId = 0 OR s.user_id = :userId) "
                      + "    AND (:provinceId = -2 "
                      + "        OR (:provinceId = -1 "
                      + "            AND (d2.province_id IS NULL)) "
                      + "            OR :provinceId = d2.province_id "
                      + "            OR d2.province_id is null "
                      + "            OR a.portal = 2) "
                      + "    AND (:periodId <> -2 AND (pmp.payment_cycle = :paymentCycle AND pmp.circle_type = :circleType)) "
                      + "    AND (:addonId = -1 OR a.id = :addonId)   "
                      + "    AND (:serviceId = -1 OR s.id = :serviceId) "
                      + "    AND (:developerId = -1 OR u.id = :developerId)  ";

    public static final String GET_PRICING =
            "WITH NewestPricing AS ( " +
                "SELECT " +
                "   p2.pricing_draft_id, " +
                "   max(p2.id) newest " +
                "FROM " +
                "   {h-schema}pricing p2 " +
                "GROUP BY " +
                "   p2.pricing_draft_id ) " +
                "SELECT "
                + "    DISTINCT d.* "
                + "FROM "
                + "    ( " +
            "SELECT " +
                    "   p.id, " +
                    "   pmp.id AS periodId, " +
                    "   p.pricing_name AS pricingName, " +
                    "   CASE " +
                    "       WHEN pmp.pricing_plan = 0 THEN 'FLAT_RATE' " +
                    "       WHEN pmp.pricing_plan = 1 THEN 'UNIT' " +
                    "       WHEN pmp.pricing_plan = 2 THEN 'TIER' " +
                    "       WHEN pmp.pricing_plan = 3 THEN 'VOLUME' " +
                    "       WHEN pmp.pricing_plan = 4 THEN 'STAIR_STEP' " +
                    "   END AS pricingPlan, " +
                    "   p.setup_fee AS setupFee, " +
                    "   s.service_name AS serviceName, " +
                    "   fa.file_path AS serviceIcon, " +
                    "   CASE " +
                    "       WHEN pmp.circle_type = 0 THEN 'DAILY' " +
                    "       WHEN pmp.circle_type = 1 THEN 'WEEKLY' " +
                    "       WHEN pmp.circle_type = 2 THEN 'MONTHLY' " +
                    "       WHEN pmp.circle_type = 3 THEN 'YEARLY' " +
                    "   END AS type, " +
                    "   pmp.circle_type AS sortType, " +
                    "   u.name AS companyName, " +
                    "   CASE\n" +
                    "       WHEN p.is_one_time = 0 THEN null\n" +
                    "       WHEN p.is_one_time = 1 THEN pmp.payment_cycle\n" +
                    "   END AS numberOfCycles " +
                    "FROM {h-schema}pricing p " +
                    "LEFT JOIN {h-schema}services s ON " +
                    "   s.id = p.service_id " +
                    "   AND s.deleted_flag = 1 " +
                    "   AND s.status = 1 " +
                    "LEFT JOIN {h-schema}users u ON " +
                    "   s.user_id = u.id " +
                    "   AND u.deleted_flag = 1 " +
                    "   AND u.status = 1 " +
                    "LEFT JOIN {h-schema}file_attach fa ON  " +
                    "   fa.service_id = s.id " +
                    "   AND fa.object_type = 0 " +
                    "INNER JOIN NewestPricing np ON " +
                    "   np.newest = p.id AND np.pricing_draft_id = p.pricing_draft_id " +
                    "JOIN {h-schema}pricing_multi_plan pmp ON " +
                    "   pmp.pricing_id = p.id " +
                    "   AND pmp.deleted_flag = 1 " +
                    "   AND pmp.display_status = 1 " +
                    "WHERE " +
                    "   p.deleted_flag = 1 AND p.status = 1 " +
                    "   AND (:numberOfCycle = -1 OR (pmp.payment_cycle = :numberOfCycle  " +
                    "   AND pmp.circle_type = :type ) )  "
                +   "   AND pmp.id NOT IN (:periodIds) "
                +   "   AND (:serviceId = -1 OR s.id = :serviceId) "
                +   "   AND s.service_owner IN (:owners) "
                +   "   AND (:apiType <> 'CREATE_SUB_TRIAL' or (:apiType = 'CREATE_SUB_TRIAL' and (s.product_type is null or s.product_type <> 1))) "
                +   "   AND (:serviceName = '' OR s.service_name = :serviceName) " +
                    "   AND (:pricingName = '' OR p.pricing_name = :pricingName) " +
                    "   AND (:userId = 0 OR s.user_id = :userId)" +
                    "   AND s.service_name NOTNULL AND u.name NOTNULL " +
                    "   AND ('' = :customerType OR pmp.customer_type_code SIMILAR TO '%('|| :customerType ||')%')"
                    + "    AND (:registerEcontract = -1 OR s.register_econtract = :registerEcontract) "
                + "UNION "
                + "SELECT "
                + "    p.id, "
                + "    CAST (NULL AS int2) AS periodId, "
                + "    p.pricing_name AS pricingName, "
                + "    CASE "
                + "        WHEN p.pricing_plan = 0 THEN 'FLAT_RATE' "
                + "        WHEN p.pricing_plan = 1 THEN 'UNIT' "
                + "        WHEN p.pricing_plan = 2 THEN 'TIER' "
                + "        WHEN p.pricing_plan = 3 THEN 'VOLUME' "
                + "        WHEN p.pricing_plan = 4 THEN 'STAIR_STEP' "
                + "    END AS pricingPlan, "
                + "    p.setup_fee AS setupFee, "
                + "    s.service_name AS serviceName, "
                + "    fa.file_path AS serviceIcon,  "
                + "    CASE "
                + "        WHEN p.cycle_type = 0 THEN 'DAILY' "
                + "        WHEN p.cycle_type = 1 THEN 'WEEKLY' "
                + "        WHEN p.cycle_type = 2 THEN 'MONTHLY' "
                + "        WHEN p.cycle_type = 3 THEN 'YEARLY' "
                + "    END AS type, "
                + "    p.cycle_type AS sortType, "
                + "    u.name AS companyName, "
                + "    CASE\n"
                + "        WHEN p.is_one_time = 0 THEN null\n"
                + "        WHEN p.is_one_time = 1 THEN pmp.payment_cycle\n"
                + "    END as numberOfCycles "
                + "FROM "
                + "    {h-schema}pricing p "
                + "LEFT JOIN {h-schema}services s ON "
                + "    s.id = p.service_id "
                + "    AND s.deleted_flag = 1 "
                + "    AND s.status = 1 "
                + "LEFT JOIN {h-schema}file_attach fa ON  " +
                    "   fa.service_id = s.id " +
                    "   AND fa.object_type = 0 "
                + "LEFT JOIN {h-schema}users u ON "
                + "    s.user_id = u.id "
                + "    AND u.deleted_flag = 1 "
                + "    AND u.status = 1 "
                + "INNER JOIN NewestPricing np ON "
                + "    np.newest = p.id "
                + "    AND np.pricing_draft_id = p.pricing_draft_id "
                + "LEFT JOIN {h-schema}pricing_multi_plan pmp ON "
                + "    pmp.pricing_id = p.id "
                + "WHERE "
                + "    p.deleted_flag = 1 "
                + "    AND p.status = 1 "
                + "    AND pmp.id IS NULL "
                + "    AND p.payment_cycle NOTNULL "
                + "    AND (:numberOfCycle = -1 OR (p.payment_cycle = :numberOfCycle   "
                + "    AND p.cycle_type = :type ) )  "
                + "    AND p.id <> :pricingIdRemove "
                + "    AND s.service_owner IN (:owners) "
                + "    AND (:apiType <> 'CREATE_SUB_TRIAL' or (:apiType = 'CREATE_SUB_TRIAL' and (s.product_type is null or s.product_type <> 1))) "
                + "    AND (:serviceId = -1 OR s.id = :serviceId) "
                + "    AND (:serviceName = '' OR s.service_name = :serviceName) "
                + "    AND (:pricingName = '' OR p.pricing_name = :pricingName) "
                + "    AND (:userId = 0 "
                + "    OR s.user_id = :userId) "
                + "    AND s.service_name NOTNULL "
                + "    AND u.name NOTNULL "
                + "    AND (:registerEcontract = -1 OR s.register_econtract = :registerEcontract) "
                + "    AND ('' = :customerType OR p.customer_type_code SIMILAR TO '%('|| :customerType ||')%')) d ";

    public static final String GET_SERVICE_NAME_FILTER =
        "SELECT  "
            + "    distinct s.service_name AS serviceName "
            + "FROM "
            + "    {h-schema}services s "
            + "JOIN {h-schema}users u ON "
            + "    s.user_id = u.id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + "JOIN {h-schema}pricing p "
            + "  ON p.service_id = s.id "
            + "  AND s.deleted_flag = 1 "
            + "  AND s.status = 1 "
            + "WHERE "
            + "    s.deleted_flag = 1 "
            + "    AND s.status = 1 "
            + "    AND (s.product_type is null or s.product_type <> 1) "
            + "    AND s.service_owner IN (:owners) "
            + "    AND (:userId = 0 OR s.user_id = :userId) "
            + "    AND (:serviceId = -1 OR s.id = :serviceId) "
            + "    AND (:name = '' "
            + "    OR s.service_name ILIKE ('%' || :name || '%')) "
            + "    AND s.customer_type_code ILIKE ('%' || :customerType || '%') "
            + "    AND (:registerEcontract = -1 OR s.register_econtract = :registerEcontract) "
            + "    ORDER BY s.service_name ASC ";

    public static final String GET_PRICING_NAME_FILTER =
        "WITH NewestPricing AS ( "
            + "SELECT "
            + "    p2.pricing_draft_id, "
            + "    max(p2.id) newest "
            + "FROM "
            + "    {h-schema}pricing p2 "
            + "GROUP BY "
            + "    p2.pricing_draft_id ) "
            + "SELECT "
            + "    DISTINCT p.pricing_name AS pricingName "
            + "FROM "
            + "    {h-schema}pricing p "
            + "LEFT JOIN {h-schema}services s ON "
            + "    s.id = p.service_id "
            + "    AND s.deleted_flag = 1 "
            + "    AND s.status = 1 "
            + "    AND (s.product_type is null or s.product_type <> 1) "
            + " JOIN {h-schema}users u ON "
            + "    s.user_id = u.id "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + "INNER JOIN NewestPricing np ON "
            + "    np.newest = p.id "
            + "    AND np.pricing_draft_id = p.pricing_draft_id "
            + "WHERE "
            + "    (:name = '' "
            + "    OR p.pricing_name ILIKE ('%' || :name || '%')) "
            + "    AND p.deleted_flag = 1 "
            + "    AND p.status = 1 "
            + "    AND (:serviceId = -1 OR s.id = :serviceId) "
            + "    AND s.service_owner IN (:owners) "
            + "    AND (:userId = 0 OR s.user_id = :userId) "
            + "    AND ('' = :customerType OR s.customer_type_code SIMILAR TO '%('|| :customerType ||')%')"
            + "    AND (:registerEcontract = -1 OR s.register_econtract = :registerEcontract) "
            + "ORDER BY "
            + "    p.pricing_name ASC";

    public static final String GET_PEDIOD_FILTER =
        "SELECT "
            + "    b.* "
            + "FROM "
            + "    ( WITH NewestPricing AS ( "
            + "    SELECT "
            + "        p2.pricing_draft_id, "
            + "        max(p2.id) newest "
            + "    FROM "
            + "        {h-schema}pricing p2 "
            + "    GROUP BY "
            + "        p2.pricing_draft_id ), "
            + "    NewestCombo AS ( "
            + "    SELECT "
            + "    c2.combo_plan_draft_id, "
            + "    max(c2.id) newest "
            + "    FROM "
            + "    {h-schema}combo_plan c2 "
            + "    GROUP BY "
            + "    c2.combo_plan_draft_id ) "
            + "    SELECT "
            + "        DISTINCT pmp.payment_cycle AS numberOfCycle, "
            + "        CASE "
            + "            WHEN pmp.circle_type = 0 THEN 'DAILY' "
            + "            WHEN pmp.circle_type = 1 THEN 'WEEKLY' "
            + "            WHEN pmp.circle_type = 2 THEN 'MONTHLY' "
            + "            WHEN pmp.circle_type = 3 THEN 'YEARLY' "
            + "        END AS TYPE, "
            + "        CASE "
            + "            WHEN pmp.circle_type = 0 THEN concat(CAST(pmp.payment_cycle AS TEXT), ' ngày') "
            + "            WHEN pmp.circle_type = 1 THEN concat(CAST(pmp.payment_cycle AS TEXT), ' tuần') "
            + "            WHEN pmp.circle_type = 2 THEN concat(CAST(pmp.payment_cycle AS TEXT), ' tháng') "
            + "            WHEN pmp.circle_type = 3 THEN concat(CAST(pmp.payment_cycle AS TEXT), ' năm') "
            + "        END AS searchText, "
            + "        pmp.circle_type AS typeSort "
            + "    FROM "
            + "        {h-schema}pricing p "
            + "    LEFT JOIN {h-schema}services s ON "
            + "        s.id = p.service_id "
            + "        AND s.deleted_flag = 1 "
            + "        AND s.status = 1 "
            + "    LEFT JOIN {h-schema}users u ON "
            + "        s.user_id = u.id "
            + "        AND u.deleted_flag = 1 "
            + "        AND u.status = 1 "
            + "    INNER JOIN NewestPricing np ON "
            + "        np.newest = p.id "
            + "        AND np.pricing_draft_id = p.pricing_draft_id "
            + "    JOIN {h-schema}pricing_multi_plan pmp ON "
            + "        pmp.pricing_id = p.id "
            + "        AND pmp.deleted_flag = 1 "
            + "    WHERE "
            + "        p.deleted_flag = 1 "
            + "        AND p.status = 1 "
            + "        AND (:serviceId = -1 OR s.id = :serviceId) "
            + "        AND s.service_owner IN (:owners) "
            + "        AND (:userId = 0 "
            + "        OR s.user_id = :userId) "
            + "        AND ('' = :customerType OR pmp.customer_type_code SIMILAR TO '%('|| :customerType ||')%') "
            + "    AND (:registerEcontract = -1 OR s.register_econtract = :registerEcontract) "
            + "UNION "
            + "    SELECT "
            + "        DISTINCT p.payment_cycle AS numberOfCycle, "
            + "        CASE "
            + "            WHEN p.cycle_type = 0 THEN 'DAILY' "
            + "            WHEN p.cycle_type = 1 THEN 'WEEKLY' "
            + "            WHEN p.cycle_type = 2 THEN 'MONTHLY' "
            + "            WHEN p.cycle_type = 3 THEN 'YEARLY' "
            + "        END AS TYPE, "
            + "        CASE "
            + "            WHEN p.cycle_type = 0 THEN concat(CAST(p.payment_cycle AS TEXT), ' ngày') "
            + "            WHEN p.cycle_type = 1 THEN concat(CAST(p.payment_cycle AS TEXT), ' tuần') "
            + "            WHEN p.cycle_type = 2 THEN concat(CAST(p.payment_cycle AS TEXT), ' tháng') "
            + "            WHEN p.cycle_type = 3 THEN concat(CAST(p.payment_cycle AS TEXT), ' năm') "
            + "        END AS searchText, "
            + "        p.cycle_type AS typeSort "
            + "    FROM "
            + "        {h-schema}pricing p "
            + "    LEFT JOIN {h-schema}services s ON "
            + "        s.id = p.service_id "
            + "        AND s.deleted_flag = 1 "
            + "        AND s.status = 1 "
            + "    LEFT JOIN {h-schema}users u ON "
            + "        s.user_id = u.id "
            + "        AND u.deleted_flag = 1 "
            + "        AND u.status = 1 "
            + "    INNER JOIN NewestPricing np ON "
            + "        np.newest = p.id "
            + "        AND np.pricing_draft_id = p.pricing_draft_id "
            + "    WHERE "
            + "        p.deleted_flag = 1 "
            + "        AND p.status = 1 "
            + "        AND (:serviceId = -1 OR s.id = :serviceId) "
            + "        AND s.service_owner IN (:owners) "
            + "        AND (:userId = 0 "
            + "        OR s.user_id = :userId) "
            + "    AND (:registerEcontract = -1 OR s.register_econtract = :registerEcontract) "
            + "        AND ('' = :customerType OR p.customer_type_code SIMILAR TO '%('|| :customerType ||')%')"
            + "UNION "
            + "    SELECT "
            + "        DISTINCT combo_plan.payment_cycle AS numberOfCycle, "
            + "        CASE "
            + "            WHEN combo_plan.cycle_type = 0 THEN 'DAILY' "
            + "            WHEN combo_plan.cycle_type = 1 THEN 'WEEKLY' "
            + "            WHEN combo_plan.cycle_type = 2 THEN 'MONTHLY' "
            + "            WHEN combo_plan.cycle_type = 3 THEN 'YEARLY' "
            + "        END AS TYPE, "
            + "        CASE "
            + "            WHEN combo_plan.cycle_type = 0 THEN concat(CAST(combo_plan.payment_cycle AS TEXT), ' ngày') "
            + "            WHEN combo_plan.cycle_type = 1 THEN concat(CAST(combo_plan.payment_cycle AS TEXT), ' tuần') "
            + "            WHEN combo_plan.cycle_type = 2 THEN concat(CAST(combo_plan.payment_cycle AS TEXT), ' tháng') "
            + "            WHEN combo_plan.cycle_type = 3 THEN concat(CAST(combo_plan.payment_cycle AS TEXT), ' năm') "
            + "        END AS searchText, "
            + "        combo_plan.cycle_type AS typeSort "
            + "    FROM "
            + "        {h-schema}combo_plan"
            + "    LEFT JOIN {h-schema}combo ON "
            + "        combo.id = combo_plan.combo_id "
            + "        AND combo.deleted_flag = 1 "
            + "        AND combo.status = 1 "
            + "    LEFT JOIN {h-schema}users u ON "
            + "        combo.user_id = u.id "
            + "        AND u.deleted_flag = 1 "
            + "        AND u.status = 1 "
            + "    INNER JOIN NewestCombo nc ON "
            + "        nc.newest = combo_plan.id "
            + "        AND nc.combo_plan_draft_id = combo_plan.combo_plan_draft_id "
            + "    WHERE "
            + "        combo_plan.deleted_flag = 1 "
            + "        AND combo_plan.status = 1 "
            + "        AND (:comboId = -1 OR combo.id = :comboId) "
            + "        AND combo.combo_owner IN (:owners) "
            + "        AND (:userId = 0 "
            + "        OR combo.user_id = :userId) "
            + "    AND (:registerEcontract = -1 OR combo.register_econtract = :registerEcontract) "
            + "        AND ('' = :customerType OR combo_plan.customer_type_code SIMILAR TO '%('|| :customerType ||')%')) b "
            + "WHERE "
            + "    (:name = '' OR b.searchText ILIKE ('%' || :name || '%')) "
            + "    and b.numberOfCycle <> -1 "
            + "ORDER BY "
            + "    b.typeSort ASC, "
            + "    b.numberOfCycle ASC";

    public static final String GET_USER_NAME_FILTER =
        "SELECT "
            + "    DISTINCT u.name AS userName, "
            + "    u.id AS userId "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN {h-schema}users_roles ur ON "
            + "    ur.user_id = u.id  "
            + "    AND u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + "    AND u.name notnull "
            + "    AND u.parent_id = -1 "
            + "JOIN {h-schema}role_portal rp ON "
            + "    rp.role_id = ur.role_id "
            + "    AND rp.portal_id = 2 "
            + "JOIN {h-schema}province p ON "
            + "    p.id = u.province_id "
            + "WHERE "
            + "    (:name = '' OR u.name ILIKE ('%' || :name || '%')) "
            + "ORDER BY "
            + "    u.name ASC";

    public static final String FIND_ALL_SUB_BY_PORTAL_TYPE_DEV_ADMIN =
            "SELECT " +
                    "tmp.id AS subscriptionId, tmp.typeReactive as typeReactive, " +
                    "tmp.icon, tmp.currentCycle as currentCycle, " +
                    "tmp.pricingName, " +
                    "tmp.serviceName, " +
                    "tmp.developerName, " +
                    "tmp.customerName, " +
                    "tmp.statusValue, " +
                    "tmp.numberOfUse, " +
                    "tmp.quantity, " +
                    "tmp.type, " +
                    "tmp.numberOfCycle, " +
                    "tmp.paymentCycle, " +
                    "tmp.createAt, " +
                    "tmp.totalAmount, " +
                    "tmp.nextPaymentTime, " +
                    "tmp.startedAt, " +
                    "tmp.tin, " +
                    "tmp.portalType, " +
                    "tmp.migrateTime,  " +
                    "tmp.migrateCode,  " +
                    "tmp.createdSource, " +
                    "tmp.migrateId, " +
                    "tmp.provinceId FROM " +
            "(SELECT " +
                    "    sc.id, " +
                    "    case when billOut.status is null then 'IN_PAYMENT' " +
                    "         else 'OUT_OF_DATE' " +
                    "    end as  typeReactive, " +
                    "    COALESCE (f.file_path, f.file_name ) as icon, " +
                    "    p.pricing_name as pricingName, " +
                    "    s.service_name as serviceName, " +
                    "    urb.name as developerName, " +
                    "    CASE " +
                    "       WHEN u.customer_type = 'CN' THEN concat(u.last_name, ' ', u.first_name) " +
                    "       ELSE u.name " +
                    "    END AS customerName, " +
                    "    sc.status as statusValue, " +
                    "    sc.quantity as numberOfUse, " +
                    "    (select count(scu.id) from {h-schema}user_subscription scu where scu.subscription_id = sc.id) as quantity, " +
                    "    CASE" +
                    "         WHEN sc.status = 1 THEN -1" +
                    "         ELSE COALESCE(p.cycle_type, pmp.circle_type) " +
                    "    END as type, " +
                    "    COALESCE(sc.number_of_cycles, p.number_of_cycles, pmp.number_of_cycles) as numberOfCycle, " +
                    "    CASE " +
                    "         WHEN sc.status = 1 THEN -1" +
                    "         ELSE COALESCE(p.payment_cycle, pmp.payment_cycle) " +
                    "    END as paymentCycle, " +//HiepNT lấy ra chu kỳ thanh toán
                    "    sc.created_at as createAt, " +
                    "    sc.total_amount as totalAmount," +
                    "    CASE " +
                    "         WHEN p.pricing_type = 0 THEN sc.next_payment_time " +
                    "         ELSE sc.current_payment_date " +
                    "    END as nextPaymentTime, " +
                    "    sc.started_at as startedAt," +
                    "    CASE " +
                    "       WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number " +
                    "       ELSE u.tin " +
                    "    END AS tin, " +
                    "    CASE " +
                    "        WHEN sc.portal_type = 1 THEN 'ADMIN' " +
                    "        WHEN sc.portal_type = 2 THEN 'DEV' " +
                    "        WHEN sc.portal_type = 3 THEN 'SME' " +
                    "    END as portalType, " +
                    "    d.province_id as provinceId," +
//                    1: affline, 2 AM, 3 admin, 4 dev, 5 sme
                    "    CASE " +
                    "       WHEN sc.created_source_migration = 1 THEN 5 " +
                    "       WHEN sc.traffic_id is not null THEN 3" +
                    "       WHEN sc.employee_code is not null THEN 2" +
                    "       WHEN sc.portal_type = 1 THEN 4" +
                    "       WHEN sc.portal_type = 2 THEN 4" +
                    "       ELSE 1 " +
                    "   END AS createdSource," +
                    "   bill.payment_date as paymentDate, " +
                    "   sc.migrate_time AS migrateTime, " +
                    "   sc.migrate_code AS migrateCode, " +
                    "   m.id AS migrateId, sc.current_cycle as currentCycle " +
                    "FROM " +
                    "    {h-schema}subscriptions sc " +
                    "JOIN {h-schema}services s ON " +
                    "    sc.service_id = s.id " +
                    "   AND s.deleted_flag = 1 " +
                    "   AND sc.deleted_flag = 1 " +
                    "   AND ( s.service_owner not in (2,3) " +
                    "   OR s.service_owner IS NULL ) " +
                    "   AND s.status = 1 " +
                    "   AND sc.confirm_status = 1 " +
                    "JOIN {h-schema}pricing p ON " +
                    "    sc.pricing_id = p.id " +
                    "   AND p.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}pricing_multi_plan pmp ON " +
                    "    sc.pricing_multi_plan_id = pmp.id " +
                    "LEFT JOIN {h-schema}users urb ON " +
                    "    s.user_id = urb.id " +
                    "JOIN {h-schema}users u ON " +
                    "    sc.user_id = u.id " +
                    "   AND u.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}users us ON " +
                    "    sc.registed_by = us.id " +
                    "LEFT JOIN {h-schema}departments d ON " +
                    "    us.department_id = d.id " +
                    "    AND d.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}file_attach f ON " +
                    "    f.service_id = s.id " +
                    "   AND f.object_type = 0 " +
                    " LEFT JOIN {h-schema}billings bill ON " +
                    "    sc.id = bill.subscriptions_id and bill.id in (select max(id) from {h-schema}billings where subscriptions_id =sc.id and status = 2) " +
                    " LEFT JOIN {h-schema}billings billOut ON " +
                    "    sc.id = billOut.subscriptions_id and billOut.id in (select max(id) from {h-schema}billings where subscriptions_id =sc.id and status = 4) " +
                    "LEFT JOIN {h-schema}migration m ON m.code = sc.migrate_code " +
                    "WHERE " +
                    "    (:portalType = 1 or s.user_id = :parentId ) " +
                    "    And (:searchText = '' OR (lower(COALESCE(s.service_name,'')) like ('%' ||:searchText || '%') OR lower(COALESCE(p.pricing_name,'')) like ('%' ||:searchText || '%'))) " +
                    "    And (:status = -1 OR sc.status = :status) " +
                    "    AND (:customerType = '' OR u.customer_type = :customerType ) " +
                    "    AND (:cusName = '' OR lower(u.name) like ('%' || :cusName || '%') " +
                    "       OR concat(u.last_name || ' ' || u.first_name) ILIKE ('%' || :cusName || '%') ) " +
                    "    AND (:developerName = '' OR lower(COALESCE(urb.name,'')) like ('%' || :developerName || '%') ) " +
                    "    AND (:provinceId = -1 OR  u.province_id = :provinceId) " +
                    "    AND (:createdBy = -1 OR sc.registed_by = :createdBy) " +
                    "    AND (:tin = '' OR COALESCE(u.tin, '') LIKE ('%' ||:tin || '%') ) " +
                    "    AND (:email = '' OR COALESCE(u.email,'') ilike ('%' ||:email || '%') ) " +
                    "    AND (:phone = '' OR COALESCE(u.phone_number,'') ilike %:phone% ) " +
                    "    AND (:representative = '' OR COALESCE(u.rep_fullname,'') ilike %:representative% ) " +
                    "    AND (:businessAreaId = -1 OR u.business_area_id = :businessAreaId ) " +
                    "    AND (:businessSizeId = -1 OR u.business_size_id = :businessSizeId ) " +
                    "    AND (:createdFrom = :defaultMinDateTime OR sc.created_at >= :createdFrom) " +
                    "    AND (:createdTo = :defaultMaxDateTime OR sc.created_at < :createdTo) " +
                    "    AND (:foundingFrom = :defaultMinDate OR u.founding_date >= :foundingFrom) " +
                    "    AND (:foundingTo = :defaultMaxDate OR u.founding_date <= :foundingTo) " +
                    "    AND (:paymentCycle = -1 OR p.payment_cycle = :paymentCycle OR pmp.payment_cycle = :paymentCycle) " +
                    "    AND (:paymentCycle = -1 OR p.cycle_type = :cycleType OR pmp.circle_type = :cycleType) "  +
                    ") AS tmp " +
                    "WHERE " +
                    "   (:createdSource = -1 OR tmp.createdSource = :createdSource) " +
                    "    AND (:nextPaymentDateFrom = :defaultMinDateTime OR tmp.nextPaymentTime >= :nextPaymentDateFrom) " +
                    "    AND (:nextPaymentDateTo = :defaultMaxDateTime OR tmp.nextPaymentTime <= :nextPaymentDateTo) " +
                    "    AND (:paymentDateFrom = :defaultMinDateTime OR tmp.paymentDate >= :paymentDateFrom) " +
                    "    AND (:paymentDateTo = :defaultMaxDateTime OR tmp.paymentDate <= :paymentDateTo) ";

    public static final String COUNT_FIND_ALL_SUB_BY_PORTAL_TYPE_DEV_ADMIN =
            "SELECT COUNT(tmp.id) FROM " +
                    "(SELECT " +
                    "    sc.id, " +
                    "    COALESCE (f.file_path, f.file_name ) as icon, " +
                    "    p.pricing_name as pricingName, " +
                    "    s.service_name as serviceName, " +
                    "    urb.name as developerName, " +
                    "    u.name as customerName, " +
                    "    sc.status as statusValue, " +
                    "    sc.quantity as numberOfUse, " +
                    "    (select count(scu.id) from {h-schema}user_subscription scu where scu.subscription_id = sc.id) as quantity, " +
                    "    CASE" +
                    "         WHEN sc.status = 1 THEN -1" +
                    "         ELSE COALESCE(p.cycle_type, pmp.circle_type) " +
                    "    END as type, " +
                    "    COALESCE(p.number_of_cycles, pmp.number_of_cycles) as numberOfCycle, " +
                    "    CASE " +
                    "         WHEN sc.status = 1 THEN -1" +
                    "         ELSE COALESCE(p.payment_cycle, pmp.payment_cycle) " +
                    "    END as paymentCycle, " +//HiepNT lấy ra chu kỳ thanh toán
                    "    sc.created_at as createAt, " +
                    "    sc.total_amount as totalAmount," +
                    "    CASE " +
                    "         WHEN p.pricing_type = 0 THEN sc.next_payment_time " +
                    "         ELSE sc.current_payment_date " +
                    "    END as nextPaymentTime, " +
                    "    sc.started_at as startedAt," +
                    "    u.tin as tin, " +
                    "    CASE " +
                    "        WHEN sc.portal_type = 1 THEN 'ADMIN' " +
                    "        WHEN sc.portal_type = 2 THEN 'DEV' " +
                    "        WHEN sc.portal_type = 3 THEN 'SME' " +
                    "    END as portalType, " +
                    "    d.province_id as provinceId," +
//                    1: affline, 2 AM, 3 admin, 4 dev, 5 sme
                    "    CASE " +
                    "       WHEN sc.created_source_migration = 1 THEN 5 " +
                    "       WHEN sc.traffic_id is not null THEN 3" +
                    "       WHEN sc.employee_code is not null THEN 2" +
                    "       WHEN sc.portal_type = 1 THEN 4" +
                    "       WHEN sc.portal_type = 2 THEN 4" +
                    "       ELSE 1 " +
                    "   END AS createdSource," +
                    " bill.payment_date as paymentDate, " +
                    "   sc.migrate_time AS migrateTime, " +
                    "   sc.migrate_code AS migrateCode " +
                    "FROM " +
                    "    {h-schema}subscriptions sc " +
                    "JOIN {h-schema}services s ON " +
                    "    sc.service_id = s.id " +
                    "   AND s.deleted_flag = 1 " +
                    "   AND sc.deleted_flag = 1 " +
                    "   AND ( s.service_owner  not in (2,3)  " +
                    "   OR s.service_owner IS NULL ) " +
                    "   AND s.status = 1 " +
                    "   AND sc.confirm_status = 1 " +
                    "JOIN {h-schema}pricing p ON " +
                    "    sc.pricing_id = p.id " +
                    "   AND p.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}pricing_multi_plan pmp ON " +
                    "    sc.pricing_multi_plan_id = pmp.id " +
                    "LEFT JOIN {h-schema}users urb ON " +
                    "    s.user_id = urb.id " +
                    "JOIN {h-schema}users u ON " +
                    "    sc.user_id = u.id " +
                    "   AND u.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}users us ON " +
                    "    sc.registed_by = us.id " +
                    "LEFT JOIN {h-schema}departments d ON " +
                    "    us.department_id = d.id " +
                    "    AND d.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}file_attach f ON " +
                    "    f.service_id = s.id " +
                    "   AND f.object_type = 0 " +
                    " LEFT JOIN {h-schema}billings bill ON " +
                    "    sc.id = bill.subscriptions_id and bill.id in (select max(id) from {h-schema}billings where subscriptions_id =sc.id and status = 2) " +
                    "LEFT JOIN {h-schema}migration m ON m.code = sc.migrate_code " +
                    "WHERE " +
                    "    (:portalType = 1 or s.user_id = :parentId ) " +
                    "    And (:searchText = '' OR (lower(COALESCE(s.service_name,'')) like ('%' ||:searchText || '%') OR lower(COALESCE(p.pricing_name,'')) like ('%' ||:searchText || '%'))) " +
                    "    And (:status = -1 OR sc.status = :status) " +
                    "    AND (:customerType = '' OR u.customer_type = :customerType ) " +
                    "    AND (:cusName = '' OR lower(u.name) like ('%' || :cusName || '%') " +
                    "       OR concat(u.last_name || ' ' || u.first_name) ILIKE ('%' || :cusName || '%') ) " +
                    "    AND (:developerName = '' OR lower(COALESCE(urb.name,'')) like ('%' || :developerName || '%') ) " +
                    "    AND (:provinceId = -1 OR  u.province_id = :provinceId) " +
                    "    AND (:createdBy = -1 OR sc.registed_by = :createdBy) " +
                    "    AND (:tin = '' OR COALESCE(u.tin, '') LIKE ('%' ||:tin || '%') ) " +
                    "    AND (:email = '' OR COALESCE(u.email,'') ilike ('%' ||:email || '%') ) " +
                    "    AND (:phone = '' OR COALESCE(u.phone_number,'') ilike %:phone% ) " +
                    "    AND (:representative = '' OR COALESCE(u.rep_fullname,'') ilike %:representative% ) " +
                    "    AND (:businessAreaId = -1 OR u.business_area_id = :businessAreaId ) " +
                    "    AND (:businessSizeId = -1 OR u.business_size_id = :businessSizeId ) " +
                    "    AND (:createdFrom = :defaultMinDateTime OR sc.created_at >= :createdFrom) " +
                    "    AND (:createdTo = :defaultMaxDateTime OR sc.created_at < :createdTo) " +
                    "    AND (:foundingFrom = :defaultMinDate OR u.founding_date >= :foundingFrom) " +
                    "    AND (:foundingTo = :defaultMaxDate OR u.founding_date <= :foundingTo) " +
                    "    AND (:paymentCycle = -1 OR p.payment_cycle = :paymentCycle OR pmp.payment_cycle = :paymentCycle) " +
                    "    AND (:paymentCycle = -1 OR p.cycle_type = :cycleType OR pmp.circle_type = :cycleType) " +
                    ") AS tmp " +
                    "WHERE " +
                    "   (:createdSource = -1 OR tmp.createdSource = :createdSource) " +
                    "    AND (:nextPaymentDateFrom = :defaultMinDateTime OR tmp.nextPaymentTime >= :nextPaymentDateFrom) " +
                    "    AND (:nextPaymentDateTo = :defaultMaxDateTime OR tmp.nextPaymentTime <= :nextPaymentDateTo) " +
                    "    AND (:paymentDateFrom = :defaultMinDateTime OR tmp.paymentDate >= :paymentDateFrom) " +
                    "    AND (:paymentDateTo = :defaultMaxDateTime OR tmp.paymentDate <= :paymentDateTo) ";

    public static final String      GET_COUPON_POPUP_FOR_PRICING =
        "SELECT "
            + "    DISTINCT coupons.id AS id, "
            + "    coupons.name AS couponName, "
            + "    coupons.code AS code, "
            + "    coupons.promotion_type AS promotionType, "
            + "    coupons.discount_type AS discountType, "
            + "    coupons.discount_value AS discountValue, "
            + "    coupons.max_used AS maxUsed, "
            + "    coupons.minimum AS minimum , "
            + "    coupons.minimum_amount AS minimumAmount, "
            + "    coupons.maximum_promotion AS maximumPromotion, "
            + "    coupons.start_date AS startDate, "
            + "    coupons.end_date AS endDate, "
            + "    coupons.portal AS portal , "
            + "    coupons.user_id AS userId, "
            + "    coupons.created_by AS createdBy, "
            + "    coupons.discount_amount AS discountAmount, "
            + "    coupons.limited_quantity AS limitedQuantity, "
            + "    coupons.type AS TYPE, "
            + "    coupons.times_used_type AS timesUsedType, "
            + "    coupons.enterprise_type AS enterpriseType, "
            + "    coupons.addons_type AS addonType, "
            + "    coupons.pricing_type AS pricingType, "
            + "    coupons.total_bill_type AS totalBillType, "
            + "    coupons.supplier_type AS supplierType, "
            + "    coupons.discount_supplier_type AS discountSupplierType, "
            + "    coupons.province_id AS provinceId,"
            + "    coupons.customer_type_code AS customerType,"
            + "    coupons.visible_status AS visibleStatus "
            + "FROM "
            + "    {h-schema}coupons "
            + "LEFT JOIN {h-schema}coupon_pricing_apply ON "
            + "    coupon_pricing_apply.coupon_id = coupons.id "
            + "LEFT JOIN {h-schema}coupon_pricing_plan ON "
            + "    coupon_pricing_plan.coupon_id = coupons.id "
            + "LEFT JOIN {h-schema}coupon_enterprise ON "
            + "    coupon_enterprise.coupon_id = coupons.id "
            + "LEFT JOIN {h-schema}coupon_variant_apply ON "
            + "    coupon_variant_apply.coupon_id = coupons.id "
            + "LEFT JOIN {h-schema}coupon_pricing ON coupon_pricing.coupon_id = coupons.id "
            + "LEFT JOIN {h-schema}coupon_set ON coupons.id = coupon_set.coupon_id \n"
            + "WHERE "
            + "     coupons.deleted_flag = 1 \n"
            + "     AND coupons.approve = 1 \n"
            + "     AND coupons.status = 1 \n"
            + "     AND (coupons.portal = 1 or :createdBy = -1 or (coupons.portal = 2 and (coupons.created_by = :createdBy or coupons.user_id = :parentId))) \n"
            + "     AND (:customerType = 'ALL' OR coupons.customer_type_code ILIKE ('%' || :customerType || '%'))\n"
            + "     AND (:checkCouponIds = 0 OR coupons.id NOT IN (:couponIds)) \n"
            + "     AND (coupons.enterprise_type IS NULL OR coupons.enterprise_type = -1 OR (coupons.enterprise_type = 1 AND coupon_enterprise.user_id = :companyId )) \n"
            + "     AND (\n"
            + "         coupons.pricing_type IS NULL\n"
            + "         OR coupons.pricing_type = -1\n"
            + "         OR (\n"
            + "             coupons.pricing_type = 1\n"
            + "             AND (\n"
            + "                 (:multiPlanOriginalId IS NOT NULL AND coupon_pricing_plan.plan_original_id = :multiPlanOriginalId)\n"
            + "                 OR (:pricingDraftId IS NOT NULL AND (coupon_pricing_apply.pricing_draft_id = :pricingDraftId\n"
            + "                 OR coupon_pricing.pricing_draft_id = :pricingDraftId))\n"
            + "                 OR (:variantDraftId IS NOT NULL AND coupon_variant_apply.variant_draft_id = :variantDraftId)\n"
            + "             )))\n"
            + "     AND coupon_set.coupon_id IS NULL\n";

    public static final String GET_COUPON_POPUP_FOR_COMBO_PLAN =
        "SELECT DISTINCT " +
            "    coupons.id AS id, " +
            "    coupons.name AS couponName, " +
            "    coupons.code AS code, " +
            "    coupons.promotion_type AS promotionType, " +
            "    coupons.discount_type AS discountType, " +
            "    coupons.discount_value AS discountValue, " +
            "    coupons.max_used AS maxUsed, " +
            "    coupons.minimum AS minimum , " +
            "    coupons.minimum_amount AS minimumAmount, " +
            "    coupons.maximum_promotion AS maximumPromotion, " +
            "    coupons.start_date AS startDate, " +
            "    coupons.end_date AS endDate, " +
            "    coupons.portal AS portal , " +
            "    coupons.user_id AS userId, " +
            "    coupons.created_by AS createdBy, " +
            "    coupons.discount_amount AS discountAmount, " +
            "    coupons.limited_quantity AS limitedQuantity, " +
            "    coupons.type AS type, " +
            "    coupons.times_used_type AS timesUsedType, " +
            "    coupons.enterprise_type AS enterpriseType, " +
            "    coupons.addons_type AS addonType, " +
            "    coupons.pricing_type AS pricingType, " +
            "    coupons.total_bill_type AS totalBillType, " +
            "    coupons.supplier_type AS supplierType, " +
            "    coupons.discount_supplier_type AS discountSupplierType, " +
            "    coupons.province_id AS provinceId," +
            "    coupons.customer_type_code AS customerType," +
            "    coupons.visible_status AS visibleStatus " +
            "FROM " +
            "    {h-schema}coupons " +
            "LEFT JOIN {h-schema}coupon_combo_plan_apply ON " +
            "    coupon_combo_plan_apply.coupon_id = coupons.id " +
            "LEFT JOIN {h-schema}coupon_enterprise ON " +
            "    coupon_enterprise.coupon_id = coupons.id " +
            "WHERE " +
            "      coupons.deleted_flag = 1 \n" +
            "      AND coupons.approve = 1 \n" +
            "      AND coupons.status = 1 \n" +
            "      AND (coupons.portal = 1 or :createdBy = -1 or (coupons.created_by = :createdBy or coupons.user_id = :parentId)) \n" +
            "      AND (:customerType = 'ALL' OR coupons.customer_type_code ILIKE ('%' || :customerType || '%')) \n" +
            "      AND (:checkCouponIds = 0 OR coupons.id NOT IN (:couponIds)) \n" +
            "      AND (coupons.enterprise_type IS NULL \n" +
            "           OR coupons.enterprise_type = -1 \n" +
            "           OR (coupons.enterprise_type = 1 AND coupon_enterprise.user_id = :companyId)\n" +
            "      ) \n" +
            "      AND (coupons.pricing_type IS NULL \n" +
            "           OR coupons.pricing_type = -1 \n" +
            "           OR (coupons.pricing_type = 1 AND coupon_combo_plan_apply.combo_plan_draft_id = :comboPlanDraftId)\n" +
            "      )";

    public static final String GET_ALL_COUPON_POPUP_FOR_COMBO_PLAN =
            "SELECT DISTINCT " +
                    "    c.id AS id, " +
                    "    c.name AS couponName, " +
                    "    c.code AS code, " +
                    "    c.promotion_type AS promotionType, " +
                    "    c.discount_type AS discountType, " +
                    "    c.discount_value AS discountValue, " +
                    "    c.max_used AS maxUsed, " +
                    "    c.minimum AS minimum , " +
                    "    c.minimum_amount AS minimumAmount, " +
                    "    c.maximum_promotion AS maximumPromotion, " +
                    "    c.start_date AS startDate, " +
                    "    c.end_date AS endDate, " +
                    "    c.portal AS portal , " +
                    "    c.user_id AS userId, " +
                    "    c.created_by AS createdBy, " +
                    "    c.discount_amount AS discountAmount, " +
                    "    c.limited_quantity AS limitedQuantity, " +
                    "    c.type AS type, " +
                    "    c.times_used_type AS timesUsedType, " +
                    "    c.enterprise_type AS enterpriseType, " +
                    "    c.addons_type AS addonType, " +
                    "    c.pricing_type AS pricingType, " +
                    "    c.total_bill_type AS totalBillType, " +
                    "    c.supplier_type AS supplierType, " +
                    "    c.discount_supplier_type AS discountSupplierType, " +
                    "    c.province_id AS provinceId," +
                    "    c.customer_type_code AS customerType," +
                    "    c.visible_status AS visibleStatus " +
                    "FROM " +
                    "    {h-schema}coupons c " +
                    "LEFT JOIN {h-schema}coupon_combo_plan_apply cc ON " +
                    "    cc.coupon_id = c.id " +
                    "LEFT JOIN {h-schema}coupon_enterprise ce ON " +
                    "    ce.coupon_id = c.id " +
                    "WHERE " +
                    "      c.deleted_flag = 1 \n" +
                    "      AND c.approve = 1 \n" +
                    "      AND c.status = 1 \n" +
                    "      AND (c.portal = 1 or :createdBy = -1 or (c.created_by = :createdBy or c.user_id = :parentId)) \n" +
                    "      AND (:customerType = 'ALL' OR c.customer_type_code ILIKE ('%' || :customerType || '%')) \n" +
                    "      AND (c.enterprise_type IS NULL \n" +
                    "           OR c.enterprise_type = -1 \n" +
                    "           OR (c.enterprise_type = 1 AND ce.user_id = :companyId)\n" +
                    "      ) \n" +
                    "      AND (c.pricing_type IS NULL \n" +
                    "           OR c.pricing_type = -1 \n" +
                    "           OR (c.pricing_type = 1 AND cc.combo_plan_id = :comboPlanId)\n" +
                    "      )";

    public static final String GET_COUPON_POPUP_FOR_ADDON_AND_PRICING =
        "select * from (SELECT  DISTINCT " +
            "    c.id AS id,  " +
            "    cs.id is not null as isCouponSet,  " +
            "    c.name AS couponName,  " +
            "    c.code AS code,  " +
            "    c.promotion_type AS promotionType,  " +
            "    c.discount_type AS discountType,  " +
            "    c.discount_value AS discountValue,  " +
            "    c.max_used AS maxUsed,  " +
            "    c.minimum AS minimum ,  " +
            "    c.minimum_amount AS minimumAmount,  " +
            "    c.maximum_promotion AS maximumPromotion,  " +
            "    c.start_date AS startDate,  " +
            "    c.end_date AS endDate, " +
            "    c.portal AS portal , " +
            "    c.user_id AS userId, " +
            "    c.created_by AS createdBy, " +
            "    c.discount_amount AS discountAmount, " +
            "    c.limited_quantity AS limitedQuantity, " +
            "    c.type AS type, " +
            "    c.times_used_type AS timesUsedType, " +
            "    c.enterprise_type AS enterpriseType, " +
            "    c.addons_type AS addonType, " +
            "    c.pricing_type AS pricingType, " +
            "    c.total_bill_type AS totalBillType, " +
            "    c.supplier_type AS supplierType, " +
            "    c.discount_supplier_type AS discountSupplierType, " +
            "    c.province_id AS provinceId," +
            "    c.customer_type_code AS customerType," +
            "    c.visible_status AS visibleStatus, " +
            "    2 AS objectType, " +
            "    pmp.id as addonMultiPlanId, " +
            "    ca.addons_id AS addonId, " +
            "    'COUPON_OF_ADDON' as classify " +
            "FROM  " +
            "    {h-schema}coupons c  " +
            "LEFT JOIN {h-schema}coupon_addons ca ON  " +
            "    ca.coupon_id = c.id  " +
            "LEFT JOIN {h-schema}coupon_pricing_plan cpp ON " +
            "    cpp.coupon_id = c.id " +
            "LEFT JOIN {h-schema}pricing_multi_plan pmp ON \n" +
            "    pmp.id = cpp.pricing_multi_plan_id " +
            "LEFT JOIN {h-schema}coupon_enterprise ce ON  " +
            "    ce.coupon_id = c.id  " +
            "LEFT JOIN {h-schema}coupon_set cs ON\n" +
            "    cs.coupon_id = c.id " +
            "WHERE  " +
            "     c.deleted_flag = 1 \n" +
            "     AND c.status = 1 \n" +
            "     AND c.approve = 1 \n" +
            "     AND -1 NOT IN :addonIds  \n" +
            "     AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))\n" +
            "     AND (:customerType = 'ALL' OR c.customer_type_code ILIKE ('%' || :customerType || '%'))\n" +
            "     AND (c.addons_type = -1 \n" +
            "          OR c.addons_type IS NULL\n" +
            "          OR (c.addons_type = 1\n" +
            "              AND (ca.addons_id IN :addonIds)\n" +
            "              AND (-1 IN :lstAddonMultiPlanId or cpp.pricing_multi_plan_id IN :lstAddonMultiPlanId))\n" +
            "     )\n" +
            "     AND (c.enterprise_type IS NULL \n" +
            "          OR c.enterprise_type = -1 \n" +
            "          OR (c.enterprise_type = 1 AND ce.user_id = :companyId)\n" +
            "     ) "
            + " UNION " +
            "SELECT "
            + "    DISTINCT c.id AS id, "
            + "    cs.id is not null as isCouponSet,  "
            + "    c.name AS couponName, "
            + "    c.code AS code, "
            + "    c.promotion_type AS promotionType, "
            + "    c.discount_type AS discountType, "
            + "    c.discount_value AS discountValue, "
            + "    c.max_used AS maxUsed, "
            + "    c.minimum AS minimum , "
            + "    c.minimum_amount AS minimumAmount, "
            + "    c.maximum_promotion AS maximumPromotion, "
            + "    c.start_date AS startDate, "
            + "    c.end_date AS endDate, "
            + "    c.portal AS portal , "
            + "    c.user_id AS userId, "
            + "    c.created_by AS createdBy, "
            + "    c.discount_amount AS discountAmount, "
            + "    c.limited_quantity AS limitedQuantity, "
            + "    c.type AS TYPE, "
            + "    c.times_used_type AS timesUsedType, "
            + "    c.enterprise_type AS enterpriseType, "
            + "    c.addons_type AS addonType, "
            + "    c.pricing_type AS pricingType, "
            + "    c.total_bill_type AS totalBillType, "
            + "    c.supplier_type AS supplierType, "
            + "    c.discount_supplier_type AS discountSupplierType, "
            + "    c.province_id AS provinceId,"
            + "    c.customer_type_code AS customerType,"
            + "    c.visible_status AS visibleStatus,"
            + "    1 as objectType, "
            + "    -1 as addonMultiPlanId, "
            + "    -1 as addonId, "
            + "    'COUPON_OF_PRICING' as classify "
            + "FROM "
            + "    {h-schema}coupons c "
            + "LEFT JOIN {h-schema}coupon_pricing_apply cpa ON "
            + "    cpa.coupon_id = c.id "
            + "LEFT JOIN {h-schema}coupon_pricing_plan cpp ON "
            + "    cpp.coupon_id = c.id "
            + "LEFT JOIN {h-schema}coupon_enterprise ce ON "
            + "    ce.coupon_id = c.id "
            + "LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = c.id "
            + "LEFT JOIN {h-schema}coupon_set cs ON "
            + "    cs.coupon_id = c.id "
            + "WHERE "
            + "     c.deleted_flag = 1 \n"
            + "     AND c.approve = 1 \n"
            + "     AND c.status = 1 \n"
            + "     AND (c.portal = 1 or :createdBy = -1 or (c.portal = 2 and (c.created_by = :createdBy or c.user_id = :parentId))) \n"
            + "     AND (:customerType = 'ALL' OR c.customer_type_code ILIKE ('%' || :customerType || '%'))\n"
            + "     AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds)) \n"
            + "     AND (c.enterprise_type IS NULL OR c.enterprise_type = -1 OR (c.enterprise_type = 1 AND ce.user_id = :companyId )) \n"
            + "     AND (\n"
            + "           c.pricing_type IS NULL \n"
            + "           OR c.pricing_type = -1 \n"
            +
            "           OR (c.pricing_type = 1 AND (cpp.pricing_multi_plan_id = :pricingMultiPlanId OR cpa.pricing_id = :pricingId OR cp.pricing_id = :pricingMultiPlanId))"
            + "         ) ) tbl order by objectType";

    public static final String GET_PRICE_ADDON =
        "select * from \n" +
            "(select price, id as addonId from {h-schema}addons \n" +
            "		where id IN (:addonIds)\n" +
            " union\n" +
            " select price, addon_id as addonId from {h-schema}pricing_multi_plan \n" +
            "		where addon_id is not null and id IN (:lstAddonMultiPlanId)\n" +
            ") tbl where price is not null";

    public static final String GET_COUPON_POPUP_FOR_ADDON =
        "SELECT  DISTINCT " +
            "    coupons.id AS id,  " +
            "    coupons.name AS couponName,  " +
            "    coupons.code AS code,  " +
            "    coupons.promotion_type AS promotionType,  " +
            "    coupons.discount_type AS discountType,  " +
            "    coupons.discount_value AS discountValue,  " +
            "    coupons.max_used AS maxUsed,  " +
            "    coupons.minimum AS minimum ,  " +
            "    coupons.minimum_amount AS minimumAmount,  " +
            "    coupons.maximum_promotion AS maximumPromotion,  " +
            "    coupons.start_date AS startDate,  " +
            "    coupons.end_date AS endDate, " +
            "    coupons.portal AS portal , " +
            "    coupons.user_id AS userId, " +
            "    coupons.created_by AS createdBy, " +
            "    coupons.discount_amount AS discountAmount, " +
            "    coupons.limited_quantity AS limitedQuantity, " +
            "    coupons.type AS type, " +
            "    coupons.times_used_type AS timesUsedType, " +
            "    coupons.enterprise_type AS enterpriseType, " +
            "    coupons.addons_type AS addonType, " +
            "    coupons.pricing_type AS pricingType, " +
            "    coupons.total_bill_type AS totalBillType, " +
            "    coupons.supplier_type AS supplierType, " +
            "    coupons.discount_supplier_type AS discountSupplierType, " +
            "    coupons.province_id AS provinceId," +
            "    coupons.customer_type_code AS customerType," +
            "    coupons.visible_status AS visibleStatus " +
            "FROM  " +
            "    {h-schema}coupons  " +
            "LEFT JOIN {h-schema}coupon_addons ON  " +
            "    coupon_addons.coupon_id = coupons.id  " +
            "LEFT JOIN {h-schema}coupon_pricing_plan ON " +
            "    coupon_pricing_plan.coupon_id = coupons.id " +
            "LEFT JOIN {h-schema}coupon_enterprise ON  " +
            "    coupon_enterprise.coupon_id = coupons.id  " +
            "WHERE  " +
            "     coupons.deleted_flag = 1 \n" +
            "     AND coupons.status = 1 \n" +
            "     AND coupons.approve = 1 \n" +
            "     AND (coupons.portal = 1 or :createdBy = -1 or (coupons.created_by = :createdBy or coupons.user_id = :parentId)) \n" +
            "     AND (:checkCouponIds = 0 OR coupons.id NOT IN (:couponIds))\n" +
            "     AND (:customerType = 'ALL' OR coupons.customer_type_code ILIKE ('%' || :customerType || '%'))\n" +
            "     AND (coupons.addons_type = -1 \n" +
            "          OR coupons.addons_type IS NULL\n" +
            "          OR (coupons.addons_type = 1\n" +
            "              AND (-1 = :addonDraftId or coupon_addons.addons_id = :addonDraftId)\n" +
            "              AND (-1 = :multiPlanOriginalId OR coupon_pricing_plan.pricing_multi_plan_id = :multiPlanOriginalId))\n" +
            "     )\n" +
            "     AND (coupons.enterprise_type IS NULL \n" +
            "          OR coupons.enterprise_type = -1 \n" +
            "          OR (coupons.enterprise_type = 1 AND coupon_enterprise.user_id = :companyId)\n" +
            "     ) ";

    public static final String GET_COUPON_POPUP_FOR_TOTAL_BILL =
            "SELECT "
                + "    c.id AS id, "
                + "    c.name AS couponName, "
                + "    c.code AS code, "
                + "    c.promotion_type AS promotionType, "
                + "    c.discount_type AS discountType, "
                + "    c.discount_value AS discountValue, "
                + "    c.max_used AS maxUsed, "
                + "    c.minimum AS minimum , "
                + "    c.minimum_amount AS minimumAmount, "
                + "    c.maximum_promotion AS maximumPromotion, "
                + "    c.start_date AS startDate, "
                + "    c.end_date AS endDate, "
                + "    c.portal AS portal , "
                + "    c.user_id AS userId, "
                + "    c.created_by AS createdBy, "
                + "    c.discount_amount AS discountAmount, "
                + "    c.limited_quantity AS limitedQuantity, "
                + "    c.type AS TYPE, "
                + "    c.times_used_type AS timesUsedType, "
                + "    c.enterprise_type AS enterpriseType, "
                + "    c.addons_type AS addonType, "
                + "    c.pricing_type AS pricingType, "
                + "    c.total_bill_type AS totalBillType, "
                + "    c.supplier_type AS supplierType, "
                + "    c.discount_supplier_type AS discountSupplierType, "
                + "    c.province_id AS provinceId,"
                + "    c.visible_status AS visibleStatus "
                + "FROM "
                + "    {h-schema}coupons c "
                + "LEFT JOIN {h-schema}coupon_pricing_apply cpa ON "
                + "    cpa.coupon_id = c.id "
                + "LEFT JOIN {h-schema}coupon_enterprise ce ON "
                + "    ce.coupon_id = c.id "
                + "WHERE "
                + "     c.total_bill_type = 1 \n"
                + "     AND c.deleted_flag = 1 \n"
                + "     AND c.status = 1 \n"
                + "     AND c.approve = 1 \n"
                + "     AND (:checkCouponIds = 0\n"
                + "          OR c.id NOT IN (:couponIds)\n"
                + "     )\n"
                + "     AND (c.enterprise_type IS NULL \n"
                + "          OR c.enterprise_type = -1 \n"
                + "          OR (c.enterprise_type = 1 AND ce.user_id = :companyId)\n"
                + "     )";

    public static final String GET_LIST_PRICING_BY_COUPON =
            "SELECT "
                + "    p.id AS productId, "
                + "    p.pricing_name AS productName, "
                + "    s.service_name AS serviceName "
                + "FROM "
                + "    {h-schema}pricing p "
                + "JOIN {h-schema}coupon_pricing cp ON "
                + "    p.id = cp.pricing_id "
                + "    AND p.deleted_flag = 1 "
                + "    AND p.approve = 1 "
                + "    AND cp.coupon_id = :couponId  "
                + "JOIN {h-schema}services s ON "
                + "    p.service_id = s.id "
                + "    AND s.deleted_flag = 1 "
                + "UNION ALL "
                + "SELECT "
                + "    p.id AS productId, "
                + "    p.combo_name AS productName , "
                + "    s.combo_name AS serviceName "
                + "FROM "
                + "    {h-schema}combo_plan p "
                + "JOIN {h-schema}coupon_combo_plan cp ON "
                + "    p.id = cp.combo_plan_id "
                + "    AND p.deleted_flag = 1 "
                + "    AND p.approve = 1 "
                + "    AND cp.coupon_id = :couponId "
                + "JOIN {h-schema}combo s ON "
                + "    p.combo_id = s.id "
                + "    AND s.deleted_flag = 1"
                + "UNION ALL "
                + "SELECT  "
                + "    p.id AS productId,  "
                + "    p.pricing_name AS productName,  "
                + "    s.service_name AS serviceName  "
                + "FROM  "
                + "    {h-schema}pricing p  "
                + "JOIN {h-schema}pricing_multi_plan pmp ON  "
                + "    pmp.pricing_id = p.id "
                + "    AND p.deleted_flag = 1  "
                + "    AND p.approve = 1 "
                + "JOIN {h-schema}coupon_pricing_plan_apply cppa  ON  "
                + "    pmp.id = cppa.pricing_multi_plan_id   "
                + "    AND cppa.coupon_id = :couponId   "
                + "JOIN {h-schema}services s ON  "
                + "    p.service_id = s.id  "
                + "    AND s.deleted_flag = 1";

    public static final String GET_SIZE_LIST_PRICING_BY_COUPON =
        "SELECT "
            + "     count(*) "
            + "FROM "
            + "     ( "
            + "     SELECT "
            + "          p.id AS productId "
            + "     FROM "
            + "          {h-schema}pricing p "
            + "     JOIN {h-schema}coupon_pricing cp ON "
            + "          p.id = cp.pricing_id "
            + "          AND p.deleted_flag = 1 "
            + "          AND p.approve = 1 "
            + "          AND cp.coupon_id = :couponId "
            + "     JOIN {h-schema}services s ON "
            + "          p.service_id = s.id "
            + "          AND s.deleted_flag = 1 "
            + "UNION ALL "
            + "     SELECT "
            + "          p.id AS productId "
            + "     FROM "
            + "          {h-schema}combo_plan p "
            + "     JOIN {h-schema}coupon_combo_plan cp ON "
            + "          p.id = cp.combo_plan_id "
            + "          AND p.deleted_flag = 1 "
            + "          AND p.approve = 1 "
            + "          AND cp.coupon_id = :couponId "
            + "     JOIN {h-schema}combo s ON "
            + "          p.combo_id = s.id "
            + "          AND s.deleted_flag = 1 "
            + "UNION ALL "
            + "     SELECT "
            + "          p.id AS productId "
            + "     FROM "
            + "          {h-schema}pricing p "
            + "     JOIN {h-schema}pricing_multi_plan pmp ON "
            + "          pmp.pricing_id = p.id "
            + "          AND p.deleted_flag = 1 "
            + "          AND p.approve = 1 "
            + "     JOIN {h-schema}coupon_pricing_plan_apply cppa ON "
            + "          pmp.id = cppa.pricing_multi_plan_id "
            + "          AND cppa.coupon_id = :couponId "
            + "     JOIN {h-schema}services s ON "
            + "          p.service_id = s.id "
            + "          AND s.deleted_flag = 1 ) t";

    public static final String COUNT_NUMBER_USED_COUPON =
        "SELECT "
            + "    count(a.id) "
            + "FROM "
            + "    ( "
            + "    SELECT "
            + "        sc.id "
            + "    FROM "
            + "        {h-schema}subscription_coupons sc "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        s.id = sc.subscription_id "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sc.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "        AND sc.coupon_id = :couponId "
            + "UNION ALL "
            + "    SELECT "
            + "        spc.id "
            + "    FROM "
            + "        {h-schema}subscription_pricing_coupon spc "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        s.id = spc.subscription_id "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = spc.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "        AND spc.coupon_id = :couponId "
            + "UNION ALL "
            + "    SELECT "
            + "        sac.id "
            + "    FROM "
            + "        {h-schema}subscription_addon_coupon sac "
            + "    JOIN {h-schema}subscription_addons sa ON "
            + "        sa.id = sac.subscription_addon_id "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        s.id = sa.subscription_id "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sac.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "        AND sac.coupon_id = :couponId "
            + "UNION ALL "
            + "    SELECT "
            + "        sac.id "
            + "    FROM "
            + "        {h-schema}subscription_combo_coupon sac "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        s.id = sac.subscription_id "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sac.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "        AND sac.coupon_id = :couponId "
            + "UNION ALL "
            + "    SELECT "
            + "        sac.id "
            + "    FROM "
            + "        {h-schema}subscription_combo_addon_coupon sac "
            + "    JOIN {h-schema}subscription_combo_addon sa ON "
            + "        sa.id = sac.subscription_combo_addon_id "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        s.id = sa.subscription_id "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sac.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "        AND sac.coupon_id = :couponId ) a ";

    public static final String GET_COMBO_DETAIL_BY_SUB_ID =
        "    with countService as (\n" +
            "        select \n" +
            "            count(1) as countService,\n" +
            "            combo_plan.id as comboPlanId\n" +
            "        from \n" +
            "            {h-schema}combo_pricing\n" +
            "            join {h-schema}combo_plan on combo_pricing.id_combo_plan = combo_plan.id\n" +
            "            join {h-schema}pricing on pricing.id = combo_pricing.object_id AND combo_pricing.object_type = 'PRICING' \n" +
            "            join {h-schema}services on pricing.service_id = services.id\n" +
            "        group by \n" +
            "            combo_plan.id\n" +
            "    )\n" +
            "select \n" +
            "    users.name as developerName, \n" +
            "    combo.combo_name AS comboName,\n" +
            "    case\n" +
            "        when combo.combo_owner = ANY (ARRAY[0, 1]) then 'ON'\n" +
            "        else 'OS'\n" +
            "    end as serviceOwner, \n" +
            "    combo_plan.combo_name as comboPlanName,\n" +
            "    countService.countService\n" +
            "from\n" +
            "    {h-schema}subscriptions  \n" +
            "    left join {h-schema}combo_plan ON subscriptions.combo_plan_id = combo_plan.id AND combo_plan.deleted_flag = 1 \n" +
            "    left join {h-schema}combo on combo.id = combo_plan.combo_id AND combo.deleted_flag = 1 \n" +
            "    left join {h-schema}users on users.id = combo.created_by \n" +
            "    left join countService on combo_plan.id = countService.comboPlanId \n" +
            "where \n" +
            "    subscriptions.id = :subId";

    public static final String GET_DEVICE_DETAIL_BY_SUB_ID =
        "select \n" +
            "    users.name as developerName, \n" +
            "    services.service_name AS deviceName,\n" +
            "    case\n" +
            "        when services.service_owner = ANY (ARRAY[0, 1]) then 'ON'\n" +
            "        else 'OS'\n" +
            "    end as serviceOwner, \n" +
            "    subscriptions.variant_name as variantName\n" +
            "from\n" +
            "    {h-schema}subscriptions  \n" +
            "    join {h-schema}services on services.id = subscriptions.service_id\n" +
            "    join {h-schema}users on services.user_id = users.id\n" +
            "where \n" +
            "    subscriptions.id = :subId\n";

    public static final String GET_SERVICE_DETAIL_BY_SUB_ID =
        "select \n" +
            "    users.name as developerName, \n" +
            "    services.service_name AS serviceName,\n" +
            "    pricing.pricing_name as pricingName,\n" +
            "    coalesce(pricing.pricing_plan, pricing_multi_plan.pricing_plan) as pricingPlan,\n" +
            "    pricing.is_one_time as isOneTime,\n" +
            "    case\n" +
            "        when services.service_owner = ANY (ARRAY[0, 1]) then 'ON'\n" +
            "        else 'OS'\n" +
            "    end as serviceOwner, \n" +
            "    pricing_multi_plan.circle_type as cycleType,\n" +
            "    pricing_multi_plan.payment_cycle as paymentCycle\n" +
            "from\n" +
            "    {h-schema}subscriptions  \n" +
            "    left join {h-schema}services on services.id = subscriptions.service_id\n" +
            "    left join {h-schema}users on services.user_id = users.id\n" +
            "    left join {h-schema}pricing on subscriptions.pricing_id = pricing.id\n" +
            "    left join {h-schema}pricing_multi_plan on subscriptions.pricing_multi_plan_id = pricing_multi_plan.id\n" +
            "where \n" +
            "    subscriptions.id = :subId";

    public static final String COUNT_NUMBER_USED_COUPON_BY_LST_COUPON_ID =
        "SELECT \n" +
            "    coupon_info.coupon_id as couponId,\n" +
            "    count(coupon_info.id) as count \n" +
            "FROM \n" +
            "    ( \n" +
            "    SELECT \n" +
            "        subscription_coupons.coupon_id,\n" +
            "        subscription_coupons.id \n" +
            "    FROM \n" +
            "        {h-schema}subscription_coupons \n" +
            "    JOIN {h-schema}subscriptions ON \n" +
            "        subscriptions.id = subscription_coupons.subscription_id \n" +
            "        AND subscriptions.confirm_status = 1 \n" +
            "    JOIN {h-schema}coupons  ON \n" +
            "        coupons.id = subscription_coupons.coupon_id \n" +
            "        AND coupons.deleted_flag = 1 \n" +
            "        AND subscription_coupons.coupon_id in (:lstCouponId) \n" +
            "UNION ALL \n" +
            "    SELECT \n" +
            "        spc.coupon_id,\n" +
            "        spc.id \n" +
            "    FROM \n" +
            "        {h-schema}subscription_pricing_coupon spc \n" +
            "    JOIN {h-schema}subscriptions ON \n" +
            "        subscriptions.id = spc.subscription_id \n" +
            "        AND subscriptions.confirm_status = 1 \n" +
            "    JOIN {h-schema}coupons ON \n" +
            "        coupons.id = spc.coupon_id \n" +
            "        AND coupons.deleted_flag = 1 \n" +
            "        AND spc.coupon_id in (:lstCouponId) \n" +
            "UNION ALL \n" +
            "    SELECT \n" +
            "        sac.coupon_id,\n" +
            "        sac.id \n" +
            "    FROM \n" +
            "        {h-schema}subscription_addon_coupon sac \n" +
            "    JOIN {h-schema}subscription_addons ON \n" +
            "        subscription_addons.id = sac.subscription_addon_id \n" +
            "    JOIN {h-schema}subscriptions ON \n" +
            "        subscriptions.id = subscription_addons.subscription_id \n" +
            "        AND subscriptions.confirm_status = 1 \n" +
            "    JOIN {h-schema}coupons ON \n" +
            "        coupons.id = sac.coupon_id \n" +
            "        AND coupons.deleted_flag = 1 \n" +
            "        AND sac.coupon_id in (:lstCouponId)  \n" +
            "UNION ALL \n" +
            "    SELECT \n" +
            "        sac.coupon_id,\n" +
            "        sac.id \n" +
            "    FROM \n" +
            "        {h-schema}subscription_combo_coupon sac \n" +
            "    JOIN {h-schema}subscriptions ON \n" +
            "        subscriptions.id = sac.subscription_id \n" +
            "        AND subscriptions.confirm_status = 1 \n" +
            "    JOIN {h-schema}coupons ON \n" +
            "        coupons.id = sac.coupon_id \n" +
            "        AND coupons.deleted_flag = 1 \n" +
            "        AND sac.coupon_id in (:lstCouponId) \n" +
            "UNION ALL \n" +
            "    SELECT \n" +
            "        sac.coupon_id,\n" +
            "        sac.id \n" +
            "    FROM \n" +
            "        {h-schema}subscription_combo_addon_coupon sac \n" +
            "    JOIN {h-schema}subscription_combo_addon sa ON \n" +
            "        sa.id = sac.subscription_combo_addon_id \n" +
            "    JOIN {h-schema}subscriptions ON \n" +
            "        subscriptions.id = sa.subscription_id \n" +
            "        AND subscriptions.confirm_status = 1 \n" +
            "    JOIN {h-schema}coupons ON \n" +
            "        coupons.id = sac.coupon_id \n" +
            "        AND coupons.deleted_flag = 1 \n" +
            "        AND sac.coupon_id in (:lstCouponId)  ) coupon_info \n" +
            "group by coupon_info.coupon_id  ";

    public static final String COUNT_NUMBER_COMPANY_USED_COUPON =
        "SELECT "
            + "    count(a.id) "
            + "FROM "
            + "    ( "
            + "    SELECT "
            + "        sc.id "
            + "    FROM "
            + "        {h-schema}subscription_coupons sc "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        sc.subscription_id = s.id "
            + "        AND s.user_id = :companyId "
            + "        AND sc.coupon_id = :couponId "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sc.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "UNION ALL "
            + "    SELECT "
            + "        spc.id "
            + "    FROM "
            + "        {h-schema}subscription_pricing_coupon spc "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        spc.subscription_id = s.id "
            + "        AND s.user_id = :companyId "
            + "        AND spc.coupon_id = :couponId "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = spc.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "UNION ALL "
            + "    SELECT "
            + "        spc.id "
            + "    FROM "
            + "        {h-schema}subscription_combo_coupon spc "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        spc.subscription_id = s.id "
            + "        AND s.user_id = :companyId "
            + "        AND spc.coupon_id = :couponId "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = spc.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "UNION ALL "
            + "    SELECT "
            + "        sac.id "
            + "    FROM "
            + "        {h-schema}subscription_addon_coupon sac "
            + "    JOIN {h-schema}subscription_addons sa ON "
            + "        sac.subscription_addon_id = sa.id "
            + "        AND sac.coupon_id = :couponId "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        sa.subscription_id = s.id "
            + "        AND s.user_id = :companyId "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sac.coupon_id "
            + "        AND c.deleted_flag = 1 "
            + "UNION ALL "
            + "    SELECT "
            + "        sac.id "
            + "    FROM "
            + "        {h-schema}subscription_combo_addon_coupon sac "
            + "    JOIN {h-schema}subscription_combo_addon sa ON "
            + "        sac.subscription_combo_addon_id = sa.id "
            + "        AND sac.coupon_id = :couponId "
            + "    JOIN {h-schema}subscriptions s ON "
            + "        sa.subscription_id = s.id "
            + "        AND s.user_id = :companyId "
            + "        AND s.confirm_status = 1 "
            + "    JOIN {h-schema}coupons c ON "
            + "        c.id = sac.coupon_id "
            + "        AND c.deleted_flag = 1 ) a ";

    public static final String COUNT_NUMBER_SUBSCRIPTION_COMPANY_USED_COUPON =
            "SELECT \n" +
                    "    count(DISTINCT a.id)\n" +
                    "FROM \n" +
                    "    ( \n" +
                    "    SELECT \n" +
                    "        s.id\n" +
                    "    FROM \n" +
                    "        {h-schema}subscription_coupons sc \n" +
                    "    JOIN {h-schema}subscriptions s ON \n" +
                    "        sc.subscription_id = s.id \n" +
                    "        AND s.user_id = :companyId \n" +
                    "        AND sc.coupon_id = :couponId \n" +
                    "        AND s.confirm_status = 1 \n" +
                    "    JOIN {h-schema}coupons c ON \n" +
                    "        c.id = sc.coupon_id \n" +
                    "        AND c.deleted_flag = 1 \n" +
                    "UNION ALL \n" +
                    "    SELECT \n" +
                    "        s.id\n" +
                    "    FROM \n" +
                    "        {h-schema}subscription_pricing_coupon spc \n" +
                    "    JOIN {h-schema}subscriptions s ON \n" +
                    "        spc.subscription_id = s.id \n" +
                    "        AND s.user_id = :companyId \n" +
                    "        AND spc.coupon_id = :couponId \n" +
                    "        AND s.confirm_status = 1 \n" +
                    "    JOIN {h-schema}coupons c ON \n" +
                    "        c.id = spc.coupon_id \n" +
                    "        AND c.deleted_flag = 1 \n" +
                    "UNION ALL \n" +
                    "    SELECT \n" +
                    "        s.id\n" +
                    "    FROM \n" +
                    "        {h-schema}subscription_combo_coupon spc \n" +
                    "    JOIN {h-schema}subscriptions s ON \n" +
                    "        spc.subscription_id = s.id \n" +
                    "        AND s.user_id = :companyId \n" +
                    "        AND spc.coupon_id = :couponId \n" +
                    "        AND s.confirm_status = 1 \n" +
                    "    JOIN {h-schema}coupons c ON \n" +
                    "        c.id = spc.coupon_id \n" +
                    "        AND c.deleted_flag = 1 \n" +
                    "UNION ALL \n" +
                    "    SELECT \n" +
                    "        s.id\n" +
                    "    FROM \n" +
                    "        {h-schema}subscription_addon_coupon sac \n" +
                    "    JOIN {h-schema}subscription_addons sa ON \n" +
                    "        sac.subscription_addon_id = sa.id \n" +
                    "        AND sac.coupon_id = :couponId \n" +
                    "    JOIN {h-schema}subscriptions s ON \n" +
                    "        sa.subscription_id = s.id \n" +
                    "        AND s.user_id = :companyId \n" +
                    "        AND s.confirm_status = 1 \n" +
                    "    JOIN {h-schema}coupons c ON \n" +
                    "        c.id = sac.coupon_id \n" +
                    "        AND c.deleted_flag = 1 \n" +
                    "UNION ALL \n" +
                    "    SELECT \n" +
                    "        s.id\n" +
                    "    FROM \n" +
                    "        {h-schema}subscription_combo_addon_coupon sac \n" +
                    "    JOIN {h-schema}subscription_combo_addon sa ON \n" +
                    "        sac.subscription_combo_addon_id = sa.id \n" +
                    "        AND sac.coupon_id = :couponId \n" +
                    "    JOIN {h-schema}subscriptions s ON \n" +
                    "        sa.subscription_id = s.id \n" +
                    "        AND s.user_id = :companyId \n" +
                    "        AND s.confirm_status = 1 \n" +
                    "    JOIN {h-schema}coupons c ON \n" +
                    "        c.id = sac.coupon_id \n" +
                    "        AND c.deleted_flag = 1 ) a ";

    public static final String COUNT_USED_COUPON_BY_LST_COUPON_ID =
        "SELECT \n" +
            "    coupon_info.coupon_id as couponId,\n" +
            "    count(coupon_info.id) as count \n" +
            "FROM \n" +
            "    ( \n" +
            "        SELECT \n" +
            "            subscription_coupons.coupon_id,\n" +
            "            subscription_coupons.id \n" +
            "        FROM \n" +
            "            {h-schema}subscription_coupons \n" +
            "        JOIN {h-schema}subscriptions ON \n" +
            "            subscription_coupons.subscription_id = subscriptions.id \n" +
            "            AND subscriptions.user_id = :companyId \n" +
            "            AND subscription_coupons.coupon_id in (:lstCouponId) \n" +
            "            AND subscriptions.confirm_status = 1 \n" +
            "        JOIN {h-schema}coupons ON \n" +
            "            coupons.id = subscription_coupons.coupon_id \n" +
            "            AND coupons.deleted_flag = 1 \n" +
            "    UNION ALL \n" +
            "        SELECT \n" +
            "            spc.coupon_id,\n" +
            "            spc.id \n" +
            "        FROM \n" +
            "            {h-schema}subscription_pricing_coupon spc \n" +
            "        JOIN {h-schema}subscriptions ON \n" +
            "            spc.subscription_id = subscriptions.id \n" +
            "            AND subscriptions.user_id = :companyId \n" +
            "            AND spc.coupon_id in (:lstCouponId) \n" +
            "            AND subscriptions.confirm_status = 1 \n" +
            "        JOIN {h-schema}coupons ON \n" +
            "            coupons.id = spc.coupon_id \n" +
            "            AND coupons.deleted_flag = 1 \n" +
            "    UNION ALL \n" +
            "        SELECT \n" +
            "            spc.coupon_id,\n" +
            "            spc.id \n" +
            "        FROM \n" +
            "            {h-schema}subscription_combo_coupon spc \n" +
            "        JOIN {h-schema}subscriptions ON \n" +
            "            spc.subscription_id = subscriptions.id \n" +
            "            AND subscriptions.user_id = :companyId \n" +
            "            AND spc.coupon_id in (:lstCouponId) \n" +
            "            AND subscriptions.confirm_status = 1 \n" +
            "        JOIN {h-schema}coupons ON \n" +
            "            coupons.id = spc.coupon_id \n" +
            "            AND coupons.deleted_flag = 1 \n" +
            "    UNION ALL \n" +
            "        SELECT \n" +
            "            sac.coupon_id,\n" +
            "            sac.id \n" +
            "        FROM \n" +
            "            {h-schema}subscription_addon_coupon sac \n" +
            "        JOIN {h-schema}subscription_addons sa ON \n" +
            "            sac.subscription_addon_id = sa.id \n" +
            "            AND sac.coupon_id in (:lstCouponId) \n" +
            "        JOIN {h-schema}subscriptions ON \n" +
            "            sa.subscription_id = subscriptions.id \n" +
            "            AND subscriptions.user_id = :companyId \n" +
            "            AND subscriptions.confirm_status = 1 \n" +
            "        JOIN {h-schema}coupons ON \n" +
            "            coupons.id = sac.coupon_id \n" +
            "            AND coupons.deleted_flag = 1 \n" +
            "    UNION ALL \n" +
            "        SELECT \n" +
            "            sac.coupon_id,\n" +
            "            sac.id \n" +
            "        FROM \n" +
            "            {h-schema}subscription_combo_addon_coupon sac \n" +
            "        JOIN {h-schema}subscription_combo_addon sa ON \n" +
            "            sac.subscription_combo_addon_id = sa.id \n" +
            "            AND sac.coupon_id in (:lstCouponId) \n" +
            "        JOIN {h-schema}subscriptions ON \n" +
            "            sa.subscription_id = subscriptions.id \n" +
            "            AND subscriptions.user_id = :companyId \n" +
            "            AND subscriptions.confirm_status = 1 \n" +
            "        JOIN {h-schema}coupons ON \n" +
            "            coupons.id = sac.coupon_id \n" +
            "            AND coupons.deleted_flag = 1\n" +
            "    ) coupon_info \n" +
            "group by coupon_info.coupon_id";

    public static final String GET_SUBSCRIPTION_BILLS =
        "SELECT "
            + "s.id AS id, "
            + "s.payment_method AS paymentMethod, "
            + "s.sub_code AS subCode, "
            + "s.start_charge_at AS currentPaymentDate, "
            + "b.current_payment_date AS requiredPaymentDate, "
            + "s2.id AS serviceId, "
            + "CASE s2.service_owner  "
            + "     WHEN 0 then 'SAAS' "
            + "     WHEN 1 then 'VNPT' "
            + "     WHEN 2 then 'OTHER' "
            + "     ELSE 'NONE' "
            + "END AS serviceOwner, "
            + "(COALESCE(c.combo_owner, s2.service_owner) = ANY (ARRAY[1]) ) AS exportInvoice, "
            + "c.id AS comboId, "
            + "s.is_one_time as isOneTime, "
            + "s.variant_id as variantId, "
            + "CASE c.combo_owner "
            + "     WHEN 0 then 'SAAS' "
            + "     WHEN 1 then 'VNPT' "
            + "     WHEN 2 then 'OTHER' "
            + "     ELSE 'NONE' "
            + "END AS comboOwner "
            + "FROM "
            + "     {h-schema}billings b "
            + "INNER JOIN {h-schema}subscriptions s ON "
            + "     b.subscriptions_id = s.id "
            + "LEFT JOIN {h-schema}services s2 ON  "
            + "     s2.id = s.service_id  "
            + "     and s.service_id IS NOT NULL "
            + "LEFT JOIN {h-schema}combo_plan cp ON  "
            + "     cp.id = s.combo_plan_id  "
            + "     and s.combo_plan_id IS NOT NULL "
            + "LEFT JOIN {h-schema}combo c ON  "
            + "     c.id = cp.combo_id  "
            + "WHERE "
            + "     s.id = :id "
            + "     AND s.deleted_flag = 1 "
            + "LIMIT 1";

    public static final String GET_MAX_SUB_CODE =
            "SELECT " +
            "     s.sub_code " +
            "FROM " +
            "     {h-schema}subscriptions s " +
            "ORDER BY " +
            "     id DESC " +
            "LIMIT 1";

    public static final String GET_CURRENT_CYCLE_SUB =
        "select "
         + "    sub.current_cycle "
         + "from "
         + "    {h-schema}subscriptions sub "
         + "where "
         + "    sub.id = :subId and sub.deleted_flag = 1";

    public static final String GET_LIST_SUBSCRIPTION_ON_FOR_BATCH =
        "SELECT "
        + "    s.* "
        + "FROM "
        + "    {h-schema}subscriptions s LEFT JOIN {h-schema}pricing p ON s.pricing_id = p.id "
        + "WHERE "
        + "    s.status = 2 "
        + "    AND p.pricing_type = 1 AND p.active_date <> 0"
        + "    AND s.confirm_status = 1 "
        + "    AND s.deleted_flag = 1 "
        + "    AND s.auto_payment = 1 "
        + "    AND s.payment_method = 1 "
        + "    AND s.end_current_cycle + 1 = :currentDate "
        + "    AND s.awaiting_cancel IS NULL "
        + "    AND s.give_away_main_sub_id IS NULL "
        + "    AND (s.change_date IS NULL OR s.change_date <> :currentDate) "
        + "    ORDER BY s.id ASC ";

    public static final String GET_ALL_SUBSCRIPTION_RENEW =
        "SELECT "
        + "    s.* "
        + "FROM "
        + "    {h-schema}subscriptions s LEFT JOIN {h-schema}pricing p ON s.pricing_id = p.id "
        + "WHERE "
        + "    s.status = 2 "
        + "    AND p.active_date <> 0"
        + "    AND s.confirm_status = 1 "
        + "    AND s.current_cycle_renew > s.current_cycle "
        + "    AND s.deleted_flag = 1 "
        + "    AND s.end_current_cycle + 1 = CURRENT_DATE "
        + "    AND s.awaiting_cancel IS NULL "
        + "    AND (s.change_date IS NULL OR s.change_date <> CURRENT_DATE) "
        + "    ORDER BY s.id ASC ";

    public static final String GET_LIST_SUBSCRIPTION_OS_FOR_BATCH =
            "SELECT "
                    + "    s.* "
                    + "FROM "
                    + "    {h-schema}subscriptions s LEFT JOIN {h-schema}pricing p ON s.pricing_id = p.id "
                    + "WHERE "
                    + "    s.status = 2 "
                    + "    AND p.pricing_type = 1 AND p.active_date <> 0"
                    + "    AND s.confirm_status = 1 "
                    + "    AND s.deleted_flag = 1 "
                    + "    AND s.payment_method IN (0, 2) "
                    + "    AND s.end_current_cycle + 1 = :currentDate "
                    + "    AND s.awaiting_cancel IS NULL "
                    + "    AND s.give_away_main_sub_id IS NULL "
                    + "    AND (s.change_date IS NULL OR s.change_date <> :currentDate) "
                    + "    ORDER BY s.id ASC ";

    public static final String GET_ALL_SUB_BY_PORTAL_TYPE_DEV_ADMIN =
        "SELECT  sc.id as id, " +
            "    p.pricingName as pricingName, " +
            "    concat(u.firstName,' ',u.lastName) as customerName, " +
            "    CASE " +
            "        WHEN sc.status = 0 THEN 'Đang chờ' " +
            "        WHEN sc.status = 1 THEN 'Dùng thử' " +
            "        WHEN sc.status = 2 THEN 'Hoạt động' " +
            "        WHEN sc.status = 3 THEN 'Hủy' " +
            "        WHEN sc.status = 4 THEN 'Kết thúc' " +
            "    END as status, " +
            "    CASE WHEN p.numberOfCycles =-1 THEN 'Không giới hạn' " +
            "         ELSE concat(p.numberOfCycles,' ',CASE WHEN p.cycleType = 0 THEN 'Ngày' " +
            "                                               WHEN p.cycleType = 1 THEN 'Tuần' " +
            "                                               WHEN p.cycleType = 2 THEN 'Tháng' " +
            "                                               WHEN p.cycleType = 3 THEN 'Năm' END ) END as cycle, " +
            "    sc.createdAt as createdAt, " +
            "    sc.totalAmount as totalAmount, " +
            "    sc.nextPaymentTime as nextPaymentTime " +
            "FROM " +
            "    Subscription sc " +
            "JOIN ServiceEntity s ON " +
            "    sc.serviceId = s.id " +
            "   AND s.deletedFlag = 1 " +
            "   AND sc.deletedFlag = 1 " +
            "JOIN Pricing p ON " +
            "    sc.pricingId = p.id " +
            "   AND p.deletedFlag = 1 " +
            "JOIN User urb ON " +
            "    p.createdBy = urb.id " +
            "   AND urb.deletedFlag = 1 " +
            "JOIN User u ON " +
            "    sc.userId = u.id " +
            "   AND u.deletedFlag = 1 " +
            "WHERE " +
            "    (:portalType = 1 or s.userId = :parentId) " +
            "    And (:searchText = '' OR (lower(s.serviceName) like %:searchText% OR lower(p.pricingName) like %:searchText% )) " +
            "    And (:status = -1 OR sc.status = :status) " +
            "    And (:cusName = '' OR concat(lower(u.firstName),' ',lower(u.lastName)) like %:cusName% ) " +
            "    And (:developerName = '' OR lower(urb.name) like %:developerName% ) " +
            "GROUP BY sc.id, pricingName, u.firstName,u.lastName, p.numberOfCycles, p.cycleType, status, createdAt, totalAmount, nextPaymentTime ";

    public static final String GET_BY_TRIAL_DAY =
        "SELECT s FROM Subscription s WHERE s.deletedFlag <> 0 AND s.status = 1 ";

    public static final String GET_SERVICE_BY_SUB_ID =
        "SELECT snew.id as id, snew.name as serviceName, snew.itemType as itemType "
            + "FROM ( "
            + "         SELECT s.id as id, s.service_name as name, 'SERVICE' as itemType "
            + "         FROM {h-schema}subscriptions sub "
            + "                  JOIN {h-schema}services s ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND s.service_owner <> 2 AND "
            + "                                     s.service_owner IS NOT NULL "
            + "         UNION ALL "
            + "         SELECT s.id as id, s.service_name as name, 'ADDON' as itemType "
            + "         FROM {h-schema}subscriptions sub "
            + "                  JOIN {h-schema}subscription_addons sa ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                  JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                  JOIN {h-schema}services s ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "         WHERE s.id <> (SELECT s.id as id "
            + "                        FROM {h-schema}subscriptions sub "
            + "                                 JOIN {h-schema}services s ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND s.service_owner <> 2 AND "
            + "                                                    s.service_owner IS NOT NULL) "
            + "         UNION ALL "
            + "         SELECT DISTINCT COALESCE(s1.id, s2.id) as id, COALESCE(s1.service_name, s2.service_name) as name, 'COUPON' as itemType "
            + "         FROM {h-schema}subscriptions sub "
            + "                  JOIN {h-schema}subscription_coupons sc ON sc.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                  LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = sc.coupon_id "
            + "                  LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sc.coupon_id "
            + "                  LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                  LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                  LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                  LEFT JOIN {h-schema}services s1 ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                  LEFT JOIN {h-schema}services s2 ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "         WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "           AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}services s ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND "
            + "                                                                          s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                              UNION ALL "
            + "                                              SELECT s.id as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}subscription_addons sa "
            + "                                                            ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                       JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                       JOIN {h-schema}services s "
            + "                                                            ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                              WHERE s.id <> (SELECT s.id as id "
            + "                                                             FROM {h-schema}subscriptions sub "
            + "                                                                      JOIN {h-schema}services s "
            + "                                                                           ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND "
            + "                                                                              s.service_owner <> 2 AND s.service_owner IS NOT NULL)) "
            + "         UNION ALL "
            + "         SELECT DISTINCT COALESCE(s1.id, s2.id) as id, COALESCE(s1.service_name, s2.service_name) as name, 'COUPON' as itemType "
            + "         FROM {h-schema}subscriptions sub "
            + "                  JOIN {h-schema}subscription_addons sa ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                  JOIN {h-schema}subscription_addon_coupon sac ON sac.subscription_addon_id = sa.id "
            + "                  LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = sac.coupon_id "
            + "                  LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sac.coupon_id "
            + "                  LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                  LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                  LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                  LEFT JOIN {h-schema}services s1 ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                  LEFT JOIN {h-schema}services s2 ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "         WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "           AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}services s ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND "
            + "                                                                          s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                              UNION ALL "
            + "                                              SELECT s.id as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}subscription_addons sa "
            + "                                                            ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                       JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                       JOIN {h-schema}services s "
            + "                                                            ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                              WHERE s.id <> (SELECT s.id as id "
            + "                                                             FROM {h-schema}subscriptions sub "
            + "                                                                      JOIN {h-schema}services s "
            + "                                                                           ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND "
            + "                                                                              s.service_owner <> 2 AND s.service_owner IS NOT NULL) "
            + "                                              UNION ALL "
            + "                                              SELECT DISTINCT COALESCE(s1.id, s2.id) as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}subscription_coupons sc "
            + "                                                            ON sc.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                       LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = sc.coupon_id "
            + "                                                       LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sc.coupon_id "
            + "                                                       LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                                                       LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                                                       LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                                                       LEFT JOIN {h-schema}services s1 "
            + "                                                                 ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                                                       LEFT JOIN {h-schema}services s2 "
            + "                                                                 ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "                                              WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "                                                AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}services s "
            + "                                                                                                 ON sub.service_id = s.id AND sub.id = :id AND "
            + "                                                                                                    sub.deleted_flag = 1 AND "
            + "                                                                                                    s.service_owner <> 2 AND "
            + "                                                                                                    s.service_owner IS NOT NULL "
            + "                                                                                   UNION ALL "
            + "                                                                                   SELECT s.id as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}subscription_addons sa "
            + "                                                                                                 ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                                                            JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                                                            JOIN {h-schema}services s "
            + "                                                                                                 ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                                                                   WHERE s.id <> (SELECT s.id as id "
            + "                                                                                                  FROM {h-schema}subscriptions sub "
            + "                                                                                                           JOIN {h-schema}services s "
            + "                                                                                                                ON sub.service_id = s.id AND "
            + "                                                                                                                   sub.id = :id AND "
            + "                                                                                                                   sub.deleted_flag = 1 AND "
            + "                                                                                                                   s.service_owner <> 2 AND "
            + "                                                                                                                   s.service_owner IS NOT NULL))) "
            + "         UNION ALL "
            + "         SELECT DISTINCT COALESCE(s1.id, s2.id) as id, COALESCE(s1.service_name, s2.service_name) as name, 'COUPON' as itemType "
            + "         FROM {h-schema}subscriptions sub "
            + "                  JOIN {h-schema}subscription_pricing_coupon spc ON spc.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                  LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = spc.coupon_id "
            + "                  LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = spc.coupon_id "
            + "                  LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                  LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                  LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                  LEFT JOIN {h-schema}services s1 ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                  LEFT JOIN {h-schema}services s2 ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "         WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "           AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}services s ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND "
            + "                                                                          s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                              UNION ALL "
            + "                                              SELECT s.id as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}subscription_addons sa "
            + "                                                            ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                       JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                       JOIN {h-schema}services s "
            + "                                                            ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                              WHERE s.id <> (SELECT s.id as id "
            + "                                                             FROM {h-schema}subscriptions sub "
            + "                                                                      JOIN {h-schema}services s "
            + "                                                                           ON sub.service_id = s.id AND sub.id = :id AND sub.deleted_flag = 1 AND "
            + "                                                                              s.service_owner <> 2 AND s.service_owner IS NOT NULL) "
            + "                                              UNION ALL "
            + "                                              SELECT DISTINCT COALESCE(s1.id, s2.id) as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}subscription_coupons sc "
            + "                                                            ON sc.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                       LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = sc.coupon_id "
            + "                                                       LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sc.coupon_id "
            + "                                                       LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                                                       LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                                                       LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                                                       LEFT JOIN {h-schema}services s1 "
            + "                                                                 ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                                                       LEFT JOIN {h-schema}services s2 "
            + "                                                                 ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "                                              WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "                                                AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}services s "
            + "                                                                                                 ON sub.service_id = s.id AND sub.id = :id AND "
            + "                                                                                                    sub.deleted_flag = 1 AND "
            + "                                                                                                    s.service_owner <> 2 AND "
            + "                                                                                                    s.service_owner IS NOT NULL "
            + "                                                                                   UNION ALL "
            + "                                                                                   SELECT s.id as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}subscription_addons sa "
            + "                                                                                                 ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                                                            JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                                                            JOIN {h-schema}services s "
            + "                                                                                                 ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                                                                   WHERE s.id <> (SELECT s.id as id "
            + "                                                                                                  FROM {h-schema}subscriptions sub "
            + "                                                                                                           JOIN {h-schema}services s "
            + "                                                                                                                ON sub.service_id = s.id AND "
            + "                                                                                                                   sub.id = :id AND "
            + "                                                                                                                   sub.deleted_flag = 1 AND "
            + "                                                                                                                   s.service_owner <> 2 AND "
            + "                                                                                                                   s.service_owner IS NOT NULL)) "
            + "                                              UNION ALL "
            + "                                              SELECT DISTINCT COALESCE(s1.id, s2.id) as id "
            + "                                              FROM {h-schema}subscriptions sub "
            + "                                                       JOIN {h-schema}subscription_addons sa "
            + "                                                            ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                       JOIN {h-schema}subscription_addon_coupon sac ON sac.subscription_addon_id = sa.id "
            + "                                                       LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = sac.coupon_id "
            + "                                                       LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sac.coupon_id "
            + "                                                       LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                                                       LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                                                       LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                                                       LEFT JOIN {h-schema}services s1 "
            + "                                                                 ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                                                       LEFT JOIN {h-schema}services s2 "
            + "                                                                 ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "                                              WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "                                                AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}services s "
            + "                                                                                                 ON sub.service_id = s.id AND sub.id = :id AND "
            + "                                                                                                    sub.deleted_flag = 1 AND "
            + "                                                                                                    s.service_owner <> 2 AND "
            + "                                                                                                    s.service_owner IS NOT NULL "
            + "                                                                                   UNION ALL "
            + "                                                                                   SELECT s.id as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}subscription_addons sa "
            + "                                                                                                 ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                                                            JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                                                            JOIN {h-schema}services s "
            + "                                                                                                 ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                                                                   WHERE s.id <> (SELECT s.id as id "
            + "                                                                                                  FROM {h-schema}subscriptions sub "
            + "                                                                                                           JOIN {h-schema}services s "
            + "                                                                                                                ON sub.service_id = s.id AND "
            + "                                                                                                                   sub.id = :id AND "
            + "                                                                                                                   sub.deleted_flag = 1 AND "
            + "                                                                                                                   s.service_owner <> 2 AND "
            + "                                                                                                                   s.service_owner IS NOT NULL) "
            + "                                                                                   UNION ALL "
            + "                                                                                   SELECT DISTINCT COALESCE(s1.id, s2.id) as id "
            + "                                                                                   FROM {h-schema}subscriptions sub "
            + "                                                                                            JOIN {h-schema}subscription_coupons sc "
            + "                                                                                                 ON sc.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                                                            LEFT JOIN {h-schema}coupon_pricing cp ON cp.coupon_id = sc.coupon_id "
            + "                                                                                            LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sc.coupon_id "
            + "                                                                                            LEFT JOIN {h-schema}pricing p1 ON p1.id = cp.pricing_id "
            + "                                                                                            LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id "
            + "                                                                                            LEFT JOIN {h-schema}pricing p2 ON p2.id = pmp.pricing_id "
            + "                                                                                            LEFT JOIN {h-schema}services s1 "
            + "                                                                                                      ON p1.service_id = s1.id AND s1.service_owner <> 2 AND s1.service_owner IS NOT NULL "
            + "                                                                                            LEFT JOIN {h-schema}services s2 "
            + "                                                                                                      ON p2.service_id = s2.id AND s2.service_owner <> 2 AND s2.service_owner IS NOT NULL "
            + "                                                                                   WHERE COALESCE(s1.id, s2.id) IS NOT NULL "
            + "                                                                                     AND COALESCE(s1.id, s2.id) NOT IN (SELECT s.id as id "
            + "                                                                                                                        FROM {h-schema}subscriptions sub "
            + "                                                                                                                                 JOIN {h-schema}services s "
            + "                                                                                                                                      ON sub.service_id = s.id AND "
            + "                                                                                                                                         sub.id = :id AND "
            + "                                                                                                                                         sub.deleted_flag = 1 AND "
            + "                                                                                                                                         s.service_owner <> 2 AND "
            + "                                                                                                                                         s.service_owner IS NOT NULL "
            + "                                                                                                                        UNION ALL "
            + "                                                                                                                        SELECT s.id as id "
            + "                                                                                                                        FROM {h-schema}subscriptions sub "
            + "                                                                                                                                 JOIN {h-schema}subscription_addons sa "
            + "                                                                                                                                      ON sa.subscription_id = sub.id AND sub.id = :id AND sub.deleted_flag = 1 "
            + "                                                                                                                                 JOIN {h-schema}addons addo ON addo.id = sa.addONs_id "
            + "                                                                                                                                 JOIN {h-schema}services s "
            + "                                                                                                                                      ON s.id = addo.service_id AND s.service_owner <> 2 AND s.service_owner IS NOT NULL "
            + "                                                                                                                        WHERE s.id <> "
            + "                                                                                                                              (SELECT s.id as id "
            + "                                                                                                                               FROM {h-schema}subscriptions sub "
            + "                                                                                                                                        JOIN {h-schema}services s "
            + "                                                                                                                                             ON sub.service_id = s.id AND "
            + "                                                                                                                                                sub.id = :id AND "
            + "                                                                                                                                                sub.deleted_flag = 1 AND "
            + "                                                                                                                                                s.service_owner <> 2 AND "
            + "                                                                                                                                                s.service_owner IS NOT NULL)))) "
            + "     ) as snew "
            + "ORDER BY name ";

    public static final String GET_PAY_MENT_METHOD_SME =
        "SELECT s.payment_method as paymentMethod "
            + "FROM {h-schema} subscriptions s "
            + "WHERE s.id = :id "
            + "AND s.deleted_flag = 1";
    public static final String GET_SUBSCRIPTION_COMMON =
        "SELECT new com.dto.subscriptions.responseDTO.SubscriptionDetailCommonDTO ("
            + "    s2.id, "
            + "    s2.serviceName, "
            + "    s2.sapoDescription, "
            + "    CASE"
            + "         WHEN u.parentId = -1 THEN u.name "
            + "         ELSE parent.name "
            + "    END AS name, "
            + "    p2.id, "
            + "    p2.pricingName, "
            + "    s.createdAt, "
            + "    CASE "
            + "        WHEN s.status = 0 THEN 'FUTURE' "
            + "        WHEN s.status = 1 THEN 'IN_TRIAL' "
            + "        WHEN s.status = 2 THEN 'ACTIVE' "
            + "        WHEN s.status = 3 THEN 'CANCELLED' "
            + "        WHEN s.status = 4 THEN 'NON_RENEWING' "
            + "    END AS status, "
            + "    COALESCE(s.startedAtSwap, s.startedAt), "
            + "    s.expiredTime, "
            + "    p2.paymentCycle, "
            + "    CASE "
            + "        WHEN p2.cycleType = 0 THEN 'DAILY' "
            + "        WHEN p2.cycleType = 1 THEN 'WEEKLY' "
            + "        WHEN p2.cycleType = 2 THEN 'MONTHLY' "
            + "        WHEN p2.cycleType = 3 THEN 'YEARLY' "
            + "    END AS cycleType, "
            + "    s.currentCycle, "
            + "    s.numberOfCycles, "
            + " CASE "
            + "        WHEN p2.pricingPlan = 0 THEN 'FLAT_RATE' "
            + "        WHEN p2.pricingPlan = 1 THEN 'UNIT' "
            + "        WHEN p2.pricingPlan = 2 THEN 'TIER' "
            + "        WHEN p2.pricingPlan = 3 THEN 'VOLUME' "
            + "        WHEN p2.pricingPlan = 4 THEN 'STAIR_STEP' "
            + "    END AS pricingPlan,"
            + "    s.quantity, "
            + "    s.usedQuantity, "
            + "    CASE "
            + "        WHEN p2.pricingType = 0 THEN 'PREPAY' "
            + "        WHEN p2.pricingType = 1 THEN 'POSTPAID' "
            + "    END AS pricingType,"
            + "    CASE "
            + "        WHEN s.awaitingCancel = 0 THEN 'CANCELED' "
            + "        WHEN s.awaitingCancel = 1 THEN 'AWAITING_CANCEL' "
            + "    END AS awaitingCancel, "
            + "     s.canceledBy, "
            + "     CASE "
            + " WHEN s2.serviceOwner = 0 THEN 'SAAS' "
            + " WHEN s2.serviceOwner = 1 THEN 'VNPT' "
            + " WHEN s2.serviceOwner = 2 THEN 'NONE' "
            + " WHEN s2.serviceOwner = 3 THEN 'OTHER' "
            + " END AS serviceOwner,"
            + "     CASE"
            + "  WHEN s.regType = 0 THEN 'TRIAL' "
            + "  WHEN s.regType = 1 THEN 'OFFICIAL' "
            + " END AS regType, "
            + " s.createdBy, "
            + "     CASE "
            + " WHEN p2.hasRenew = 0 THEN 'NO' "
            + " ELSE 'YES' "
            + " END AS isRenewal, s2.registerEcontract as registerEcontract ) "
            + "FROM Subscription s "
            + "LEFT JOIN ServiceEntity s2 ON "
            + "s.serviceId = s2.id "
            + "AND s2.deletedFlag = 1 "
            + "LEFT JOIN Pricing p2 ON "
            + "s.pricingId = p2.id "
            + "AND p2.deletedFlag = 1 "
            + "LEFT JOIN User u ON "
            + "p2.createdBy = u.id "
            + "AND u.deletedFlag = 1 "
            + "LEFT JOIN User parent ON "
            + "  u.parentId = parent.id "
            + " WHERE "
            + "s.id = :id "
            + "AND s.deletedFlag = 1";

    public static final String GET_SUBSCRIPTION_COMMON_INTERFACE =
            "SELECT \n" +
            "      s.id as subscriptionId, \n" +
            "      s2.id as serviceId, \n" +
            "      s.service_draft_id as serviceDraftId, \n" +
            "      p2.is_one_time as isOneTime, \n" +
            "      s2.service_name as serviceName, s.reactive_date as reactiveDate, \n" +
            "      s2.sapo_description as shortDescription, \n" +
            "      s2.service_type_application as serviceTypeApplication, \n" +
            "      CASE\n" +
            "           WHEN u.parent_id = -1 THEN u.name \n" +
            "           ELSE parent.name \n" +
            "      END as developerName, \n" +
            "      p2.id as pricingId , \n" +
            "      p2.pricing_name as pricingName, \n" +
            "      s.created_at as createdAt, s.reactive_status as reactiveStatus, \n" +
            "      CASE \n" +
            "          WHEN s.status = 0 THEN 'FUTURE' \n" +
            "          WHEN s.status = 1 THEN 'IN_TRIAL' \n" +
            "          WHEN s.status = 2 THEN 'ACTIVE' \n" +
            "          WHEN s.status = 3 THEN 'CANCELLED' \n" +
            "          WHEN s.status = 5 THEN 'NOT_EXTEND' \n" +
            "          WHEN s.status = 4 AND s2.service_owner in (0,1) AND s.expired_time < DATE(NOW()) AND p2.has_renew = 1  \n" +
            "               AND s.expired_time + cast ((SELECT payment_date_fail_on FROM {h-schema}system_params WHERE param_type = 'COUPON') as INTEGER ) - DATE(NOW()) > 0 THEN 'ACTIVE' \n" +
            "          WHEN s.status = 4 AND s2.service_owner in (2,3) AND s.expired_time < DATE(NOW()) AND p2.has_renew = 1 \n" +
            "               AND s.expired_time + cast ((SELECT payment_date_fail_off FROM {h-schema}system_params WHERE param_type = 'COUPON') as INTEGER ) - DATE(NOW()) > 0 THEN 'ACTIVE' \n" +
            "      ELSE 'NON_RENEWING' \n" +
            "      END AS status, \n" +
            "      s.started_at as startDateSubscription, \n" +
            "      s.expired_time as endDateSubscription, \n" +
            "      p2.payment_cycle as paymentCycle, \n" +
            "      CASE \n" +
            "          WHEN p2.cycle_type = 0 THEN 'DAILY' \n" +
            "          WHEN p2.cycle_type = 1 THEN 'WEEKLY' \n" +
            "          WHEN p2.cycle_type = 2 THEN 'MONTHLY' \n" +
            "          WHEN p2.cycle_type = 3 THEN 'YEARLY' \n" +
            "      END AS cycleType, \n" +
            "      s.current_cycle as currentCycle, \n" +
            "      s.number_of_cycles as numberOfCycles, \n" +
            "   CASE \n" +
            "          WHEN p2.pricing_plan = 0 THEN 'FLAT_RATE' \n" +
            "          WHEN p2.pricing_plan = 1 THEN 'UNIT' \n" +
            "          WHEN p2.pricing_plan = 2 THEN 'TIER' \n" +
            "          WHEN p2.pricing_plan = 3 THEN 'VOLUME' \n" +
            "          WHEN p2.pricing_plan = 4 THEN 'STAIR_STEP' \n" +
            "      END AS pricingPlan,\n" +
            "      s.quantity, \n" +
            "      s.used_quantity as usedQuantity, \n" +
            "      CASE \n" +
            "          WHEN p2.pricing_type = 0 THEN 'PREPAY' \n" +
            "          WHEN p2.pricing_type = 1 THEN 'POSTPAID' \n" +
            "      END AS pricingType,\n" +
            "      CASE \n" +
            "          WHEN s.awaiting_cancel = 0 THEN 'CANCELED' \n" +
            "          WHEN s.awaiting_cancel = 1 THEN 'AWAITING_CANCEL' \n" +
            "      END AS awaitingCancel, \n" +
            "       s.created_by as canceledBy, s.variant_draft_id as variantDraftId, s.variant_name as variantName," +
            "       s.is_only_service as isOnlyService, s.is_buy_service as isBuyService, \n" +
            "       CASE \n" +
            "           WHEN s2.service_owner = 0 THEN 'SAAS' \n" +
            "           WHEN s2.service_owner = 1 THEN 'VNPT' \n" +
            "           WHEN s2.service_owner = 2 THEN 'NONE' \n" +
            "           WHEN s2.service_owner = 3 THEN 'OTHER' \n" +
            "           END AS serviceOwner,\n" +
            "       CASE\n" +
            "            WHEN s.reg_type = 0 THEN 'TRIAL' \n" +
            "            WHEN s.reg_type = 1 THEN 'OFFICIAL' \n" +
            "           END AS regType, \n" +
            "           s.created_by as createdBy, \n" +
            "       CASE\n" +
            "	        WHEN s.cart_code IS NOT NULL THEN s.cart_code\n" +
            "	        WHEN s.group_code IS NOT NULL THEN s.group_code\n" +
            "	        ELSE concat('ID', to_char(s.id, 'FM09999999'::::text))::::character varying\n" +
            "       END AS subCode," +
            "       CASE \n" +
            "           WHEN p2.has_renew = 0 THEN 'NO' \n" +
            "           ELSE 'YES' \n" +
            "           END AS isRenewal\n" +
            "  FROM {h-schema}subscriptions s \n" +
            "  LEFT JOIN {h-schema}services s2 ON \n" +
            "  s.service_id = s2.id \n" +
            "  AND s2.deleted_flag = 1 \n" +
            "  LEFT JOIN {h-schema}pricing p2 ON \n" +
            "  s.pricing_id = p2.id \n" +
            "  AND p2.deleted_flag = 1 \n" +
            "  LEFT JOIN {h-schema}users u ON \n" +
            "  s2.created_by = u.id \n" +
            "  AND u.deleted_flag = 1 \n" +
            "  LEFT JOIN {h-schema}users parent ON \n" +
            "    u.parent_id = parent.id \n" +
            "   WHERE \n" +
            "  s.id = :id \n" +
            "  AND s.deleted_flag = 1";

    public static final String GET_AMOUNT_CYCLE_CURRENT =
        " SELECT new com.dto.subscriptions.responseDTO.AmountOfCycleNext ("
            + "    s.totalAmount, "
            + "    c.currencyType, "
            + "    s.startCurrentCycle,"
            + "    s.endCurrentCycle, "
            + "    s.currentPaymentDate )"
            + "FROM "
            + "    Subscription s "
            + "LEFT JOIN Pricing p2 ON "
            + "    p2.id = s.pricingId "
            + "    AND p2.deletedFlag = 1 "
            + "LEFT JOIN Currency c ON "
            + "    c.id = p2.currencyId "
            + "WHERE "
            + "    s.id = :id AND s.confirmStatus = 1 and s.deletedFlag = 1 and s.currentCycle is not null";
    public static final String GET_CYCLE_NEXT_WHEN_EXIST_CHANGE_SUBS =
        " SELECT new com.dto.subscriptions.responseDTO.AmountOfCycleNext ("
            + "    s.totalAmount, "
            + "    c.currencyType, "
            + "    s.startCurrentCycle,"
            + "    s.endCurrentCycle, "
            + "    s.currentPaymentDate )"
            + "FROM "
            + "    ChangeSubscription  s "
            + "LEFT JOIN Pricing p2 ON "
            + "    p2.id = s.pricingId "
            + "    AND p2.deletedFlag = 1 "
            + "LEFT JOIN Currency c ON "
            + "    c.id = p2.currencyId "
            + "WHERE "
            + "    s.status = 0 "
            + "    AND s.action = 0 "
            + "    AND s.subscriptionId = :id"
            + "    ORDER BY s.id DESC ";

    public static final String FIND_BY_ID_FOR_UPDATE =
            "SELECT " +
            "    * " +
            "FROM " +
            "    {h-schema}subscriptions s " +
            "WHERE " +
            "    s.id = :subId " +
            "    AND s.deleted_flag = 1 " +
            "    AND s.status = :status ";

    public static final String DELETE_SUB_ADDON_BY_SUBSCRIPTION_ID =
            "DELETE FROM " +
            "    {h-schema}subscription_addon_coupon " +
            "WHERE " +
            "    subscription_addon_id IN ( " +
            "            SELECT " +
            "                id " +
            "            FROM " +
            "                 {h-schema}subscription_addons " +
            "            WHERE " +
            "                subscription_id = :subId " +
            "        )";

    public static final String GET_COUPON_BY_SUBSCRIPTION =
            " SELECT DISTINCT c " +
                    " FROM Coupon c" +
                    " LEFT JOIN SubscriptionCoupons sc ON" +
                    " c.id = sc.couponId" +
                    " WHERE" +
                    " c.totalBillType = 1 " +
                    " AND sc.subscriptionId = :id " +
                    " AND (-1 = :type OR c.promotionType = :type) " +
                    " AND c.approve = 1 " +
                    " AND c.status = 1 " +
                    " AND c.deletedFlag = 1" +
                    " ORDER BY c.discountType DESC";

    public static final String GET_LIST_SUBSCRIPTION_AWAITING_CANCEL =
            "SELECT " +
                    "    s.* " +
                    "FROM " +
                    "    {h-schema}subscriptions s " +
                    "WHERE " +
                    "    s.deleted_flag = 1 " +
                    "    AND s.awaiting_cancel = 1 " +
                    "    AND s.status <> 3 " +
                    "    AND s.end_current_cycle + 1 = :currentDate ";
    public static final String GET_SUBSCRIPTION_INFO =
            "SELECT s.id as id, "
                + "       s.trial_day as trialDay, "
                + "       s.started_at as startedAt, "
                + "       sv.service_name as serviceName, "
                + "       p.pricing_name as pricingName, "
                + "       p.cycle_type as cycleType, "
                + "       u.name as name "
                + "FROM {h-schema}subscriptions s "
                + "INNER JOIN {h-schema}services sv ON s.service_id = sv.id "
                + "AND sv.deleted_flag = 1 "
                + "INNER JOIN {h-schema}pricing p ON p.id = s.pricing_id "
                + "AND p.deleted_flag = 1 "
                + "INNER JOIN {h-schema}users u ON s.user_id = u.id "
                + "AND u.deleted_flag = 1"
                + "WHERE s.id = :id "
                + "AND s.deleted_flag = 1";

    public static final String GET_NUMBER_OF_CYCLES =
            "SELECT " +
                    "COALESCE(s.numberOfCycles, 0) " +
                    "FROM Subscription s " +
                    "WHERE s.id = :id";

    public static final String SUBSCRIPTION_EXITS =
            "SELECT s.* "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "WHERE "
            + "    s.deleted_flag = 1 "
            + "    AND s.status = 3 "
            + "    AND s.id = :id";

    public static final String GET_USE_CODE =
        "SELECT "
            + "    c.user_code "
            + "FROM "
            + "    {h-schema}province p "
            + "JOIN {h-schema}clue c ON "
            + "    p.code = c.province_code "
            + "WHERE "
            + "    p.id = :id";

    public static final String GET_SERVICE_CODE =
        "SELECT s.serviceCode FROM Pricing p JOIN ServiceEntity s ON p.serviceId = s.id WHERE p.id = :pricingId";

    public static final String GET_ENTERPRISE_NAME_BY_SUBSCRIPTION =
            "SELECT COALESCE(u.name, concat(u.first_name, ' ', u.last_name)) AS name, u.id " +
                    "FROM {h-schema}users u " +
                    "WHERE u.id = :id";

    public static final String GET_INSTALL_TYPE =
        "SELECT "
            + "    st.kieuld_id "
            + "FROM "
            + "    {h-schema}subscription_type st "
            + "WHERE "
            + "    st.loaitb_id = :serviceCode";

    public static final String CHECK_USER_SUBSCRIPTION_COMBO_PLAN =
        "SELECT " +
            "    count(s.*) > 0 " +
            "FROM " +
            "    {h-schema}subscriptions s " +
            "WHERE " +
            "    s.combo_plan_id = :comboPlanId " +
            "    AND s.user_id = :userId " +
            "    AND s.deleted_flag = 1 ";

    public static final String GET_REG_TYPE =
        "SELECT "
            + "    s.id, "
            + "    s.reg_type AS regType,"
            + "    s.started_at AS startDateSubscription,"
            + "    s.expired_time AS endDateSubscription"
            + " FROM "
            + "    {h-schema}subscriptions s "
            + "WHERE "
            + "    s.created_by = :userId "
            + "    AND s.pricing_id = :id "
            + "    AND s.confirm_status = 1 "
            + "    AND s.cancelled_time IS NULL"
            + "    AND s.deleted_flag = 1 LIMIT 1";

    public static final String FIND_SUBS_FOR_COMBO =
            "SELECT new com.dto.subscriptions.combo.SubComboFilterResDTO( " +
            "    s.id , " +
            "    c.id , " +
            "    c.comboName as comboName , " +
            "    cp.id , " +
            "    cp.comboName as comboPlanName, " +
            "    fa.filePath , " +
            "    fa.externalLink, " +
            "    CASE " +
            "        WHEN c.publisher IS NULL THEN u.name " +
            "        ELSE c.publisher " +
            "    END as companyName, " +
            "    CASE " +
            "        WHEN s.status = 0 THEN 'FUTURE' " +
            "        WHEN s.status = 1 THEN 'IN_TRIAL' " +
            "        WHEN s.status = 2 THEN 'ACTIVE' " +
            "        WHEN s.status = 3 THEN 'CANCELLED' " +
            "        WHEN s.status = 4 THEN 'NON_RENEWING' " +
            "    END as status, " +
            "    s.nextPaymentTime as nextPaymentTime, " +
            "    s.startedAt, " +
            "    s.totalAmount, " +
            "    cp.currencyId, " +
            "    u2.currencyType ) " +
            "FROM " +
            "    ComboPlan cp " +
            "JOIN Subscription s ON " +
            "    s.comboPlanId = cp.id " +
            "LEFT JOIN Combo c ON " +
            "    cp.comboId = c.id " +
            "LEFT JOIN FileAttach fa  " +
            "    ON fa.comboId = c.id  " +
            "    AND fa.objectType = 14 " +
            "LEFT JOIN User u ON " +
            "    c.userId = u.id " +
            "LEFT JOIN Currency u2 ON " +
            "    cp.currencyId = u2.id " +
            "WHERE " +
            "    (:nameSearch = '' OR lower(c.comboName) LIKE ('%' || lower(:nameSearch) || '%') ) " +
            "    AND (:companyName = '' OR " +
            "       lower(u.name) LIKE ('%' || lower(:companyName) || '%') OR " +
            "       lower(c.publisher) LIKE ('%' || lower(:companyName) || '%') ) " +
            "    AND (:status = -1 OR s.status = :status ) " +
            "    AND (:type = '' OR (:type = 'SERVICE' AND c.comboOwner <> 3) OR (:type = 'ORDER' AND c.comboOwner = 3) ) " +
            "    AND s.confirmStatus = 1  " +
            "    AND s.deletedFlag = 1  " +
            "    AND s.userId = :userId";//HiepNT fix bug 162347 lấy danh sách combo dung thử giống với service

    public static final String FIND_SUBS_FOR_COMBO_INTERFACE =
            "SELECT  \n" +
            "                s.id as subscriptionId ,  \n" +
            "                c.id as comboId,  \n" +
            "                c.combo_name as comboName ,  \n" +
            "                cp.id as comboPlanId ,  \n" +
            "                cp.combo_name as comboPlanName,  \n" +
            "                fa.file_path as filePath,  \n" +
            "                fa.ext_link as extLink,  \n" +
            "                CASE  \n" +
            "                    WHEN c.publisher IS NULL THEN u.name  \n" +
            "                    ELSE c.publisher  \n" +
            "                END as companyName,  \n" +
            "                CASE  \n" +
            "                    WHEN s.status = 0 THEN 'FUTURE'  \n" +
            "                    WHEN s.status = 1 THEN 'IN_TRIAL'  \n" +
            "                    WHEN s.status = 2 THEN 'ACTIVE'  \n" +
            "                    WHEN s.status = 3 THEN 'CANCELLED'  \n" +
            " WHEN s.status = 4 AND c.combo_owner in (0,1) AND s.expired_time < DATE(NOW()) AND cp.has_renew = 1  \n" +
            "                           AND s.expired_time + cast ((SELECT payment_date_fail_on FROM {h-schema}system_params WHERE param_type = 'COUPON') as INTEGER ) - DATE(NOW()) > 0 THEN 'ACTIVE' \n" +
            "                    WHEN s.status = 4 AND c.combo_owner in (2,3) AND s.expired_time < DATE(NOW()) AND cp.has_renew = 1 \n" +
            "                           AND s.expired_time + cast ((SELECT payment_date_fail_off FROM {h-schema}system_params WHERE param_type = 'COUPON') as INTEGER ) - DATE(NOW()) > 0 THEN 'ACTIVE' \n" +
            "                  ELSE 'NON_RENEWING'\n" +
            "                END as status,  \n" +
            "                s.next_payment_time as nextPaymentTime,  \n" +
            "                s.total_amount as price,  \n" +
            "                s.started_at as startDate ,  \n" +
            "                cp.currency_id as currencyId,  \n" +
            "                u2.currency_type as currencyName,\n" +
            " CASE " +
            " WHEN c.combo_owner = 1 OR c.combo_owner = 0 THEN s.installed ELSE null END as installationStatus " +
            "            FROM  \n" +
            "                {h-schema}combo_plan cp  \n" +
            "            JOIN {h-schema}subscriptions s ON  \n" +
            "                s.combo_plan_id = cp.id  \n" +
            "            LEFT JOIN {h-schema}combo c ON  \n" +
            "                cp.combo_id = c.id  \n" +
            "            LEFT JOIN {h-schema}file_attach fa   \n" +
            "                ON fa.combo_id = c.id   \n" +
            "                AND fa.object_type = 14  \n" +
            "            LEFT JOIN {h-schema}users u ON  \n" +
            "                c.user_id = u.id  \n" +
            "            LEFT JOIN {h-schema}currency u2 ON  \n" +
            "                cp.currency_id = u2.id  \n" +
            "            WHERE  \n" +
            "                (:nameSearch = '' OR lower(c.combo_name) LIKE ('%' || lower(:nameSearch) || '%') )  \n" +
            "                AND (:companyName = '' OR  \n" +
            "                   lower(u.name) LIKE ('%' || lower(:companyName) || '%') OR  \n" +
            "                   lower(c.publisher) LIKE ('%' || lower(:companyName) || '%') )  \n" +
            "                AND (:status = -1 OR s.status = :status )  \n" +
            "                AND (:type = '' OR (:type = 'SERVICE' AND c.combo_owner <> 3) OR (:type = 'ORDER' AND c.combo_owner = 3) )  \n" +
            "                AND s.confirm_status = 1   \n" +
            "                AND s.deleted_flag = 1   \n" +
            "                AND s.user_id = :userId ";

    public static final String FIND_SUBS_FOR_COMBO_DEV =
            "SELECT * FROM (" +
            "SELECT  " +
                    "    s.id , " +
                    "    c.id AS comboId, " +
                    "    c.combo_name AS comboName, " +
                    "    file.file_path AS filePath , " +
                    "    file.ext_link AS extLink, " +
                    "    CASE " +
                    "        WHEN u1.customer_type = 'CN' THEN concat(COALESCE (u1.last_name, ''), ' ', COALESCE (u1.first_name, '')) " +
                    "        ELSE u1.name  " +
                    "    END AS companyName, " +
                    "    CASE " +
                    "        WHEN u1.customer_type = 'CN' THEN u1.rep_personal_cert_number " +
                    "        ELSE u1.tin " +
                    "    END AS taxNo, " +
                    "    CASE " +
                    "        WHEN c.publisher IS NULL THEN u.name " +
                    "        ELSE c.publisher " +
                    "    END AS devName, " +// dev name
                    "    cp.id AS comboPlanId, " +
                    "    cp.combo_name AS comboPlanName, " +
                    "    to_char(s.created_at, 'dd/MM/yyyy') AS createDate, " +
                    "    to_char(s.started_at, 'dd/MM/yyyy') AS startDate, " +
                    "    s.status AS status, " +
                    "   CASE" +
                    "       WHEN s.status = 2 THEN 'A'" +
                    "       WHEN s.status = 1 THEN 'B'" +
                    "       WHEN s.status = 0 THEN 'C'" +
                    "       WHEN s.status = 3 THEN 'D'" +
                    "       WHEN s.status = 4 THEN 'E'" +
                    "   END AS statusSort, " +
                    "   CASE" +
                    "       WHEN s.status = 1 THEN -1" +
                    "       ELSE cp.cycle_type" +
                    "   END AS cycleType, " +
                    "   CASE" +
                    "       WHEN s.status = 1 THEN -1" +
                    "       ELSE cp.payment_cycle" +
                    "   END AS paymentCycle, " +
                    "    s.total_amount AS amount, " +
                    "    cp.unit_id AS unitId, " +
                    "    u2.name AS unitName, " +
                    "   CASE" +
                    "       WHEN (s.next_payment_time IS NULL OR s.status = 1 ) THEN '' " +
                    "       ELSE to_char(s.next_payment_time, 'dd/MM/yyyy')" +
                    "   END AS nextPaymentTime, " +
                    "   CASE " +
                    "       WHEN s.portal_type = 1 THEN 'ADMIN' " +
                    "       WHEN s.portal_type = 2 THEN 'DEV' " +
                    "       WHEN s.portal_type = 3 THEN 'SME' " +
                    "   END AS portalType, " +
                    "   d.province_id AS provinceId,  " +
//                    1: affline, 2 AM, 3 admin, 4 dev, 5 sme
                    "    CASE " +
                    "       WHEN s.traffic_id is not null then 3" +
                    "       WHEN s.employee_code is not null then 2" +
                    "       WHEN s.portal_type = 1 then 4" +
                    "       WHEN s.portal_type = 2 then 4" +
                    "       WHEN s.portal_type = 3 then 1" +
                    "   END AS createdSource, " +
                    "    CASE " +
                    "       WHEN u1.customer_type = 'HKD' then 'HOUSE_HOLD' " +
                    "       WHEN u1.customer_type = 'CN' then 'PERSONAL'  " +
                    "       ELSE 'ENTERPRISE' end AS customerType,   "  +
                    "   bill.payment_date as paymentDate " +
                    " FROM " +
                    "    {h-schema}combo_plan cp " +
                    " JOIN {h-schema}subscriptions s ON " +
                    "    s.combo_plan_id = cp.id" +
                    "  AND s.deleted_flag = 1 " +
                    " LEFT JOIN {h-schema}combo c ON " +
                    "    cp.combo_id = c.id " +
                    " AND c.deleted_flag = 1 " +
                    " LEFT JOIN {h-schema}file_attach file  " +
                    "    ON file.id = (SELECT max(fa.id) FROM {h-schema}file_attach fa " +
                    " WHERE fa.combo_id = c.id AND fa.object_type = 14) " +
                    " LEFT JOIN {h-schema}users u1 ON " +
                    "    s.user_id = u1.id " +
                    " AND u1.deleted_flag = 1 " +
                    " LEFT JOIN {h-schema}users u ON " +
                    "    c.user_id = u.id " +
                    " AND u.deleted_flag = 1 " +
                    "LEFT JOIN {h-schema}users us ON " +
                    "    s.registed_by = us.id " +
                    "LEFT JOIN {h-schema}departments d ON " +
                    "    us.department_id = d.id " +
                    "    AND d.deleted_flag = 1 " +
                    " LEFT JOIN {h-schema}units u2 ON " +
                    "    cp.unit_id = u2.id " +
                    " LEFT JOIN {h-schema}billings bill ON " +
                    "    s.id = bill.subscriptions_id and bill.id in (select max(id) from {h-schema}billings where subscriptions_id =s.id and status = 2) " +
                    " WHERE " +
                    "    cp.deleted_flag = 1 AND " +
                    "    (:#{#search.nameSearch} = '' OR lower(c.combo_name) iLIKE ('%' || lower(:#{#search.nameSearch}) || '%') ) " +
                    "    AND (:#{#search.companyName} = '' OR lower(u1.name) iLIKE ('%' || lower(:#{#search.companyName}) || '%') or lower(concat(u1.last_name, ' ', u1.first_name)) iLIKE ('%' || lower (:#{#search.companyName}) || '%') ) " +
                    "    AND (:#{#search.devName} = '' OR  CASE " +
                    "                               When c.publisher IS NULL THEN lower(u.name)" +
                    "                               ELSE lower(c.publisher) " +
                    "                           END LIKE ('%' || lower(:#{#search.devName}) || '%') ) " +
                    "    AND (:#{#search.status.value} = '-1'  OR s.status = :#{#search.status.value} ) " +
                    "    AND (:#{#search.customerType.getValue()} = '' OR u1.customer_type = :#{#search.customerType.getValue()} ) " +
                    "    AND (:#{#search.type} = '' OR (:#{#search.type} = 'SERVICE' AND c.combo_owner <> 3) OR (:#{#search.type} = 'ORDER' AND c.combo_owner = 3) ) " +
                    "    AND s.confirm_status = 1  " +
                    "    AND s.deleted_flag = 1  " +
                    "    AND (:provinceId = -1 OR u1.province_id = :provinceId) " +
                    "    AND (:#{#search.provinceId} = -1 OR u1.province_id = :#{#search.provinceId}) " +
                    "    AND (:userId = -1 OR c.user_id = :userId)" +
                    "    AND (:createdBy = -1 OR s.registed_by = :createdBy) " +
                    "    AND (:#{#search.tin} = '' OR COALESCE(u1.tin,'') iLIKE ('%' || :#{#search.tin} || '%')) " +
                    "    AND (:#{#search.email} = '' OR COALESCE(u1.email,'') ilike %:#{#search.email}%) " +
                    "    AND (:#{#search.phone} = '' OR COALESCE(u1.phone_number,'') ilike %:#{#search.phone}%) " +
                    "    AND (:#{#search.representative} = '' OR COALESCE(u1.rep_fullname,'') ilike %:#{#search.representative}%) " +
                    "    AND (:#{#search.foundingFrom} = :#{#search.defaultMinDate} OR u1.founding_date >= (:#{#search.foundingFrom})) " +
                    "    AND (:#{#search.foundingTo} = :#{#search.defaultMaxDate} OR u1.founding_date <= (:#{#search.foundingTo})) " +
                    "    AND (:#{#search.businessAreaId} = -1 OR u1.business_area_id = (:#{#search.businessAreaId})) " +
                    "    AND (:#{#search.businessSizeId} = -1 OR u1.business_size_id = (:#{#search.businessSizeId})) " +
                    "    AND (:#{#search.createdFrom} = :#{#search.defaultMinDateTime} OR s.created_at >= (:#{#search.createdFrom})) " +
                    "    AND (:#{#search.createdTo} = :#{#search.defaultMaxDateTime} OR s.created_at <= (:#{#search.createdTo})) " +
                    " ) AS s " +
                    " WHERE " +
                    "    (:#{#search.createdSource} = -1 OR s.createdSource = :#{#search.createdSource}) " +
                    "    AND (:#{#search.paymentCycle} = -1 OR s.paymentCycle = :#{#search.paymentCycle}) " +
                    "    AND (:#{#search.paymentCycle} = -1 OR s.cycleType = :#{#search.cycleType}) " +
                    "    AND (:#{#search.nextPaymentDateFrom} = :#{#search.defaultMinDateTime} OR s.nextPaymentTime >= (:#{#search.nextPaymentDateFrom})) " +
                    "    AND (:#{#search.nextPaymentDateTo} = :#{#search.defaultMaxDateTime} OR s.nextPaymentTime <= (:#{#search.nextPaymentDateTo})) " +
                    "    AND (:#{#search.paymentDateFrom} = :#{#search.defaultMinDateTime} OR s.paymentDate >= (:#{#search.paymentDateFrom})) " +
                    "    AND (:#{#search.paymentDateTo} = :#{#search.defaultMaxDateTime} OR s.paymentDate <= (:#{#search.paymentDateTo})) " ;

    public static final String FIND_PRICING_OF_COMBO =
            "SELECT " +
            "    DISTINCT new com.dto.subscriptions.combo.SubComboPricingFilterResDTO( " +
            "    s.id, " +
            "    s.serviceName, " +
            "    p.id, " +
            "    p.pricingName, " +
            "    CASE " +
            "        WHEN sp.name = 'Tiếp nhận' THEN 1 " +
            "        WHEN sp.name = 'Đã triển khai' THEN 2 " +
            "        WHEN sp.name = 'Hủy' THEN 3 " +
            "        WHEN sp.name = 'Hoàn thành' THEN 4 " +
            "    END as orderService, " +
            "    fa.filePath , " +
            "    fa.externalLink, " +
            "    u.name) " +
            "FROM " +
            "    Pricing p " +
            "JOIN ComboPricing cp ON " +
            "    p.id = cp.objectId " +
            "JOIN ServiceEntity s ON " +
            "    p.serviceId = s.id " +
            "LEFT JOIN ComboPlan cplan ON " +
            "    cp.comboPlanId = cplan.id " +
            "LEFT JOIN Combo c ON " +
            "    cplan.comboId = c.id " +
            "LEFT JOIN OrderServiceReceive osr ON " +
            "    osr.comboId = c.id " +
            "    AND osr.subscriptionId = :subId " +
            "LEFT JOIN OrderServiceCombo osc ON " +
            "    osc.serviceId = s.id " +
            "    AND osc.orderServiceReceiveId = osr.id " +
            "LEFT JOIN OrderServiceStatus oss ON " +
            "    oss.id = CAST(osc.orderStatus as int) " +
            "LEFT JOIN SmeProgress sp ON " +
            "    sp.id = oss.smeProgressId " +
            "LEFT JOIN FileAttach fa ON " +
            "    fa.serviceId = s.id " +
            "    AND fa.objectType = 0 " +
            "LEFT JOIN User u ON " +
            "    s.userId = u.id " +
            "WHERE " +
            "    cp.comboPlanId = :comboPlanId";

    public static final String GET_AMOUNT_CYCLE_NEXT =
        "SELECT new com.dto.subscriptions.responseDTO.AmountOfCycleNext ("
            + "    s.totalAmount , "
            + "    c.currencyType , "
            + "    s.startCurrentCycle , "
            + "    s.endCurrentCycle , "
            + "    s.currentPaymentDate ) "
            + "FROM "
            + "    Subscription s "
            + "LEFT JOIN ComboPlan cp ON "
            + "    cp.id = s.comboPlanId "
            + "LEFT JOIN Currency c ON "
            + "    c.id = cp.currencyId "
            + "WHERE "
            + "    s.id =:id AND s.confirmStatus = 1 and s.deletedFlag = 1 ";

    public static final String GET_SUBSCRIPTION_ACTIVE =
        "SELECT "
            + "    s.* "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "WHERE "
            + "    s.deleted_flag = 1 "
            + "    AND s.status <> 3 "
            + "    AND (s.awaiting_cancel IS NULL OR s.awaiting_cancel = 0) "
            + "    AND s.id = :id ";

    public static final String GET_SUBSCRIPTION_ACTIVE_BY_CODE =
        "SELECT "
            + "    s.* \n "
            + "FROM "
            + "    {h-schema}subscriptions s  \n"
            + "WHERE \n "
            + "    s.deleted_flag = 1 \n "
            + "    AND s.status <> 3 \n "
            + "    AND (s.awaiting_cancel IS NULL OR s.awaiting_cancel = 0)  \n"
            + "    AND s.dhsxkd_sub_code = :code  \n"
            + "    AND s.created_source_migration = 5";

    public static final String GET_SUBSCRIPTION_CANCEL_FROM_ONE_BSS_BY_CODE =
        "SELECT "
            + "    s.* \n"
            + "FROM \n"
            + "    {h-schema}subscriptions s \n"
            + "WHERE "
            + "    s.deleted_flag = 1 \n"
            + "    and s.status = 3 \n"
            + "    and s.dhsxkd_sub_code = :code \n"
            + "    and s.created_source_migration = 5";

    public static final String GET_SUBSCRIPTION_FROM_ONE_BSS_BY_CODE =
        "SELECT "
            + "    s.* \n"
            + "FROM \n"
            + "    {h-schema}subscriptions s \n"
            + "WHERE "
            + "    s.deleted_flag = 1 \n"
            + "    and s.dhsxkd_sub_code = :code \n"
            + "    and s.created_source_migration = 5";

    public static final String DELETE_BY_SUBSCRIPTION_ID_COMBO =
            "DELETE FROM " +
                    "    {h-schema}subscription_combo_addon_coupon " +
                    "WHERE " +
                    "    subscription_combo_addon_id IN ( " +
                    "            SELECT " +
                    "                id " +
                    "            FROM " +
                    "                 {h-schema}subscription_combo_addon " +
                    "            WHERE " +
                    "                subscription_id = :subId " +
                    "        )";

    public static final String DELETE_BY_SUBSCRIPTION_ID_AND_ADDON_ID =
        "DELETE "
            + "FROM {h-schema}subscription_combo_addon_coupon "
            + "WHERE subscription_combo_addon_id IN ( " +
            "            SELECT " +
            "                id " +
            "            FROM " +
            "                 {h-schema}subscription_combo_addon " +
            "            WHERE " +
            "                subscription_id = :subId " +
            "            AND "  +
            "                addon_id in (:addonIds) " +
            "        )";

    public static final String GET_COUPON_IDS_BY_SUBSCRIPTION_ID_AND_ADDON_ID =
         "SELECT "
            + "    c.coupon_id "
            + "FROM "
            + "    {h-schema}subscription_combo_addon_coupon c "
            + "JOIN {h-schema}subscription_combo_addon a ON "
            + "    a.id = c.subscription_combo_addon_id "
            + "WHERE "
            + "    a.subscription_id = :subId "
            + "AND "
            + "    a.addon_id = :addonId" ;

    public static final String GET_PROVINCE_ID =
        "SELECT "
            + "    p.id "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN {h-schema}province p ON "
            + "    u.province_id = p.id "
            + "WHERE "
            + "    u.id = :createBy";

    public static final String GET_ALLOW_CONTRACT =
            "SELECT COUNT(ec.id_subscription) " +
                    "FROM {h-schema}e_contract ec " +
                    "WHERE ec.id_subscription = :id " +
                    "LIMIT 1";

    public static final String GET_SUBSCRIPTION_COMBO_PLAN =
            "WITH NewestComboPlan AS ( " +
            "SELECT " +
            "   cp.combo_plan_draft_id, " +
            "   max(cp.id) newest " +
            "FROM " +
            "   {h-schema}combo_plan cp " +
            "GROUP BY " +
            "   cp.combo_plan_draft_id ) " +
            "SELECT " +
                    "cp.id, " +
                    "cp.combo_name AS name, " +
                    "c.combo_name AS comboName, " +
                " CASE " +
                    "WHEN c.portal_type = 1 THEN c.publisher " +
                    "WHEN c.portal_type = 2 THEN u.name  " +
                "END AS developerName, " +
                    "COALESCE(cp.price, 0) AS price, " +
                    "cp.payment_cycle AS paymentCycle, " +
                    "cp.customer_type_code AS customerType, " +
                    "CASE " +
                    "   WHEN cp.cycle_type = 0 THEN 'DAILY' " +
                    "   WHEN cp.cycle_type = 1 THEN 'WEEKLY' " +
                    "   WHEN cp.cycle_type = 2 THEN 'MONTHLY' " +
                    "   WHEN cp.cycle_type = 3 THEN 'YEARLY' " +
                    "   WHEN cp.cycle_type = 4 THEN 'QUARTER' " +
                    "END AS cycleType " +
                    "FROM {h-schema}combo_plan cp " +
                    "INNER JOIN {h-schema}combo_plan_draft cpd ON " +
                    "    cpd.id = cp.combo_plan_draft_id AND cpd.deleted_flag = 1" +
                    "INNER JOIN {h-schema}combo c ON " +
                    "   c.id = cp.combo_id AND c.status = 1 AND c.deleted_flag = 1 " +
                    "INNER JOIN {h-schema}users u ON " +
                    "   u.id = c.user_id AND u.status = 1 AND u.deleted_flag = 1 " +
                    "INNER JOIN NewestComboPlan nc ON " +
                    "   nc.newest = cp.id AND nc.combo_plan_draft_id = cp.combo_plan_draft_id " +
                    "LEFT JOIN {h-schema}departments d ON u.department_id = d.id AND d.deleted_flag = 1 AND d.status = 1 " +
                    "WHERE " +
                    "   cp.deleted_flag = 1 AND cp.status = 1 AND cp.approve = 1 "
                +   "   AND cp.id <> :comboPlanId "
                +   "   AND (:osService = -1 OR (c.combo_owner <> 2 AND c.combo_owner <> 3)) "
                +   "   AND (:comboId = -1 OR c.id = :comboId) "
                +   "   AND (:comboOwner = -1 "
                + "        OR (:comboOwner = 0 "
                + "            AND c.combo_owner IN (2, 3)) "
                + "        OR (:comboOwner = 1 "
                + "            AND (c.combo_owner NOT IN (2, 3) OR c.combo_owner IS NULL))) "
                +   "   AND (:isDev = -1 OR (u.id IN (SELECT ur.user_id FROM {h-schema}users_roles ur "
                +   "                               WHERE ur.user_id = u.id AND ur.role_id IN (4, 7, 8)) "
                +   "                               OR ((:provinceId = -1 OR d.province_id = :provinceId) "
                +   "                        AND (:provinceId <> -1 OR d.province_id IS NULL)))) " +
                    "   AND (:name = '' OR c.combo_name ILIKE ('%' || :name || '%')) " +
                    "   AND (:comboPlanname = '' OR cp.combo_name ILIKE ('%' || :comboPlanname || '%')) " +
                    "   AND (:developerName = '' OR u.name ILIKE ('%' || :developerName || '%')) " +
                    "   AND (:cycleType = -1" +
                    "   OR cp.cycle_type = :cycleType)" +
                    "   AND (:paymentCycle = -1" +
                    "   OR cp.payment_cycle = :paymentCycle)" +
                    "   AND (:userId = 0 OR c.user_id = :userId)" +
                    "   AND cp.id <> :removeId " +
                    "   AND ('' = :customerType OR cp.customer_type_code SIMILAR TO '%('|| :customerType ||')%')";

    public static final String GET_PARENT_ID =
        "SELECT "
            + "    CASE "
            + "        WHEN u.parent_id = -1 THEN u.id "
            + "        ELSE u.parent_id "
            + "    END AS parent_id "
            + "FROM "
            + "    {h-schema}users u "
            + "WHERE "
            + "    u.id = :id ";

    public static final String GET_COMBO_PLAN_DETAIL =
        "SELECT new com.dto.subscriptions.combo.SubComboPlanDetailDTO (" +
            "c.id,c.comboDraftId, c.comboName, cp.id, cp.comboName, c2.id, c2.currencyType, cp.paymentCycle, " +
            "CASE " +
            "   WHEN cp.cycleType = 0 THEN 'DAILY' " +
            "   WHEN cp.cycleType = 1 THEN 'WEEKLY' " +
            "   WHEN cp.cycleType = 2 THEN 'MONTHLY' " +
            "   WHEN cp.cycleType = 3 THEN 'YEARLY' " +
            "   WHEN cp.cycleType = 4 THEN 'QUARTER' " +
            "END AS cycleType, " +
            "cp.numberOfCycles, u.id, u.name, cp.numberOfTrial, " +
            "CASE " +
            "   WHEN cp.trialType = 0 THEN 'DAILY' " +
            "   WHEN cp.trialType = 1 THEN 'WEEKLY' " +
            "   WHEN cp.trialType = 2 THEN 'MONTHLY' " +
            "   WHEN cp.trialType = 3 THEN 'YEARLY' " +
            "   WHEN cp.trialType = 4 THEN 'QUARTER' " +
            "END AS trialType, " +
            "cp.setupFee, " +
            "CASE " +
            "   WHEN cp.hasChangePrice = 0 THEN 'NO' " +
            "   WHEN cp.hasChangePrice = 1 THEN 'YES' " +
            "END AS hasChangePrice, cp.price, " +
            "CASE " +
            "    WHEN cp.comboPlanType = 0 THEN 'PREPAY' " +
            "    WHEN cp.comboPlanType = 1 THEN 'POSTPAID' " +
            "END AS comboPlanType)" +
            "FROM ComboPlan cp " +
            "LEFT JOIN Combo c ON c.id = cp.comboId AND c.status = 1 AND c.deletedFlag = 1 " +
            "LEFT JOIN Currency c2 ON c2.id = cp.currencyId AND c2.status = 1 " +
            "LEFT JOIN Unit u ON u.id = cp.unitId AND u.status = 1 " +
            "WHERE " +
            "   cp.id = :id AND cp.status = 1 AND cp.deletedFlag = 1 AND cp.approve = 1";

    public static final String GET_COMBO_PLAN_PRICING =
        "SELECT " +
            "p.id AS pricingId, " +
            "p.pricing_name AS pricingName, " +
            "u.name AS unitName, " +
            "s.service_name AS serviceName, " +
            "cp.free_quantity AS freeQuantity, " +
            "cp.quantity, " +
            "cp.amount " +
            "FROM {h-schema}pricing p " +
            "LEFT JOIN {h-schema}units u ON u.id = p.unit_id AND u.status = 1 " +
            "LEFT JOIN {h-schema}services s ON s.id = p.service_id AND s.status = 1 AND s.deleted_flag = 1 " +
            "INNER JOIN {h-schema}combo_pricing cp ON cp.object_id = p.id AND cp.id_combo_plan = :id " +
            // HiepNT fix bug 164375: them order by giống
            // SQLCombo.GET_PRICING_COMBO_PLAN và SQLCombo.GET_PRICING_COMBO_PLAN_DRAFT
            " ORDER BY p.id ASC ";

    public static final String GET_COMBO_PLAN_TAX =
        "SELECT " +
            "t.id AS taxId, " +
            "t.name AS taxName, " +
            "ct.PERCENT, " +
            "ct.id_combo_plan AS comboPlanId, " +
            "ct.has_tax AS hasTax " +
            "FROM {h-schema}tax t " +
            "INNER JOIN {h-schema}combo_tax ct ON ct.tax_id = t.id " +
            "   AND t.status = 1 AND t.deleted_flag = 1 AND ct.id_combo_plan = :id";

    public static final String GET_COMBO_PLAN_ADDON_TAX =
        "SELECT " +
            "t.id AS taxId, " +
            "t.name AS taxName, " +
            "at2.tax_percent AS percent, " +
            "at2.addons_id AS addonId, " +
            "at2.has_tax AS hasTax " +
            "FROM {h-schema}tax t " +
            "INNER JOIN {h-schema}addons_tax at2 ON at2.tax_id = t.id " +
            "   AND t.status = 1 AND t.deleted_flag = 1 AND at2.addons_id IN :ids";

    public static final String GET_COMBO_PLAN_ADDON =
        "SELECT new com.dto.subscriptions.combo.SubComboPlanAddon(" +
            "a.id, ca.pricingMultiPlanId, a.name, a.code, a.description, s.comboName, " +
            "   CASE " +
            "       WHEN a.type = 0 THEN 'DAILY' " +
            "       WHEN a.type = 1 THEN 'WEEKLY' " +
            "       WHEN a.type = 2 THEN 'MONTHLY' " +
            "       WHEN a.type = 3 THEN 'YEARLY' " +
            "   END AS type, " +
            "   CASE " +
            "       WHEN a.bonusType = 0 THEN 'ONCE' " +
            "       WHEN a.bonusType = 1 THEN 'PERIODIC'" +
            "   END AS bonusType, a.bonusValue, " + //HiepNT add bonusValue
            "a.setupFee, a.freeQuantity, a.price, " +
            "   CASE " +
            "       WHEN a.pricingPlan = 0 THEN 'FLAT_RATE' " +
            "       WHEN a.pricingPlan = 1 THEN 'UNIT' " +
            "       WHEN a.pricingPlan = 2 THEN 'TIER' " +
            "       WHEN a.pricingPlan = 3 THEN 'VOLUME' " +
            "       WHEN a.pricingPlan = 4 THEN 'STAIR_STEP' " +
            "   END AS pricingPlan, " +
            "   CASE " +
            "       WHEN ca.isRequired = 0 THEN 'NO' " +
            "       WHEN ca.isRequired = 1 THEN 'YES' " +
            "       ELSE 'UNSET' " +
            "   END AS isRequired, " +
            "   CASE " +
            "       WHEN a.allowPriceChange = 0 THEN 'NO' " +
            "       WHEN a.allowPriceChange = 1 THEN 'YES' " +
            "       ELSE 'UNSET' " +
            "   END AS hasChangePrice, " +
            "   CASE " +
            "       WHEN a.allowChangeQuantity = '1' THEN 'INCREASE' " +
            "       WHEN a.allowChangeQuantity = '2' THEN 'DECREASE' " +
            "       WHEN a.allowChangeQuantity = '1,2' THEN 'ALL' " +
            "       WHEN a.allowChangeQuantity IS NULL THEN 'NONE' " +
            "       ELSE 'UNSET' " +
            "   END AS hasChangeQuantity)" +
            "FROM Addon a " +
            "LEFT JOIN Combo s ON s.id = a.comboId " +
            "INNER JOIN ComboAddon ca ON ca.addonsId = a.id " +
            "WHERE ca.isRequired = 1 AND ca.comboPlanId = :id AND a.status = 1 AND a.deletedFlag = 1 AND a.approve = 1";

    public static final String GET_COMBO_PLAN_ADDON_SUBSCRIPTION =
        "SELECT new com.dto.subscriptions.combo.SubComboPlanAddon(" +
            "a.id, ca.pricingMultiPlanId, a.name, a.code, a.description, s.serviceName, " +
            "   CASE " +
            "       WHEN a.type = 0 THEN 'DAILY' " +
            "       WHEN a.type = 1 THEN 'WEEKLY' " +
            "       WHEN a.type = 2 THEN 'MONTHLY' " +
            "       WHEN a.type = 3 THEN 'YEARLY' " +
            "   END AS type, " +
            "   CASE " +
            "       WHEN a.bonusType = 0 THEN 'ONCE' " +
            "       WHEN a.bonusType = 1 THEN 'PERIODIC' " +
            "   END AS bonusType, a.bonusValue, " + //HiepNT add bonusValue
            "a.setupFee, a.freeQuantity, a.price, " +
            "   CASE " +
            "       WHEN a.pricingPlan = 0 THEN 'FLAT_RATE' " +
            "       WHEN a.pricingPlan = 1 THEN 'UNIT' " +
            "       WHEN a.pricingPlan = 2 THEN 'TIER' " +
            "       WHEN a.pricingPlan = 3 THEN 'VOLUME' " +
            "       WHEN a.pricingPlan = 4 THEN 'STAIR_STEP' " +
            "   END AS pricingPlan, " +
            "   CASE " +
            "       WHEN ca.isRequired = 0 THEN 'NO' " +
            "       WHEN ca.isRequired = 1 THEN 'YES' " +
            "       ELSE 'UNSET' " +
            "   END AS isRequired, " +
            "   CASE " +
            "       WHEN a.allowPriceChange = 0 THEN 'NO' " +
            "       WHEN a.allowPriceChange = 1 THEN 'YES' " +
            "       ELSE 'UNSET' " +
            "   END AS hasChangePrice, " +
            "   CASE " +
            "       WHEN a.allowChangeQuantity = '1' THEN 'INCREASE' " +
            "       WHEN a.allowChangeQuantity = '2' THEN 'DECREASE' " +
            "       WHEN a.allowChangeQuantity = '1,2' THEN 'ALL' " +
            "       WHEN a.allowChangeQuantity = '0' THEN 'NONE' " +
            "       ELSE 'UNSET' " +
            "   END AS hasChangeQuantity)" +
            "FROM Addon a " +
            "LEFT JOIN ServiceEntity s ON s.id = a.serviceId " +
            "INNER JOIN SubscriptionAddons sa ON sa.subscriptionId = :id AND sa.addonsId = a.id "
            + "     AND a.status = 1 AND a.deletedFlag = 1 AND a.approve = 1 "
            + "INNER JOIN Subscription s2 ON s2.id = sa.subscriptionId  "
            + "INNER JOIN ComboPlan cp ON cp.id = s2.comboPlanId AND cp.id = :comboPlanId "
            + "LEFT JOIN ComboAddon ca ON ca.comboPlanId = cp.id";

    public static final String GET_OWNER_COMBO_PLAN_BY_SUBSCRIPTION_ID =
        "SELECT "
            + "    CASE "
            + "        WHEN u.parent_id = -1 THEN u.id "
            + "        ELSE u.parent_id "
            + "    END AS parent_id "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}combo_plan cp ON "
            + "    s.combo_plan_id = cp.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND cp.deleted_flag = 1 "
            + "JOIN {h-schema}users u ON "
            + "    cp.created_by = u.id "
            + "    AND u.deleted_flag = 1 "
            + "WHERE "
            + "    s.id = :id ";

    public static final String GET_UNIT_LIMITED_ADDON =
        "SELECT " +
            "ul.unit_from AS unitFrom, " +
            "ul.unit_to AS unitTo, " +
            "ul.price AS price, " +
            "ul.addons_id AS addonId " +
            "FROM {h-schema}unit_limited ul " +
            "WHERE ul.addons_id IN :ids";

    public static final String FIND_BY_USER_ID_AND_COMBO_ID =
            "SELECT * "
                + "FROM {h-schema}subscriptions s "
                + "INNER JOIN {h-schema}combo_plan cp on  s.combo_plan_id = cp.id and cp.combo_id = :comboId "
                + "WHERE s.user_id = :userId "
                + "AND s.deleted_flag = 1 "
                + "AND s.reg_type = 0 ";

    public static final String CHECK_SUB_COMBO_EXITS = 
            "SELECT "
            + "    s.* "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "LEFT JOIN {h-schema}combo_plan cp ON "
            + "    s.combo_plan_id = cp.id "
            + "    AND cp.deleted_flag = 1 "
            + "LEFT JOIN {h-schema}combo c ON "
            + "    cp.combo_id = c.id "
            + "    AND c.deleted_flag = 1 "
            + "WHERE "
            + "    s.deleted_flag = 1 "
            + "    AND s.id = :id "
            + "    AND s.status = :status "
            + "    AND ( c.user_id IN (:idsInCompany) "
            + "    OR s.user_id IN(:idsInCompany))";

    public static final String UPDATE_SUBSCRIPTION_STATUS_BY_LIST_ID =
        "UPDATE {h-schema}subscriptions s " +
                "SET status = 4 " +
                "WHERE s.id IN (:ids) ";

    public static final String UPDATE_SUBSCRIPTION_STATUS_BY_FUTURE =
        "UPDATE {h-schema}subscriptions s "
            + "SET status = 2 "
            + "WHERE s.status = 0 "
            + "AND s.deleted_flag = 1 "
            + "AND s.reg_type = 1 "
            + "AND CURRENT_DATE >= s.started_at ";

    public static final String GET_LIST_SUB_REACTIVE =
        "select * from {h-schema}subscriptions s where s.reactive_date is not null and CURRENT_DATE >= s.reactive_date and s.status = 3 and s.reactive_status = 1";

    public static final String GET_LIST_SUBSCRIPTION_EXPIRED =
            " SELECT s.*  \n" +
                    "  FROM {h-schema}subscriptions s LEFT JOIN {h-schema}pricing p ON p.id = s.pricing_id   \n" +
                    "  WHERE s.status IN (1,2,3,5)  \n" +
                    "  AND s.deleted_flag = 1  \n" +
                    "  AND ((p.pricing_type = 0 AND (CURRENT_DATE > DATE(s.cancelled_time) + p.active_date  \n" +
                    "     OR (CURRENT_DATE > s.end_current_cycle AND (s.status = 1 OR (s.status <> 1 AND p.active_date = 0))))) \n" +
                    "  OR (p.pricing_type = 1 AND CURRENT_DATE > DATE(s.cancelled_time) + p.active_date  \n" +
                    "     OR (CURRENT_DATE - s.end_current_cycle > :paymentDateFailOff AND (s.status = 1 OR (s.status <> 1 AND p.active_date = 0))))) \n" +
                    "  AND p.active_date is not null AND ( s.status = 1 OR p.active_date <> -1 ) ";

    public static final String UPDATE_STATUS_TRIAL_DAY =
        "UPDATE Subscription sa SET sa.status = 3 WHERE sa.id = :id";

    public static final String CHECK_PRICING_USED =
        "SELECT "
            + "    count(1) > 0 "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "WHERE "
            + "    s.pricing_id = :pricingId "
            + "    AND s.user_id = :userId "
            + "    AND s.id <> :id "
            + "    AND s.status = :status";

    public static final String COUNT_SUBSCRIPTION_HISTORY =
        "SELECT count (1)\n" +
        "FROM {h-schema}subscription_history sh \n" +
        "LEFT JOIN {h-schema}users u ON u.id = sh.created_by AND u.status = 1 AND u.deleted_flag = 1\n" +
        "LEFT JOIN {h-schema}file_attach fa ON fa.user_id = sh.created_by AND fa.object_type = 5\n" +
        "LEFT JOIN {h-schema}billings b ON b.id = sh.billing_id  AND b.subscriptions_id = :subId \n" +
        "     AND b.status IN (0,1,2)\n" +
        "WHERE sh.subscription_id = :subId\n" +
        "    AND (sh.billing_id IS NULL OR b.id IS NOT NULL) \n";

    public static final String GET_SUBSCRIPTION_HISTORY =
            "SELECT \n" +
                "   sh.id AS id, \n" +
                "   u.id AS modifiedBy, \n" +
                "   CASE \n" +
                "      WHEN u.customer_type = 'CN' THEN concat(coalesce(u.last_name, ''), ' ', coalesce(u.first_name, '')) \n" +
                "      ELSE coalesce(u.name, 'Quản trị viên') \n" +
                "   END AS modifiedName, \n" +
                "   sh.created_at AS modifiedAt, \n" +
                "   sh.content, \n" +
                "   fa.file_path AS icon, \n" +
                "   fa.ext_link AS embedURL \n" +
                "FROM {h-schema}subscription_history sh \n" +
                "LEFT JOIN {h-schema}users u ON u.id = sh.created_by AND u.status = 1 AND u.deleted_flag = 1\n" +
                "LEFT JOIN {h-schema}file_attach fa ON fa.user_id = sh.created_by AND fa.object_type = 5 \n" +
                "LEFT JOIN {h-schema}billings b ON b.id = sh.billing_id  AND b.subscriptions_id = :subId \n" +
                "     AND b.status IN (0,1,2)\n" +
                "WHERE sh.subscription_id = :subId\n" +
                "    AND (sh.billing_id IS NULL OR b.id IS NOT NULL)  \n";

    public static final String GET_SUB_ADDON_IN_USED =
        "SELECT "
            + "    sa.addons_id  "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}subscription_addons sa ON "
            + "    s.id = sa.subscription_id "
            + "WHERE "
            + "    s.user_id = :userId "
            + "    AND s.id <> :id";

    public static final String GET_SUB_ADDON_COUPON_IN_USED =
        "SELECT "
            + "    sac.coupon_id  "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}subscription_addons sa ON "
            + "    s.id = sa.subscription_id "
            + "JOIN {h-schema}subscription_addon_coupon sac ON "
            + "    sac.subscription_addon_id = sa.id "
            + "WHERE "
            + "    s.user_id = :userId "
            + "    AND s.id <> :id "
            + "    AND sa.addons_id = :addonId";

    public static final String GET_COUPON_IN_USED =
        "SELECT "
            + "    sc.coupon_id "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}subscription_coupons sc ON "
            + "    s.id = sc.subscription_id "
            + "WHERE "
            + "    s.user_id = :userId "
            + "    AND s.id <> :id";

    public static final String GET_SUBSCRIPTION_REMINDER =
        "SELECT s.end_current_cycle as expiredTime, "
            + "       u.email as emailUse, "
            + "       ure.email as emailRegisted, "
            + "       uup.email as emailUpdate, "
            + "       u.name as nameSme, "
            + "       concat(u.last_name , ' ' , u.first_name) AS fullNameSme, "
            + "       u.customer_type AS customerType, "
            + "       sv.service_name as serviceName, "
            + "       p.pricing_name as pricingName, p.active_date as activeDate, "
            + "       s.end_current_cycle - CURRENT_DATE as daysLeft, "
            + "       s.id as subscriptionId, "
            + "       s.portal_type as portalType, "
            + "       s.user_id as userId, "
            + "       s.sub_code as subCode, "
            + "       u.province_id as provinceId, "
            + "       s.assignee_id as assigneeId, "
            + "       s.installed as installed, "
            + "       sv.service_owner as serviceOwner, "
            + "       s.created_source_migration as createdSourceMigration, "
            + "       COALESCE(s.end_current_cycle_new, s.end_current_cycle) as endCurrentCycle, "
            + "       s.end_current_cycle_contract as endCurrentCycleContract, "
            + "       (s.end_current_cycle - s.start_current_cycle + 1) AS totalDayUse, s.confirm_status as confirmStatus "
            + "FROM {h-schema}subscriptions s "
            + "JOIN {h-schema}users u ON s.user_id = u.id AND u.status = 1 AND u.deleted_flag = 1 "
            + "INNER JOIN {h-schema}services sv ON s.service_id = sv.id AND sv.status = 1 AND sv.deleted_flag = 1 "
            + "LEFT JOIN {h-schema}users ure ON s.registed_by = ure.id AND ure.status = 1 AND ure.deleted_flag = 1 "
            + "LEFT JOIN {h-schema}users uup ON s.modified_by = uup.id AND uup.status = 1 AND uup.deleted_flag = 1 "
            + "INNER JOIN {h-schema}pricing p ON p.id = s.pricing_id AND p.status = 1 AND p.deleted_flag = 1 "
            + "AND s.status = 2 "
            + "AND s.deleted_flag = 1 "
            + "AND (s.end_current_cycle_new is null OR (s.end_current_cycle_new is not null AND s.end_current_cycle_new = s.end_current_cycle) ) "
            + "AND s.end_current_cycle - CURRENT_DATE <= :reminderDay "
            + "AND s.end_current_cycle - CURRENT_DATE >= 0 ";

    public static final String GET_SUBSCRIPTION_BILLING_BY_SUBSCRIPTION_ID =
            "SELECT b.current_cycle as currentCycle, b.end_date as endDate, b.billing_date as billingDate, b.total_amount as totalAmount "
            + "FROM {h-schema}subscriptions s "
            + "JOIN {h-schema}billings b on s.id = b.subscriptions_id "
            + "WHERE s.id = :subscriptionId ORDER BY b.current_cycle DESC LIMIT 1 "
            ;

    public static final String GET_ALL_COUPON_SUBSCRIBED =
            "SELECT " +
            "    STRING_AGG(CONCAT(sc.coupon_id::::TEXT, ' ', spc.coupon_id::::TEXT, ' ', sac.coupon_id::::TEXT, ' ', scc.coupon_id::::TEXT, ' ', scac.coupon_id::::TEXT), ' ') " +
            "FROM " +
            "    {h-schema}subscriptions s " +
            "LEFT JOIN {h-schema}subscription_coupons sc ON " +
            "    s.id = sc.subscription_id " +
            "LEFT JOIN {h-schema}subscription_pricing_coupon spc ON " +
            "    s.id = spc.subscription_id " +
            "LEFT JOIN {h-schema}subscription_addons sa ON " +
            "    s.id = sa.subscription_id " +
            "LEFT JOIN {h-schema}subscription_addon_coupon sac ON " +
            "    sa.id = sac.subscription_addon_id " +
            "LEFT JOIN {h-schema}subscription_combo_coupon scc ON " +
            "    s.id = scc.subscription_id " +
            "LEFT JOIN {h-schema}subscription_combo_addon sca ON " +
            "    s.id = sca.subscription_id " +
            "LEFT JOIN {h-schema}subscription_combo_addon_coupon scac ON " +
            "    scac.subscription_combo_addon_id = sca.id ";

    public static final String GET_SUBSCRIPTION_BY_SME_PROVINCE_ID =
        "SELECT s "
            + "FROM Subscription s "
            + "INNER JOIN User u ON s.userId = u.id AND s.deletedFlag = 1 AND "
            + "     u.provinceId = :smeProvinceId AND s.id = :id";

    public static final String GET_SUBSCRIPTION_WITH_OUT_ADMIN_PROVINCE =
        "SELECT s "
            + "FROM Subscription s "
            + "LEFT JOIN Department d ON d.userId = s.registedBy "
            + "WHERE s.deletedFlag = 1 AND s.id = :id AND d.provinceId IS NULL";

    public static final String GET_SUBSCRIPTION_ADMIN_PROVINCE =
        "SELECT s "
            + "FROM Subscription s "
            + "LEFT JOIN Department d ON d.userId = s.registedBy "
            + "LEFT JOIN UserRoles ur ON ur.userId = s.registedBy "
            + "WHERE s.deletedFlag = 1 AND s.id = :id AND (d.provinceId = :provinceId OR ur.roleId IN (3,4,6,7,8)) ";

    public static final String GET_SUBSCRIPTION_BY_ID_AND_USER_ID =
        "SELECT s "
            + "FROM Subscription s "
            + "WHERE s.deletedFlag = 1 AND s.id = :id AND (s.registedBy =:userId OR s.userId = :userId)";

    public static final String FIND_BY_CREATED_BY_AND_COMBO_ID_REG_TYPE_AND_CONFIRM_STATUS = "SELECT s.* "
            + "FROM {h-schema}subscriptions s "
            + "INNER JOIN {h-schema}combo_plan cp on  s.combo_plan_id = cp.id and cp.combo_draft_id = :comboDraftId "
            + "WHERE s.user_id = :userId "
            + "AND s.confirm_status = :confirmStatus "
            + "AND s.reg_type = :regType "
            + "AND s.deleted_flag = 1 "
            + "AND s.status <> 3 "
            + "AND s.status <> 4 ";

    public static final String GET_SUBSCRIPTION_IDS_BY_CHANGE_STATUS =
        "SELECT s.id "
            + "FROM {h-schema}subscriptions s "
            + "WHERE deleted_flag = 1 "
            + "AND s.change_date = CURRENT_DATE "
            + "AND s.change_status = 0 "
            + "AND s.status = 2 ";

    public static final String CHECK_SUBSCRIPTION_EXITS =
            "SELECT s.* " +
                    " FROM " +
                    " {h-schema}subscriptions s " +
                    " WHERE " +
                    " s.id = :id " +
                    " AND s.deleted_flag = :deleteFlag " +
                    " AND (s.user_id = :userId " +
                    " OR s.created_by = :createdBy)";

    public static final String GET_SUBSCRIPTION_BY_UPDATE_STATUS =
        "SELECT s.* "
            + "FROM {h-schema}subscriptions s "
            + "WHERE deleted_flag = 1 "
            + "AND s.update_date = CURRENT_DATE "
            + "AND s.update_status = 0 "
            + "AND s.status = 2 ";
    public static final String GET_SUBSCRIPTION_COMBO_BY_ADDON =
            " SELECT "
                + "  s.*  "
                + "FROM  "
                + "  {h-schema}subscriptions s  "
                + "JOIN {h-schema}subscription_combo_addon sca ON  "
                + "  sca.subscription_id = s.id  " 
                + " JOIN {h-schema}combo_plan cp ON   "
                + "   cp.id = s.combo_plan_id  "
                + "   AND cp.deleted_flag = 1 "
                + "JOIN {h-schema}combo c ON   "
                + "   c.id = cp.combo_id  "
                + " AND c.deleted_flag = 1 "
                + "   AND c.combo_owner IN (0, 1)"
                + "WHERE  "
                + "  s.user_id = :companyId  "
                + "  AND sca.addon_id IN (:addonIds)  "
                + "  AND s.deleted_flag = 1  "
                + "  AND s.confirm_status = 1  "
                + "  AND s.status IN (0, 2)";

    public static final String GET_SUB_SERVICE_ORDER_SME =
        " SELECT DISTINCT "
            + "    s.id as subscriptionId, "
            + "    osr.id as orderReceiveServiceId,"
            + "    service.id AS serviceId, "
            + "    service.service_name AS serviceName, "
            + "    CASE  "
            + "        WHEN service.service_owner = 0 THEN 'SAAS'  "
            + "        WHEN service.service_owner = 1 THEN 'VNPT'  "
            + "        WHEN service.service_owner = 2 THEN 'NONE'  "
            + "        WHEN service.service_owner = 3 THEN 'OTHER'  "
            + "    END AS serviceOwner, "
            + "    fa.file_path AS icon, "
            + "    fa.ext_link AS embedUrl, "
            + "    p.id AS pricingId, "
            + "    p.pricing_name AS pricingName, "
            + "    osr.order_status AS status, "
            + "    osr.payment_status AS paymentStatus,"
            + "    dev.name AS developerName, "
            + "    s.total_amount AS totalAmount, "
            + "    s.created_at AS createdAt "
            + "FROM "
            + "{h-schema}subscriptions s "
            + "JOIN {h-schema}order_service_receive osr  on "
            + "s.id = osr.subscription_id AND s.deleted_flag = 1 AND s.confirm_status = 1 "
            + "JOIN  {h-schema}pricing p ON "
            + "    s.pricing_id = p.id "
            + "JOIN  {h-schema}services service ON "
            + "    p.service_id = service.id and service.service_owner = 3 "
            + "JOIN  {h-schema}users dev ON "
            + "    dev.id = service.user_id "
            + "LEFT JOIN  {h-schema}file_attach fa ON "
            + "    fa.service_id = service.id "
            + "    AND fa.object_type = 0 "
            + "WHERE "
            + "    s.user_id = :userId AND "
            + "    (:devName = '' OR dev.name ILIKE  ('%' ||:devName || '%')) AND "
            + "    (:status = '' OR osr.order_status = :status) AND "
            + "    (:serviceName = '' OR service.service_name ILIKE ('%' ||:serviceName || '%')) "
            + "    AND ( :defaultDate = :createAt OR s.created_at = :createAt) ";

    public  static final String GET_LIST_SUBSCRIPTION_REPORT =
            "select * FROM "
                    + " {h-schema}get_report_subscriptions_actual_revenue(:isTarget, :customerEmail, :customerName, :subCode,:provinceId,:customerType, " +
                    ":createdSource,:migrateStartDate,:migrateEndDate,:migrateCodes,:startDate,:endDate,:status,:serviceId,:comboIds,:pricingId, " +
                    ":pricingIds,:comboPlanIds,:subscriptionType,:categoryService,:categoryCombo,:employeeCode,:subscriptionState,:creator, " +
                    ":cancelledTimeStart, :cancelledTimeEnd, :affiliateCode) ";

    public static final String GET_LIST_SUBSCRIPTION_REPORT_EXPORT_CSV =
        "SELECT \n" +
            "     CAST(row_number() over (ORDER BY  registrationDate DESC) AS TEXT) AS rowNum, \n" +
            "     COALESCE(provinceName, '') AS provinceName, \n" +
            "     CASE\n" +
            "          WHEN  billStatus IS NOT NULL THEN  state \n" +
            "          ELSE '' \n" +
            "     END AS subStatus, \n" +
            "     COALESCE(smeName, '') AS smeName, \n" +
            "     COALESCE(customerType, '') AS customerType, \n" +
            "     '''' || COALESCE(taxtNo, '') || '''' AS taxNo, \n" +
            "     '''' || COALESCE(identityNo, '') || '''' AS identityNo, \n" +
            "     CONCAT_WS(',',  address,  street,  district,  province) AS address, \n" +
            "     '''' || COALESCE(phoneNo, '') || '''' AS phoneNo, \n" +
            "     COALESCE(email, '') AS email, \n" +
            "     COALESCE(employeeCode, '') AS employeeCode, \n" +
            "     COALESCE(serviceName, '') AS serviceName, \n" +
            "     {h-schema}func_convert_installed_status_to_text(installedStatus, serviceOwnerType) AS installedStatus,\n"+
            "     COALESCE(pricingName, '') AS pricingName, \n" +
            "     COALESCE(serviceOwnerType, '') AS serviceOwnerType, \n" +
            "     CASE \n" +
            "           WHEN  registrationDate IS NOT NULL THEN ' ' || CAST(registrationDate AS TEXT) \n" +
            "           ELSE '' \n" +
            "     END AS registrationDate, \n" +
            "     CASE \n" +
            "            WHEN  startAt IS NOT NULL THEN ' ' || CAST(startAt AS TEXT) \n" +
            "            ELSE '' \n" +
            "     END AS startAt, \n" +
            "     CASE \n" +
            "           WHEN  endCurrentCycle IS NOT NULL THEN ' ' || to_char(endCurrentCycle, 'yyyy-MM-dd') \n" +
            "           ELSE '' \n" +
            "      END AS endCurrentCycle, \n" +
            "      '' AS endCurrentCycleContract, \n" +
            "      '' AS renewStatus, \n" +
            "      CASE \n" +
            "           WHEN  paymentDate IS NOT NULL AND  billStatus = 'Đã thanh toán' THEN ' ' || CAST(paymentDate AS TEXT)\n" +
            "           ELSE '' \n" +
            "     END AS paymentDate, \n" +
            "     CASE \n" +
            "            WHEN  cancelledTime IS NOT NULL THEN ' ' || CAST(cancelledTime AS TEXT)\n" +
            "            ELSE '' \n" +
            "     END AS cancelledTime, \n" +
            "     CASE \n" +
            "          WHEN  isOneTime <> 0 THEN {h-schema}func_convert_num_of_payment_cycle_to_text(numberOfCycle) \n"+
            "          ELSE '' \n" +
            "      END AS numberOfCycle , \n" +
            "     CASE \n" +
            "           WHEN  isOneTime <> 0 THEN {h-schema}func_convert_payment_cycle_to_text(paymentCycle, cycleType, status) \n" +
            "           ELSE '' \n" +
            "     END AS paymentCycle, \n" +
            "     CASE \n" +
            "           WHEN  unitAmount IS NOT NULL THEN to_char(unitAmount, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS unitAmount, \n" +
            "     CASE \n" +
            "           WHEN  promotionAmount IS NOT NULL THEN to_char(promotionAmount, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS promotionAmount, \n" +
            "     CASE \n" +
            "           WHEN  preAmountTax IS NOT NULL THEN to_char(preAmountTax, 'FM999G999G999G999')\n" +
            "           ELSE '0' \n" +
            "     END AS preAmountTax, \n" +
            "     CASE \n" +
            "           WHEN  amountTax IS NOT NULL THEN to_char(amountTax, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS amountTax, \n" +
            "     CASE \n" +
            "           WHEN  afterAmountTax IS NOT NULL THEN to_char(afterAmountTax, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS afterAmountTax, \n" +
            "     COALESCE(payTransactionCode, ''), \n" +
            "     COALESCE(dhsxkdCode, ''), \n" +
            "     CASE \n" +
            "           WHEN  createdExportInvoice IS NOT NULL THEN ' ' || CAST(createdExportInvoice AS TEXT) \n" +
            "           ELSE '' \n" +
            "     END AS createdExportInvoice, \n" +
            "     COALESCE(codeInvoice, '') AS codeInvoice, \n" +
            "     COALESCE(creator, '') AS creator , \n" +
            "     COALESCE(subCode, '') AS subCode, \n" +
            "     COALESCE(setupCode, '') AS setupCode, \n" +
            "     COALESCE(billCode, '') AS billCode, \n" +
            "     COALESCE(billStatus, '') AS billStatus, \n" +
            "     COALESCE(createdSource, '') AS createdSource, \n" +
            "     COALESCE(affiliateCode, '') AS affiliateCode, \n" +
            "     COALESCE(migrateCode, '') AS migrateCode, \n" +
            "     CASE \n" +
            "         WHEN  migrateTime IS NOT NULL THEN ' ' || CAST(migrateTime AS TEXT) \n" +
            "         ELSE '' \n" +
            "     END AS migrateTime  \n" +
            "FROM {h-schema}get_report_subscriptions_actual_revenue_new(:isTarget, :customerEmail, :customerName, :subCode, :provinceId::int8, :customerType, \n" +
            "       :createdSource, :migrateStartDate, :migrateEndDate, :migrateCodes, :startDate, :endDate, :status, :serviceId::int8, :comboIds, :pricingId::int8, \n" +
            "       :pricingIDs, :comboPlanIds, :subscriptionType, :categoryService::int8, :categoryCombo, :employeeCode, :subscriptionState, :creator, \n" +
            "       :cancelledTimeStart, :cancelledTimeEnd, :affiliateCode) \n" +
            "ORDER BY  registrationDate DESC ";

    public  static final String GET_LIST_SUBSCRIPTION_REPORT_O2O =
            "select * FROM "
                    + " {h-schema}get_report_subscriptions_o2o_update(:provinceId,:customerType,:createdSource,:migrateStartDate, " +
                    ":migrateEndDate,:migrateCodes,:startDate,:endDate,:status,:serviceId,:comboIds,:pricingId,:pricingIds, " +
                    ":comboPlanIds,:subscriptionType,:categoryService,:categoryCombo,:employeeCode,:subscriptionState,:creator, " +
                    ":cancelledTimeStart, :cancelledTimeEnd, :providerIds) ";
    public static final String GET_LIST_EMPLOYEE_CODE_REPORT =
        "SELECT DISTINCT "
            + "    s.employee_code as employeeCode "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "LEFT JOIN "
            + "    {h-schema}users u on u.id = s.user_id \n"
            + "WHERE "
            + "    s.deleted_flag = 1 \n"
            + "    AND s.confirm_status = 1 \n"
            + "    AND s.employee_code NOTNULL  \n"
            + "    AND (:search LIKE 'ALL' OR s.employee_code ILIKE ('%' || :search || '%') ) \n"
            + "    AND ('ALL' in (:lstCustomerType) OR u.customer_type in (:lstCustomerType))";

    public static final String GET_MERCHANT_SERVICE_ID_BY_USER_ID =
        "SELECT "
            + "    NEW com.dto.payment.ClueDTO(c.merchantServiceId, "
            + "    c.privateKey, "
            + "    c.apiKey, "
            + "    c.baseUrl, "
            + "    c.qrSecretKey, "
            + "    c.qrTerminalId, "
            + "    c.qrApiKey,"
            + "    c.provinceCodePay) "
            + "FROM "
            + "    User u "
            + "JOIN Province p ON "
            + "    (p.id = u.provinceId "
            + "    OR u.provinceCode = p.code) "
            + "JOIN Clue c ON "
            + "    c.provinceCode = p.code "
            + "WHERE "
            + "    u.id = :userId";

    public static final String GET_MERCHANT_SERVICE_ID_BY_PROVINCE_CODE =
        "SELECT "
            + "    NEW com.dto.payment.ClueDTO(c.merchantServiceId, "
            + "    c.privateKey, "
            + "    c.apiKey, "
            + "    c.baseUrl, "
            + "    c.qrSecretKey, "
            + "    c.qrTerminalId, "
            + "    c.qrApiKey,"
            + "    c.provinceCodePay ) "
            + "FROM "
            + "    Clue c "
            + "WHERE "
            + "    c.provinceCode = :provinceCode";

    public static final String GET_CUS_MANAGER_IN_CLUE =
        "select c.user_code from {h-schema}users u \n" +
            "join {h-schema}province p ON u.province_id = p.id\n" +
            "join {h-schema}clue c ON c.province_code = p.code \n" +
            "where u.id = :userId";

    public static final String GET_COUPON_CHECKED_SUBSCRIPTION =
        "SELECT "
            + " CASE "
            + "     WHEN COUNT(sac.coupon_id) > 0 THEN TRUE "
            + "     WHEN COUNT(sac.coupon_id) <= 0 THEN FALSE "
            + "     ELSE FALSE "
            + " END AS checked, "
            + " sa.addons_id AS id, "
            + " sac.coupon_id AS couponId "
            + "FROM {h-schema}subscription_addon_coupon sac "
            + "JOIN {h-schema}subscription_addons sa ON "
            + "sac.subscription_addon_id = sa.id AND sa.subscription_id = :subscriptionId "
            + "    AND sa.addons_id IN :addonIds "
            + "WHERE sac.coupon_id IN :couponIds "
            + "GROUP BY sa.addons_id, sac.coupon_id";

    public static final String GET_PRICING_NAME_BY_SUBS_ID =
        "SELECT "
            + "    p.pricing_name "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}pricing p ON "
            + "    s.pricing_id = p.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND p.deleted_flag = 1 "
            + "    AND s.id = :id";

    public static final String GET_COMBO_PLAN_NAME_BY_SUBS_ID =
        "SELECT "
            + "    p.combo_name  "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}combo_plan p ON "
            + "    s.combo_plan_id = p.id "
            + "    AND s.deleted_flag = 1 "
            + "    AND p.deleted_flag = 1 "
            + "    AND s.id = :id";

    public static final String FIND_PRICING =
        " SELECT new com.dto.combo.ComboDetailDTO ( p.id, " +
            " p.pricingName, " +
            " s.urlPreOrder ) " +
            " FROM " +
            " Pricing p " +
            " LEFT JOIN ServiceEntity s ON " +
            " s.id = p.serviceId " +
            " AND s.deletedFlag = 1 " +
            " WHERE " +
            " p.deletedFlag <> 0 " +
            " AND p.id =:id ";

    public static final String GET_ORIGIN_QUANTITY_BY_SERVICE_GROUP_ID_AND_LIST_PRICING_ID =
            "SELECT quantity " +
                    "From {h-schema}service_group_pricing_item " +
                    "where " +
                    "   service_group_pricing_id in (select id from {h-schema}service_group_pricing where service_group_id = :serviceGroupId) " +
                    "   and pricing_multi_plan_id in :lstPricingMultiPlanId order by id desc limit 1";

    public static final String FIND_SUBSCRIPTION_BY_BILLING_ID =
        "SELECT "
            + "s.* "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "INNER JOIN {h-schema}billings b ON "
            + "    s.id = b.subscriptions_id "
            + "WHERE "
            + "    b.id = :billId "
            + "    AND s.deleted_flag = 1";

    public static final String GET_CHANGE_SUBSCRIPTION_BY_SUB_ID =
        "SELECT "
            + "s.* "
            + "FROM "
            + "    {h-schema}change_subscription s "
            + "WHERE "
            + "    s.subscription_id = :id "
            + "    AND s.status = :status "
            + "    AND s.action = :action "
            + "ORDER BY s.id desc "
            + "LIMIT 1 ";
    public static final String GET_CHANGE_SUBSCRIPTION_BY_SUB_ID_AND_PAYMENT_STATUS =
        "SELECT "
            + "s.* "
            + "FROM "
            + "    {h-schema}change_subscription s "
            + "WHERE "
            + "    s.subscription_id = :id "
            + "    AND s.status = :status "
            + "    AND s.action = :action "
            + "    AND s.payment_status = :paymentStatus "
            + "ORDER BY s.id desc "
            + "LIMIT 1 ";

    public static final String GET_PARENT_ID_BY_SUB_ID =
        "select sv.user_id "
            + "from {h-schema}subscriptions s "
            + "join {h-schema}services sv on sv.id = s.service_id "
            + "and s.id = :id"
            + " UNION  "
            + "select c.user_id  "
            + "from {h-schema}subscriptions s  "
            + "join {h-schema}combo_plan cp on s.combo_plan_id = cp.id  "
            + "JOIN {h-schema}combo c ON cp.combo_id = c.id "
            + "and s.id = :id";

    public static final String GET_ONLY_ONE_COUPON_POPUP_FOR_PRICING =
            "SELECT "
                    + "    DISTINCT c.id AS id, "
                    + "    c.name AS couponName, "
                    + "    c.code AS code, "
                    + "    c.promotion_type AS promotionType, "
                    + "    c.discount_type AS discountType, "
                    + "    c.discount_value AS discountValue, "
                    + "    c.max_used AS maxUsed, "
                    + "    c.minimum AS minimum , "
                    + "    c.minimum_amount AS minimumAmount, "
                    + "    c.maximum_promotion AS maximumPromotion, "
                    + "    c.start_date AS startDate, "
                    + "    c.end_date AS endDate, "
                    + "    c.portal AS portal , "
                    + "    c.user_id AS userId, "
                    + "    c.created_by AS createdBy, "
                    + "    c.discount_amount AS discountAmount, "
                    + "    c.limited_quantity AS limitedQuantity, "
                    + "    c.type AS TYPE, "
                    + "    c.times_used_type AS timesUsedType, "
                    + "    c.enterprise_type AS enterpriseType, "
                    + "    c.addons_type AS addonType, "
                    + "    c.pricing_type AS pricingType, "
                    + "    c.total_bill_type AS totalBillType, "
                    + "    c.supplier_type AS supplierType, "
                    + "    c.discount_supplier_type AS discountSupplierType, "
                    + "    c.province_id AS provinceId "
                    + "FROM "
                    + "    {h-schema}coupons c "
                    + "LEFT JOIN {h-schema}coupon_pricing_apply cpa ON "
                    + "    cpa.coupon_id = c.id "
                    + "LEFT JOIN {h-schema}coupon_pricing_plan cpp ON "
                    + "    cpp.coupon_id = c.id "
                    + "LEFT JOIN {h-schema}coupon_enterprise ce ON "
                    + "    ce.coupon_id = c.id "
                    + "WHERE "
                    + "     c.deleted_flag = 1  and c.id = :couponId  \n"
                    + "     AND c.approve = 1 \n"
                    + "     AND c.status = 1 \n"
                    + "     AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds)) \n"
                    + "     AND ( c.enterprise_type IS NULL OR c.enterprise_type IN (-1, 0) OR (:companyId =-1 OR ce.user_id = :companyId) ) \n"
                    + "     AND (c.pricing_type IS NULL OR c.pricing_type = -1 OR ( ( cpp.pricing_multi_plan_id = :multiPlanId) OR cpa.pricing_id = :pricingId))";

    public static final String GET_ONLY_ONE_COUPON_POPUP_FOR_COMBO_PLAN =
            "SELECT DISTINCT " +
                    "    c.id AS id, " +
                    "    c.name AS couponName, " +
                    "    c.code AS code, " +
                    "    c.promotion_type AS promotionType, " +
                    "    c.discount_type AS discountType, " +
                    "    c.discount_value AS discountValue, " +
                    "    c.max_used AS maxUsed, " +
                    "    c.minimum AS minimum , " +
                    "    c.minimum_amount AS minimumAmount, " +
                    "    c.maximum_promotion AS maximumPromotion, " +
                    "    c.start_date AS startDate, " +
                    "    c.end_date AS endDate, " +
                    "    c.portal AS portal , " +
                    "    c.user_id AS userId, " +
                    "    c.created_by AS createdBy, " +
                    "    c.discount_amount AS discountAmount, " +
                    "    c.limited_quantity AS limitedQuantity, " +
                    "    c.type AS type, " +
                    "    c.times_used_type AS timesUsedType, " +
                    "    c.enterprise_type AS enterpriseType, " +
                    "    c.addons_type AS addonType, " +
                    "    c.pricing_type AS pricingType, " +
                    "    c.total_bill_type AS totalBillType, " +
                    "    c.supplier_type AS supplierType, " +
                    "    c.discount_supplier_type AS discountSupplierType, " +
                    "    c.province_id AS provinceId " +
                    "FROM " +
                    "    {h-schema}coupons c " +
                    "LEFT JOIN {h-schema}coupon_combo_plan_apply cc ON " +
                    "    cc.coupon_id = c.id " +
                    "LEFT JOIN {h-schema}coupon_enterprise ce ON " +
                    "    ce.coupon_id = c.id " +
                    "WHERE " +
                    "    c.deleted_flag = 1  and c.id = :couponId   " +
                    "    AND c.approve = 1 " +
                    "    AND c.status = 1 " +
                    "    AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds)) " +
                    "    AND (c.enterprise_type IS NULL OR c.enterprise_type = -1 OR (c.enterprise_type = 1 AND (:companyId = -1 OR ce.user_id = :companyId) ) \n" +
                    "    AND (c.pricing_type IS NULL OR c.pricing_type = -1 OR (c.pricing_type = 1 AND cc.combo_plan_id = :comboPlanId) ";

    public static final String GET_ONLY_ONE_COUPON_POPUP_FOR_ADDON =
            "SELECT  DISTINCT " +
                    "    c.id AS id,  " +
                    "    c.name AS couponName,  " +
                    "    c.code AS code,  " +
                    "    c.promotion_type AS promotionType,  " +
                    "    c.discount_type AS discountType,  " +
                    "    c.discount_value AS discountValue,  " +
                    "    c.max_used AS maxUsed,  " +
                    "    c.minimum AS minimum ,  " +
                    "    c.minimum_amount AS minimumAmount,  " +
                    "    c.maximum_promotion AS maximumPromotion,  " +
                    "    c.start_date AS startDate,  " +
                    "    c.end_date AS endDate, " +
                    "    c.portal AS portal , " +
                    "    c.user_id AS userId, " +
                    "    c.created_by AS createdBy, " +
                    "    c.discount_amount AS discountAmount, " +
                    "    c.limited_quantity AS limitedQuantity, " +
                    "    c.type AS type, " +
                    "    c.times_used_type AS timesUsedType, " +
                    "    c.enterprise_type AS enterpriseType, " +
                    "    c.addons_type AS addonType, " +
                    "    c.pricing_type AS pricingType, " +
                    "    c.total_bill_type AS totalBillType, " +
                    "    c.supplier_type AS supplierType, " +
                    "    c.discount_supplier_type AS discountSupplierType, " +
                    "    c.province_id AS provinceId " +
                    "FROM  " +
                    "    {h-schema}coupons c  " +
                    "LEFT JOIN {h-schema}coupon_addons ca ON  " +
                    "    ca.coupon_id = c.id  " +
                    " LEFT JOIN {h-schema}coupon_pricing_plan cpp ON "
                    +   "    cpp.coupon_id = c.id " +
                    "LEFT JOIN {h-schema}coupon_enterprise ce ON  " +
                    "    ce.coupon_id = c.id  " +
                    "WHERE  " +
                    "     (c.addons_type = -1 OR (:addonId = 1 AND (cpp.pricing_multi_plan_id = :multiPlanId OR ca.addons_id = :addonId)) )\n" +
                    "     AND (c.enterprise_type IS NULL OR c.enterprise_type = -1 OR (c.enterprise_type = 1 AND (:companyId =-1 OR ce.user_id = :companyId)) ) \n" +
                    "     AND c.deleted_flag = 1  and c.id = :couponId   \n" +
                    "     AND c.status = 1 \n" +
                    "     AND c.approve = 1 \n" +
                    "     AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))";

    public static final String GET_ONLY_ONE_COUPON_POPUP_FOR_TOTAL_BILL_WHEN_SUBS_PRICING =
//            "SELECT  " +
//                    "    cot.*  " +
//                    "FROM  " +
//                    "    (  " +
//                    "    SELECT  " +
//                    "        c.id AS id,  " +
//                    "        c.name AS couponName,  " +
//                    "        c.promotion_type AS promotionType,  " +
//                    "        c.discount_type AS discountType,  " +
//                    "        c.discount_value AS discountValue,  " +
//                    "        c.max_used AS maxUsed,  " +
//                    "        c.minimum AS minimum ,  " +
//                    "        c.minimum_amount AS minimumAmount,  " +
//                    "        c.maximum_promotion AS maximumPromotion,  " +
//                    "        c.start_date AS startDate,  " +
//                    "        c.end_date AS endDate, " +
//                    "        c.portal AS portal , " +
//                    "        c.user_id AS userId, " +
//                    "        c.created_by AS createdBy, " +
//                    "        c.discount_amount AS discountAmount, " +
//                    "        c.limited_quantity AS limitedQuantity, " +
//                    "        c.type AS type, " +
//                    "        c.times_used_type AS timesUsedType, " +
//                    "        c.enterprise_type AS enterpriseType, " +
//                    "        c.addons_type AS addonType, " +
//                    "        c.pricing_type AS pricingType, " +
//                    "        c.total_bill_type AS totalBillType, " +
//                    "        c.province_id AS provinceId " +
//                    "    FROM  " +
//                    "        {h-schema}coupons c  " +
//                    "    LEFT JOIN {h-schema}coupon_pricing_apply cpa ON  " +
//                    "        cpa.coupon_id = c.id  " +
//                    "    LEFT JOIN {h-schema}coupon_enterprise ce ON  " +
//                    "        ce.coupon_id = c.id  " +
//                    "    WHERE  " +
//                    "        (c.enterprise_type ISNULL OR c.enterprise_type IN (-1, 0) OR (:companyId =-1 OR ce.user_id = :companyId) )  " +
//                    "        AND (c.pricing_type ISNULL OR c.pricing_type IN (-1, 0)  OR cpa.pricing_id = :pricingId )  " +
//                    "        AND c.deleted_flag = 1 AND c.id = :couponId   " +
//                    "        AND c.status = 1 " +
//                    "        AND c.approve = 1  " +
//                    "        AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))  " +
//                    "        AND c.total_bill_type = 1  " +
//                    "UNION  " +
//                    "    SELECT  " +
//                    "        c.id AS id,  " +
//                    "        c.name AS couponName,  " +
//                    "        c.promotion_type AS promotionType,  " +
//                    "        c.discount_type AS discountType,  " +
//                    "        c.discount_value AS discountValue,  " +
//                    "        c.max_used AS maxUsed,  " +
//                    "        c.minimum AS minimum ,  " +
//                    "        c.minimum_amount AS minimumAmount,  " +
//                    "        c.maximum_promotion AS maximumPromotion,  " +
//                    "        c.start_date AS startDate,  " +
//                    "        c.end_date AS endDate, " +
//                    "        c.portal AS portal , " +
//                    "        c.user_id AS userId, " +
//                    "        c.created_by AS createdBy, " +
//                    "        c.discount_amount AS discountAmount, " +
//                    "        c.limited_quantity AS limitedQuantity, " +
//                    "        c.type AS type, " +
//                    "        c.times_used_type AS timesUsedType, " +
//                    "        c.enterprise_type AS enterpriseType, " +
//                    "        c.addons_type AS addonType, " +
//                    "        c.pricing_type AS pricingType, " +
//                    "        c.total_bill_type AS totalBillType, " +
//                    "        c.province_id AS provinceId " +
//                    "    FROM  " +
//                    "        {h-schema}coupons c  " +
//                    "    LEFT JOIN {h-schema}coupon_addons ca ON  " +
//                    "        ca.coupon_id = c.id  " +
//                    "    LEFT JOIN {h-schema}coupon_enterprise ce ON  " +
//                    "        ce.coupon_id = c.id  " +
//                    "    WHERE  " +
//                    "        (c.addons_type ISNULL OR c.addons_type IN (-1, 0) OR -1 IN (:addonIds) OR ca.addons_id IN (:addonIds) )  " +
//                    "        AND c.deleted_flag = 1 AND c.id = :couponId  " +
//                    "        AND c.approve = 1  " +
//                    "        AND c.status = 1 " +
//                    "        AND c.total_bill_type = 1  " +
//                    "        AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))  " +
//                    "        AND (c.enterprise_type ISNULL OR c.enterprise_type IN (-1, 0) OR (:companyId =-1 OR ce.user_id = :companyId) ) ) cot  " +
//                    "JOIN {h-schema}coupons c ON  " +
//                    "    c.id = cot.id" ;
            "SELECT  \n" +
                    "  c.id AS id,  \n" +
                    "  c.name AS couponName,  \n" +
                    "  c.promotion_type AS promotionType,  \n" +
                    "  c.discount_type AS discountType,  \n" +
                    "  c.discount_value AS discountValue,  \n" +
                    "  c.max_used AS maxUsed,  \n" +
                    "  c.minimum AS minimum ,  \n" +
                    "  c.minimum_amount AS minimumAmount,  \n" +
                    "  c.maximum_promotion AS maximumPromotion,  \n" +
                    "  c.start_date AS startDate,  \n" +
                    "  c.end_date AS endDate, \n" +
                    "  c.portal AS portal , \n" +
                    "  c.user_id AS userId, \n" +
                    "  c.created_by AS createdBy, \n" +
                    "  c.discount_amount AS discountAmount, \n" +
                    "  c.limited_quantity AS limitedQuantity, \n" +
                    "  c.type AS type, \n" +
                    "  c.times_used_type AS timesUsedType, \n" +
                    "  c.enterprise_type AS enterpriseType, \n" +
                    "  c.addons_type AS addonType, \n" +
                    "  c.pricing_type AS pricingType, \n" +
                    "  c.total_bill_type AS totalBillType, \n" +
                    "  c.province_id AS provinceId \n" +
                    "FROM {h-schema}coupons c  \n" +
                    "LEFT JOIN {h-schema}coupon_enterprise ce ON ce.coupon_id = c.id  \n" +
                    "WHERE  \n" +
                    "  (c.enterprise_type IS NULL OR c.enterprise_type = -1 OR (c.enterprise_type = 1 AND (:companyId = -1 OR ce.user_id = :companyId)) )\n" +
                    "  AND (c.total_bill_type IS NULL OR c.total_bill_type = 1 )\n" +
                    "  AND c.deleted_flag = 1 AND c.id = :couponId\n" +
                    "  AND c.status = 1\n" +
                    "  AND c.approve = 1\n" +
                    "  AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))";

    public static final String GET_ONLY_ONE_COUPON_POPUP_FOR_TOTAL_BILL_WHEN_SUBS_COMBO_PLAN =
//            "SELECT  " +
//                    "    cot.*  " +
//                    "FROM  " +
//                    "    (  " +
//                    "    SELECT  " +
//                    "        c.id AS id,  " +
//                    "        c.name AS couponName,  " +
//                    "        c.promotion_type AS promotionType,  " +
//                    "        c.discount_type AS discountType,  " +
//                    "        c.discount_value AS discountValue,  " +
//                    "        c.max_used AS maxUsed,  " +
//                    "        c.minimum AS minimum ,  " +
//                    "        c.minimum_amount AS minimumAmount,  " +
//                    "        c.maximum_promotion AS maximumPromotion,  " +
//                    "        c.start_date AS startDate,  " +
//                    "        c.end_date AS endDate,  " +
//                    "        c.portal AS portal , " +
//                    "        c.user_id AS userId, " +
//                    "        c.created_by AS createdBy, " +
//                    "        c.discount_amount AS discountAmount, " +
//                    "        c.limited_quantity AS limitedQuantity, " +
//                    "        c.type AS type, " +
//                    "        c.times_used_type AS timesUsedType, " +
//                    "        c.enterprise_type AS enterpriseType, " +
//                    "        c.addons_type AS addonType, " +
//                    "        c.pricing_type AS pricingType, " +
//                    "        c.total_bill_type AS totalBillType, " +
//                    "        c.province_id AS provinceId " +
//                    "    FROM  " +
//                    "        {h-schema}coupons c  " +
//                    "    LEFT JOIN {h-schema}coupon_combo_plan_apply cc ON  " +
//                    "        cc.coupon_id = c.id  " +
//                    "    LEFT JOIN {h-schema}coupon_enterprise ce ON  " +
//                    "        ce.coupon_id = c.id  " +
//                    "    WHERE  " +
//                    "        (c.enterprise_type ISNULL OR c.enterprise_type IN (-1, 0) OR ce.user_id = :companyId )  " +
//                    "        AND (c.pricing_type ISNULL OR c.pricing_type IN (-1, 0)  OR cc.combo_plan_id = :comboPlanId )  " +
//                    "        AND c.deleted_flag = 1 AND c.id = :couponId   " +
//                    "        AND c.status = 1 " +
//                    "        AND c.approve = 1  " +
//                    "        AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))  " +
//                    "        AND c.total_bill_type = 1  " +
//                    "UNION  " +
//                    "    SELECT  " +
//                    "        c.id AS id,  " +
//                    "        c.name AS couponName,  " +
//                    "        c.promotion_type AS promotionType,  " +
//                    "        c.discount_type AS discountType,  " +
//                    "        c.discount_value AS discountValue,  " +
//                    "        c.max_used AS maxUsed,  " +
//                    "        c.minimum AS minimum ,  " +
//                    "        c.minimum_amount AS minimumAmount,  " +
//                    "        c.maximum_promotion AS maximumPromotion,  " +
//                    "        c.start_date AS startDate,  " +
//                    "        c.end_date AS endDate,  " +
//                    "        c.portal AS portal , " +
//                    "        c.user_id AS userId, " +
//                    "        c.created_by AS createdBy, " +
//                    "        c.discount_amount AS discountAmount, " +
//                    "        c.limited_quantity AS limitedQuantity, " +
//                    "        c.type AS type, " +
//                    "        c.times_used_type AS timesUsedType, " +
//                    "        c.enterprise_type AS enterpriseType, " +
//                    "        c.addons_type AS addonType, " +
//                    "        c.pricing_type AS pricingType, " +
//                    "        c.total_bill_type AS totalBillType, " +
//                    "        c.province_id AS provinceId " +
//                    "    FROM  " +
//                    "        {h-schema}coupons c  " +
//                    "    LEFT JOIN {h-schema}coupon_addons ca ON  " +
//                    "        ca.coupon_id = c.id  " +
//                    "    LEFT JOIN {h-schema}coupon_enterprise ce ON  " +
//                    "        ce.coupon_id = c.id  " +
//                    "    WHERE  " +
//                    "        (c.addons_type ISNULL OR c.addons_type IN (-1, 0) OR -1 IN (:addonIds) OR ca.addons_id IN (:addonIds) )  " +
//                    "        AND c.deleted_flag = 1 AND c.id = :couponId   " +
//                    "        AND c.approve = 1  " +
//                    "        AND c.status = 1 " +
//                    "        AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds)) " +
//                    "        AND c.total_bill_type = 1  " +
//                    "        AND (c.enterprise_type ISNULL OR c.enterprise_type IN (-1, 0) OR ce.user_id = :companyId ) ) cot  " +
//                    "JOIN {h-schema}coupons c ON  " +
//                    "    c.id = cot.id";
            "SELECT  \n" +
                    "  c.id AS id,  \n" +
                    "  c.name AS couponName,  \n" +
                    "  c.promotion_type AS promotionType,  \n" +
                    "  c.discount_type AS discountType,  \n" +
                    "  c.discount_value AS discountValue,  \n" +
                    "  c.max_used AS maxUsed,  \n" +
                    "  c.minimum AS minimum ,  \n" +
                    "  c.minimum_amount AS minimumAmount,  \n" +
                    "  c.maximum_promotion AS maximumPromotion,  \n" +
                    "  c.start_date AS startDate,  \n" +
                    "  c.end_date AS endDate, \n" +
                    "  c.portal AS portal , \n" +
                    "  c.user_id AS userId, \n" +
                    "  c.created_by AS createdBy, \n" +
                    "  c.discount_amount AS discountAmount, \n" +
                    "  c.limited_quantity AS limitedQuantity, \n" +
                    "  c.type AS type, \n" +
                    "  c.times_used_type AS timesUsedType, \n" +
                    "  c.enterprise_type AS enterpriseType, \n" +
                    "  c.addons_type AS addonType, \n" +
                    "  c.pricing_type AS pricingType, \n" +
                    "  c.total_bill_type AS totalBillType, \n" +
                    "  c.province_id AS provinceId \n" +
                    "FROM {h-schema}coupons c  \n" +
                    "LEFT JOIN {h-schema}coupon_enterprise ce ON ce.coupon_id = c.id  \n" +
                    "WHERE  \n" +
                    "  (c.enterprise_type IS NULL OR c.enterprise_type = -1 OR (c.enterprise_type = 1 AND (:companyId = -1 OR ce.user_id = :companyId)) )\n" +
                    "  AND (c.total_bill_type IS NULL OR c.total_bill_type = 1 )\n" +
                    "  AND c.deleted_flag = 1 AND c.id = :couponId\n" +
                    "  AND c.status = 1\n" +
                    "  AND c.approve = 1\n" +
                    "  AND (:checkCouponIds = 0 OR c.id NOT IN (:couponIds))";

    public static final String UPDATE_SUBSCRIPTION_QUANTITY_BY_ID =
        "UPDATE {h-schema}subscriptions SET used_quantity = :quantity WHERE id = :subscriptionId";

    public static final String UPDATE_SUBSCRIPTION_QUANTITY_BY_ADDON_ID =
          "UPDATE "
        + "    {h-schema}subscription_addons "
        + "SET "
        + "    dhsxkd_used_quantity = :quantity "
        + "WHERE "
        + "    subscription_id = :subscriptionId "
        + "    AND addons_id = :addonId";

    public static final String GET_DEVELOPE_NAME = " SELECT "
        + "    DISTINCT u.name AS developerName "
        + "  FROM "
        + "     {h-schema}users u "
        + " LEFT JOIN {h-schema}departments d ON "
        + " d.user_id = u.id"
        + "  WHERE "
        + "    u.deleted_flag = 1 "
        + "    AND u.status = 1 "
        + "    AND u.name NOTNULL "
        + "    AND( '' = :devName "
        + "    OR u.name ILIKE ('%'|| :devName ||'%')) "
        + "    AND ('' = :customerType OR u.customer_type SIMILAR TO '%('|| :customerType ||')%') "
        + " ORDER BY "
        + " u.name ASC";

    public static final String GET_LIST_PROVINCE_FOR_SUB =
        "SELECT "
            + "   DISTINCT p.* "
            + "FROM "
            + "   {h-schema}users u "
            + "INNER JOIN ( "
            + "   SELECT "
            + "      DISTINCT s.user_id "
            + "   FROM "
            + "      {h-schema}subscriptions s "
            + "   WHERE "
            + "      s.deleted_flag = 1 "
            + ") AS tbl "
            + "   ON u.id = tbl.user_id "
            + "INNER JOIN {h-schema}province p "
            + "   ON u.province_id = p.id "
            + "WHERE (:provinceId = -1 OR p.id = :provinceId) "
            + "ORDER BY p.id ASC";

    public static final String GET_DEVELOPE_NAME_BY_PROVINCE_ADMIN =
             " SELECT "
            + "    DISTINCT u.name AS developerName "
            + "  FROM "
            + "     {h-schema}users u "
            + " LEFT JOIN {h-schema}departments d ON "
            + " d.user_id = u.id"
            + "  WHERE "
            + "    u.deleted_flag = 1 "
            + "    AND u.status = 1 "
            + "    AND u.name NOTNULL "
            + "    AND d.province_id = :provinceId "
            + "    AND( '' = :devName "
            + "    OR u.name ILIKE ('%'|| :devName ||'%')) "
            + "    AND ('' = :customerType OR u.customer_type SIMILAR TO '%('|| :customerType ||')%') "
            + " ORDER BY "
            + " u.name ASC";

    public static final String GET_SUBSCRIPTION_SERVICE_BY_ADDON =
        " SELECT s.*   "
            + "FROM   "
            + "   {h-schema}subscriptions s   "
            + "JOIN {h-schema}subscription_addons sa ON   "
            + "   sa.subscription_id = s.id   " 
            + " JOIN {h-schema}services s2 ON   "
            + "   s2.id = s.service_id   "
            + "   AND s2.service_owner IN (0, 1)"
            + "   AND s2.deleted_flag = 1 "
            + "WHERE   "
            + "   s.user_id = :companyId   "
            + "   AND sa.addons_id IN (:addonIds)   "
            + "   AND s.deleted_flag = 1   "
            + "   AND s.confirm_status = 1   "
            + "   AND s.status IN (0, 2)";
    public static final String GET_SUB_COMBO_BY_COUPON = 
        " SELECT s.*   "
            + "FROM   "
            + "   {h-schema}subscriptions s   "
            + "JOIN {h-schema}subscription_combo_coupon scc ON   "
            + "   scc.subscription_id = s.id   "
            + "JOIN {h-schema}combo_plan cp ON   "
            + "   cp.id = s.combo_plan_id   "
            + "   AND cp.deleted_flag = 1   "
            + "JOIN {h-schema}combo c2 ON   "
            + "   c2.id = cp.combo_id   "
            + "   AND c2.deleted_flag = 1  "
            + "   AND c2.combo_owner IN (0, 1) "
            + "WHERE   "
            + "   s.user_id = :companyId   "
            + "   AND scc.coupon_id IN (:couponIds)   "
            + "   AND s.deleted_flag = 1   "
            + "   AND s.confirm_status = 1   "
            + "   AND s.status IN (0, 2)";
    public static final String GET_SUB_SERVICE_BY_COUPON =
        " SELECT s.*   "
            + "FROM   "
            + "   {h-schema}subscriptions s   "
            + "JOIN {h-schema}subscription_coupons sc ON   "
            + "   sc.subscription_id = s.id   "
            + "JOIN {h-schema}services s2 ON   "
            + "   s2.id = s.service_id   "
            + "   AND s2.deleted_flag = 1   "
            + "   AND s2.service_owner IN (0, 1)   "
            + "WHERE   "
            + "   s.user_id = :companyId   "
            + "   AND sc.coupon_id IN (:couponIds)   "
            + "   AND s.deleted_flag = 1   "
            + "   AND s.confirm_status = 1   "
            + "   AND s.status IN (0, 2)";

    public static final String GET_ADDON_SET_UP_FEE =
                "SELECT "
              + "    DISTINCT -1 AS unitFrom, -1 AS unitTo, "
              + "    COALESCE(ssf.setup_fee, null) AS price, "
              + "    CASE "
              + "        WHEN ssf.id IS NOT NULL THEN TRUE "
              + "        ELSE FALSE "
              + "    END AS isEdited "
              + "FROM "
              + "    {h-schema}addons a "
              + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
              + "    ssf.addon_id = a.id "
              + "    AND ssf.subscription_id = :subscriptionId "
              + "LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id = a.id "
              + "    AND :pricingMultiPlanId <> -1 AND pmp.id = :pricingMultiPlanId "
              + "WHERE "
              + "    a.id = :addonId";

    public static final String GET_PRICING_SET_UP_FEE =
              "SELECT "
            + "    -1 AS unitFrom, "
            + "    -1 AS unitTo, "
            + "    COALESCE(ssf.setup_fee, p.setup_fee, null) AS price, "
            + "    CASE "
            + "        WHEN ssf.id IS NOT NULL THEN TRUE "
            + "        ELSE FALSE "
            + "    END AS isEdited "
            + "FROM "
            + "    {h-schema}pricing p "
            + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
            + "    ssf.pricing_id = p.id "
            + "    AND ssf.subscription_id = :subscriptionId "
            + "LEFT JOIN {h-schema}pricing_multi_plan pmp ON "
            + "    pmp.pricing_id = p.id "
            + "    AND :pricingMultiPlanId <> -1 AND pmp.id = :pricingMultiPlanId "
            + "WHERE "
            + "    p.id = :pricingId";

    public static final String GET_COMBO_PLAN_SET_UP_FEE =
              "SELECT "
            + "    -1 AS unitFrom, "
            + "    -1 AS unitTo, "
            + "    COALESCE(ssf.setup_fee, cp.setup_fee, null) AS price, "
            + "    CASE "
            + "        WHEN ssf.id IS NOT NULL THEN TRUE "
            + "        ELSE FALSE "
            + "    END AS isEdited "
            + "FROM "
            + "    {h-schema}combo_plan cp "
            + "LEFT JOIN {h-schema}subscription_setup_fee ssf ON "
            + "    ssf.combo_plan_id = cp.id "
            + "    AND ssf.subscription_id = :subscriptionId "
            + "WHERE "
            + "    cp.id = :comboPlanId";

    public static final String GET_PRICING_TYPE_BY_SUBSCRIPTION_ID =
        "SELECT "
            + "    COALESCE(p.pricing_type, cp.combo_plan_type) AS pricingType "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "LEFT JOIN {h-schema}pricing p ON "
            + "    s.pricing_id = p.id "
            + "LEFT JOIN {h-schema}combo_plan cp ON "
            + "    s.combo_plan_id = cp.id "
            + "WHERE "
            + "    s.id = :id";

    public static final String GET_UNIT_LIMITED_BY_ADDON_ID =
        "SELECT new com.entity.unitLimited.UnitLimited(ul.unitFrom, ul.unitTo, ul.price) "
            + "FROM UnitLimited ul "
            + "    WHERE ul.addonsId = :id and ul.subscriptionSetupFeeId is null";

    public static final String GET_PLAN_DETAIL_BY_PERIOD_ID =
        "SELECT new com.entity.unitLimited.UnitLimited(ppd.unitFrom, ppd.unitTo, ppd.price) "
            + "FROM PricingPlanDetail ppd "
            + "    WHERE ppd.pricingMultiPlanId = :id and ppd.subscriptionSetupFeeId is null";

    public static final String GET_ALL_IDS_OF_SUBSCRIPTIONS_CANCELED =
        "SELECT s.id FROM {h-schema}subscriptions s  "
            + "WHERE s.deleted_flag = 1 "
            + "AND s.status IN (0, 1) "
            + "AND s.started_at > now() "
            + "AND s.id IN (:ids)";

    public static final String DELETE_SOFT_ALL_BY_IDS =
        "UPDATE "
            + "    {h-schema}subscriptions "
            + "SET "
            + "    deleted_flag = 0 "
            + "WHERE "
            + "    id IN (:ids)";

    public static final String UPDATE_SUBSCRIPTION_IS_MASOFFER =
        "UPDATE {h-schema}subscriptions "
            + "SET is_call_masoffer = 1 "
            + "WHERE id IN :ids ";

    public static final String UPDATE_BATCH_MASOFFER_DELETE_FLAG =
        " UPDATE {h-schema}batch_masoffer "
            + "SET deleted_flag = 0 "
            + "WHERE id IN :ids ";

    public static final String UPDATE_USED_QUANTITY_BY_SUBSCRIPTION_ID_AND_PRICING_ID =
        "UPDATE "
            + "    {h-schema}subscriptions "
            + "SET "
            + "    used_quantity = :quantity "
            + "WHERE "
            + "    id = :id "
            + "    AND pricing_id = :pricingId";
    public static final String GET_TOP_USED_SUBSCRIPTION =
            " select * from ( " +
            " select" +
                    " count(user_id) total,pricingId,pricingName,serviceId, serviceName, subscriptionType, subscriptionId, ROW_NUMBER() OVER( ORDER BY count(user_id) DESC) as rank " +
                    " from (" +
                    " select " +
//                    " count(us.user_id) total, " +
                    " us.user_id as user_id, " +
//                    " ROW_NUMBER() OVER( ORDER BY count(us.subscription_id) DESC) as rank, " +
                    " case " +
                    " when sub.pricing_id is not null then cast(concat(sub.pricing_id, '0000') as BIGINT) " +
                    " when sub.combo_plan_id is not null then cast(concat(sub.combo_plan_id,'0001') as BIGINT) " +
                    " end as pricingId, " +
                    " case " +
                    " when sub.pricing_id is not null then pri.pricing_name " +
                    " when sub.combo_plan_id is not null then cp.combo_name " +
                    " end as pricingName, " +
                    " case " +
                    " when ser.id is not null then cast(concat(ser.id,'0000') as bigint) " +
                    " when co.id is not null then cast(concat(co.id,'0001') as bigint) " +
                    " end as serviceId, " +
                    " case " +
                    " when ser.id is not null then ser.service_name " +
                    " when co.id is not null then co.combo_name " +
                    " end as serviceName, " +
                    " CASE " +
                    " WHEN (sub.combo_plan_id is not null and co.combo_owner in (0,1)) then 1 " +
                    "    WHEN (sub.combo_plan_id is not null and (co.combo_owner in (2,3) or co.combo_owner is null)) then 2 " +
                    "    WHEN ser.service_owner in (0,1) then 1 " +
                    "    ELSE 2 " +
                    " END AS subscriptionType, " +
                    " us.subscription_id as subscriptionId " +
                    " from {h-schema}user_subscription as us " +
                    "inner join {h-schema}subscriptions sub on sub.id = us.subscription_id " +
                    "inner join {h-schema}billings b on b.subscriptions_id = sub.id " +
                    "left join {h-schema}pricing as pri on pri.id = sub.pricing_id and sub.combo_plan_id is null " +
                    "left join {h-schema}services as ser on ser.id = pri.service_id " +
                    "left join {h-schema}combo_plan as cp on cp.id = sub.combo_plan_id and sub.combo_plan_id is not null " +
                    "left join {h-schema}combo as co on co.id = cp.combo_id " +
                    " left join {h-schema}users as u on (us.user_id = u.id or us.user_id = u.parent_id) " +
                    " where CAST(sub.created_at as date) between CAST(:fromDate as DATE) and CAST(:toDate as DATE) " +
                    " and (u.id = :userId or u.parent_id = :userId) " +
                    " and sub.deleted_flag = 1 and sub.confirm_status=1 " +
                    " and u.status = 1 " +
                    " and sub.status = 2 " +
                    " and us.status = 1 " +
                    " and us.service_id IS NOT NULL " +
//                    " and b.payment_date is not null " +
                    "group by sub.pricing_id,sub.combo_plan_id,pri.pricing_name,cp.combo_name,ser.id,co.id,us.subscription_id, us.user_id " +
//                    "order by total desc " +
                    ") d " +
                    "where 1 = 1 " +
                    "and (0 in (:filterServiceList) or serviceId in (:filterServiceList)) " +
                    "and (0 = :filterServiceType or subscriptionType = :filterServiceType) " +
//                    "and (0 = :topTier or rank <= :topTier)" +
                    " group by pricingId,pricingName,serviceId, serviceName, subscriptionType, subscriptionId " +
                    ") e where (0 = :topTier or rank <= :topTier) ";
    public static final String GET_TOP_USED_SUBSCRIPTION_DETAIL =
        "select \n" +
                " u.id as id, \n" +
                " u.email as email, \n" +
                " u.tech_id as techId, \n" +
                " concat_ws(' ', u.last_name, u.first_name) as username, \n" +
                " u.phone_number as phone, \n" +
                " d.department_name as department, \n" +
                " case \n" +
                "   when u.status = 0 then 'Không hoạt động' \n" +
                "   when u.status = 1 then 'Hoạt động' \n" +
                " end as status, \n" +
                " to_char(date_trunc('day', u.created_at),'dd-MM-yyyy') as createdAt, \n" +
                " to_char(date_trunc('day', u.modified_at),'dd-MM-yyyy') as modifiedAt, \n" +
                " '' as expiredDate \n" +
                " from {h-schema}user_subscription as us \n" +
                " inner join {h-schema}subscriptions sub on us.subscription_id = sub.id \n" +
                " inner join {h-schema}users as u on u.id = us.user_id \n" +
                " left join {h-schema}departments as d on d.id = u.department_id\n" +
                " where (:subscriptionId = -1 or us.subscription_id = :subscriptionId) \n" +
                " and us.status = 1 \n" +
                " and us.service_id IS NOT NULL \n" +
                " and CAST(sub.created_at as date) between CAST(:fromDate as date) and CAST(:toDate as date) \n" +
                " group by email, tech_id, username, phone, department, u.created_at, u.modified_at, u.status, u.id ";

    public static final String GET_LIST_NOTIFY_REMIND_SUB =
        "SELECT "
            + "    s.id AS subId, "
            + "    b.id AS billId, "
            + "    u.id AS userId, "
            + "    u.email AS userEmail, "
            + "    uparent.id AS parentId, "
            + "    uparent.email AS parentUserEmail, "
            + "    concat(u.first_name , ' ', u.last_name) AS userFullName, "
            + "    concat(uparent.first_name , ' ', uparent.last_name) AS parentFullName, "
            + "    s.portal_type AS portalType, "
            + "    u.name AS companyName, "
            + "    CASE "
            + "        WHEN s.combo_plan_id IS NOT NULL THEN c.combo_name "
            + "        ELSE s2.service_name "
            + "    END AS nameService, "
            + "    CASE "
            + "        WHEN s.combo_plan_id IS NOT NULL THEN cp.combo_name "
            + "        ELSE p.pricing_name "
            + "    END AS namePricing, "
            + "    b.current_payment_date AS currentPaymentDate, "
            + "    b.final_payment_term AS finalPaymentTern "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}billings b ON "
            + "    s.id = b.subscriptions_id "
            + "JOIN ( "
            + "    SELECT "
            + "        MAX(b.id) AS id "
            + "    FROM "
            + "        {h-schema}billings b "
            + "    GROUP BY "
            + "        b.subscriptions_id ) AS tbl ON "
            + "    b.id = tbl.id "
            + "LEFT JOIN {h-schema}pricing p ON "
            + "    s.pricing_id = p.id "
            + "LEFT JOIN {h-schema}pricing_multi_plan pmp ON "
            + "    pmp.id = s.pricing_multi_plan_id "
            + "    AND pmp.pricing_id = s.pricing_id "
            + "LEFT JOIN {h-schema}services s2 ON "
            + "    s2.id = p.service_id "
            + "LEFT JOIN {h-schema}combo_plan cp ON "
            + "    s.combo_plan_id = cp.id "
            + "LEFT JOIN {h-schema}combo c ON "
            + "    c.id = cp.combo_id "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = s.user_id "
            + "LEFT JOIN {h-schema}users uparent ON "
            + "    u.parent_id = uparent.id "
            + "WHERE "
            + "    s.deleted_flag = 1 "
            + "    AND s.status NOT IN (3, 4) "
            + "    AND (b.status <> 2) "
            + "    AND (b.next_payment_time > now() OR  b.final_payment_term > now()) "
            + "    AND (s.pricing_id IS NOT NULL "
            + "        AND ((s.pricing_multi_plan_id IS NULL "
            + "            AND p.cycle_type = 0 "
            + "            AND p.payment_cycle <= 3) "
            + "        OR (s.pricing_multi_plan_id IS NOT NULL "
            + "            AND pmp.circle_type = 0 "
            + "            AND pmp.payment_cycle <= 3)) "
            + "        OR (s.combo_plan_id IS NOT NULL "
            + "            AND (cp.cycle_type = 0 "
            + "                AND cp.payment_cycle <= 3)) "
            + "        OR ((p.pricing_type = 1 OR cp.combo_plan_type = 1) AND  "
            + "        b.next_payment_time - Date(NOW()) BETWEEN 0 AND 3)  "
            + "        OR ((p.pricing_type = 0 OR cp.combo_plan_type = 0) AND  "
            + "        b.current_payment_date - Date(NOW()) BETWEEN 0 AND 3))";

    public static final String GET_LIST_NOTIFY_REMIND_EXTEND_SUB =
        "SELECT "
            + "    s.id AS subId, "
            + "    b.id AS billId, "
            + "    u.id AS userId, "
            + "    u.email AS userEmail, "
            + "    uparent.id AS parentId, "
            + "    uparent.email AS parentUserEmail, "
            + "    concat(u.last_name , ' ', u.first_name) AS userFullName, "
            + "    concat(uparent.last_name , ' ', uparent.first_name) AS parentFullName, "
            + "    s.portal_type AS portalType, "
            + "    u.name AS companyName, "
            + "    s.expired_time - DATE(NOW()) AS numbersOfDateRemaining, "
            + "    CASE "
            + "        WHEN s.combo_plan_id IS NOT NULL THEN c.combo_name "
            + "        ELSE s2.service_name "
            + "    END AS nameService, "
            + "    CASE "
            + "        WHEN s.combo_plan_id IS NOT NULL THEN cp.combo_name "
            + "        ELSE p.pricing_name "
            + "    END AS namePricing, "
            + "    b.current_payment_date AS currentPaymentDate, "
            + "    s.expired_time AS expiredTime  "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}billings b ON "
            + "    s.id = b.subscriptions_id "
            + "JOIN ( "
            + "    SELECT "
            + "        MAX(b.id) AS id "
            + "    FROM "
            + "        {h-schema}billings b "
            + "    GROUP BY "
            + "        b.subscriptions_id ) AS tbl ON "
            + "    b.id = tbl.id "
            + "LEFT JOIN {h-schema}pricing p ON "
            + "    s.pricing_id = p.id "
            + "LEFT JOIN {h-schema}pricing_multi_plan pmp ON "
            + "    pmp.id = s.pricing_multi_plan_id "
            + "    AND pmp.pricing_id = s.pricing_id "
            + "LEFT JOIN {h-schema}services s2 ON "
            + "    s2.id = p.service_id "
            + "LEFT JOIN {h-schema}combo_plan cp ON "
            + "    s.combo_plan_id = cp.id "
            + "LEFT JOIN {h-schema}combo c ON "
            + "    c.id = cp.combo_id "
            + "LEFT JOIN {h-schema}users u ON "
            + "    u.id = s.user_id "
            + "LEFT JOIN {h-schema}users uparent ON "
            + "    u.parent_id = uparent.id "
            + "INNER JOIN {h-schema}system_params sp ON "
            + "    sp.param_type = 'RENEWAL' "
            + "WHERE "
            + "    s.deleted_flag = 1 "
            + "    AND s.status NOT IN (3, 4) "
            + "    AND s.number_of_cycles <> -1 "
            + "    AND s.expired_time >= DATE(NOW()) "
            + "    AND s.expired_time - DATE(NOW()) <= sp.number_of_day_remind";
    public static final String FIND_SET_UP_FEE =
        " SELECT "
            + " DISTINCT sef.setup_fee "
            + "FROM "
            + " {h-schema}subscription_setup_fee sef "
            + "WHERE "
            + " sef.subscription_id = :subscriptionId "
            + " AND sef.pricing_id = :pricingId "
            + " AND sef.addon_id IS NULL";

    public static final String FIND_SET_UP_FEE_COMBO_PLAN =
        " SELECT "
            + " DISTINCT sef.setup_fee "
            + "FROM "
            + " {h-schema}subscription_setup_fee sef "
            + "WHERE "
            + " sef.subscription_id = :subscriptionId "
            + " AND sef.combo_plan_id = :comboPlanId "
            + " AND sef.addon_id IS NULL";

    public static final String GET_OBJECT_NAME_BY_ADDON_ID =
              "SELECT "
            + "    a.id, "
            + "    COALESCE(s.service_name, c.combo_name) AS name "
            + "FROM "
            + "    {h-schema}addons a "
            + "LEFT JOIN {h-schema}services s ON "
            + "    s.id = a.service_id "
            + "LEFT JOIN {h-schema}combo c ON "
            + "    c.id = a.combo_id "
            + "WHERE "
            + "    a.id IN :addonIdList";

    public static final String FIND_ALL_USER_ID_BY_ID_IN =
        "select user_id from {h-schema}subscriptions where subscriptions.id in (:lstSubId)";
    public static final String FIND_MAX_VER_IDS_BY_SERVICE_ID1 =
            "SELECT " +
                    "    p.id " +
                    "FROM " +
                    "    {h-schema}pricing p " +
                    "WHERE " +
                    "    p.status = 1 " +
                    "    AND p.deleted_flag = 1 " +
                    "    AND p.approve = 1 " +
                    "    AND id IN ( " +
                    "    SELECT " +
                    "        MAX(p.id) " +
                    "    FROM " +
                    "        {h-schema}pricing_draft pd " +
                    "    JOIN {h-schema}pricing p ON " +
                    "        pd.id = p.pricing_draft_id " +
                    "        AND pd.deleted_flag = 1 " +
                    "    LEFT JOIN {h-schema}services s ON " +
                    "        pd.service_id = s.id " +
                    "    WHERE " +
                    "        s.deleted_flag = 1 " +
                    "        AND s.approve = 1 " +
                    "        AND s.id = :serviceId " +
                    "    GROUP BY " +
                    "        pd.id )";
    public static final String COUNT_BY_USER_ID =
        "select " +
        "    count(id) " +
        "from " +
        "    {h-schema}subscriptions " +
        "where " +
        "    deleted_flag = 1 and " +
        "    confirm_status = 1 and " +
        "    user_id = :userId";

    public static final String GET_SUBSCRIPTION_PRODUCT =
        "select \n" +
            "    mSubscription.id as subscriptionId, \n" +
            "    coalesce(mComboPlan.combo_id, mPricing.service_id) as objectId, \n" +
            "    case \n" +
            "        when mSubscription.combo_plan_id is not null \n" +
            "            then 1 \n" +
            "        else 0 \n" +
            "    end as objectType \n" +
            "from \n" +
            "    {h-schema}subscriptions as mSubscription \n" +
            "    left join {h-schema}pricing as mPricing on mPricing.id = mSubscription.pricing_id \n" +
            "    left join {h-schema}combo_plan as mComboPlan on mComboPlan.id = mSubscription.combo_plan_id \n" +
            "where \n" +
            "    mSubscription.id in (:lstSubscriptionId) ";

    public static final String GET_ALL_SUBS_NOT_ECONTRACT =
            "select a.* from (select sub.id FROM {h-schema}subscriptions as sub " +
            " INNER JOIN {h-schema}services as s on s.id = sub.service_id " +
            " WHERE s.service_owner in (0,1)  AND sub.installed = 1 AND sub.id not in (SELECT ec.id_subscription FROM {h-schema}e_contract as ec) GROUP BY sub.id " +
            "  " +
            " union all " +
            "  " +
            " select sub.id FROM {h-schema}subscriptions as sub " +
            " INNER JOIN {h-schema}combo_plan cp on sub.combo_plan_id = cp.id " +
            " INNER JOIN {h-schema}combo as c on c.id = cp.combo_id  " +
            " WHERE c.combo_owner in (0,1) AND sub.installed = 1 AND sub.id not in (SELECT ec.id_subscription FROM {h-schema}e_contract as ec) GROUP BY sub.id " +
            "  " +
            "  " +
            " union all " +
            "  " +
            " select sub.id FROM {h-schema}subscriptions as sub " +
            " INNER JOIN {h-schema}services as s on s.id = sub.service_id " +
            " WHERE s.service_owner in (2,3) AND sub.id not in (SELECT ec.id_subscription FROM {h-schema}e_contract as ec) " +
            "  " +
            " union all " +
            "  " +
            " select sub.id FROM {h-schema}subscriptions as sub " +
            " INNER JOIN {h-schema}combo_plan cp on sub.combo_plan_id = cp.id " +
            " INNER JOIN {h-schema}combo as c on c.id = cp.combo_id  " +
            " WHERE c.combo_owner in (2,3) AND sub.id not in (SELECT ec.id_subscription FROM {h-schema}e_contract as ec)) as a ORDER BY a.id desc limit 100";

    public static final String FIND_ALL_PRICING_SORT_BY_COMBO =
        "SELECT " +
            " cp.id as id," +
            " cp.customer_type_code as customerType, " +
            " cp.combo_plan_draft_id as pricingDraftId," +
            " cp.combo_name as pricingName," +
            " cp.description as description," +
            " cp.combo_order as comboOrder" +
            " from {h-schema}combo_plan cp " +
            " WHERE cp.status = 1 " +
            " AND cp.deleted_flag = 1 " +
            " AND cp.combo_id = :serviceId AND (:customerType = '' OR cp.customer_type_code ILIKE ('%' || :customerType || '%'))" +
            " AND cp.id = (select max(cp1.id) from {h-schema}combo_plan cp1 where cp1.combo_plan_draft_id = cp.combo_plan_draft_id)" +
            " ORDER BY cp.combo_order ;";

    public static final String GET_ALL_SUB_BY_PROVIDER =
        "select sub.* from {h-schema}subscriptions sub \n" +
            "left join {h-schema}services ser ON sub.service_id = ser.id \n" +
            "left join {h-schema}users u ON u.id = ser.user_id\n" +
            "where sub.cart_code = :cartCode and sub.combo_plan_id is null and (u.id = :providerId or u.id = -1) \n" +
            "union all\n" +
            "select sub.* from {h-schema}subscriptions sub \n" +
            "left join {h-schema}combo_plan cp ON cp.id = sub.combo_plan_id\n" +
            "left join {h-schema}combo c ON c.id = cp.combo_id\n" +
            "left join {h-schema}users u ON u.id = c.user_id\n" +
            "where sub.cart_code = :cartCode and sub.combo_plan_id is not null and (u.id = :providerId or u.id = -1)";

    public static final String GET_SUB_ADDON =
        "with raw_data as (\n"
            + "        select\n"
            + "             addons.code as code,\n"
            + "             coalesce ( bill_item.quantity, 1 ) as quantity,\n"
            + "             addons.bonus_type as type,\n"
            + "             coalesce ( bill_item.amount / coalesce ( bill_item.quantity, 1 ), 0 ) as price,\n"
            + "             coalesce ( bill_item.amount_after_tax, 0 ) as totalAmount,\n"
            + "             coalesce( (coalesce ( bill_item.amount_after_tax, 0) - coalesce ( bill_item.amount_pre_tax, 0)) / coalesce ( bill_item.quantity, 1 ), 0) as vat\n"
            + "        from\n"
            + "             {h-schema}subscription_addons as subscription_addons\n"
            + "             left join {h-schema}addons as addons on addons.id = subscription_addons.addons_id\n"
            + "             left join {h-schema}billings as billings on billings.subscriptions_id = subscription_addons.subscription_id\n"
            + "             left join {h-schema}bill_item as bill_item on bill_item.billing_id = billings.id and bill_item.object_type in (2, 3) "
            + "                 and bill_item.object_id = addons.id\n"
            + "        where\n"
            + "             subscription_addons.subscription_id = :subId \n"
            + "             and billings.id in ( select billings.id from {h-schema}billings as billings where billings.subscriptions_id = :subId order by id DESC limit 1 )\n"
            + "     )\n"
            + "     \n"
            + "select\n"
            + "    raw_data.code as code,\n"
            + "    raw_data.quantity as quantity,\n"
            + "    raw_data.type as type,\n"
            + "    sum(raw_data.price) as price,\n"
            + "    sum(raw_data.totalAmount) as totalAmount,\n"
            + "    sum(raw_data.vat) as vat\n"
            + "from raw_data as raw_data\n"
            + "group by raw_data.code, raw_data.quantity, raw_data.type";

    public static final String EXISTS_SUB_BY_USER_ID_AND_SERVICE_ID_IS_ACTIVE =
        "SELECT EXISTS (SELECT s.id  FROM " +
            "                {h-schema}user_subscription us " +
            "            JOIN {h-schema}subscriptions s ON " +
            "                us.subscription_id = s.id " +
            "  LEFT JOIN {h-schema}pricing p ON " +
            "                s.pricing_id = p.id " +
            "            LEFT JOIN {h-schema}services service ON " +
            "                p.service_id = service.id " +
            " WHERE " +
            "      s.user_id = :userId AND s.status = 2 " +
            "    AND  s.confirm_status = 1 " +
            "    AND s.deleted_flag = 1 " +
            "     AND s.reg_type = 1 " +
            "and service.id = :serviceId )";

    public static final String GET_ID_SUB_BY_USER_ID_AND_SERVICE_ID =
            "SELECT id \n" +
                    "FROM {h-schema}subscriptions\n" +
                    "WHERE service_id = :serviceId\n" +
                    "	AND user_id = :userId\n" +
                    "	AND deleted_flag = 1\n" +
                "	AND combo_plan_id is null \n" +
                "order by id desc limit 1";

    public static final String GET_ID_SUB_BY_USER_ID_AND_COMBO_ID =
            "SELECT id \n" +
                    "FROM {h-schema}subscriptions\n" +
                    "WHERE service_id = :comboId\n" +
                    "	AND user_id = :userId\n" +
                    "	AND deleted_flag = 1\n" +
                    "	AND combo_plan_id is not null";

    public static final String COUNT_SERVICE_BY_SUB =
        "SELECT count(s.id) "
            + "FROM {h-schema}subscriptions s "
            + "JOIN {h-schema}services s2 ON s2.id  = s.service_id "
            + "WHERE s.id = :id ";

    public static final String GET_INFO_SUB_ORDER_BY_SUB_IDS =
        " SELECT  "
            + "    s.id AS subId, "
            + "    osr.id AS orderReceiveId, "
            + "    osr.transaction_code AS transactionCode, "
            + "    u.province_id AS provinceId "
            + " FROM {h-schema}order_service_receive osr  "
            + " JOIN {h-schema}subscriptions s  "
            + "    ON s.id = osr.subscription_id  "
            + " JOIN {h-schema}users u  "
            + "    ON u.id = s.user_id "
            + " WHERE osr.subscription_id IN (:ids)";

    public static final String UPDATE_CANCEL_SUB_ORDER =
        "UPDATE {h-schema}order_service_receive "
            + "SET order_status = 7 "
            + "WHERE id = :id";

    public static final String UPDATE_DHSX_CODE_AND_CONTRACT_ID =
        "UPDATE {h-schema}subscriptions "
            + "SET dhsxkd_sub_code = :subCodeDHSXKD, "
            + "   subscription_contract_id = :subscriptionContractId "
            + "WHERE id = :id";

    public static final String COUNT_UNEXPIRED_SUB_BY_USER_ID_AND_SERVICE_ID =
        "SELECT count(1) FROM {h-schema}subscriptions as s " +
                "WHERE s.confirm_status = 1 " +
                " AND s.deleted_flag = 1  " +
                " AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) " +
                " AND s.user_id = :userId " +
                " AND s.service_id = :serviceId " +
                " AND (s.expired_time is null OR (s.expired_time - DATE(NOW()) >= 0)) ";

   public static final String GET_CHANGE_SUBSCRIPTION_BY_SUB_ID_AND_ACTION =
           "SELECT "
                   + "s.* "
                   + "FROM "
                   + "    {h-schema}change_subscription s "
                   + "WHERE "
                   + "    s.subscription_id = :id "
                   + "    AND s.action = :action "
                   + "ORDER BY s.id desc "
                   + "LIMIT 1 ";

    public static final String GET_STATUS_ORDER_SERVICE =
            "SELECT oss.sme_progress_id " +
                    "FROM {h-schema}order_service_receive osr " +
                    "LEFT JOIN {h-schema}order_service_status oss ON osr.order_status = CAST(oss.id AS VARCHAR) " +
                    "WHERE osr.subscription_id = :subId order by osr.id desc limit 1";

    public static final String GET_STATUS_ORDER =
            "SELECT case when osr.payment_status = '1' then 2 " +
                    "when osr.payment_status = '0' then 1 " +
                    "when osr.payment_status is null and osr.id is not null then 1 " +
                    "end as payment_status " +
                    "     FROM {h-schema}order_service_receive osr " +
                    "     LEFT JOIN {h-schema}subscriptions sub ON osr.subscription_id = sub.id " +
                    "     WHERE osr.subscription_id = :subId ";

    public static final String CHECK_ENABLE_SMARTCA =
        "select \n" +
            "    coalesce(coalesce(combo.register_econtract, services.register_econtract), 0) = 1 as enableSmartCA \n" +
            "from \n" +
            "    {h-schema}subscriptions \n" +
            "    left join {h-schema}pricing on pricing.id = subscriptions.pricing_id \n" +
            "    left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id \n" +
            "    left join {h-schema}services on services.id = pricing.service_id \n" +
            "    left join {h-schema}combo on combo.id = combo_plan.combo_id \n" +
            "where \n" +
            "    subscriptions.id = :subscriptionId \n" +
            "limit 1";

    public static final String UPDATE_CALLED_TRANS =
        "UPDATE {h-schema}subscriptions "
            + "SET called_trans = :value "
            + "WHERE id = :id";

    public static final String FIND_SUBS_ADDON_COUPON_BY_SUBS_ID =
        "SELECT "
            + "    sac.* "
            + "FROM "
            + "    {h-schema}subscription_addon_coupon sac "
            + "JOIN {h-schema}subscription_addons sa ON "
            + "    sa.id = sac.subscription_addon_id "
            + "WHERE "
            + "    sa.subscription_id = :subscriptionId ";

    public static final String FIND_SUBS_COMBO_ADDON_COUPON_BY_SUBS_ID =
        "SELECT "
            + "    sac.* "
            + "FROM "
            + "    {h-schema}subscription_combo_addon_coupon sac "
            + "JOIN {h-schema}subscription_combo_addon sa ON "
            + "    sa.id = sac.subscription_combo_addon_id "
            + "WHERE "
            + "    sa.subscription_id = :subscriptionId ";

    public static final String GET_CART_CODE =
        "SELECT DISTINCT s.cart_code "
            + " FROM {h-schema}subscriptions s "
            + " WHERE s.deleted_flag = 1 "
            + " AND s.cart_code  ILIKE :cartCode ";

    public static final String IS_ON =
        "SELECT "
            + "    COALESCE(COALESCE (combo.combo_owner, service.service_owner), 2) IN ( 0, 1 ) \n"
            + "FROM "
            + "    {h-schema}subscriptions sub \n"
            + "LEFT JOIN {h-schema}combo_plan combo_plan ON sub.combo_plan_id = combo_plan.id \n"
            + "LEFT JOIN {h-schema}combo combo ON combo.id = combo_plan.combo_id \n"
            + "LEFT JOIN {h-schema}pricing pricing ON pricing.id = sub.pricing_id \n"
            + "LEFT JOIN {h-schema}services service ON sub.service_id = service.id \n"
            + "WHERE \n"
            + "    sub.id = :subId ";

    public static final String FIND_SUBSCRIPTION_BY_ID =
            "SELECT s.id AS id, "
            + "       s.pricing_id AS pricingId, "
            + "       s.combo_plan_id AS comboPlanId, "
            + "       s.pricing_multi_plan_id AS pricingMultiPlanId, "
            + "       s.user_id AS userId "
            + "FROM {h-schema}subscriptions s "
            + "WHERE s.id = :subscriptionId";

    public static final String GET_USERS_BY_SUB_ID =
        "SELECT "
            + "s.created_by AS userId "
            + "FROM "
            + "{h-schema}subscriptions s "
            + "WHERE "
            + "s.id = :subscriptionId";

    public static final String GET_LIST_BILLING_OF_SUB =
            "with total_amount_billing as (\n"
                    + " select\n"
                    + "   billing_code as billing_code,\n"
                    + "   sum (COALESCE(amount_after_tax,0)) as total_amount\n"
                    + " from {h-schema}feature_view_shopping_cart_get_list_billing\n"
                    + " group by billing_code \n"
                    + " )\n"
                    + " select "
                    + "   * "
                    + " from \n"
                    + "("
                    + " select \n"
                    + "   distinct on (vBillings.billing_code)\n"
                    + "   vBillings.billing_code as billingCode,\n"
                    + "   vBillings.billings_id as billingId,\n"
                    + "   vBillings.sub_code as subCode,\n"
                    + "   total_amount_billing.total_amount as totalAmount,\n"
                    + "   vBillings.required_payment_date as paymentDate,\n"
                    + "   vBillings.is_cart as isCart,\n"
                    + "   vBillings.created_at as createdAt,\n"
                    + "   vBillings.payment_status as status,\n"
                    + "   vBillings.is_one_time as isOneTime\n"
                    + " from {h-schema}feature_view_shopping_cart_get_list_billing as vBillings\n"
                    + " left join total_amount_billing on total_amount_billing.billing_code = vBillings.billing_code\n"
                    + " where \n"
                    + "   (:subId = any (vBillings.sub_id_array)) \n"
                    + " order by vBillings.billing_code desc\n"
                    + ") as a";

    public static final String GET_LIST_CUSTOMER_DETAIL =
        "SELECT DISTINCT \n" +
            "    users.id, \n" +
            "    users.name AS companyName, \n" +
            "    CASE \n" +
            "        WHEN users.rep_fullname IS NOT NULL THEN users.rep_fullname \n" +
            "        WHEN users.customer_type = 'CN' THEN concat(users.last_name,' ' ,users.first_name) \n" +
            "    END AS adminName,\n" +
            "    users.last_name AS lastName, \n" +
            "    users.first_name AS firstName, \n" +
            "    users.tin, \n" +
            "    users.nation_id as countryId, \n" +
            "    nation.name as countryName, \n" +
            "    users.province_id as provinceId, \n" +
            "    users.province_code as provinceCode, \n" +
            "    province.name as provinceName, \n" +
            "    users.district_id as districtId, \n" +
            "    district.name as districtName, \n" +
            "    users.ward_id as wardId, \n" +
            "    ward.name as wardName, \n" +
            "    users.street_id as streetId,  \n" +
            "    street.name as streetName, \n" +
            "    users.address as address, \n" +
            "    users.email as email, \n" +
            "    users.phone_number as phoneNumber, \n" +
            "    users.birthday as birthday, \n" +
            "    users.business_area_id as businessAreasId, \n" +
            "    business_area.name as businessAreasName, \n" +
            "    users.business_size_id as businessScaleId, \n" +
            "    business_size.name as businessScaleName, \n" +
            "    users.social_insurance_number as socialInsuranceNumber, \n" +
            "    users.description as description,\n" +
            "    users.home_number as apartmentNumber, \n" +
            "    users.rep_personal_cert_number as repPersonalCertNumber, \n" +
            "    users.rep_personal_cert_type_id as repPersonalCertType, \n" +
            "    users.rep_personal_cert_date as identityCreatedDate, \n" +
            "    CASE \n" +
            "        WHEN users.provider_type = 0 THEN 'LOCAL' \n" +
            "        WHEN users.provider_type = 1 THEN 'GOOGLE' \n" +
            "        WHEN users.provider_type = 2 THEN 'FACEBOOK' \n" +
            "        WHEN users.provider_type = 3 THEN 'PHONE' \n" +
            "        WHEN users.provider_type = 4 THEN 'APPLE' \n" +
            "    END AS userProviderType, \n" +
            "    address.tin as customerTin,\n" +
            "    address.sme_name as customerSmeName,\n" +
            "    users.home_number as homeNumber, \n" +
            "    users.rep_fullname as repFullName, \n" +
            "    coalesce(users.customer_type, 'KHDN') as customerType, \n" +
            "    users.employee_code as employeeCode \n" +
            "FROM \n" +
            "    {h-schema}users  \n" +
            "    LEFT JOIN {h-schema}province ON province.id = users.province_id \n" +
            "    LEFT JOIN {h-schema}nation ON nation.id = users.nation_id \n" +
            "    LEFT join {h-schema}district ON users.district_id = district.id AND users.province_code = district.province_code \n" +
            "    LEFT join {h-schema}ward ON ward.id = users.ward_id AND ward.district_id = district.id AND ward.province_id = province.id \n" +
            "    LEFT JOIN {h-schema}street ON street.id = users.street_id AND street.ward_id = users.ward_id AND street.district_id = users.district_id AND street.province_code = users.province_code \n" +
            "    LEFT join {h-schema}business_area  ON users.business_area_id = business_area.id  \n" +
            "    LEFT join {h-schema}business_size ON users.business_size_id = business_size.id  \n" +
            "    LEFT join {h-schema}address on users.id = address.user_id and address.type = 0 and address.default_location = 1 \n" +
            "WHERE \n" +
            "    users.id in (:lstUserId)";

    public static final String BODY_LIST_CUSTOMER_ID =
        "    FROM \n" +
        "        {h-schema}users \n" +
        "        JOIN {h-schema}view_role_sme ON users.id = view_role_sme.user_id \n" +
        "        LEFT JOIN {h-schema}province ON province.id = users.province_id \n" +
        "    WHERE \n" +
        "        users.status = 1 \n" +
        "        AND users.deleted_flag = 1 \n" +
        "        AND (:customerType = 'CN' OR users.parent_id = -1) \n" +
        "        AND (:searchProvinceStatus = -1 OR :provinceId = users.province_id) \n" +
        "        AND (:companyName = '' OR users.name ILIKE ('%' || :companyName || '%')) \n" +
        "        AND (:adminName = '' or \n" +
        "            (users.rep_fullname ILIKE ('%' || :adminName || '%') and users.rep_fullname is not null) or \n" +
        "            (concat(users.last_name,' ' ,users.first_name) ILIKE ('%' || :adminName || '%') and users.customer_type = 'CN')) \n" +
        "        AND (:tin = '' OR users.tin ILIKE ('%' || :tin || '%') ) \n" +
        "        AND (:provinceName = '' OR province.name ILIKE ('%' || :provinceName || '%') ) \n" +
        "        AND users.id <> :removeId \n" +
        "        AND (:customerType = '' OR users.customer_type = :customerType) \n" +
        "        AND (:repPersonalCertNumber = '' OR users.rep_personal_cert_number = :repPersonalCertNumber) \n" +
            "        AND (:userId = -1 OR users.id = :userId) " +
            "    AND ( " +
            "       :values = '' or  " +
            "       (:searchName = '1' and :customerType <> 'KHDN' and :customerType <> 'HKD' and (CONCAT(users.last_name, ' ', users.first_name)) ilike ('%' || :values || '%')) or  " +
            "       (:searchName = '1' and ( :customerType = 'KHDN' or :customerType = 'HKD') and users.name ilike ('%' || :values || '%')) or  " +
            "       (:searchEmail = '1' and users.email ilike ('%' || :values || '%')) or  " +
            "       (:searchPhone = '1' and users.phone_number ilike ('%' || :values || '%')) or  " +
            "       (:searchTin = '1' and (users.tin ilike ('%' || :values || '%') or users.rep_personal_cert_number ilike ('%' || :values || '%')))  " +
            "   )";

    public static final String GET_LIST_CUSTOMER_ID =
        "select id from (\n" +
            "    SELECT \n" +
            "        users.id,\n" +
            "        CASE \n" +
            "            WHEN users.rep_fullname IS NOT NULL THEN users.rep_fullname \n" +
            "            WHEN users.customer_type = 'CN' THEN concat(users.last_name,' ' ,users.first_name) \n" +
            "        END AS adminName,\n" +
            "        users.name AS companyName\n" +
            BODY_LIST_CUSTOMER_ID +
            ") as query ";

    public static final String COUNT_LIST_CUSTOMER_ID =
        "select count(users.id) \n" + BODY_LIST_CUSTOMER_ID ;

    public static final String FIND_MY_SERVICE_SUBSCRIPTION_NEW =
            "SELECT \n" +
                    "     rs.* \n" +
                    "FROM \n" +
                    "    ( \n" +
                    "    SELECT      \n" +
                    "        service.id AS serviceId, \n" +
                    "        service.service_name AS serviceName, \n" +
                    "        p.pricing_name AS pricingName, \n" +
                    "        fa.file_path AS icon, \n" +
                    "        banner.file_path AS banner, \n" +
                    "        fa.ext_link AS extLink, \n" +
                    "        service.url_service AS urlService, \n" +
                    "        CASE \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO' \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL' \n" +
                    "            ELSE 'YES' \n" +
                    "        END AS installed, \n" +
                    "        service.categories_id AS categoryId, \n" +
                    "        cate.name AS categoryName, \n" +
                    "        u.id AS companyId, \n" +
                    "        u.name AS companyName, \n" +
                    "        s.id AS subscriptionId, \n" +
                    "        s.created_at AS createdAt, \n" +
                    "        CASE \n" +
                    "            WHEN s.created_source_migration = 1 THEN 'DHSXKD'\n" +
                    "            WHEN s.created_source_migration = 0 THEN 'ONE_SME'\n" +
                    "            ELSE 'UNSET' \n" +
                    "        END AS createdSourceMigration,  \n" +
                    "        CASE \n" +
                    "            WHEN s.expired_time is null THEN 0 \n" +
                    "            WHEN s.expired_time - DATE(NOW()) < 0 THEN 1 \n" +
                    "            WHEN s.expired_time - DATE(NOW()) >= 0 THEN 0 \n" +
                    "        END AS isExpired, " +
                    "s.pricing_id as pricingId, " +
                    "pmp.id as pricingMultiPlanId, " +
                    "COALESCE(pmp.circle_type, p.cycle_type) as cycleType, " +
                    "COALESCE(pmp.payment_cycle, p.payment_cycle)as paymentCycle, " +
                "    rate.avg_rating as avgRating, \n" +
                    "service.service_owner as serviceOwner, \n" +
                    "        CASE \n" +
                    "            WHEN s.expired_time is null THEN 0 \n" +
                    "            WHEN s.expired_time < DATE(NOW()) AND p.has_renew = 1 AND s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0 THEN 1  \n" +
                    "        ELSE 0 END AS isRenew \n" +
                    "    FROM \n" +
                    "        {h-schema}subscriptions s \n" +
                    "    JOIN ( \n" +
                    "        SELECT \n" +
                    "            s.id, \n" +
                    "            raw_pricing_coupon.pricing_id as pricing_id \n" +
                    "        FROM {h-schema}subscriptions s \n" +
                    "        JOIN (\n" +
                    "            select \n" +
                    "            sa.subscription_id as id,\n" +
                    "            COALESCE(pmp.pricing_id, cp.coupon_id) as pricing_id\n" +
                    "        FROM {h-schema}subscription_addons sa \n" +
                    "        LEFT JOIN {h-schema}subscription_addon_coupon sac ON sa.id = sac.subscription_addon_id \n" +
                    "        LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sac.coupon_id\n" +
                    "        LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id \n" +
                    "        LEFT JOIN {h-schema}coupon_pricing cp ON sac.coupon_id = cp.coupon_id \n" +
                    "        ) as raw_pricing_coupon on raw_pricing_coupon.id = s.id and raw_pricing_coupon.pricing_id is not null\n" +
                    "        UNION ALL \n" +
                    "        SELECT \n" +
                    "            s.id, \n" +
                    "            raw_pricing_coupon.pricing_id\n" +
                    "        FROM {h-schema}subscriptions s \n" +
                    "            JOIN (\n" +
                    "                select \n" +
                    "                    sa.subscription_id as id,\n" +
                    "                    COALESCE(pmp.pricing_id, cp.coupon_id) as pricing_id\n" +
                    "                FROM {h-schema}subscription_coupons sa\n" +
                    "                LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sa.coupon_id \n" +
                    "                LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id \n" +
                    "                LEFT JOIN {h-schema}coupon_pricing cp ON sa.coupon_id = cp.coupon_id \n" +
                    "            ) as raw_pricing_coupon on raw_pricing_coupon.id = s.id and raw_pricing_coupon.pricing_id is not null\n" +
                    "        UNION ALL \n" +
                    "        SELECT \n" +
                    "            s.id, \n" +
                    "            raw_pricing_coupon.pricing_id\n" +
                    "        FROM {h-schema}subscriptions s \n" +
                    "        JOIN (\n" +
                    "            SELECT\n" +
                    "                spc.subscription_id as id,\n" +
                    "                COALESCE(pmp.pricing_id, cp.coupon_id) as pricing_id\n" +
                    "            FROM {h-schema}subscription_pricing_coupon spc\n" +
                    "            LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = spc.coupon_id \n" +
                    "            LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id \n" +
                    "            LEFT JOIN {h-schema}coupon_pricing cp ON spc.coupon_id = cp.coupon_id\n" +
                    "        ) as raw_pricing_coupon on raw_pricing_coupon.id = s.id and raw_pricing_coupon.pricing_id is not null\n" +
                    "    ) subGift ON subGift.id = s.id \n" +
                    "    LEFT JOIN {h-schema}pricing p ON p.id = subGift.pricing_id \n" +
                    "    JOIN {h-schema}services service ON service.id = p.service_id AND s.service_id <> p.service_id AND service.service_owner IS NOT NULL AND service.service_owner <> 2 AND service.service_owner <> 3 \n" +
                    "    JOIN {h-schema}categories cate ON cate.id = service.categories_id \n" +
                    "    JOIN {h-schema}users u ON u.id = service.user_id \n" +
                    "    LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13 \n" +
                    "    LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0 \n" +
                    "    JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId\n" +
                    "    LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = s.pricing_multi_plan_id \n" +
                "    LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "    WHERE \n" +
                    "        s.confirm_status = 1 \n" +
                    "        AND s.deleted_flag = 1 \n" +
                    "       AND ('' = :inputValue " +
                    "           or (:isPricingName = true and p.pricing_name ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  \n" +
                    "        AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) \n" +
                    "        AND s.user_id = :userId \n" +
                    "        AND service.service_name ILIKE ('%' || :search || '%') \n" +
                    "        AND (:categoryId = -1 OR cate.id = :categoryId) \n" +
                    "        AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR (p.has_renew = 1 AND s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0) OR (p.has_renew = 0 AND s.expired_time - DATE(NOW()) >= 0)) \n" +
                    "    \n" +


                    "    UNION ALL \n" +
                    "    SELECT         \n" +
                    "        service.id AS serviceId, \n" +
                    "        service.service_name AS serviceName, \n" +
                    "        a.\"name\" AS pricingName,\n" +
                    "        fa.file_path AS icon, \n" +
                    "        banner.file_path AS banner, \n" +
                    "        fa.ext_link AS extLink, \n" +
                    "        service.url_service AS urlService, \n" +
                    "        CASE \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO' \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL' \n" +
                    "            ELSE 'YES' \n" +
                    "        END AS installed, \n" +
                    "        service.categories_id AS categoryId, \n" +
                    "        cate.name AS categoryName, \n" +
                    "        u.id AS companyId, \n" +
                    "        u.name AS companyName, \n" +
                    "        s.id AS subscriptionId, \n" +
                    "        s.created_at AS createdAt, \n" +
                    "        CASE \n" +
                    "            WHEN s.created_source_migration = 1 THEN 'DHSXKD'\n" +
                    "            WHEN s.created_source_migration = 0 THEN 'ONE_SME'\n" +
                    "            ELSE 'UNSET' \n" +
                    "        END AS createdSourceMigration,  \n" +
                    "        0 as isExpired, \n" +
                    "        s.pricing_id as pricingId, \n" +
                    "        pmp.id as pricingMultiPlanId, \n" +
                    "COALESCE(pmp.circle_type, a.type) as cycleType, " +
                    "COALESCE(pmp.payment_cycle, a.bonus_value)as paymentCycle, " +
                "        rate.avg_rating as avgRating, \n" +
                    "        service.service_owner as serviceOwner, \n" +
                    "        0 AS isRenew \n" +
                    "    FROM \n" +
                    "        {h-schema}subscriptions s \n" +
                    "    JOIN {h-schema}subscription_addons sa ON s.id = sa.subscription_id \n" +
                    "    JOIN {h-schema}addons a ON sa.addons_id = a.id \n" +
                    "    LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id = a.id \n" +
                    "    JOIN {h-schema}services service ON service.id = a.service_id AND service.service_owner IS NOT NULL AND service.service_owner <> 2 AND service.service_owner <> 3 \n" +
                "    LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "    JOIN {h-schema}categories cate ON cate.id = service.categories_id \n" +
                    "    JOIN {h-schema}users u ON u.id = service.user_id \n" +
                    "    LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0 \n" +
                    "    LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13 \n" +
                    "    JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId\n" +
                    "    WHERE \n" +
                    "        s.confirm_status = 1 \n" +
                    "        AND s.deleted_flag = 1 \n" +
                    "       AND ('' = :inputValue " +
                    "           or (:isPricingName = true and a.\"name\" ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  \n" +
                    "        AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) \n" +
                    "        AND s.user_id = :userId \n" +
                    "        AND service.service_name ILIKE ('%' || :search || '%') \n" +
                    "        AND (:categoryId = -1 OR cate.id = :categoryId) \n" +
                    "        AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0)\n" +
                    "        AND pmp.id IS null\n" +
                    "        \n" +
                    "        \n" +


                    "    UNION ALL \n" +
                    "    SELECT \n" +
                    "        service.id AS serviceId, \n" +
                    "        service.service_name AS serviceName, \n" +
                    "        a.\"name\" AS pricingName, \n" +
                    "        fa.file_path AS icon, \n" +
                    "        banner.file_path AS banner, \n" +
                    "        fa.ext_link AS extLink, \n" +
                    "        service.url_service AS urlService, \n" +
                    "        CASE \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO' \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL' \n" +
                    "            ELSE 'YES' \n" +
                    "        END AS installed, \n" +
                    "        service.categories_id AS categoryId, \n" +
                    "        cate.name AS categoryName, \n" +
                    "        u.id AS companyId, \n" +
                    "        u.name AS companyName, \n" +
                    "        s.id AS subscriptionId, \n" +
                    "        s.created_at AS createdAt, \n" +
                    "        CASE \n" +
                    "            WHEN s.created_source_migration = 1 THEN 'DHSXKD'\n" +
                    "            WHEN s.created_source_migration = 0 THEN 'ONE_SME'\n" +
                    "            ELSE 'UNSET' \n" +
                    "        END AS createdSourceMigration,  \n" +
                    "        0 AS isExpired, \n" +
                    "        s.pricing_id as pricingId, \n" +
                    "        pmp2.id as pricingMultiPlanId, \n" +
                    "COALESCE(pmp2.circle_type, a.type) as cycleType, " +
                    "COALESCE(pmp2.payment_cycle, a.bonus_value)as paymentCycle, " +
                "    rate.avg_rating as avgRating, \n" +
                    "        service.service_owner as serviceOwner, \n" +
                    "        0 AS isRenew \n" +
                    "    FROM {h-schema}subscriptions s \n" +
                    "    JOIN {h-schema}subscription_addons sa ON sa.subscription_id = s.id AND sa.addons_id NOTNULL \n" +
                    "    JOIN {h-schema}pricing_multi_plan pmp2 ON sa.addons_id = pmp2.addon_id AND sa.pricing_multi_plan_id = pmp2.id AND pmp2.deleted_flag = 1 \n" +
                    "    JOIN {h-schema}addons a ON a.id = pmp2.addon_id AND a.deleted_flag = 1 AND a.bonus_type = 1 \n" +
                    "    JOIN {h-schema}services service ON service.id = a.service_id AND service.service_owner IS NOT NULL AND service.service_owner <> 2 AND service.service_owner <> 3 \n" +
                "    LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "    JOIN {h-schema}categories cate ON cate.id = service.categories_id \n" +
                    "    JOIN {h-schema}users u ON u.id = service.user_id \n" +
                    "    LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0 \n" +
                    "    LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13 \n" +
                    "    JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId \n" +
                    "    WHERE \n" +
                    "        s.confirm_status = 1 AND s.deleted_flag = 1 \n" +
                    "        AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) \n" +
                    "        AND s.user_id = :userId \n" +
                    "       AND ('' = :inputValue " +
                    "           or (:isPricingName = true and a.\"name\" ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  \n" +
                    "        AND service.service_name ILIKE ('%' || :search || '%') \n" +
                    "        AND (:categoryId = -1 OR cate.id = :categoryId) \n" +
                    "        AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0)\n" +


                    "    UNION ALL \n" +
                    "    select \n" +
                    "        services.id as serviceId, \n" +
                    "        services.service_name as serviceName, \n" +
                    "        pricing.pricing_name as pricingName, \n" +
                    "        icon.file_path as icon,     \n" +
                    "        banner.file_path as banner, \n" +
                    "        icon.ext_link as extLink, \n" +
                    "        services.url_service as urlService, \n" +
                    "        case \n" +
                    "            when subscriptions.installed is null or subscriptions.installed = 0 then 'NO' \n" +
                    "            when subscriptions.installed = 2 then 'FAIL' \n" +
                    "            else 'YES' \n" +
                    "        end as installed, \n" +
                    "        services.categories_id AS categoryId,     \n" +
                    "        categories.name as categorieName, \n" +
                    "        company.id as companyId, \n" +
                    "        company.name as companyName, \n" +
                    "        subscriptions.id as subscriptionId, \n" +
                    "        subscriptions.created_at AS createdAt, \n" +
                    "        case \n" +
                    "            when subscriptions.created_source_migration = 1 then 'DHSXKD' \n" +
                    "            when subscriptions.created_source_migration = 0 then 'ONE_SME' \n" +
                    "            else 'UNSET' \n" +
                    "        end as createdSourceMigration, \n" +
                    "        case \n" +
                    "            when subscriptions.expired_time is null then 0 \n" +
                    "            when subscriptions.expired_time::::date < now()::::date then 1 \n" +
                    "            when subscriptions.expired_time::::date >= now()::::date then 0 \n" +
                    "        end as isExpired, \n" +
                    "        subscriptions.pricing_id as pricingId, \n" +
                    "        pmp.id as pricingMultiPlanId, \n" +
                    "COALESCE(pmp.circle_type, pricing.cycle_type) as cycleType, " +
                    "COALESCE(pmp.payment_cycle, pricing.payment_cycle)as paymentCycle, " +
                "        rate.avg_rating as avgRating, \n" +
                    "        services.service_owner as serviceOwner, \n" +
                    "        case \n" +
                    "            when subscriptions.expired_time is null then 0 \n" +
                    "            when subscriptions.expired_time::::date < now()::::date and  pricing.has_renew = 1 and subscriptions.expired_time + cast(:paymentDateFailOn as integer) >= now()::::date  then 1 \n" +
                    "            else 0 \n" +
                    "        end as isRenew \n" +
                    "    from \n" +
                    "        {h-schema}subscriptions \n" +
                    "        LEFT join {h-schema}pricing on pricing.id = subscriptions.pricing_id \n" +
                    "    LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = subscriptions.pricing_multi_plan_id \n" +
                    "        join {h-schema}services on services.id = subscriptions.service_id and services.service_owner in (0, 1) \n" +
                "        LEFT JOIN {h-schema}mview_stats_services_rating rate ON services.id = rate.service_id \n" +
                    "        join {h-schema}users as company on company.id = services.user_id \n" +
                    "        join {h-schema}categories on categories.id = services.categories_id \n" +
                    "        left join {h-schema}file_attach as banner on banner.service_id = services.id and banner.object_type = 13 \n" +
                    "        left join {h-schema}file_attach as icon on icon.service_id = services.id and icon.object_type = 0 \n" +
                    "    where \n" +
                    "        subscriptions.deleted_flag = 1 and \n" +
                    "        subscriptions.confirm_status = 1 and \n" +
                    "        (subscriptions.status not in (0, 1, 3) or (subscriptions.status = 1 and subscriptions.started_at <= now())) and \n" +
                    "        (subscriptions.expired_time is null or  \n" +
                    "            (pricing.has_renew = 1 and subscriptions.expired_time + cast(:paymentDateFailOn as integer) >= now()::::date) or  \n" +
                    "            (pricing.has_renew = 0 and subscriptions.expired_time >= now()::::date) \n" +
                    "        ) and \n" +
                    "        subscriptions.user_id = :userId and \n" +
                    "        ('' = :inputValue " +
                    "           or (:isPricingName = true and  pricing.pricing_name ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and services.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  and \n" +
                    "        services.service_name ilike ('%' || :search || '%') and \n" +
                    "        (:categoryId = -1 or categories.id = :categoryId) and \n" +
                    "        (:devId = -1 or company.id = :devId) \n" +


                    "    UNION ALL \n" +
                    "    SELECT \n" +
                    "        service.id AS serviceId, \n" +
                    "        service.service_name AS serviceName,\n" +
                    "        p2.pricing_name AS pricingName, \n" +
                    "        fa.file_path AS icon, \n" +
                    "        banner.file_path AS banner, \n" +
                    "        fa.ext_link AS extLink, \n" +
                    "        service.url_service AS urlService, \n" +
                    "        CASE \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO' \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL' \n" +
                    "            ELSE 'YES' \n" +
                    "        END AS installed, \n" +
                    "        service.categories_id AS categoryId, \n" +
                    "        cate.name AS categoryName, \n" +
                    "        u.id AS companyId, \n" +
                    "        u.name AS companyName, \n" +
                    "        s.id AS subscriptionId, \n" +
                    "        s.created_at AS createdAt, \n" +
                    "        CASE \n" +
                    "            WHEN s.created_source_migration = 1 THEN 'DHSXKD'\n" +
                    "            WHEN s.created_source_migration = 0 THEN 'ONE_SME'\n" +
                    "            ELSE 'UNSET' \n" +
                    "        END AS createdSourceMigration,  \n" +
                    "        CASE \n" +
                    "            WHEN s.expired_time is null THEN 0 \n" +
                    "            WHEN s.expired_time - DATE(NOW()) < 0 THEN 1 \n" +
                    "            WHEN s.expired_time - DATE(NOW()) >= 0 THEN 0 \n" +
                    "        END AS isExpired, s.pricing_id as pricingId, \n" +
                    "        pmp.id as pricingMultiPlanId, \n" +
                    "COALESCE(pmp.circle_type, p2.cycle_type) as cycleType, " +
                    "COALESCE(pmp.payment_cycle, p2.payment_cycle)as paymentCycle, " +
                "        rate.avg_rating as avgRating, \n" +
                    "        service.service_owner as serviceOwner, \n" +
                    "        CASE \n" +
                    "            WHEN s.expired_time is null THEN 0 \n" +
                    "            WHEN s.expired_time < DATE(NOW()) AND p2.has_renew = 1 AND  s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0 THEN 1 \n" +
                    "        ELSE 0 END AS isRenew \n" +
                    "    FROM {h-schema}subscriptions s \n" +
                    "    JOIN ( \n" +
                    "        SELECT \n" +
                    "            s.id, \n" +
                    "            s.combo_plan_id \n" +
                    "        FROM {h-schema}subscriptions s \n" +
                    "        LEFT JOIN {h-schema}subscription_combo_addon sca ON s.id = sca.subscription_id \n" +
                    "        LEFT JOIN {h-schema}subscription_combo_addon_coupon scac ON sca.id = scac.subscription_combo_addon_id \n" +
                    "        LEFT JOIN {h-schema}coupon_combo_plan ccp3 ON scac.coupon_id = ccp3.coupon_id \n" +
                    "        WHERE s.combo_plan_id IS NOT NULL \n" +
                    "        UNION ALL \n" +
                    "        SELECT \n" +
                    "            s.id, \n" +
                    "            s.combo_plan_id \n" +
                    "        FROM {h-schema}subscriptions s \n" +
                    "        LEFT JOIN {h-schema}subscription_coupons sa ON s.id = sa.subscription_id \n" +
                    "        LEFT JOIN {h-schema}coupon_combo_plan ccp2 ON sa.coupon_id = ccp2.coupon_id \n" +
                    "        WHERE \n" +
                    "            s.combo_plan_id IS NOT NULL \n" +
                    "        UNION ALL \n" +
                    "        SELECT \n" +
                    "            s.id, \n" +
                    "            s.combo_plan_id \n" +
                    "        FROM {h-schema}subscriptions s \n" +
                    "        LEFT JOIN {h-schema}subscription_combo_coupon scc ON s.id = scc.subscription_id \n" +
                    "        LEFT JOIN {h-schema}coupon_combo_plan ccp ON scc.coupon_id = ccp.coupon_id \n" +
                    "        WHERE \n" +
                    "            s.combo_plan_id IS NOT NULL ) subGift ON subGift.id = s.id \n" +
                    "    JOIN {h-schema}combo_plan cp2 ON cp2.id = subGift.combo_plan_id AND cp2.deleted_flag = 1 \n" +
                    "    JOIN {h-schema}combo_pricing cp3 ON cp3.id_combo_plan = cp2.id \n" +
                    "    LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cp3.multi_pricing_plan_id \n" +
                "    LEFT JOIN {h-schema}pricing p2 ON p2.id = cp3.object_id AND cp3.object_type = 'PRICING' AND p2.deleted_flag = 1 \n" +
                    "    JOIN {h-schema}services service ON service.id = s.service_id \n" +
                "    LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "    JOIN {h-schema}categories cate ON cate.id = service.categories_id \n" +
                    "    JOIN {h-schema}users u ON u.id = service.user_id \n" +
                    "    LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13 \n" +
                    "    LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0 \n" +
                    "    JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id =  :userId \n" +
                    "    WHERE \n" +
                    "        s.confirm_status = 1 AND s.deleted_flag = 1 \n" +
                    "        AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) \n" +
                    "        AND s.user_id = :userId \n" +
                    "       AND ('' = :inputValue " +
                    "           or (:isPricingName = true and  p2.pricing_name ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  \n" +
                    "        AND service.service_name ILIKE ('%' || :search || '%') \n" +
                    "        AND (:categoryId = -1 OR cate.id = :categoryId) \n" +
                    "        AND (:devId = -1 OR u.id = :devId) AND ( s.expired_time is null OR (p2.has_renew = 1 AND s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0) OR (p2.has_renew = 0 AND s.expired_time - DATE(NOW()) >= 0))\n" +


                    "    UNION ALL \n" +
                    "    SELECT \n" +
                    "        service.id AS serviceId, \n" +
                    "        service.service_name AS serviceName, \n" +
                    "        a.\"name\" AS pricingName,\n" +
                    "        fa.file_path AS icon, \n" +
                    "        banner.file_path AS banner, \n" +
                    "        fa.ext_link AS extLink, \n" +
                    "        service.url_service AS urlService, \n" +
                    "        CASE \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO' \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL' \n" +
                    "            ELSE 'YES' \n" +
                    "        END AS installed, \n" +
                    "        service.categories_id AS categoryId, \n" +
                    "        cate.name AS categoryName, \n" +
                    "        u.id AS companyId, \n" +
                    "        u.name AS companyName, \n" +
                    "        s.id AS subscriptionId, \n" +
                    "        cate.created_at AS created_at , \n" +
                    "        CASE \n" +
                    "           WHEN s.created_source_migration = 1 THEN 'DHSXKD'\n" +
                    "           WHEN s.created_source_migration = 0 THEN 'ONE_SME'\n" +
                    "           ELSE 'UNSET' \n" +
                    "        END AS createdSourceMigration,  \n" +
                    "        0 AS isExpired, \n" +
                    "        s.pricing_id as pricingId, \n" +
                    "        pmp.id as pricingMultiPlanId, \n" +
                    "COALESCE(pmp.circle_type, a.type) as cycleType, " +
                    "COALESCE(pmp.payment_cycle, a.bonus_value)as paymentCycle, " +
                "        rate.avg_rating as avgRating, \n" +
                    "        service.service_owner as serviceOwner,\n" +
                    "        0 AS isRenew \n" +
                    "    FROM \n" +
                    "        {h-schema}subscriptions s \n" +
                    "    JOIN {h-schema}subscription_combo_addon sca ON s.id = sca.subscription_id \n" +
                    "    JOIN {h-schema}addons a ON sca.addon_id = a.id \n" +
                    "    LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id = a.id \n" +
                    "    JOIN {h-schema}services service ON service.id = a.service_id AND service.service_owner IS NOT NULL " +
                    "AND service.service_owner <> 2 AND service.service_owner <> 3 \n" +
                "    LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "    JOIN {h-schema}categories cate ON cate.id = service.categories_id \n" +
                    "    JOIN {h-schema}users u ON u.id = service.user_id \n" +
                    "    LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0 \n" +
                    "    LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13 \n" +
                    "    JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId \n" +
                    "    WHERE \n" +
                    "        s.confirm_status = 1 AND s.deleted_flag = 1 \n" +
                    "        AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) \n" +
                    "        AND s.user_id = :userId \n" +
                    "       AND ('' = :inputValue " +
                    "           or (:isPricingName = true and a.\"name\" ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  \n" +
                    "        AND service.service_name ILIKE ('%' || :search || '%') \n" +
                    "        AND (:categoryId = -1 OR cate.id = :categoryId) \n" +
                    "        AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0)\n" +


                    "    UNION ALL \n" +
                    "    SELECT \n" +
                    "        service.id AS serviceId, \n" +
                    "        service.service_name AS serviceName, \n" +
                    "        p.pricing_name AS pricingName,\n" +
                    "        fa.file_path AS icon, \n" +
                    "        banner.file_path AS banner, \n" +
                    "        fa.ext_link AS extLink, \n" +
                    "        service.url_service AS urlService, \n" +
                    "        CASE \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO' \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL' \n" +
                    "            ELSE 'YES' \n" +
                    "        END AS installed, \n" +
                    "        service.categories_id AS categoryId, \n" +
                    "        cate.name AS categoryName, \n" +
                    "        u.id AS companyId, \n" +
                    "        u.name AS companyName, \n" +
                    "        s.id AS subscriptionId, \n" +
                    "        s.created_at AS createdAt, \n" +
                    "        CASE \n" +
                    "            WHEN s.created_source_migration = 1 THEN 'DHSXKD'\n" +
                    "            WHEN s.created_source_migration = 0 THEN 'ONE_SME'\n" +
                    "            ELSE 'UNSET' \n" +
                    "        END AS createdSourceMigration,  \n" +
                    "        CASE \n" +
                    "            WHEN s.expired_time is null THEN 0 \n" +
                    "            WHEN s.expired_time - DATE(NOW()) < 0 THEN 1 \n" +
                    "             WHEN s.expired_time - DATE(NOW()) >= 0 THEN 0 \n" +
                    "        END AS isExpired, \n" +
                    "        s.pricing_id as pricingId, \n" +
                    "        pmp.id as pricingMultiPlanId, \n" +
                    "COALESCE(pmp.circle_type, p.cycle_type) as cycleType, " +
                    "COALESCE(pmp.payment_cycle, p.payment_cycle)as paymentCycle, " +
                "        rate.avg_rating as avgRating, \n" +
                    "        service.service_owner as serviceOwner, \n" +
                    "        CASE \n" +
                    "            WHEN s.expired_time is null THEN 0 \n" +
                    "            WHEN  s.expired_time < DATE(NOW()) AND p.has_renew = 1 AND  s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0 THEN 1   \n" +
                    "        ELSE 0 END AS isRenew \n" +
                    "    FROM \n" +
                    "        {h-schema}subscriptions s \n" +
                    "    JOIN {h-schema}combo_plan cpl ON s.combo_plan_id = cpl.id \n" +
                    "    JOIN {h-schema}combo_pricing cp on cp.id_combo_plan = cpl.id \n" +
                    "    LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cp.multi_pricing_plan_id \n" +
                "    LEFT JOIN {h-schema}pricing p ON  p.id = cp.object_id AND cp.object_type = 'PRICING'  \n" +
                    "    JOIN {h-schema}services service ON service.id = s.service_id AND service.service_owner IS NOT NULL " +
                    "AND service.service_owner <> 2 AND service.service_owner <> 3 \n" +
                "    LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "    JOIN {h-schema}categories cate ON cate.id = service.categories_id \n" +
                    "    JOIN {h-schema}users u ON u.id = service.user_id \n" +
                    "    LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0 \n" +
                    "    LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13 \n" +
                    "    JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId \n" +
                    "    LEFT JOIN {h-schema}subscription_combo_used_quantity scuq ON scuq.pricing_id = p.id AND scuq.subscription_id = s.id \n" +
                    "    WHERE \n" +
                    "        s.confirm_status = 1 AND s.deleted_flag = 1 \n" +
                    "        AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now())) \n" +
                    "        AND s.user_id = :userId \n" +
                    "       AND ('' = :inputValue " +
                    "           or (:isPricingName = true and  p.pricing_name ilike ('%' || :inputValue || '%')) " +
                    "           or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%')) " +
                    "        )  \n" +
                    "        AND service.service_name ILIKE ('%' || :search || '%') \n" +
                    "        AND (:categoryId = -1 OR cate.id = :categoryId) \n" +
                    "        AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR (p.has_renew = 1 AND s.expired_time + cast (:paymentDateFailOn as INTEGER ) - DATE(NOW()) >= 0) OR (p.has_renew = 0 AND s.expired_time - DATE(NOW()) >= 0)) \n" +
                    "        AND s.cancelled_time IS NULL ) rs";

    public static final String FIND_MY_SERVICE_SUBSCRIPTION_OS_ON =
            "SELECT rs.*  \n" +
                    "FROM (  \n" +
                    "   SELECT       \n" +
                    "      service.id AS serviceId,  \n" +
                    "      service.service_name AS serviceName,  \n" +
                    "      p.pricing_name AS pricingName,  \n" +
                    "      fa.file_path AS icon,  \n" +
                    "      banner.file_path AS banner,  \n" +
                    "      fa.ext_link AS extLink,  \n" +
                    "      service.url_service AS urlService,  \n" +
                    "      CASE  \n" +
                    "         WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO'  \n" +
                    "         WHEN (s.installed = 2) THEN 'FAIL'  \n" +
                    "         ELSE 'YES'  \n" +
                    "      END AS installed,  \n" +
                    "      service.categories_id AS categoryId,  \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      u.id AS companyId,  \n" +
                    "      u.name AS companyName,  \n" +
                    "      s.id AS subscriptionId,  \n" +
                    "      s.created_at AS createdAt,  \n" +
                    "      CASE  \n" +
                    "         WHEN s.created_source_migration = 1 THEN 'DHSXKD' \n" +
                    "         WHEN s.created_source_migration = 0 THEN 'ONE_SME' \n" +
                    "         ELSE 'UNSET'  \n" +
                    "      END AS createdSourceMigration,   \n" +
                    "      CASE  \n" +
                    "         WHEN s.expired_time is null THEN 0  \n" +
                    "         WHEN s.expired_time - DATE(NOW()) < 0 THEN 1  \n" +
                    "         WHEN s.expired_time - DATE(NOW()) >= 0 THEN 0  \n" +
                    "      END AS isExpired,  \n" +
                    "      s.pricing_id as pricingId,  \n" +
                    "      pmp.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp.circle_type, p.cycle_type) as cycleType,  \n" +
                    "      COALESCE(pmp.payment_cycle, p.payment_cycle)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      service.service_owner as serviceOwner,  \n" +
                    "      CASE  \n" +
                    "         WHEN s.expired_time is null \n" +
                    "            THEN 0  \n" +
                    "         WHEN s.expired_time < DATE(NOW()) AND p.has_renew = 1  \n" +
                    "               AND s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0  \n" +
                    "            THEN 1   \n" +
                    "      ELSE 0 END AS isRenew  \n" +
                    "   FROM {h-schema}subscriptions s  \n" +
                    "      JOIN (\n" +
                    "         SELECT s.id, raw_pricing_coupon.pricing_id as pricing_id  \n" +
                    "         FROM {h-schema}subscriptions s  \n" +
                    "         JOIN ( \n" +
                    "            select sa.subscription_id as id, COALESCE(pmp.pricing_id, cp.coupon_id) as pricing_id \n" +
                    "         FROM {h-schema}subscription_addons sa  \n" +
                    "            LEFT JOIN {h-schema}subscription_addon_coupon sac ON sa.id = sac.subscription_addon_id  \n" +
                    "            LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sac.coupon_id \n" +
                    "            LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id  \n" +
                    "            LEFT JOIN {h-schema}coupon_pricing cp ON sac.coupon_id = cp.coupon_id  \n" +
                    "         ) as raw_pricing_coupon on raw_pricing_coupon.id = s.id and raw_pricing_coupon.pricing_id is not null \n" +
                    "         \n" +
                    "         UNION ALL  \n" +
                    "         SELECT s.id, raw_pricing_coupon.pricing_id \n" +
                    "         FROM {h-schema}subscriptions s  \n" +
                    "         JOIN ( \n" +
                    "            select sa.subscription_id as id, COALESCE(pmp.pricing_id, cp.coupon_id) as pricing_id \n" +
                    "            FROM {h-schema}subscription_coupons sa \n" +
                    "               LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = sa.coupon_id  \n" +
                    "               LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id  \n" +
                    "               LEFT JOIN {h-schema}coupon_pricing cp ON sa.coupon_id = cp.coupon_id  \n" +
                    "         ) as raw_pricing_coupon on raw_pricing_coupon.id = s.id and raw_pricing_coupon.pricing_id is not null \n" +
                    "         \n" +
                    "         UNION ALL  \n" +
                    "            SELECT s.id, raw_pricing_coupon.pricing_id \n" +
                    "            FROM {h-schema}subscriptions s  \n" +
                    "            JOIN ( \n" +
                    "               SELECT spc.subscription_id as id, COALESCE(pmp.pricing_id, cp.coupon_id) as pricing_id \n" +
                    "               FROM {h-schema}subscription_pricing_coupon spc \n" +
                    "                  LEFT JOIN {h-schema}coupon_pricing_plan_apply cppa ON cppa.coupon_id = spc.coupon_id  \n" +
                    "                  LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cppa.pricing_multi_plan_id  \n" +
                    "                  LEFT JOIN {h-schema}coupon_pricing cp ON spc.coupon_id = cp.coupon_id \n" +
                    "            ) as raw_pricing_coupon on raw_pricing_coupon.id = s.id and raw_pricing_coupon.pricing_id is not null \n" +
                    "      ) subGift ON subGift.id = s.id  \n" +
                    "      LEFT JOIN {h-schema}pricing p ON p.id = s.pricing_id  \n" +
                    "      JOIN {h-schema}services service ON service.id = s.service_id AND s.service_id <> p.service_id  \n" +
                    "          AND service.service_owner IS NOT NULL AND service.service_owner in :lstServiceOwner  \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON service.id = cate.id" +
                    "      JOIN {h-schema}users u ON u.id = service.user_id  \n" +
                    "      LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13  \n" +
                    "      LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0  \n" +
                    "      JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId \n" +
                    "      LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = s.pricing_multi_plan_id  \n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "   WHERE  \n" +
                    "      s.confirm_status = 1  \n" +
                    "      AND s.deleted_flag = 1  \n" +
                    "      AND ('' = :inputValue  \n" +
                    "          or (:isPricingName = true and p.pricing_name ilike ('%' || :inputValue || '%'))  \n" +
                    "          or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )   \n" +
                    "      AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now()))  \n" +
                    "      AND s.user_id = :userId  \n" +
                    "      AND service.service_name ILIKE ('%' || :search || '%')  \n" +
                    "      AND (:devId = -1 OR u.id = :devId) AND (\n" +
                    "         s.expired_time is null OR \n" +
                    "         (p.has_renew = 1 AND s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0) OR \n" +
                    "         (p.has_renew = 0 AND s.expired_time - DATE(NOW()) >= 0))  \n" +
                    "       \n" +

                    "   UNION ALL  \n" +
                    "   SELECT          \n" +
                    "      service.id AS serviceId,  \n" +
                    "      service.service_name AS serviceName,  \n" +
                    "      a.\"name\" AS pricingName, \n" +
                    "      fa.file_path AS icon,  \n" +
                    "      banner.file_path AS banner,  \n" +
                    "      fa.ext_link AS extLink,  \n" +
                    "      service.url_service AS urlService,  \n" +
                    "      CASE  \n" +
                    "            WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO'  \n" +
                    "            WHEN (s.installed = 2) THEN 'FAIL'  \n" +
                    "            ELSE 'YES'  \n" +
                    "      END AS installed,  \n" +
                    "      service.categories_id AS categoryId,  \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      u.id AS companyId,  \n" +
                    "      u.name AS companyName,  \n" +
                    "      s.id AS subscriptionId,  \n" +
                    "      s.created_at AS createdAt,  \n" +
                    "      CASE  \n" +
                    "            WHEN s.created_source_migration = 1 THEN 'DHSXKD' \n" +
                    "            WHEN s.created_source_migration = 0 THEN 'ONE_SME' \n" +
                    "            ELSE 'UNSET'  \n" +
                    "      END AS createdSourceMigration,   \n" +
                    "      0 as isExpired,  \n" +
                    "      s.pricing_id as pricingId,  \n" +
                    "      pmp.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp.circle_type, a.type) as cycleType,  \n" +
                    "      COALESCE(pmp.payment_cycle, a.bonus_value)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      service.service_owner as serviceOwner,  \n" +
                    "      0 AS isRenew  \n" +
                    "   FROM {h-schema}subscriptions s  \n" +
                    "      JOIN {h-schema}subscription_addons sa ON s.id = sa.subscription_id  \n" +
                    "      JOIN {h-schema}addons a ON sa.addons_id = a.id  \n" +
                    "      LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id = a.id  \n" +
                    "      JOIN {h-schema}services service ON service.id = a.service_id AND service.service_owner IS NOT NULL AND service.service_owner in :lstServiceOwner  \n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON service.id = cate.id" +
                    "      JOIN {h-schema}users u ON u.id = service.user_id  \n" +
                    "      LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0  \n" +
                    "      LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13  \n" +
                    "      JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId \n" +
                    "   WHERE  \n" +
                    "      s.confirm_status = 1  \n" +
                    "      AND s.deleted_flag = 1  \n" +
                    "      AND ('' = :inputValue  \n" +
                    "         or (:isPricingName = true and a.\"name\" ilike ('%' || :inputValue || '%'))  \n" +
                    "         or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )   \n" +
                    "      AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now()))  \n" +
                    "      AND s.user_id = :userId  \n" +
                    "      AND service.service_name ILIKE ('%' || :search || '%')  \n" +
                    "      AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0) \n" +
                    "      AND pmp.id IS null \n" +
                    "             \n" +

                    "   UNION ALL  \n" +
                    "   SELECT  \n" +
                    "      service.id AS serviceId,  \n" +
                    "      service.service_name AS serviceName,  \n" +
                    "      a.\"name\" AS pricingName,  \n" +
                    "      fa.file_path AS icon,  \n" +
                    "      banner.file_path AS banner,  \n" +
                    "      fa.ext_link AS extLink,  \n" +
                    "      service.url_service AS urlService,  \n" +
                    "      CASE  \n" +
                    "         WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO'  \n" +
                    "         WHEN (s.installed = 2) THEN 'FAIL'  \n" +
                    "         ELSE 'YES'  \n" +
                    "      END AS installed,  \n" +
                    "      service.categories_id AS categoryId,  \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      u.id AS companyId,  \n" +
                    "      u.name AS companyName,  \n" +
                    "      s.id AS subscriptionId,  \n" +
                    "      s.created_at AS createdAt,  \n" +
                    "      CASE  \n" +
                    "         WHEN s.created_source_migration = 1 THEN 'DHSXKD' \n" +
                    "         WHEN s.created_source_migration = 0 THEN 'ONE_SME' \n" +
                    "         ELSE 'UNSET'  \n" +
                    "      END AS createdSourceMigration,   \n" +
                    "      0 AS isExpired,  \n" +
                    "      s.pricing_id as pricingId,  \n" +
                    "      pmp2.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp2.circle_type, a.type) as cycleType,  \n" +
                    "      COALESCE(pmp2.payment_cycle, a.bonus_value)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      service.service_owner as serviceOwner,  \n" +
                    "      0 AS isRenew  \n" +
                    "   FROM {h-schema}subscriptions s  \n" +
                    "      JOIN {h-schema}subscription_addons sa ON sa.subscription_id = s.id AND sa.addons_id NOTNULL  \n" +
                    "      JOIN {h-schema}pricing_multi_plan pmp2 ON sa.addons_id = pmp2.addon_id AND sa.pricing_multi_plan_id = pmp2.id AND pmp2.deleted_flag = 1  \n" +
                    "      JOIN {h-schema}addons a ON a.id = pmp2.addon_id AND a.deleted_flag = 1 AND a.bonus_type = 1  \n" +
                    "      JOIN {h-schema}services service ON service.id = a.service_id AND service.service_owner IS NOT NULL AND service.service_owner in :lstServiceOwner\n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON service.id = cate.id" +
                    "      JOIN {h-schema}users u ON u.id = service.user_id  \n" +
                    "      LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0  \n" +
                    "      LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13  \n" +
                    "      JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId  \n" +
                    "   WHERE  \n" +
                    "      s.confirm_status = 1 AND s.deleted_flag = 1  \n" +
                    "      AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now()))  \n" +
                    "      AND s.user_id = :userId  \n" +
                    "      AND ('' = :inputValue  \n" +
                    "         or (:isPricingName = true and a.\"name\" ilike ('%' || :inputValue || '%'))  \n" +
                    "         or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )   \n" +
                    "      AND service.service_name ILIKE ('%' || :search || '%')  \n" +
                    "      AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0) \n" +
                    "\n" +

                    "   UNION ALL  \n" +
                    "   select  \n" +
                    "      services.id as serviceId,  \n" +
                    "      services.service_name as serviceName,  \n" +
                    "      pricing.pricing_name as pricingName,  \n" +
                    "      icon.file_path as icon,      \n" +
                    "      banner.file_path as banner,  \n" +
                    "      icon.ext_link as extLink,  \n" +
                    "      services.url_service as urlService,  \n" +
                    "      case  \n" +
                    "         when subscriptions.installed is null or subscriptions.installed = 0 then 'NO'  \n" +
                    "         when subscriptions.installed = 2 then 'FAIL'  \n" +
                    "         else 'YES'  \n" +
                    "      end as installed,  \n" +
                    "      services.categories_id AS categoryId,      \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      company.id as companyId,  \n" +
                    "      company.name as companyName,  \n" +
                    "      subscriptions.id as subscriptionId,  \n" +
                    "      subscriptions.created_at AS createdAt,  \n" +
                    "      case  \n" +
                    "         when subscriptions.created_source_migration = 1 then 'DHSXKD'  \n" +
                    "         when subscriptions.created_source_migration = 0 then 'ONE_SME'  \n" +
                    "         else 'UNSET'  \n" +
                    "      end as createdSourceMigration,  \n" +
                    "      case  \n" +
                    "         when subscriptions.expired_time is null then 0  \n" +
                    "         when subscriptions.expired_time::::date < now()::::date then 1  \n" +
                    "         when subscriptions.expired_time::::date >= now()::::date then 0  \n" +
                    "      end as isExpired,  \n" +
                    "      subscriptions.pricing_id as pricingId,  \n" +
                    "      pmp.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp.circle_type, pricing.cycle_type) as cycleType,  \n" +
                    "      COALESCE(pmp.payment_cycle, pricing.payment_cycle)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      services.service_owner as serviceOwner,  \n" +
                    "      case  \n" +
                    "         when subscriptions.expired_time is null then 0  \n" +
                    "         when subscriptions.expired_time::::date < now()::::date and  pricing.has_renew = 1 and \n" +
                    "            subscriptions.expired_time + cast(:paymentDateFailOn as integer) >= now()::::date  \n" +
                    "            then 1  \n" +
                    "      else 0  \n" +
                    "      end as isRenew  \n" +
                    "   from {h-schema}subscriptions  \n" +
                    "      LEFT join {h-schema}pricing on pricing.id = subscriptions.pricing_id  \n" +
                    "      LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = subscriptions.pricing_multi_plan_id  \n" +
                    "      join {h-schema}services on services.id = subscriptions.service_id AND services.service_owner IS NOT NULL AND services.service_owner in :lstServiceOwner  \n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON services.id = rate.service_id \n" +
                    "      join {h-schema}users as company on company.id = services.user_id  \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON services.id = cate.id" +
                    "      left join {h-schema}file_attach as banner on banner.service_id = services.id and banner.object_type = 13  \n" +
                    "      left join {h-schema}file_attach as icon on icon.service_id = services.id and icon.object_type = 0  \n" +
                    "   where  \n" +
                    "      subscriptions.deleted_flag = 1 and  \n" +
                    "      subscriptions.confirm_status = 1 and  \n" +
                    "      (subscriptions.status not in (0, 1, 3) or (subscriptions.status = 1 and subscriptions.started_at <= now())) and  \n" +
                    "      (subscriptions.expired_time is null or   \n" +
                    "      (pricing.has_renew = 1 and subscriptions.expired_time + cast(:paymentDateFailOn as integer) >= now()::::date) or   \n" +
                    "         (pricing.has_renew = 0 and subscriptions.expired_time >= now()::::date)  \n" +
                    "      ) and  \n" +
                    "      subscriptions.user_id = :userId and  \n" +
                    "      ('' = :inputValue  \n" +
                    "         or (:isPricingName = true and  pricing.pricing_name ilike ('%' || :inputValue || '%'))  \n" +
                    "         or (:isServiceName = true and services.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )  and  \n" +
                    "      services.service_name ilike ('%' || :search || '%') and  \n" +
                    "      (:devId = -1 or company.id = :devId)  \n" +
                    "\n" +

                    "   UNION ALL  \n" +
                    "   SELECT  \n" +
                    "      service.id AS serviceId,  \n" +
                    "      service.service_name AS serviceName, \n" +
                    "      p2.pricing_name AS pricingName,  \n" +
                    "      fa.file_path AS icon,  \n" +
                    "      banner.file_path AS banner,  \n" +
                    "      fa.ext_link AS extLink,  \n" +
                    "      service.url_service AS urlService,  \n" +
                    "      CASE  \n" +
                    "         WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO'  \n" +
                    "         WHEN (s.installed = 2) THEN 'FAIL'  \n" +
                    "         ELSE 'YES'  \n" +
                    "      END AS installed,  \n" +
                    "      service.categories_id AS categoryId,  \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      u.id AS companyId,  \n" +
                    "      u.name AS companyName,  \n" +
                    "      s.id AS subscriptionId,  \n" +
                    "      s.created_at AS createdAt,  \n" +
                    "      CASE  \n" +
                    "         WHEN s.created_source_migration = 1 THEN 'DHSXKD' \n" +
                    "         WHEN s.created_source_migration = 0 THEN 'ONE_SME' \n" +
                    "         ELSE 'UNSET'  \n" +
                    "      END AS createdSourceMigration,   \n" +
                    "      CASE  \n" +
                    "         WHEN s.expired_time is null THEN 0  \n" +
                    "         WHEN s.expired_time - DATE(NOW()) < 0 THEN 1  \n" +
                    "         WHEN s.expired_time - DATE(NOW()) >= 0 THEN 0  \n" +
                    "      END AS isExpired, s.pricing_id as pricingId,  \n" +
                    "      pmp.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp.circle_type, p2.cycle_type) as cycleType,  \n" +
                    "      COALESCE(pmp.payment_cycle, p2.payment_cycle)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      service.service_owner as serviceOwner,  \n" +
                    "      CASE  \n" +
                    "         WHEN s.expired_time is null THEN 0  \n" +
                    "         WHEN s.expired_time < DATE(NOW()) AND p2.has_renew = 1 AND  \n" +
                    "            s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0 \n" +
                    "            THEN 1  \n" +
                    "      ELSE 0 END AS isRenew  \n" +
                    "   FROM {h-schema}subscriptions s  \n" +
                    "      JOIN (  \n" +
                    "         SELECT s.id, s.combo_plan_id  \n" +
                    "         FROM {h-schema}subscriptions s  \n" +
                    "            LEFT JOIN {h-schema}subscription_combo_addon sca ON s.id = sca.subscription_id  \n" +
                    "            LEFT JOIN {h-schema}subscription_combo_addon_coupon scac ON sca.id = scac.subscription_combo_addon_id  \n" +
                    "            LEFT JOIN {h-schema}coupon_combo_plan ccp3 ON scac.coupon_id = ccp3.coupon_id  \n" +
                    "         WHERE s.combo_plan_id IS NOT NULL  \n" +
                    "         \n" +
                    "         UNION ALL  \n" +
                    "         SELECT s.id, s.combo_plan_id  \n" +
                    "         FROM {h-schema}subscriptions s  \n" +
                    "            LEFT JOIN {h-schema}subscription_coupons sa ON s.id = sa.subscription_id  \n" +
                    "            LEFT JOIN {h-schema}coupon_combo_plan ccp2 ON sa.coupon_id = ccp2.coupon_id  \n" +
                    "         WHERE s.combo_plan_id IS NOT NULL\n" +
                    "          \n" +
                    "         UNION ALL  \n" +
                    "         SELECT s.id, s.combo_plan_id  \n" +
                    "         FROM {h-schema}subscriptions s  \n" +
                    "            LEFT JOIN {h-schema}subscription_combo_coupon scc ON s.id = scc.subscription_id  \n" +
                    "            LEFT JOIN {h-schema}coupon_combo_plan ccp ON scc.coupon_id = ccp.coupon_id  \n" +
                    "         WHERE s.combo_plan_id IS NOT NULL \n" +
                    "      ) subGift ON subGift.id = s.id  \n" +
                    "      JOIN {h-schema}combo_plan cp2 ON cp2.id = subGift.combo_plan_id AND cp2.deleted_flag = 1  \n" +
                    "      JOIN {h-schema}combo_pricing cp3 ON cp3.id_combo_plan = cp2.id  \n" +
                    "      LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cp3.multi_pricing_plan_id  \n" +
                "      LEFT JOIN {h-schema}pricing p2 ON p2.id = cp3.object_id AND cp3.object_type = 'PRICING' AND p2.deleted_flag = 1  \n" +
                    "      JOIN {h-schema}services service ON service.id = s.service_id AND service.service_owner IS NOT NULL AND service.service_owner in :lstServiceOwner \n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON service.id = cate.id" +
                    "      JOIN {h-schema}users u ON u.id = service.user_id  \n" +
                    "      LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13  \n" +
                    "      LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0  \n" +
                    "      JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id =  :userId  \n" +
                    "   WHERE  \n" +
                    "      s.confirm_status = 1 AND s.deleted_flag = 1  \n" +
                    "      AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now()))  \n" +
                    "      AND s.user_id = :userId  \n" +
                    "      AND ('' = :inputValue  \n" +
                    "         or (:isPricingName = true and  p2.pricing_name ilike ('%' || :inputValue || '%'))  \n" +
                    "         or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )   \n" +
                    "      AND service.service_name ILIKE ('%' || :search || '%')  \n" +
                    "      AND (:categoryId = -1 OR cate.id = :categoryId)  \n" +
                    "      AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR \n" +
                    "         (p2.has_renew = 1 AND s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0) OR \n" +
                    "         (p2.has_renew = 0 AND s.expired_time - DATE(NOW()) >= 0)) \n" +
                    "\n" +

                    "   UNION ALL  \n" +
                    "   SELECT  \n" +
                    "      service.id AS serviceId,  \n" +
                    "      service.service_name AS serviceName,  \n" +
                    "      a.\"name\" AS pricingName, \n" +
                    "      fa.file_path AS icon,  \n" +
                    "      banner.file_path AS banner,  \n" +
                    "      fa.ext_link AS extLink,  \n" +
                    "      service.url_service AS urlService,  \n" +
                    "      CASE  \n" +
                    "         WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO'  \n" +
                    "         WHEN (s.installed = 2) THEN 'FAIL'  \n" +
                    "         ELSE 'YES'  \n" +
                    "      END AS installed,  \n" +
                    "      service.categories_id AS categoryId,  \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      u.id AS companyId,  \n" +
                    "      u.name AS companyName,  \n" +
                    "      s.id AS subscriptionId,  \n" +
                    "      s.created_at AS created_at ,  \n" +
                    "      CASE  \n" +
                    "       WHEN s.created_source_migration = 1 THEN 'DHSXKD' \n" +
                    "       WHEN s.created_source_migration = 0 THEN 'ONE_SME' \n" +
                    "       ELSE 'UNSET'  \n" +
                    "      END AS createdSourceMigration,   \n" +
                    "      0 AS isExpired,  \n" +
                    "      s.pricing_id as pricingId,  \n" +
                    "      pmp.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp.circle_type, a.type) as cycleType,  \n" +
                    "      COALESCE(pmp.payment_cycle, a.bonus_value)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      service.service_owner as serviceOwner, \n" +
                    "      0 AS isRenew  \n" +
                    "   FROM {h-schema}subscriptions s  \n" +
                    "      JOIN {h-schema}subscription_combo_addon sca ON s.id = sca.subscription_id  \n" +
                    "      JOIN {h-schema}addons a ON sca.addon_id = a.id  \n" +
                    "      LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.addon_id = a.id  \n" +
                    "      JOIN {h-schema}services service ON service.id = a.service_id AND service.service_owner IS NOT NULL AND service.service_owner in :lstServiceOwner  \n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON service.id = cate.id" +
                    "      JOIN {h-schema}users u ON u.id = service.user_id  \n" +
                    "      LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0  \n" +
                    "      LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13  \n" +
                    "      JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId  \n" +
                    "   WHERE  \n" +
                    "      s.confirm_status = 1 AND s.deleted_flag = 1  \n" +
                    "      AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now()))  \n" +
                    "      AND s.user_id = :userId  \n" +
                    "      AND ('' = :inputValue  \n" +
                    "         or (:isPricingName = true and a.\"name\" ilike ('%' || :inputValue || '%'))  \n" +
                    "         or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )   \n" +
                    "      AND service.service_name ILIKE ('%' || :search || '%')  \n" +
                    "      AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0) \n" +
                    "\n" +

                    "   UNION ALL  \n" +
                    "   SELECT  \n" +
                    "      service.id AS serviceId,  \n" +
                    "      service.service_name AS serviceName,  \n" +
                    "      p.pricing_name AS pricingName, \n" +
                    "      fa.file_path AS icon,  \n" +
                    "      banner.file_path AS banner,  \n" +
                    "      fa.ext_link AS extLink,  \n" +
                    "      service.url_service AS urlService,  \n" +
                    "      CASE  \n" +
                    "         WHEN (s.installed IS NULL OR s.installed = 0) THEN 'NO'  \n" +
                    "         WHEN (s.installed = 2) THEN 'FAIL'  \n" +
                    "      ELSE 'YES'  \n" +
                    "      END AS installed,  \n" +
                    "      service.categories_id AS categoryId,  \n" +
                    "      cast(cate.name as varchar)  AS categoryName,  \n" +
                    "      u.id AS companyId,  \n" +
                    "      u.name AS companyName,  \n" +
                    "      s.id AS subscriptionId,  \n" +
                    "      s.created_at AS createdAt,  \n" +
                    "      CASE  \n" +
                    "      WHEN s.created_source_migration = 1 THEN 'DHSXKD' \n" +
                    "      WHEN s.created_source_migration = 0 THEN 'ONE_SME' \n" +
                    "      ELSE 'UNSET'  \n" +
                    "      END AS createdSourceMigration,   \n" +
                    "      CASE  \n" +
                    "      WHEN s.expired_time is null THEN 0  \n" +
                    "      WHEN s.expired_time - DATE(NOW()) < 0 THEN 1  \n" +
                    "      WHEN s.expired_time - DATE(NOW()) >= 0 THEN 0  \n" +
                    "      END AS isExpired,  \n" +
                    "      s.pricing_id as pricingId,  \n" +
                    "      pmp.id as pricingMultiPlanId,  \n" +
                    "      COALESCE(pmp.circle_type, p.cycle_type) as cycleType,  \n" +
                    "      COALESCE(pmp.payment_cycle, p.payment_cycle)as paymentCycle,  \n" +
                "      rate.avg_rating as avgRating, \n" +
                    "      service.service_owner as serviceOwner,  \n" +
                    "      CASE  \n" +
                    "         WHEN s.expired_time is null THEN 0  \n" +
                    "         WHEN s.expired_time < DATE(NOW()) AND p.has_renew = 1 \n" +
                    "            AND  s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0 \n" +
                    "            THEN 1    \n" +
                    "      ELSE 0 END AS isRenew  \n" +
                    "   FROM {h-schema}subscriptions s  \n" +
                    "      JOIN {h-schema}combo_plan cpl ON s.combo_plan_id = cpl.id  \n" +
                    "      JOIN {h-schema}combo_pricing cp on cp.id_combo_plan = cpl.id  \n" +
                    "      LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = cp.multi_pricing_plan_id  \n" +
                "      LEFT JOIN {h-schema}pricing p ON  p.id = cp.object_id AND cp.object_type = 'PRICING' \n" +
                    "      JOIN {h-schema}services service ON service.id = s.service_id AND service.service_owner IS NOT NULL AND service.service_owner in :lstServiceOwner\n" +
                "      LEFT JOIN {h-schema}mview_stats_services_rating rate ON service.id = rate.service_id \n" +
                    "       JOIN (       -- lấy những service thuộc danh mục đang filter\n" +
                    "			SELECT  \n" +
                    "			services.id as id,  \n" +
                    "			array_agg(categories.name) as name  \n" +
                    "			FROM {h-schema}services  \n" +
                    "				JOIN {h-schema}mapping_services_categories mServiceCategory ON mServiceCategory.service_id = services.id  \n" +
                    "				LEFT JOIN {h-schema}categories ON categories.id = mServiceCategory.categories_id  \n" +
                    "			where services.service_type_application is null  \n" +
                    "				and (:categoryId = -1 OR mServiceCategory.categories_id = :categoryId)  \n" +
                    "				GROUP BY services.id  \n" +
                    "			) cate ON service.id = cate.id" +
                    "      JOIN {h-schema}users u ON u.id = service.user_id  \n" +
                    "      LEFT JOIN {h-schema}file_attach fa ON service.id = fa.service_id AND fa.object_type = 0  \n" +
                    "      LEFT JOIN {h-schema}file_attach banner ON service.id = banner.service_id AND banner.object_type = 13  \n" +
                    "      JOIN {h-schema}user_subscription us ON us.subscription_id = s.id AND us.user_id = :userId  \n" +
                    "      LEFT JOIN {h-schema}subscription_combo_used_quantity scuq ON scuq.pricing_id = p.id AND scuq.subscription_id = s.id  \n" +
                    "   WHERE  \n" +
                    "      s.confirm_status = 1 AND s.deleted_flag = 1  \n" +
                    "      AND (s.status NOT IN (0, 1, 3) OR (s.status = 1 AND s.started_at <= now()))  \n" +
                    "      AND s.user_id = :userId  \n" +
                    "      AND ('' = :inputValue  \n" +
                    "         or (:isPricingName = true and  p.pricing_name ilike ('%' || :inputValue || '%'))  \n" +
                    "         or (:isServiceName = true and service.service_name ilike ('%' || :inputValue || '%'))  \n" +
                    "      )   \n" +
                    "      AND service.service_name ILIKE ('%' || :search || '%')  \n" +
                    "      AND (:devId = -1 OR u.id = :devId) AND (s.expired_time is null OR \n" +
                    "         (p.has_renew = 1 AND s.expired_time + cast(:paymentDateFailOn as INTEGER) - DATE(NOW()) >= 0) OR \n" +
                    "         (p.has_renew = 0 AND s.expired_time - DATE(NOW()) >= 0))  \n" +
                    "      AND s.cancelled_time IS NULL \n" +
                    ") rs";

    public static final String COUNT_MY_SERVICE_SUBSCRIPTION_NEW =
            "select count(*) from (" + FIND_MY_SERVICE_SUBSCRIPTION_NEW + ") as myService";

    public static final String COUNT_MY_SERVICE_SUBSCRIPTION_OS_ON =
            "select count(*) from (" + FIND_MY_SERVICE_SUBSCRIPTION_OS_ON + ") as myService";

    public static final String GET_SUB_REGISTER_INFO =
        "select \n" +
            "       sub.id as subId,\n" +
            "       coalesce(pricing.pricing_name, combo_plan.combo_name, '') as pricingName, \n" +
            "       coalesce(services.service_name, combo.combo_name, '') as serviceName, \n" +
            "       case  \n" +
            "            when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name) \n" +
            "            else coalesce(users.name, '')\n" +
            "       end as enterpriseName \n" +
            "from {h-schema}subscriptions as sub\n" +
            "      left join {h-schema}pricing on pricing.id = sub.pricing_id\n" +
            "      left join {h-schema}services on services.id = pricing.service_id\n" +
            "      left join {h-schema}combo_plan on sub.combo_plan_id = combo_plan.id \n" +
            "      left join {h-schema}combo on combo_plan.combo_id = combo.id\n" +
            "      left join {h-schema}users on sub.user_id = users.id \n" +
            "where sub.id = :subId";

    public static final String COUNT_NUM_SUB_BY_COMBOPLAN_ID =
        "select count(id) from {h-schema}subscriptions where combo_plan_id = :comboPlanId and confirm_status = 1 and deleted_flag = 1";

    public static final String FIND_SERVICE_OWNER_INFO =
        "select \n" +
            "    services.service_owner_partner as serviceOwnerPartner, \n" +
            "    coalesce(services.service_owner, combo.combo_owner) as serviceOwner \n" +
            "from \n" +
            "    {h-schema}subscriptions \n" +
            "    left join {h-schema}pricing on pricing.id = subscriptions.pricing_id \n" +
            "    left join {h-schema}services on services.id = pricing.service_id \n" +
            "    left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id \n" +
            "    left join {h-schema}combo on combo.id = combo_plan.combo_id \n" +
            "where \n" +
            "    subscriptions.id = :subscriptionId ";

    public static final String GET_TRANSACTION_INFO_BY_SUBSCRIPTION_ID =
        "select \n" +
            "    users.id as smeId, \n" +
            "    users.name as smeName, \n" +
            "    users.email as smeEmail, \n" +
            "    users.tin as smeTaxCode, \n" +
            "    users.phone_number as smePhone, \n" +
            "    coalesce(services.service_code, combo.combo_code) as serviceCode, \n" +
            "    coalesce(services.user_id, combo.user_id) as providerId \n" +
            "from \n" +
            "    {h-schema}users \n" +
            "    join {h-schema}subscriptions on subscriptions.user_id = users.id and subscriptions.id = :subscriptionId \n" +
            "    left join {h-schema}services on services.id = subscriptions.service_id and subscriptions.pricing_id is not null \n" +
            "    left join {h-schema}combo on combo.id = subscriptions.service_id and subscriptions.pricing_id is null \n" +
            "where \n" +
            "    users.deleted_flag = 1 and \n" +
            "    (coalesce(customer_type, 'KHDN') = 'KHDN' or customer_type = 'HKD') and \n" +
            "    subscriptions.deleted_flag = 1 \n" +
            "limit 1 ";

    public static final String GET_PRICING_TYPE =
        "select \n" +
            "    coalesce(pricing.pricing_type, combo_plan.combo_plan_type) \n" +
            "from \n" +
            "    {h-schema}subscriptions \n" +
            "    left join {h-schema}pricing on pricing.id = subscriptions.pricing_id \n" +
            "    left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id \n" +
            "where \n" +
            "    subscriptions.id = :subscriptionId \n" +
            "limit 1";

    public static final String FIND_PROVIDER_ID =
        "select\n" +
            "    coalesce(services.user_id, combo.user_id) as providerId \n" +
            "from\n" +
            "    {h-schema}subscriptions \n" +
            "    left join {h-schema}pricing on pricing.id = subscriptions.pricing_id \n" +
            "    left join {h-schema}services on services.id = pricing.service_id \n" +
            "    left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id \n" +
            "    left join {h-schema}combo on combo.id = combo_plan.combo_id \n" +
            "where \n" +
            "    subscriptions.id = :subscriptionId \n" +
            "limit 1 ";

    public static final String GET_CATEGORIES_MIGRATION_ID_BY_SUB_ID =
        "select \n" +
            "    coalesce(categories.categories_id_migration, -1)\n" +
            "from\n" +
            "    {h-schema}subscriptions \n" +
            "    join {h-schema}services on subscriptions.service_id = services.id\n" +
            "    join {h-schema}categories on services.categories_id = categories.id\n" +
            "where \n" +
            "    subscriptions.id = :subId\n";

    public static final String GET_CATEGORIES_MIGRATION_ID_BY_BILLING_CODE =
        "select \n" +
            "    coalesce(categories.categories_id_migration, -1)\n" +
            "from\n" +
            "    {h-schema}billings\n" +
            "    join {h-schema}subscriptions on billings.subscriptions_id = subscriptions.id\n" +
            "    join {h-schema}services on subscriptions.service_id = services.id\n" +
            "    join {h-schema}categories on services.categories_id = categories.id\n" +
            "where \n" +
            "    COALESCE(billings.cart_code, billings.billing_code) = :billingCode\n" +
            "order by \n" +
            "    billings.created_at desc\n" +
            "limit 1\n";

    public static final String GET_SUB_CUSTOMER_EMAIL_BY_BILLING_CODE =
        "select\n" +
            "    users.email\n" +
            "from \n" +
            "    {h-schema}users\n" +
            "    join {h-schema}subscriptions on subscriptions.user_id = users.id\n" +
            "    join {h-schema}billings on billings.subscriptions_id = subscriptions.id\n" +
            "where\n" +
            "    billings.billing_code = :billingCode\n";

    public static final String CHECK_EXISTS_APP_BY_STATUS =
            "SELECT EXISTS (\n" +
                    "   SELECT id \n" +
                    "   FROM {h-schema}subscriptions\n" +
                    "   Where user_id = :userId\n" +
                    "      AND service_id = :serviceId\n" +
                    "      AND (status in (0, 3, 4, 5) OR awaiting_cancel = 0)\n" +
                    ")";


    public static final String IS_ORDERS =
        "SELECT EXISTS (\n" +
            "   SELECT id \n" +
            "   FROM {h-schema}subscriptions s\n" +
            "   WHERE s.id = :subscriptionId \n" +
            "   AND ( \n" +
            "   EXISTS ( \n" +
            "   SELECT 1 \n" +
            "   FROM {h-schema}services srv  \n" +
            "   WHERE srv.id = s.service_id \n" +
            "   AND srv.product_type = 1 \n" +
            "   AND srv.service_owner IN (4, 5) \n" +
            "  ) \n" +
        " OR \n" +
        " EXISTS ( \n" +
        " SELECT 1 \n" +
            "  FROM {h-schema}combo_plan cp \n" +
            "  JOIN {h-schema}combo c ON c.id = cp.combo_id \n" +
            "  WHERE cp.id = s.combo_plan_id \n" +
            "  AND c.combo_owner IN (6, 7, 8) \n" +
            "        ) \n" +
            "       ) \n" +
            "     )";

    public static final String GET_SOLD_NUMBER_BY_SERVICE_ID =
        "select \n" +
            "    sum(quantity_variant) \n" +
            "from {h-schema}subscriptions s  \n" +
            "left join {h-schema}billings b on b.subscriptions_id = s.id \n" +
            "where \n" +
            " s.deleted_flag = 1 and s.confirm_status = 1 and b.status = 2 and b.payment_date is not null and s.service_id = :serviceId \n" +
            "group by service_id ";

    public static final String GET_CURRENT_PRICING_QUANTITY_BY_SUBSCRIPTION =
        "select coalesce (\n" +
            "    (\n" +
            "        select\n" +
            "            quantity \n" +
            "        from\n" +
            "            {h-schema}change_subscription \n" +
            "        where\n" +
            "            subscription_id = :subId and status = 1 and deleted_flag = 1 \n" +
            "        order by\n" +
            "            id desc \n" +
            "        limit 1\n" +
            "    ), \n" +
            "    (\n" +
            "        select \n" +
            "            quantity \n" +
            "        from \n" +
            "            {h-schema}subscriptions s \n" +
            "        where\n" +
            "            s.id = :subId\n" +
            "        order by s.id \n" +
            "        limit 1\n" +
            "    )\n" +
            ")";

    public static final String GET_CURRENT_ADDON_QUANTITY_BY_SUBSCRIPTION =
            "select coalesce (\n" +
                    "    (\n" +
                    "        select quantity \n" +
                    "        from {h-schema}change_subscription_addons \n" +
                    "        where subscription_id = :subId and addons_id = :addonId \n" +
                    "        order by id desc limit 1\n" +
                    "    ), \n" +
                    "    (\n" +
                    "        select quantity \n" +
                    "        from {h-schema}subscription_addons \n" +
                    "        where subscription_id = :subId and addons_id = :addonId \n" +
                    "        order by id limit 1 \n" +
                    "    )\n" +
                    ")";

    public static final String GET_LIST_DETAIL_SUB_BY_ID_IN =
        "select\n" +
        "    subscriptions.id as subscriptionId \n, " +
        "    sum(coalesce(bill_item.amount_pre_tax, 0)) as amountPreTax \n" +
        "from {h-schema}subscriptions \n" +
        "    left join {h-schema}billings on subscriptions.id = billings.subscriptions_id\n" +
        "    left join {h-schema}bill_item on bill_item.billing_id = billings.id \n" +
        "where \n" +
        "    subscriptions.id in (:lstSubId)\n" +
        "    GROUP BY subscriptions.id \n";

    public static final String GET_TOTAL_AMOUNT_PRE_TAX_BY_SUB_ID =
        "select\n" +
            "    sum(coalesce(bill_item.amount_pre_tax, 0)) as amountPreTax \n" +
            "from {h-schema}subscriptions \n" +
            "    left join {h-schema}billings on subscriptions.id = billings.subscriptions_id\n" +
            "    left join {h-schema}bill_item on bill_item.billing_id = billings.id \n" +
            "where \n" +
            "    subscriptions.id = :subscriptionId \n" +
            "    GROUP BY subscriptions.id \n";

    public static final String UPDATE_SUBSCRIPTION_CREATED_AT =
        "update {h-schema}subscriptions set created_at = :createdAt, modified_at = :createdAt where id = :subscriptionId";

    public static final String UPDATE_SUBSCRIPTION_MODIFIED_AT =
            "update {h-schema}subscriptions set modified_at = :modifiedAt where id = :subscriptionId";

    public static final String GET_LIST_SUB_CODE_DHSXKD =
        "select * from {h-schema}subscriptions where dhsxkd_sub_code in (:lstSubCodeDHSXKD)";

    public static final String UPDATE_MERGE_SUBSCRIPTION_CART_CODE =
        "update {h-schema}subscriptions set cart_code = :comboCodeDHSXKD where dhsxkd_sub_code in (:lstSubCodeDHSXKD)";

    public static final String FIND_SUBSCRIPTION_BY_USER_ID_AND_SUB_CODE =
        "select * from {h-schema}subscriptions where user_id = :userId and dhsxkd_sub_code = :subCode";

    public static final String GET_SUBSCRIPTION_SETUP_ADDRESS =
        "SELECT CONCAT_WS(', ', street.name, ward.name, district.name, province.name) \n" +
            "FROM \n" +
            "    {h-schema}province \n" +
            "    LEFT JOIN {h-schema}district ON district.province_id = province.id \n" +
            "    LEFT JOIN {h-schema}ward ON ward.district_id = district.id AND ward.province_id = province.id \n" +
            "    LEFT JOIN {h-schema}street ON street.ward_id = ward.id AND street.district_id = district.id and street.province_id = province.id \n" +
            "WHERE province.id = :provinceId AND \n" +
            "    (-1 = :districtId OR district.id = :districtId) AND \n" +
            "    (-1 = :wardId OR ward.id = :wardId) AND \n" +
            "    (-1 = :streetId OR street.id = :streetId) ";

    public static final String FIND_LIST_SUB_ID_BY_CART_CODE =
        "select id from {h-schema}subscriptions where cart_code = :cartCode";

    public static final String GET_LIST_BOS_SUBSCRIPTION =
        "select \n" +
            "   vAllSub.sub_id as subId, \n" +
            "   vAllSub.sub_code as subCode, \n" +
            "   vAllSub.billing_code as billingCode, \n" +
            "   vAllSub.is_combo as isCombo, \n" +
            "   coalesce((vAllSub.variant_draft_id is not null or vAllSub.is_only_service), false) as isBuyDevice, \n" +
            "   vAllSub.service_id as serviceId, \n" +
            "   vAllSub.service_name as serviceName, \n" +
            "   vAllSub.variant_name as variantName, \n" +
            "   vAllSub.price_variant as priceVariant, \n" +
            "   vAllSub.variant_draft_id as variantDraftId, \n" +
            "   coalesce(vAllSub.quantity_variant, 1) as quantity, \n" +
            "   case\n" +
            "       when vAllSub.product_type = 'VARIANT' then coalesce(variantThumb.file_path, deviceThumb.file_path) \n" +
            "       when vAllSub.product_type = 'COMBO_PLAN' then cbPlanThumb.file_path \n" +
            "       when vAllSub.product_type = 'PRICING' then pricingThumb.file_path \n" +
            "       when vAllSub.product_type = 'DEVICE' then deviceThumb.file_path \n" +
            "   end as productImg, \n" +
            "   case \n" +
            "        when vAllSub.product_type IN ('VARIANT', 'DEVICE') THEN 1 + vAllSub.quantity_pricing + vAllSub.quantity_combo_plan \n" +
            "        else 1 \n" +
            "   end as numOfProducts, \n" +
            "   vAllSub.total_amount as totalAmount, \n" +
            "   date(vAllSub.created_at) as createdAt, \n" +
            "   date(vAllSub.created_at + interval '15 days') as estimatedDeliveryDate, \n" +
            "   vAllSub.cycle_type as cycleType, \n" +
            "   vAllSub.payment_cycle as paymentCycle, \n" +
            "   case \n" +
            "        when vAllSub.variant_draft_id is not null then variant_name \n" +
            "        else ( \n" +
            "            case \n" +
            "                when vAllSub.cycle_type = 0 then concat_ws(' ', vAllSub.payment_cycle, 'ngày') \n" +
            "                when vAllSub.cycle_type = 1 then concat_ws(' ', vAllSub.payment_cycle, 'tuần') \n" +
            "                when vAllSub.cycle_type = 2 then concat_ws(' ', vAllSub.payment_cycle, 'tháng') \n" +
            "                when vAllSub.cycle_type = 3 then concat_ws(' ', vAllSub.payment_cycle, 'năm') \n" +
            "            end \n" +
            "            ) \n" +
            "   end as classification, \n" +
            "   vAllSub.pricing_id as pricingId, \n" +
            "   vAllSub.pricing_name as pricingName, \n" +
            "   vAllSub.provider_id as providerId, \n" +
            "   coalesce(provider.name, concat_ws(' ', provider.last_name, provider.first_name)) as providerName, \n" +
            "   vAllSub.product_type AS productType, \n" +
            "   case \n" +
            "        when vAllSub.status = 0 then 'FUTURE' \n" +
            "        when vAllSub.status = 1 then 'IN_TRIAL' \n" +
            "        when vAllSub.status = 2 then 'ACTIVE' \n" +
            "        when vAllSub.status = 3 then 'CANCELLED' \n" +
            "        when vAllSub.status = 4 then 'FINISHED' \n" +
            "   end as status, \n" +
            "   coalesce(orderDeviceStatus.id, osServiceStatus.id) as processId, \n" +
            "   coalesce(orderDeviceStatus.code, osServiceStatus.code) AS processStatus, \n" +
            "   vAllSub.message as message, \n" +
            "   vAllSub.pricing_price AS pricingPrice, \n" +
            "   vAllSub.combo_plan_price AS comboPlanPrice, \n" +
            "   vAllSub.variant_price AS variantPrice, \n" +
            "   vAllSub.service_price AS servicePrice, \n" +
            "   vAllSub.service_tax AS serviceTax \n" +
            "from {h-schema}bos_view_get_all_sub as vAllSub \n" +
            "    left join {h-schema}users as provider on vAllSub.provider_id = provider.id \n" +
            "    -- với dịch vụ -- \n" +
            "    left join {h-schema}order_service_receive osServiceReceive ON osServiceReceive.subscription_id = vAllSub.sub_id \n" +
            "    left join {h-schema}order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8) \n" +
            "    -- với thiết bị -- \n" +
            "    left join {h-schema}order_service_device_status as orderDeviceStatus on vAllSub.os_3rd_status = orderDeviceStatus.id \n" +
            "    -- thumbnail sản phẩm -- \n" +
            "    left join {h-schema}view_products_thumbnail as variantThumb on (variantThumb.object_id = vAllSub.variant_draft_id and variantThumb.type = 'VARIANT') \n" +
            "    left join {h-schema}view_products_thumbnail as cbPlanThumb on (cbPlanThumb.object_id = vAllSub.pricing_id and cbPlanThumb.type = 'COMBO_PLAN') \n" +
            "    left join {h-schema}view_products_thumbnail as pricingThumb on (pricingThumb.object_id = vAllSub.pricing_id and pricingThumb.type = 'PRICING') \n" +
            "    left join {h-schema}view_products_thumbnail as deviceThumb on (deviceThumb.object_id = (vAllSub.service_id / 10) and deviceThumb.type = 'DEVICE') \n" +
            "where \n" +
            "    ('' = :keyword or \n" +
            "       vAllSub.sub_code ilike ('%' || :keyword || '%') or \n" +
            "       vAllSub.billing_code ilike ('%' || :keyword || '%') or \n" +
            "       vAllSub.service_name ilike ('%' || :keyword || '%') \n" +
            "    ) and \n" +
            "    (-1 = :processStatus OR :processStatus = coalesce(orderDeviceStatus.id, osServiceStatus.id)) and \n" +
            "    vAllSub.user_id = :currentUserId \n";

    public static final String GET_COMBINED_ORDER_INFO_BY_CODE =
        "select  \n" +
            "    vAllSub.sub_code as orderCode, \n" +
            "    sum(vAllSub.total_amount) as totalAmount, \n" +
            "    sum( \n" +
            "        case when vAllSub.product_type IN ('VARIANT', 'DEVICE') THEN 1 + vAllSub.quantity_pricing + vAllSub.quantity_combo_plan \n" +
            "        else 1 \n" +
            "    end ) AS numOfItems, \n" +
            "    count(1) filter ( where vAllSub.total_amount <= 0) as numOfFreeItems \n" +
            "from {h-schema}bos_view_get_all_sub as vAllSub \n" +
            "where vAllSub.sub_code in (:lstSubCode) \n" +
            "group by vAllSub.sub_code ";

    public static final String GET_ORDER_DATA =
        "with arrProgressStatus as (\n" +
            "	select array_agg(distinct osd.code)::::text as progressStatus, sub.sub_code from {h-schema}feature_view_get_all_sub sub \n"
            +
            "	left join {h-schema}order_service_device_status osd ON osd.id = sub.os_3rd_status \n" +
            "	where sub.sub_code IN (:subCode) and osd.code is not null\n" +
            "	group by sub.sub_code\n" +
            "), \n" +
            "userAvatar as (\n" +
            "    select DISTINCT ON (user_id) \n" +
            "        id,\n" +
            "        user_id,\n" +
            "        file_name,\n" +
            "        file_path,\n" +
            "        ext_link\n" +
            "    from \n" +
            "        {h-schema}file_attach\n" +
            "    where \n" +
            "        user_id is not null and \n" +
            "        object_type in (0, 3) -- 0: AVATAR, 3: BUSINESS_AVATAR\n" +
            "    ORDER BY \n" +
            "        user_id, object_type -- ưu tiên object_type = 0\n" +
            ")" +
            "SELECT\n" +
            "	 sub.sub_id AS subId,\n" +
            "	 sub.bill_id AS billId, \n" +
            "	 sub.sub_code AS subCode,\n" +
            "	 COALESCE(bill.cart_code,bill.billing_code) AS code,\n" +
            "	 bill.cart_code is not null AS isCart,\n" +
            "	 sub.status_id AS statusId,\n" +
            "	 bill.status AS billingStatus,\n" +
            "	 COALESCE(pricing.pricing_type,combo_plan.combo_plan_type) AS pricingType,\n" +
            "	 sub.status_name AS statusName,\n" +
            "	 op.id as processStatus,\n" +
            "	 sub.user_id AS userId,\n" +
            "	 file.id AS  fileId,\n" +
            "	 file.file_name AS imgName,\n" +
            "	 COALESCE(file.file_path, file.ext_link) AS imgPath,\n" +
            "    case when u.customer_type = 'CN' then concat(u.last_name,' ',u.first_name) \n" +
            "    else u.name end as userName,\n" +
            "	 u.email as userEmail,\n" +
            "	 sub.provider_id AS providerId,\n" +
            "	 COALESCE(mProvider.name, concat(mProvider.last_name, ' ', mProvider.first_name)) AS providerName,\n" +
            "	 sub.total_amount AS billTotal,\n" +
            "	 sub.created_at AS createdAt,\n" +
            "	 CASE\n" +
            "           WHEN sub_infor.portal_type = 1 THEN 'Admin' \n" +
            "			WHEN sub_infor.portal_type = 2 THEN 'Developer' \n" +
            "			WHEN sub_infor.portal_type = 3 THEN 'Khách hàng' \n" +
            "			WHEN sub_infor.traffic_id IS NOT NULL THEN 'Affiliate' \n" +
            "	END AS createSource,\n" +
            "	COALESCE(sub_infor.address_invoice, CONCAT_WS(', ', bill.street_name_customer, bill.ward_name_customer, bill.district_name_customer, bill.province_name_customer) ) AS invoiceAddress,\n" +
            "	sub_infor.address AS address,\n" +
            "	sub.sub_assignee_id AS asassigneeId,\n" +
            "	sub_infor.payment_method AS paymentMethod,\n" +
            "	COALESCE(concat(mAssignee.last_name, ' ', mAssignee.first_name), mAssignee.name)  AS assigneeName,\n" +
            "	COALESCE(services.installation_configuration, combo_plan.installation_configuration) AS installationConfiguration,\n" +
            "	ps.progressStatus as progressStatus,\n" +
            "   COALESCE(inv.id, inv1.id) is null as exportInvoice,\n" +
            "	bill.status as statusBill,\n" +
            "	mAssignee.phone_number  AS assigneePhone\n" +
            "FROM {h-schema}feature_view_get_all_sub sub\n" +
            "JOIN {h-schema}subscriptions sub_infor ON sub.sub_id = sub_infor.id\n" +
            "JOIN {h-schema}billings bill ON sub.sub_id = bill.subscriptions_id\n" +
            "LEFT JOIN arrProgressStatus ps ON ps.sub_code = sub.sub_code\n" +
            "LEFT JOIN {h-schema}e_invoice inv ON inv.billing_id = bill.id\n" +
            "LEFT JOIN {h-schema}e_invoice inv1 ON inv1.cart_code = bill.cart_code\n" +
            "LEFT JOIN {h-schema}users u ON sub.user_id = u.id\n" +
            "LEFT JOIN {h-schema}users mProvider ON sub.provider_id = mProvider.id\n" +
            "LEFT JOIN {h-schema}users mAssignee ON sub.sub_assignee_id = mAssignee.id\n" +
            "LEFT JOIN userAvatar file ON u.id = file.user_id\n" +
            "LEFT JOIN {h-schema}pricing ON pricing.id = sub_infor.pricing_id\n" +
            "LEFT JOIN {h-schema}combo_plan ON combo_plan.id = sub_infor.combo_plan_id\n" +
            "LEFT JOIN {h-schema}combo_pricing ON combo_pricing.id = sub_infor.combo_plan_id\n" +
            "LEFT JOIN {h-schema}services ON services.id = sub_infor.service_id\n" +
            "LEFT JOIN {h-schema}combo ON combo.id = combo_plan.combo_id\n" +
            "LEFT JOIN {h-schema}order_service_device_status osd ON osd.id = sub_infor.os_3rd_status\n" +
            "LEFT JOIN {h-schema}order_progress op ON op.id = osd.order_progress_id \n" +
            "WHERE \n" +
            "    sub.sub_code in (:subCode) ";

    public static final String GET_SUB_CODE_ORDER_DATA =
        "    with subInfoCTE as ( \n" +
            "        SELECT\n" +
            "            COALESCE(subscriptions.cart_code, concat('ID', to_char(subscriptions.id, 'FM09999999'::::text))::::character varying) AS subCode,\n" +
            "            subscriptions.created_at as createdAt\n" +
            "        FROM \n" +
            "            {h-schema}subscriptions subscriptions\n" +
            "            JOIN {h-schema}billings ON subscriptions.id = billings.subscriptions_id\n" +
            "            LEFT JOIN {h-schema}users u ON subscriptions.user_id = u.id\n" +
            "            LEFT JOIN {h-schema}users mAssignee ON subscriptions.assignee_id = mAssignee.id\n" +
            "            LEFT JOIN {h-schema}pricing ON pricing.id = subscriptions.pricing_id\n" +
            "            LEFT JOIN {h-schema}combo_plan ON combo_plan.id = subscriptions.combo_plan_id\n" +
            "            LEFT JOIN {h-schema}combo_pricing ON combo_pricing.id_combo_plan = subscriptions.combo_plan_id\n" +
            "            LEFT JOIN {h-schema}services ON services.id = subscriptions.service_id\n" +
            "            LEFT JOIN {h-schema}combo ON combo.id = combo_plan.combo_id\n" +
            "            left join {h-schema}order_service_device_status osd ON osd.id = subscriptions.os_3rd_status\n" +
            "            left join {h-schema}order_progress op ON op.id = osd.order_progress_id \n" +
            "        WHERE \n" +
            "            ((services.product_type  = 1 AND services.service_owner IN (4,5)) OR combo_pricing.object_type IN ('PRICING', 'DEVICE_VARIANT', 'DEVICE_NO_VARIANT'))\n" +
            "            AND (:devId = -1 OR :devId = COALESCE(combo.user_id, services.user_id))\n" +
            "            AND (:portal = 'DEV' OR (combo.id IS NULL) OR combo.combo_owner IN (6,7,8)) -- check combo_owner admin portal -- \n" +
            "            AND (\n" +
            "                :value = '' OR (\n" +
            "                   (:isName = 1 AND \n" +
            "                        (CASE \n" +
            "                            WHEN u.customer_type = 'CN' THEN concat(u.last_name, ' ', u.first_name) \n" +
            "                            ELSE u.name \n" +
            "                        END ILIKE ('%' || :value || '%'))) OR\n" +
            "                   (:isPhone = 1 AND u.phone_number ilike ('%' || :value || '%')) OR\n" +
            "                   (:isEmail = 1 AND u.email ilike ('%' || :value || '%')) OR\n" +
            "                   (:isServiceName = 1 AND (services.service_name ilike ('%' || :value || '%') OR combo.combo_name ilike ('%' || :value || '%'))) OR\n" +
            "                   (:isSubCode = 1 AND (billings.billing_code ilike ('%' || :value || '%') or billings.cart_code ilike ('%' || :value || '%')) )\n" +
            "                )\n" +
            "            )\n" +
            "            AND (-1 IN (:lstProvider) OR COALESCE(combo.user_id, services.user_id) IN (:lstProvider))\n" +
            "            AND ((date('1970-01-01') = date(:startDate) or date(:startDate) <= date(subscriptions.created_at)) AND (date('1970-01-01') = date(:endDate) or date(:endDate) >= date(subscriptions.created_at)))\n" +
            "            AND (-1 = :progress OR :progress = op.id)\n" +
            "            AND (-1 = :createSource OR (:createSource = 4 AND subscriptions.traffic_id IS NOT NULL) OR :createSource = subscriptions.portal_type)\n" +
            "            AND (-1 IN (:lstProvince) OR billings.province_id IN (:lstProvince))\n" +
            "            AND (-1 IN (:lstMst) OR u.id in (:lstMst))\n" +
            "            AND ('' = :invoiceAddress OR subscriptions.address_invoice ilike ('%' || :invoiceAddress || '%') \n" +
            "                OR CONCAT_WS(', ', billings.street_name_customer, billings.ward_name_customer, billings.district_name_customer, billings.province_name_customer) ilike ('%' || :invoiceAddress || '%'))\n" +
            "            AND ('' = :address OR subscriptions.address ilike ('%' || :address || '%'))\n" +
            "            AND ('' = :assigneeName OR concat(mAssignee.last_name, ' ', mAssignee.first_name) ilike ('%' || :assigneeName || '%') OR mAssignee.name ilike ('%' || :assigneeName || '%'))\n" +
            "            AND subscriptions.deleted_flag = 1 AND ((billings.status = ANY (ARRAY[0, 1, 2, 4])) OR billings.status IS NULL)\n" +
            "        ),\n" +
            "    get_sub_code as \n" +
            "        (select distinct on (subCode) subCode, createdAt from subInfoCTE )\n" +
            "select subCode \n" +
            "from get_sub_code \n";

    public static final String COUNT_GET_SUB_CODE_ORDER_DATA =
        " select count(*) from (" + GET_SUB_CODE_ORDER_DATA + ") as count_sub_code";

    public static final String GET_LIST_SUB_TOTAL_COUPON =
        "select  \n" +
            "    type AS type, \n" +
            "    subscriptions_id AS subscriptionId,  \n" +
            "    coupon_id AS couponId, \n" +
            "    coupon_amount AS couponAmount,  \n" +
            "    coupon_desc AS couponDesc  \n" +
            "from {h-schema}view_bos_subscription_coupons where subscriptions_id in (:subIds) \n" +
            "order by subscriptions_id ";

    public static final String COUNT_PROVIDER_IN_CART_ORDER =
        "select count(distinct provider_id) from {h-schema}feature_view_get_all_sub where sub_code = :cartCode";

    public static final String GET_LIST_PRODUCT_NAME_BY_SUB_IDS_IN =
        "select \n" +
            "    case \n" +
            "        when product_type = 'VARIANT' THEN service_name || ' - ' || variant_name \n" +
            "        when product_type IN ('PRICING', 'COMBO_PLAN') then pricing_name \n" +
            "        when product_type = 'DEVICE' THEN service_name \n" +
            "    end \n" +
            "from {h-schema}feature_view_get_all_sub where sub_id IN (:subIds)";

    public static final String UPDATE_MERGE_BILLING_CART_CODE =
        "update {h-schema}billings set cart_code = :comboCodeDHSXKD where subscriptions_id in (select id from {h-schema}subscriptions where dhsxkd_sub_code in (:lstSubCodeDHSXKD))";

    public static final String COUNT_NUM_SUBS_BY_USER_ID_AND_STATUS =
        "select count(1) from {h-schema}subscriptions where user_id = :userId and status in (:lstStatus) ";

    public static final String GET_IS_PRICING_ALLOW_RENEW =
        "select exists \n" +
            "(   select \n" +
            "        subscriptions.id \n" +
            "    from \n" +
            "        {h-schema}subscriptions \n" +
            "        join {h-schema}pricing on subscriptions.pricing_id = pricing.id\n" +
            "    where \n" +
            "        pricing.id = :pricingId and\n" +
            "        coalesce(pricing.has_renew, 0) = 1\n" +
            ")";

    public static final String GET_SUBSCRIPTION_ON_OS_TYPE =
        "select \n" +
            "case \n" +
            "    when sub.service_id is not null then ownService.on_os_type \n" +
            "    when sub.pricing_id is not null then pricingService.on_os_type " +
            "    when sub.combo_plan_id is not null then combo.on_os_type \n" +
            "end as subOnOsType \n" +
            "from {h-schema}subscriptions as sub \n" +
            "    left join {h-schema}pricing on sub.pricing_id is not null and pricing.id = sub.pricing_id \n" +
            "    left join {h-schema}services as pricingService on pricing.service_id = pricingService.id \n" +
            "    left join {h-schema}services as ownService on sub.service_id is not null and sub.service_id = ownService.id \n" +
            "    left join {h-schema}combo_plan as cbPlan on cbPlan.id = sub.combo_plan_id \n" +
            "    left join {h-schema}combo on cbPlan.combo_id = combo.id \n" +
            "where sub.id = :subId";

    public static final String GET_SUBSCRIPTION_SUMMARY_BY_ID =
        "select \n" +
            "    is_combo as isCombo, \n" +
            "    case \n" +
            "        when is_on then 'ON' \n" +
            "        else 'OS' \n" +
            "    end as onOsType \n" +
            "from {h-schema}feature_view_get_all_sub_group \n" +
            "where \n" +
            "    sub_id = :subscriptionId";

    public static final String GET_FIRST_SUB_AND_USER_INDEX =
        "with first_sub as ( \n" +
            "    select user_id, min(created_at) as created_at \n" +
            "    from {h-schema}subscriptions \n" +
            "    where deleted_flag = 1 and reg_type = 1 and confirm_status = 1 \n" +
            "    group by user_id \n" +
            "), \n" +
            "index_sub as ( \n" +
            "    select user_id, created_at, rank() over (order by created_at) as idx from first_sub \n" +
            ") \n" +
            "select \n" +
            "    users.id AS userId,\n" +
            "    coalesce(index_sub.created_at, now()) as createdAt, \n" +
            "    coalesce(index_sub.idx, (SELECT max(idx) + 1 FROM index_sub)) as idx, \n" +
            "    case when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name) else users.name end as name, \n" +
            "    users.phone_number as phone, \n" +
            "    concat_ws(', ', street.name, ward.name, district.name, province.name) AS userAddress,\n" +
            "    users.user_code as userCode, \n" +
            "    users.province_code as provinceCode \n" +
            "from {h-schema}users \n" +
            "    left join {h-schema}province on province.id = users.province_id\n" +
            "    left join {h-schema}district on district.id = users.district_id and district.province_id = province.id \n" +
            "    left join {h-schema}ward on ward.id = users.ward_id and ward.district_id = district.id and ward.province_code = province.code \n" +
            "    left join {h-schema}street on street.id = users.street_id and street.ward_id = users.ward_id and street.district_id = users.district_id and street.province_id = province.id\n" +
            "    left join index_sub on index_sub.user_id = users.id \n" +
            "where users.id = :userId ";

    public static final String GET_LIST_PARTNER_ORDER =
        "select\n" +
            "   sub.sub_id as orderId,\n" +
            "   sub.partner_id as partnerId,\n" +
            "   sub.customer_type as customerType,\n" +
            "   sub.customer_name as customerName,\n" +
            "   sub.email as customerEmail,\n" +
            "   sub.phone as customerPhone,\n" +
            "   sub.identityno as taxId,\n" +
            "   sub.total_amount as totalAmount,\n" +
            "   sub.status_code as status,\n" +
            "   '' as payment_status,\n" +
            "   '' as orderType,\n" +
            "   sub.created_at as createdAt,\n" +
            "   sub.bill_id as billingId \n" +
            "from {h-schema}feature_view_get_all_sub_partner as sub\n" +
            "where\n" +
            "   ('' = :partnerId or sub.partner_id = :partnerId) and\n" +
            "   ('' = :status or lower(sub.status_code) = lower(:status)) and\n" +
            "   (date('1970-01-01') = date(:fromDate) or date(sub.created_at) >= date(:fromDate)) and\n" +
            "   (date('1970-01-01') = date(:toDate) or date(sub.created_at) <= date(:toDate)) and\n" +
            "   ('' = :paymentStatus) and\n" +
            "   ('ALL' = :productType or sub.product_type = :productType) and\n" +
            "   (-1 = :categoryId or :categoryId = any(sub.category_ids)) and\n" +
            "   ('' = :customerType or sub.customer_type = :customerType) and\n" +
            "   ('' = :tin or sub.identityno ilike ('%' || :tin || '%')) and\n" +
            "   ('' = :customerEmail or sub.email ilike ('%' || :customerEmail || '%')) and\n" +
            "   ('' = :customerPhone or sub.email ilike ('%' || :customerPhone || '%'))";

    public static final String VALIDATE_PARTNER_BY_SUB_ID_AND_API_KEY =
        "select exists ( \n" +
            "    select \n" +
            "        metadata.subscription_id \n" +
            "    from \n" +
            "        {h-schema}subscription_metadata as metadata \n" +
            "        join {h-schema}client_secret on client_secret.client_id = metadata.partner_id \n" +
            "    where \n" +
            "        metadata.subscription_id = :subId and \n" +
            "        client_secret.secret_key = :apiKey \n" +
            ")";

    public static final String GET_LIST_VALID_PARTNER_SUB_ID =
        "select \n" +
            "    metadata.subscription_id \n" +
            "from \n" +
            "    {h-schema}subscription_metadata as metadata \n" +
            "    join {h-schema}client_secret on client_secret.client_id = metadata.partner_id \n" +
            "    join {h-schema}subscriptions on metadata.subscription_id = subscriptions.id \n" +
            "where \n" +
            "    subscriptions.cart_code = :cartCode and \n" +
            "    client_secret.secret_key = :apiKey \n";

    public static final String FIND_PARTNER_ID_BY_SUBSCRIPTION_ID =
        "select partner_id from {h-schema}subscription_metadata where subscription_id = :subscriptionId";

    public static final String GET_ORDER_DETAIL = "(SELECT \n" +
            "    sub.id AS orderId,\n" +
            "    TRUE AS is_service,\n" +
            "    ser.id AS productId,\n" +
            "    CASE\n" +
            "        WHEN ser.product_type = 1 THEN 'PHYSICAL'::::text\n" +
            "        WHEN ser.product_type = ANY (ARRAY[2, 6, 7, 9]) THEN 'DIGITAL'::::text\n" +
            "        WHEN ser.product_type = ANY (ARRAY[3, 4, 5]) THEN 'SERVICE'::::text\n" +
            "        ELSE cast(NULL as text)\n" +
            "    END AS productType,\n" +
            "    ser.service_name AS productName,\n" +
            "    CAST(ser.categories_id AS text) AS categoryId,\n" +
            "    categories.name AS categoryName,\n" +
            "    CASE \n" +
            "        WHEN u.customer_type = 'CN' THEN CONCAT(u.last_name, ' ', u.first_name)\n" +
            "        ELSE u.name \n" +
            "    END AS customerName,\n" +
            "    u.phone_number AS customerPhone,\n" +
            "    u.customer_type AS customerType,\n" +
            "    u.email as customerEmail,\n" +
            "    sub.pricing_id AS pricingId,\n" +
            "    sub.pricing_multi_plan_id as pricingMultiPlanId,\n" +
            "    sub.quantity,\n" +
            "    sub.total_amount AS totalAmount,\n" +
            "    sub.combo_plan_id as comboPlanId,\n" +
            "    sub.created_at AS createdAt,\n" +
            "    sub.assignee_id as assignedStaffId,\n" +
            "    sub.current_cycle as currentCycle,\n" +
            "    COALESCE(pmp.number_of_cycles, p.number_of_cycles) as numberOfCycles," +
            "    CASE \n" +
            "        WHEN u.customer_type = 'CN' THEN NULL\n" +
            "        ELSE cast(u.tin as bigint)\n" +
            "    END AS taxId,\n" +
            "    metadata.partner_id as partnerId\n" +
            "FROM {h-schema}subscriptions sub\n" +
        "JOIN {h-schema}subscription_metadata as metadata on metadata.subscription_id = sub.id\n" +
            "LEFT JOIN {h-schema}services ser ON sub.service_id = ser.id\n" +
            "LEFT JOIN {h-schema}users u ON u.id = sub.user_id\n" +
            "LEFT JOIN {h-schema}categories categories ON categories.id = ser.categories_id\n" +
            "LEFT JOIN {h-schema}pricing p ON sub.pricing_id = p.id  \n" +
            "LEFT JOIN {h-schema}pricing_multi_plan pmp ON sub.pricing_multi_plan_id = pmp.id  \n" +
            "WHERE \n" +
            "    sub.id = :orderId AND \n" +
            "    sub.combo_plan_id IS NULL AND \n" +
            "    metadata.partner_id = :partnerId \n" +
            ")\n" +
            "UNION\n" +
            "(SELECT \n" +
            "    sub.id AS orderId,\n" +
            "    FALSE AS is_service,\n" +
            "    c.id AS productId,\n" +
            "    CAST(NULL AS text) AS productType,\n" +
            "    c.combo_name AS productName,\n" +
            "    CAST(c.categories_id AS text) AS categoryId,\n" +
            "    CAST(NULL AS text) AS categoryName,\n" +
            "    CASE \n" +
            "        WHEN u.customer_type = 'CN' THEN CONCAT(u.last_name, ' ', u.first_name)\n" +
            "        ELSE u.name \n" +
            "    END AS customerName,\n" +
            "    u.phone_number AS customerPhone,\n" +
            "    u.customer_type AS customerType,\n" +
            "    u.email as customerEmail,\n" +
            "    sub.pricing_id as pricingId,\n" +
            "    sub.pricing_multi_plan_id as pricingMultiPlanId,\n" +
            "    sub.quantity,\n" +
            "    sub.total_amount AS totalAmount,\n" +
            "    sub.combo_plan_id as comboPlanId,\n" +
            "    sub.created_at AS createdAt,\n" +
            "    sub.assignee_id as assignedStaffId,\n" +
            "    sub.current_cycle as currentCycle,\n" +
            "	 cp.number_of_cycles as numberOfCycles, " +
            "    CASE \n" +
            "        WHEN u.customer_type = 'CN' THEN NULL\n" +
            "        ELSE cast(u.tin as bigint)\n" +
            "    END AS taxId,\n" +
            "    metadata.partner_id as partnerId\n" +
            "FROM {h-schema}subscriptions sub\n" +
        "JOIN {h-schema}subscription_metadata as metadata on metadata.subscription_id = sub.id\n" +
            "LEFT JOIN {h-schema}combo_plan cp ON sub.combo_plan_id = cp.id\n" +
            "LEFT JOIN {h-schema}combo c ON c.id = cp.combo_id\n" +
            "LEFT JOIN {h-schema}users u ON u.id = sub.user_id\n" +
            "WHERE \n" +
            "    sub.id = :orderId AND \n" +
            "    sub.combo_plan_id IS NOT NULL AND \n" +
            "    metadata.partner_id = :partnerId \n" +
            ")\n";

    public static final String FIND_BY_SUBSCRIPTION_ID_AND_PARTNER_ID =
        "select * \n" +
            "from {h-schema}subscriptions \n" +
            "    join {h-schema}subscription_metadata as metadata on subscriptions.id = metadata.subscription_id \n" +
            "where subscriptions.id = :subId and metadata.partner_id = :partnerId ";

    public static final String FIND_ALL_BY_CART_CODE_AND_PARTNER_ID =
        "select * \n" +
            "from {h-schema}subscriptions \n" +
            "    join {h-schema}subscription_metadata as metadata on subscriptions.id = metadata.subscription_id \n" +
            "where subscriptions.cart_code = :cartCode and metadata.partner_id = :partnerId ";

    public static final String EXISTS_BY_SOLUTION_DRAFT_ID_IN =
        "SELECT EXISTS ( \n" +
            "SELECT 1 \n" +
            "FROM {h-schema}subscription_metadata metadata \n" +
            "    JOIN {h-schema}product_solutions solution ON solution.id = metadata.solution_id \n" +
            "WHERE solution.draft_id IN (:solutionDraftIds) \n" +
            ")";

    public static final String EXISTS_BY_PACKAGE_DRAFT_ID_IN =
        "SELECT EXISTS ( \n" +
            "SELECT 1 \n" +
            "FROM {h-schema}subscription_metadata metadata \n" +
            "    JOIN {h-schema}packages ON packages.id = metadata.package_id \n" +
            "WHERE packages.draft_id IN (:packageDraftIds) \n" +
            ")";
}
