package com.constant.enums.coupon;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 10/5/2021
 */
public enum ApproveTypeEnum {
    UNAPPROVED(0), APPROVED(1), AWAITING_APPROVAL(2), REJECTED(3);

    public final int value;

    ApproveTypeEnum(int value) {
        this.value = value;
    }

    private static final Map<Integer, ApproveTypeEnum> map = new HashMap<>();

    static {
        for (ApproveTypeEnum approveTypeEnum : ApproveTypeEnum.values()) {
            map.put(approveTypeEnum.value, approveTypeEnum);
        }
    }

    public static ApproveTypeEnum valueOf(int value) {
        return map.get(value);
    }
}
