package com.constant.enums.action.log;

import java.util.HashMap;
import java.util.Map;

/**
 * * Action Log Combo Type Enum
 *
 * <AUTHOR>
 * @since 2/7/2021
 */
public enum ActionLogVersionEnum {
    LATEST(0),
    OLDEST(1),
    ALL(-1);
    public final int value;

    ActionLogVersionEnum(int value) {
        this.value = value;
    }

    private static final Map<Integer, ActionLogVersionEnum> map = new HashMap<>();

    static {
        for (ActionLogVersionEnum bonusType : ActionLogVersionEnum.values()) {
            map.put(bonusType.value, bonusType);
        }
    }

    public static ActionLogVersionEnum valueOf(int value) {
        return map.get(value);
    }

}
