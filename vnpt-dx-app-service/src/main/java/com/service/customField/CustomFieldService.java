package com.service.customField;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dto.customField.ActiveLayoutDTO;
import com.dto.customField.CustomFieldCodeAndValueDTO;
import com.dto.customField.CustomFieldDTO;
import com.dto.customField.CustomLayoutDTO;
import com.dto.customField.CustomNodeDTO;
import com.dto.customField.GetListFieldDTO;
import com.dto.customField.GetListLayoutDTO;
import com.dto.customField.GetPermissionDTO;
import com.dto.customField.ICustomNodeDTO;
import com.dto.customField.IGetPermissionDTO;
import com.dto.customField.ITemplateLayoutDTO;
import com.dto.customField.LayoutContentDTO;
import com.dto.customField.MandatoryFieldDTO;
import com.dto.customField.PermissionRequestDTO;
import com.dto.customField.RenameLayoutDTO;
import com.dto.customField.ReplaceLayoutDTO;
import com.dto.customField.UpdateLayoutContentDTO;
import com.dto.customField.UpdateListLayoutContentDTO;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.customFields.CustomFieldCategoryEnum;
import com.onedx.common.dto.base.ICommonIdAndNameDTO;

public interface CustomFieldService {

    /* ******************************************************************************************************************************************
     * CUSTOM LAYOUT
     *******************************************************************************************************************************************/
    Page<GetListLayoutDTO> getListLayout(String name, CustomFieldCategoryEnum category, List<Long> lstObjectId, String portal,
        PortalType portalType, String customerType, Integer version, Pageable pageable);

    Long createLayout(CustomLayoutDTO layoutDTO);

    CustomLayoutDTO getLayoutDetail(Long layoutId);

    void updateLayout(CustomLayoutDTO layoutDTO);

    void deleteLayout(Long id);

    void renameLayout(RenameLayoutDTO requestDTO);

    void activeLayout(Long id, ActiveLayoutDTO requestDTO);

    Long duplicateLayout(Long id);

    Page<ITemplateLayoutDTO> getListTemplateLayout(String layoutCategory, String customerType, Integer version, Pageable pageable);

    List<ICommonIdAndNameDTO> getListConvertibleLayout(String category, Long sourceLayoutId, Integer version);

    Page<ICommonIdAndNameDTO> getListLayoutName(Integer version, Pageable pageable);

    void replaceLayout(ReplaceLayoutDTO requestDTO);

    List<LayoutContentDTO> getListLayoutContent(CustomFieldCategoryEnum category);

    void updateListLayoutContent(UpdateListLayoutContentDTO lstLayoutContent);

    /* ******************************************************************************************************************************************
     * CUSTOM FIELD
     *******************************************************************************************************************************************/
    List<MandatoryFieldDTO> getMandatoryFieldWhenSub(Boolean isService, Long pricingId, Long addonId);

    Page<GetListFieldDTO> getListCustomField(String category, String name, String fieldType, String fieldCode, String layoutName, Pageable pageable);

    List<GetPermissionDTO> getAllPermission(String roleName, String portal, Long layoutId, Long fieldId, String type);

    List<IGetPermissionDTO> getAllUserPermission();

    void updatePermission(PermissionRequestDTO permissionRequestDTO);

    CustomFieldDTO getFieldDetail(String fieldId);

    boolean existByLayoutName(String layoutName, String category);

    void updateLayoutContent(UpdateLayoutContentDTO request);

    /**
     * Lấy layout màn tạo mặc định sub
     */
    CustomLayoutDTO getCreateSubLayout(CustomerTypeEnum customerType, PortalType portal);

    /**
     * Lấy layout đang active màn tạo khách hàng
     */
    CustomLayoutDTO getCreateCustomerLayout(CustomerTypeEnum customerTypeEnum, PortalType portalType);

    CustomLayoutDTO getCreateContactLayout(CustomerTypeEnum customerTypeEnum);

    void activeCustomerLayout(Long id, ActiveLayoutDTO requestDTO);

    List<ICommonIdAndNameDTO> getListConvertibleCustomerLayout(String category, String customerType, Integer portalType, Long sourceLayoutId,
        Integer version);

    boolean existByFieldCode(String fieldCode);

    boolean checkExistByValue(CustomFieldCodeAndValueDTO customFieldValue);

    List<ICustomNodeDTO> getListCustomNode(String layoutCode);

    void setListCustomNode(List<CustomNodeDTO> lstCustomNode);
}
