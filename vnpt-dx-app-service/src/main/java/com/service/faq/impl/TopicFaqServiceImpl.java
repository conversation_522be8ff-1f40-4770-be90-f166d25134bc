package com.service.faq.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.onedx.common.utils.SqlUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.constant.EntitiesConstant;
import com.constant.NotifyConst;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.services.ServiceStatusEnum;
import com.dto.faq.FaqCreateDTO;
import com.dto.faq.FaqDetailResDTO;
import com.dto.faq.FaqEditDTO;
import com.dto.faq.FaqServiceDetailResDTO;
import com.dto.faq.ListCatalog;
import com.dto.faq.ListCatalogDTO;
import com.dto.faq.ListCatalogRes;
import com.dto.faq.ListCatalogServiceRes;
import com.dto.faq.ListQuestion;
import com.dto.faq.ListQuestionDTO;
import com.dto.faq.ListQuestionService;
import com.dto.faq.ListTopicIdDTO;
import com.dto.faq.ListTopicService;
import com.dto.faq.ListTopicServiceDTO;
import com.dto.faq.SearchTopicResDTO;
import com.dto.faq.TopicApproveDTO;
import com.dto.product_solustions.IGetTopicDTO;
import com.dto.product_solustions.TopicDetailDTO;
import com.dto.services.ServiceTopicUpdateRequest;
import com.entity.faq.FaqFaqsQuestion;
import com.entity.faq.FaqFaqsService;
import com.entity.faq.TopicFaq;
import com.entity.faq.TopicFaqDraft;
import com.enums.ApproveStatusEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.google.common.base.Strings;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.repository.combo.ComboRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.faq.FaqDraftRepository;
import com.repository.faq.FaqsQuestionRepository;
import com.repository.faq.FaqsServiceRepository;
import com.repository.faq.TopicFaqRepository;
import com.repository.services.ServiceRepository;
import com.repository.users.UserRepository;
import com.service.faq.TopicFaqService;
import com.service.system.param.SystemParamService;
import com.util.AuthUtil;
import com.util.NotifyUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Service
@Slf4j
public class TopicFaqServiceImpl implements TopicFaqService {

    @Autowired
    TopicFaqRepository topicFaqRepository;
    @Autowired
    ServiceRepository serviceRepository;
    @Autowired
    ComboRepository comboRepository;
    @Autowired
    FaqsServiceRepository faqsServiceRepository;
    @Autowired
    FaqDraftRepository faqDraftRepository;
    @Autowired
    FaqsQuestionRepository faqsQuestionRepository;
    @Autowired
    DepartmentsRepository departmentsRepository;
    @Autowired
    SystemParamService systemParamService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private Environment evn;

    private MessageSource messageSource;
    private final String[] Faq = {"faq"};

    private final String APPROVE_TOPIC_CONFIG = "APPROVE_TOPIC_CONFIG";


    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFaq(FaqCreateDTO faqCreateDTO, PortalType portal) {
        validateName(faqCreateDTO.getName());
        validateSlugName(faqCreateDTO.getSlugName());
        Integer appoveTopicConfig = systemParamService.findByParamType(APPROVE_TOPIC_CONFIG).getApproveTopic();
        TopicFaq currentFaq = TopicFaq.builder()
                .name(faqCreateDTO.getName().trim())
                .slugName(faqCreateDTO.getSlugName().trim())
                .code(faqCreateDTO.getCode().trim())
                .description(faqCreateDTO.getDescription())
                .status(StatusEnum.INACTIVE.value)
                .topicType(faqCreateDTO.getTopicType())
                .approve(portal == PortalType.ADMIN || appoveTopicConfig == 0 ? ApproveStatusEnum.APPROVED : ApproveStatusEnum.UNAPPROVED)
                .deletedFlag(EntitiesConstant.DeleteFlag.ACTIVE)
                .createdBy(Objects.requireNonNull(AuthUtil.getCurrentUser()).getId())
                .createdAt(new Date())
                .build();
        TopicFaq faq = topicFaqRepository.save(currentFaq);
        TopicFaqDraft topicFaqDraft = null;
        if (portal == PortalType.ADMIN || appoveTopicConfig == 0) {
            topicFaqDraft = cloneData(faq, topicFaqDraft);
        }
        for (ListCatalog listCatalog : faqCreateDTO.getCatalogs()) {
            if (listCatalog.getQuesId() == null) {
                for (ListQuestion listQuestion : listCatalog.getQuestions()) {
                    FaqFaqsQuestion faqsQuestion = new FaqFaqsQuestion(faq.getId(), listQuestion.getQuesId(), listCatalog.getCatalogId(), null, listCatalog.getPriority(), listQuestion.getPriority(), 0);
                    faqsQuestionRepository.save(faqsQuestion);
                    if (topicFaqDraft != null) {
                        FaqFaqsQuestion faqsQuestionDraft = new FaqFaqsQuestion(null, listQuestion.getQuesId(), listCatalog.getCatalogId(), topicFaqDraft.getId(), listCatalog.getPriority(), listQuestion.getPriority(), 0);
                        faqsQuestionRepository.save(faqsQuestionDraft);
                    }
                }
                continue;
            }
            FaqFaqsQuestion faqsQuestion = new FaqFaqsQuestion(faq.getId(), listCatalog.getQuesId(), null, null, listCatalog.getPriority(), null, 1);
            faqsQuestionRepository.save(faqsQuestion);
            if (topicFaqDraft != null) {
                FaqFaqsQuestion faqsQuestionDraft = new FaqFaqsQuestion(null, listCatalog.getQuesId(), null, topicFaqDraft.getId(), listCatalog.getPriority(), null, 1);
                faqsQuestionRepository.save(faqsQuestionDraft);
            }
        }
        return faq.getId();
    }

    @Override
    public FaqDetailResDTO getDetail(Long id, TopicFaqDraft faqDraft, PricingDetailInputEnum type) {
        if (Objects.equals(PricingDetailInputEnum.PROCESSING, type) && Objects.nonNull(faqDraft)) {
            return findServiceDraftBasicById(faqDraft);
        }
        TopicFaq faqs = topicFaqRepository.findById(id).orElseThrow(() -> {
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, Faq, null);
            return new ResourceNotFoundException(messageNotFound, Resources.FAQS, com.onedx.common.exception.ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });
        return getFaqDraftBasicOrFaq(faqs, null, false);
    }

    @Override
    public FaqDetailResDTO getDetailBySlugName(String slugName, String quesName) {
        TopicFaq topicFaq = topicFaqRepository.findFirstBySlugName(slugName);
        if (topicFaq == null || topicFaq.getApprove() == ApproveStatusEnum.REJECTED) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.FAQS, ErrorKey.TopicFaq.SLUG_NAME,
                    (MessageKeyConstant.NOT_FOUND));
        }
        if (topicFaq.getStatus() == StatusEnum.INACTIVE.value) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.INACTIVE, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.FAQS, ErrorKey.TopicFaq.SLUG_NAME,
                    (MessageKeyConstant.INACTIVE));
        }
        if (topicFaq.getApprove() == ApproveStatusEnum.UNAPPROVED || topicFaq.getApprove() == ApproveStatusEnum.AWAITING_APPROVAL) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.Topic.UNAPPROVED, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.FAQS, ErrorKey.TopicFaq.SLUG_NAME,
                    (MessageKeyConstant.Topic.UNAPPROVED));
        }
        FaqDetailResDTO resp = getFaqBySlugName(topicFaq, null, false, quesName);
        resp.setTopicServiceType(1); // Fix cứng hỗ trợ màn chọn topic trong spdv
        return resp;
    }

    @Override
    @Transactional
    public BaseResponseDTO update(FaqEditDTO paramDto, PortalType portal) {
        TopicFaq topicFaq = getCurrentTopic(paramDto.getId());
        Integer appoveTopicConfig = systemParamService.findByParamType(APPROVE_TOPIC_CONFIG).getApproveTopic();
        if (!StringUtils.equals(
                StringUtils.trim(paramDto.getName()),
                StringUtils.trim(topicFaq.getName()))) {
            validateName(paramDto.getName());
        }
        if (!StringUtils.equals(
                StringUtils.trim(paramDto.getSlugName()),
                StringUtils.trim(topicFaq.getSlugName()))) {
            validateSlugName(paramDto.getSlugName());
        }
        topicFaq.setModifiedBy(AuthUtil.getCurrentUser().getId());
        topicFaq.setModifiedAt(new Date());
        TopicFaqDraft topicFaqDraft = findDraftByFaqId(paramDto.getId());
        if (Objects.equals(portal, PortalType.DEV) && Objects.equals(ApproveStatusEnum.APPROVED, topicFaq.getApprove())) {
            if (Objects.nonNull(topicFaqDraft) && Objects.equals(ApproveStatusEnum.APPROVED, topicFaqDraft.getApprove()) && appoveTopicConfig == 0) {
                TopicFaqDraft topicFaqDraftNew = cloneData(topicFaq, null);
                updateTopicDraft(paramDto, topicFaqDraftNew);
            } else if (Objects.nonNull(topicFaqDraft)) {
                topicFaqDraft.setApprove(ApproveStatusEnum.UNAPPROVED);
                updateTopicDraft(paramDto, topicFaqDraft);
                return new BaseResponseDTO(topicFaq.getId());
            } else {
                topicFaqDraft = new TopicFaqDraft();
                topicFaqDraft.setApprove(ApproveStatusEnum.UNAPPROVED);
                topicFaqDraft.setCreatedBy(AuthUtil.getCurrentUserId());
                topicFaqDraft.setModifiedBy(AuthUtil.getCurrentUserId());
                updateTopicDraft(paramDto, topicFaqDraft);
                return new BaseResponseDTO(topicFaq.getId());
            }
        }
        topicFaq.setName(paramDto.getName());
        topicFaq.setSlugName(paramDto.getSlugName());
        topicFaq.setTopicType(paramDto.getTopicType());
        topicFaq.setDescription(paramDto.getDescription());
        if ((Objects.equals(portal, PortalType.DEV) && Objects.equals(appoveTopicConfig, 0)) ||
                (Objects.equals(portal, PortalType.DEV) && Objects.equals(appoveTopicConfig, 1)
                        && Objects.equals(ApproveStatusEnum.APPROVED, topicFaq.getApprove())) ||
                Objects.equals(portal, PortalType.ADMIN)) {
            topicFaq.setApprove(ApproveStatusEnum.APPROVED);
        } else {
            topicFaq.setApprove(ApproveStatusEnum.UNAPPROVED);
        }
        List<FaqFaqsQuestion> faqsQuestions = faqsQuestionRepository.findAllByTopicId(topicFaq.getId());
        if (faqsQuestions != null && !faqsQuestions.isEmpty()) {
            faqsQuestionRepository.deleteAll(faqsQuestions);
        }
        for (ListCatalog listCatalog : paramDto.getCatalogs()) {
            if (listCatalog.getQuesId() == null) {
                for (ListQuestion listQuestion : listCatalog.getQuestions()) {
                    FaqFaqsQuestion faqsQuestion = new FaqFaqsQuestion(topicFaq.getId(), listQuestion.getQuesId(), listCatalog.getCatalogId(), null, listCatalog.getPriority(), listQuestion.getPriority(), 0);
                    faqsQuestionRepository.save(faqsQuestion);
                    if (topicFaqDraft != null) {
                        FaqFaqsQuestion faqsQuestionDraft = new FaqFaqsQuestion(null, listQuestion.getQuesId(), listCatalog.getCatalogId(), topicFaqDraft.getId(), listCatalog.getPriority(), listQuestion.getPriority(), 0);
                        faqsQuestionRepository.save(faqsQuestionDraft);
                    }
                }
                continue;
            }
            FaqFaqsQuestion faqsQuestion = new FaqFaqsQuestion(topicFaq.getId(), listCatalog.getQuesId(), null, null, listCatalog.getPriority(), null, 1);
            faqsQuestionRepository.save(faqsQuestion);
            if (topicFaqDraft != null) {
                FaqFaqsQuestion faqsQuestionDraft = new FaqFaqsQuestion(null, listCatalog.getQuesId(), null, topicFaqDraft.getId(), listCatalog.getPriority(), null, 1);
                faqsQuestionRepository.save(faqsQuestionDraft);
            }
        }
        return new BaseResponseDTO(topicFaq.getId());
    }

    @Override
    @Transactional
    public void approve(Long id, TopicApproveDTO serviceApproveDTO) {
        Long provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUser().getId());
        if (Objects.nonNull(provinceId)) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        TopicFaq topicFaq = getCurrentTopic(id);
        adminUpdateApproveStatus(serviceApproveDTO, topicFaq, id);
        topicFaq.setModifiedBy(AuthUtil.getCurrentUser().getId());
        topicFaq.setModifiedAt(new Date());
    }

    @Override
    public Page<SearchTopicResDTO> searchForAdmin(String name, String code, Integer topicType, String portalType, ApproveStatusEnum approve, Integer status, Long notId, Pageable pageable) {
        if (!Strings.isNullOrEmpty(name) && name.contains("%")) {
            name = name.replace("%", "\\%");
        }
        return topicFaqRepository.search(name, code, topicType, portalType, approve.value, status, -1L, notId, pageable);
    }

    @Override
    public Page<SearchTopicResDTO> searchForDev(String name, String code, Integer topicType, String portalType, ApproveStatusEnum approve, Integer status, Long notId, Pageable pageable) {
        if (!Strings.isNullOrEmpty(name) && name.contains("%")) {
            name = name.replace("%", "\\%");
        }
        Long userId = AuthUtil.getCurrentParentId();
        return topicFaqRepository.search(name, code, topicType, portalType, approve.value, status, userId, notId, pageable);
    }

    private FaqDetailResDTO findServiceDraftBasicById(TopicFaqDraft faqDraft) {

        // check quyen
        validateAccessFaq(faqDraft.getCreatedBy());
        FaqDetailResDTO faqDetailResDTO = getFaqDraftBasicOrFaq(null, faqDraft, true); //draft
        faqDetailResDTO.setId(faqDraft.getTopicId());
        return faqDetailResDTO;
    }

    private FaqDetailResDTO getFaqDraftBasicOrFaq(TopicFaq faqs, TopicFaqDraft faqDraft, Boolean isDraft) {
        FaqDetailResDTO detailResDTO = new FaqDetailResDTO();
        if (isDraft) {
            BeanUtils.copyProperties(faqDraft, detailResDTO);
        } else {
            BeanUtils.copyProperties(faqs, detailResDTO);
        }
        List<ListCatalogDTO> catalogDTOS = faqsQuestionRepository.getCatalogByTopicId(detailResDTO.getId(), isDraft ? 1 : 0, "");
        List<ListCatalogDTO> catalogQuestions = faqsQuestionRepository.getQuestionByTopicId(detailResDTO.getId(), isDraft ? 1 : 0, "");

        List<ListCatalogRes> listCatalogs = new ArrayList<>();
        for (ListCatalogDTO catalogDTO : catalogDTOS) {
            ListCatalogRes listCatalog = new ListCatalogRes();
            listCatalog.setCatalogId(catalogDTO.getCatalogId());
            listCatalog.setCatalogName(catalogDTO.getCatalogName());
            listCatalog.setPriority(catalogDTO.getPriority());
            listCatalog.setType(catalogDTO.getType());
            List<ListQuestionDTO> questionFaqs = faqsQuestionRepository.getQuestionByTopicIdAndCatalogId(detailResDTO.getId(), catalogDTO.getCatalogId(), isDraft ? 1 : 0, "");
            listCatalog.setQuestions(questionFaqs);
            log.info(catalogDTO.getCatalogName());
            listCatalogs.add(listCatalog);
        }

        for (ListCatalogDTO catalogDTO : catalogQuestions) {
            ListCatalogRes listCatalog = new ListCatalogRes();
            BeanUtils.copyProperties(catalogDTO, listCatalog);
            listCatalog.setQuestId(catalogDTO.getQuestId());
            listCatalog.setQuestAnswer(catalogDTO.getQuesAnswer());
            listCatalogs.add(listCatalog);
        }
        listCatalogs.sort(Comparator.comparing(ListCatalogRes::getPriority));
        detailResDTO.setCatalogs(listCatalogs);
        detailResDTO.setTopicServiceType(2); // Fix cứng hỗ trợ màn chọn topic trong spdv
//        Integer appoveTopicConfig = getAppoveTopicConfig();
//        detailResDTO.setApprove(appoveTopicConfig == 0 ? ApproveStatusEnum.APPROVED : ApproveStatusEnum.UNAPPROVED);
        return detailResDTO;
    }

    private FaqDetailResDTO getFaqBySlugName(TopicFaq faqs, TopicFaqDraft faqDraft, Boolean isDraft, String quesName) {
        FaqDetailResDTO detailResDTO = new FaqDetailResDTO();
        if (isDraft) {
            BeanUtils.copyProperties(faqDraft, detailResDTO);
        } else {
            BeanUtils.copyProperties(faqs, detailResDTO);
        }
        List<ListCatalogDTO> catalogDTOS = faqsQuestionRepository.getCatalogByTopicId(detailResDTO.getId(), isDraft ? 1 : 0, quesName);
        List<ListCatalogDTO> catalogQuestions = faqsQuestionRepository.getQuestionByTopicId(detailResDTO.getId(), isDraft ? 1 : 0, quesName);

        List<ListCatalogRes> listCatalogs = new ArrayList<>();
        for (ListCatalogDTO catalogDTO : catalogDTOS) {
            ListCatalogRes listCatalog = new ListCatalogRes();
            listCatalog.setCatalogId(catalogDTO.getCatalogId());
            listCatalog.setCatalogName(catalogDTO.getCatalogName());
            listCatalog.setPriority(catalogDTO.getPriority());
            listCatalog.setType(catalogDTO.getType());
            List<ListQuestionDTO> questionFaqs = faqsQuestionRepository.getQuestionByTopicIdAndCatalogId(detailResDTO.getId(), catalogDTO.getCatalogId(), isDraft ? 1 : 0,quesName);
            listCatalog.setQuestions(questionFaqs);
            log.info(catalogDTO.getCatalogName());
            listCatalogs.add(listCatalog);
        }

        for (ListCatalogDTO catalogDTO : catalogQuestions) {
            ListCatalogRes listCatalog = new ListCatalogRes();
            BeanUtils.copyProperties(catalogDTO, listCatalog);
            listCatalog.setQuestId(catalogDTO.getQuestId());
            listCatalog.setQuestAnswer(catalogDTO.getQuesAnswer());
            listCatalogs.add(listCatalog);
        }
        listCatalogs.sort(Comparator.comparing(ListCatalogRes::getPriority));
        detailResDTO.setCatalogs(listCatalogs);
        detailResDTO.setTopicServiceType(2); // Fix cứng hỗ trợ màn chọn topic trong spdv
        return detailResDTO;
    }

    private void validateAccessFaq(Long createdBy) {
        if (AuthUtil.getCurrentUser() == null) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        if (AuthUtil.checkUserRoles(Arrays
                .asList(RoleType.FULL_DEV.getValue(), RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_BUSINESS.getValue(),
                        RoleType.DEVELOPER_OPERATOR.getValue()))
                && !Objects.equals(createdBy, AuthUtil.getCurrentUser().getId())) {
            throw new AccessDeniedException(MessageKeyConstant.USER_ACCESS_DENIED);
        }
    }


    @Override
    public TopicFaqDraft findDraftByFaqId(Long id) {
        // tim FAQDraft moi nhat
        Optional<TopicFaqDraft> faqDraft = faqDraftRepository.findByFaqId(id);
        return faqDraft.orElse(null);
    }

    @Override
    public TopicFaq getCurrentTopic(Long id) {
        Optional<TopicFaq> topicFaq = topicFaqRepository.findByIdAndDeletedFlag(id,
                EntitiesConstant.DeleteFlag.ACTIVE);
        if (!topicFaq.isPresent()) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, Faq, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.FAQS, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        return topicFaq.get();
    }

    @Override
    @Transactional
    public void requestApprove(Long id) {
        TopicFaq topicFaq = getCurrentTopic(id);
        TopicFaqDraft topicFaqDraft = findDraftByFaqId(id);

        boolean checkApproveStatus;
        if (Objects.nonNull(topicFaqDraft)) {
            checkApproveStatus = ApproveStatusEnum.UNAPPROVED.equals(topicFaqDraft.getApprove())
                    || ApproveStatusEnum.REJECTED.equals(topicFaqDraft.getApprove());
        } else {
            checkApproveStatus = ApproveStatusEnum.UNAPPROVED.equals(topicFaq.getApprove())
                    || ApproveStatusEnum.REJECTED.equals(topicFaq.getApprove());
        }
        if (checkApproveStatus) {
            if (Objects.nonNull(topicFaqDraft)) {
                topicFaqDraft.setApprove(ApproveStatusEnum.AWAITING_APPROVAL);
                topicFaqDraft.setModifiedBy(AuthUtil.getCurrentUser().getId());
            } else {
                topicFaq.setApprove(ApproveStatusEnum.AWAITING_APPROVAL);
                topicFaq.setModifiedBy(AuthUtil.getCurrentUser().getId());
            }
        } else {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.STATUS,
                    MessageKeyConstant.Topic.TOPIC_APPROVE_STATUS_CAN_NOT_CHANGE);
        }
        // Thông báo firebase
        notifyUtil(evn.getProperty(NotifyConst.TP01_CONTENT), topicFaq, evn.getProperty(NotifyConst.TP01_TITLE), EmailCodeEnum.TP01.getValue());
        notifyRequestApproveForAdmin(topicFaq);
        topicFaqRepository.save(topicFaq);
    }


    /**
     * Thong bao toi man hinh khi phe duyet
     */
    private void notifyUtil(String notifyContent, TopicFaq topicFaq, String notifyTitle, String notifyCode) {
        val notifyModel = NotificationDTO.builder()
                .title(notifyTitle)
                .body(String.format(notifyContent, topicFaq.getName()))
                .screenId(notifyCode)
                .userId(topicFaq.getCreatedBy())
                .portalType(PortalType.DEV.getType())
                .status(StatusEnum.INACTIVE.value)
                .createdAt(LocalDateTime.now())
                .objectId(topicFaq.getId())
                .build();
        NotifyUtil.sendNotify(notifyModel, notifyCode);
    }

    /**
     * Thông báo có service yêu cầu phê duyệt trên màn hình Admin
     */
    private void notifyRequestApproveForAdmin(TopicFaq topicFaq) {
        List<User> userAdmins = userRepository.getAllUserByRoleNameIn(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN
                .getValue(), RoleType.CUSTOMER_SUPPORT.getValue()));
        Optional<User> developer = userRepository.findByIdAndDeletedFlag(topicFaq.getCreatedBy(), 1);
        List<NotificationDTO> notifyDTOs = new ArrayList<>();
        userAdmins.forEach(user -> {
            val notifyModel = NotificationDTO.builder()
                    .title(evn.getProperty(NotifyConst.TP02_TITLE))
                    .body(String.format(evn.getProperty(NotifyConst.TP02_CONTENT), topicFaq.getName(), developer.get().getName()))
                    .screenId(EmailCodeEnum.TP02.getValue())
                    .userId(user.getId())
                    .portalType(PortalType.ADMIN.getType())
                    .status(StatusEnum.INACTIVE.value)
                    .createdAt(LocalDateTime.now())
                    .objectId(topicFaq.getId())
                    .build();
            notifyDTOs.add(notifyModel);
        });
        if (!CollectionUtils.isEmpty(notifyDTOs)) {
            NotifyUtil.sendNotify(notifyDTOs, EmailCodeEnum.TP02.getValue());
        }
    }

    private void validateSlugName(String slugName) {
        boolean existsBySlugName = topicFaqRepository.existsBySlugName(slugName.trim());
        if (existsBySlugName) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.DUPLICATE_SLUG_NAME, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.FAQS, ErrorKey.TopicFaq.SLUG_NAME,
                    (MessageKeyConstant.DUPLICATE_SLUG_NAME));
        }
    }

    private void validateName(String name) {
        boolean existsByName = topicFaqRepository.existsByName(name.trim());
        if (existsByName) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.DUPLICATE_NAME, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.FAQS, ErrorKey.TopicFaq.NAME,
                    (MessageKeyConstant.DUPLICATE_SLUG_NAME));
        }
    }

    private TopicFaq approveData(TopicFaqDraft topicFaqDraft, TopicFaq topicFaq) {
        topicFaq.setName(topicFaqDraft.getName());
        topicFaq.setCode(topicFaqDraft.getCode());
        topicFaq.setSlugName(topicFaqDraft.getSlugName());
        topicFaq.setDescription(topicFaqDraft.getDescription());
        topicFaq.setTopicType(topicFaqDraft.getTopicType());

        faqsQuestionRepository.deleteByTopicId(topicFaq.getId());
        List<FaqFaqsQuestion> faqsQuestions = faqsQuestionRepository.findAllByTopicDraftId(topicFaqDraft.getId());
        for (FaqFaqsQuestion faqsQuestion : faqsQuestions) {
            FaqFaqsQuestion faqsQuestion1 = new FaqFaqsQuestion();
            faqsQuestion1.setTopicId(topicFaq.getId());
            faqsQuestion1.setQuestionId(faqsQuestion.getQuestionId());
            faqsQuestion1.setCatalogId(faqsQuestion.getCatalogId());
            faqsQuestion1.setPriorityQuestion(faqsQuestion.getPriorityQuestion());
            faqsQuestion1.setPriorityCatalog(faqsQuestion.getPriorityCatalog());
            faqsQuestion1.setType(faqsQuestion.getType());
            faqsQuestionRepository.save(faqsQuestion1);
        }
        return topicFaq;
    }

    private TopicFaqDraft cloneData(TopicFaq topicFaq, TopicFaqDraft topicFaqDraft) {
        if (Objects.isNull(topicFaqDraft)) {
            topicFaqDraft = new TopicFaqDraft();
        }
        BeanUtils.copyProperties(topicFaq, topicFaqDraft);
        topicFaqDraft.setId(null);
        topicFaqDraft.setTopicId(topicFaq.getId());
        topicFaqDraft = faqDraftRepository.save(topicFaqDraft);

        List<FaqFaqsQuestion> faqsQuestions = faqsQuestionRepository.findAllByTopicId(topicFaq.getId());

        for (FaqFaqsQuestion faqsQuestion : faqsQuestions) {
            FaqFaqsQuestion faqsQuestion1 = new FaqFaqsQuestion();
            faqsQuestion1.setQuestionId(faqsQuestion.getQuestionId());
            faqsQuestion1.setCatalogId(faqsQuestion.getCatalogId());
            faqsQuestion1.setTopicDraftId(topicFaqDraft.getId());
            faqsQuestionRepository.save(faqsQuestion1);
        }
        return topicFaqDraft;
    }


    private void updateTopicDraft(FaqEditDTO paramDto, TopicFaqDraft topicFaqDraft) {

        topicFaqDraft.setName(paramDto.getName());
        topicFaqDraft.setCode(paramDto.getCode());
        topicFaqDraft.setStatus(paramDto.getStatus());
        topicFaqDraft.setSlugName(paramDto.getSlugName());
        topicFaqDraft.setModifiedBy(AuthUtil.getCurrentUser().getId());
        topicFaqDraft.setTopicType(paramDto.getTopicType());
        topicFaqDraft.setTopicId(paramDto.getId());
        topicFaqDraft.setDescription(paramDto.getDescription());

        List<FaqFaqsQuestion> faqsQuestions = faqsQuestionRepository.findAllByTopicDraftId(topicFaqDraft.getId());
        if (faqsQuestions != null && !faqsQuestions.isEmpty()) {
            faqsQuestionRepository.deleteAll(faqsQuestions);
        }
        for (ListCatalog listCatalog : paramDto.getCatalogs()) {
            if (listCatalog.getQuesId() == null) {
                for (ListQuestion listQuestion : listCatalog.getQuestions()) {
                    FaqFaqsQuestion faqsQuestionDraft = new FaqFaqsQuestion(null, listQuestion.getQuesId(), listCatalog.getCatalogId(), topicFaqDraft.getId(), listCatalog.getPriority(), listQuestion.getPriority(), 0);
                    faqsQuestionRepository.save(faqsQuestionDraft);
                }
                continue;
            }
            FaqFaqsQuestion faqsQuestionDraft = new FaqFaqsQuestion(null, listCatalog.getQuesId(), null, topicFaqDraft.getId(), listCatalog.getPriority(), null, 1);
            faqsQuestionRepository.save(faqsQuestionDraft);
        }
        faqDraftRepository.save(topicFaqDraft);
    }

    private void adminUpdateApproveStatus(TopicApproveDTO topicApproveDTO, TopicFaq topicFaq, Long id) {
        TopicFaqDraft topicDraft = findDraftByFaqId(id);
        boolean checkApproveStatus = ApproveStatusEnum.AWAITING_APPROVAL.equals(topicFaq.getApprove());
        if (Objects.nonNull(topicDraft)) {
            checkApproveStatus = ApproveStatusEnum.AWAITING_APPROVAL.equals(topicDraft.getApprove());
            topicDraft.setApprove(topicApproveDTO.getApproveStatus());
            topicDraft.setComment(topicApproveDTO.getComment());
            faqDraftRepository.save(topicDraft);
        } else {
            topicFaq.setApprove(topicApproveDTO.getApproveStatus());
            topicFaq.setComment(topicApproveDTO.getComment());
            topicFaqRepository.save(topicFaq);
        }
        if (checkApproveStatus) {
            switch (topicApproveDTO.getApproveStatus()) {
                case APPROVED:
                    topicFaq.setApproveAt(new Date());
                    topicFaq.setApproveBy(AuthUtil.getCurrentUser().getId());
                    if (Objects.nonNull(topicDraft)) {
                        approveData(topicDraft, topicFaq);
                    } else {
                        topicDraft = cloneData(topicFaq, null);
                    }
                    notifyUtil(evn.getProperty(NotifyConst.TP03_CONTENT), topicFaq, evn.getProperty(NotifyConst.TP03_TITLE), EmailCodeEnum.TP03.getValue());
                    break;
                case REJECTED:
                    notifyUtil(evn.getProperty(NotifyConst.TP04_CONTENT), topicFaq, evn.getProperty(NotifyConst.TP04_TITLE), EmailCodeEnum.TP04.getValue());
                    break;
                case UNAPPROVED:
                    break;
                default:
                    String message = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, Faq, LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.FAQS, ErrorKey.Services.STATUS,
                            MessageKeyConstant.Topic.TOPIC_APPROVE_STATUS_CAN_NOT_CHANGE);
            }
        } else {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.FAQS, ErrorKey.Services.STATUS,
                    MessageKeyConstant.Topic.TOPIC_APPROVE_STATUS_CAN_NOT_CHANGE);
        }
    }

    @Override
    @Transactional
    public void delete(Long id) {
        TopicFaq topicFaq = getCurrentTopic(id);
        TopicFaqDraft topicFaqDraft = findDraftByFaqId(id);
        if (topicFaqDraft != null) {
            List<FaqFaqsQuestion> faqsQuestions = faqsQuestionRepository.findAllByTopicDraftId(topicFaqDraft.getId());
            if (faqsQuestions != null && !faqsQuestions.isEmpty()) {
                faqsQuestionRepository.deleteAll(faqsQuestions);
            }
            faqDraftRepository.delete(topicFaqDraft);
        }
        List<FaqFaqsQuestion> faqsQuestions = faqsQuestionRepository.findAllByTopicId(topicFaq.getId());
        if (faqsQuestions != null && !faqsQuestions.isEmpty()) {
            faqsQuestionRepository.deleteAll(faqsQuestions);
        }
        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByTopicId(id);
        if (faqsServices != null && !faqsServices.isEmpty()) {
            faqsServiceRepository.deleteAll(faqsServices);
        }
        topicFaqRepository.delete(topicFaq);
    }

//    @Override
//    @Transactional
//    public void updateTopicService(Long idService, Integer objectType, List<ServiceTopicUpdateDTO> topicUpdateDTOs) {
//        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByServiceIdAndObjectTypeOrderByPriorityAsc(idService, objectType);
//        faqsServiceRepository.deleteAll(faqsServices);
//        List<FaqFaqsService> faqFaqsServices = new ArrayList<>();
//        for (ServiceTopicUpdateDTO dto : topicUpdateDTOs) {
//            FaqFaqsService faqFaqsService = new FaqFaqsService(dto.getId(), idService, objectType, dto.getType(), dto.getPriority(),
//                    dto.getCatalogId(), dto.getQuesId(), dto.getCatalogStatus(), dto.getQuesStatus());
//            faqFaqsServices.add(faqFaqsService);
//        }
//        faqsServiceRepository.saveAll(faqFaqsServices);
//    }

    @Override
    @Transactional
    public void updateTopicService(Long objectId, Integer objectType, List<ServiceTopicUpdateRequest> topicUpdateDTOs) {
        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByObjectIdAndObjectTypeOrderByPriorityAsc(objectId, objectType);
        faqsServiceRepository.deleteAll(faqsServices);
        List<FaqFaqsService> faqFaqsServices = new ArrayList<>();
        topicUpdateDTOs.forEach(t -> {
            if (t.getId() != null) {
                if(t.getCatalogs().isEmpty()) {
                    FaqFaqsService faqFaqsService = new FaqFaqsService(t.getId(), objectId, null, objectType, t.getType(), t.getPriority(),
                            null, null, null, null);
                    faqFaqsServices.add(faqFaqsService);
                } else {
                    t.getCatalogs().forEach(c -> {
                        if (c.getCatalogId() != null) {
                            List<Long> questIds = new ArrayList<>();
                            c.getQuestions().forEach(q -> {
                                if (!questIds.contains(q.getQuesId())) {
                                    questIds.add(q.getQuesId());
                                    FaqFaqsService faqFaqsService = new FaqFaqsService(t.getId(), objectId, null, objectType, t.getType(), t.getPriority(),
                                            c.getCatalogId(), q.getQuesId(), c.getStatus(), q.getStatus());
                                    faqFaqsServices.add(faqFaqsService);
                                }
                            });
                        } else {
                            FaqFaqsService faqFaqsService = new FaqFaqsService(t.getId(), objectId, null, objectType, t.getType(), t.getPriority(),
                                    0L, c.getQuestId(), c.getStatus(), c.getStatus());
                            faqFaqsServices.add(faqFaqsService);
                        }
                    });
                }

            }
        });
        faqsServiceRepository.saveAll(faqFaqsServices);
    }

    @Override
    public List<TopicDetailDTO> getLstTopic(Long id, Integer objectType) {
        List<IGetTopicDTO> topics = faqsServiceRepository.getTopicSolutionPackage(objectType, id);
        List<TopicDetailDTO> topicDetailDTOS = new ArrayList<>();
        topics.forEach(item -> {
            TopicDetailDTO topic = new TopicDetailDTO();
            topic.setId(item.getId());
            topic.setName(item.getName());
            topic.setCatalogs(item.getLstCatalog());
            topicDetailDTOS.add(topic);
        });
        return topicDetailDTOS;
    }

    @Override
    public void updateTopicProductSolution(Long objectDraftId, Integer objectType, List<ServiceTopicUpdateRequest> topicUpdateDTOs) {
        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByObjectDraftIdAndObjectTypeAndObjectIdIsNull(objectDraftId, objectType);
        faqsServiceRepository.deleteAll(faqsServices);
        List<FaqFaqsService> faqFaqsServices = new ArrayList<>();
        topicUpdateDTOs.forEach(t -> {
            if (t.getId() != null) {
                if(t.getCatalogs().isEmpty()) {
                    FaqFaqsService faqFaqsService = new FaqFaqsService(t.getId(), objectDraftId, objectType, t.getType(), t.getPriority());
                    faqFaqsServices.add(faqFaqsService);
                } else {
                    t.getCatalogs().forEach(c -> {
                        if (c.getCatalogId() != null) {
                            List<Long> questIds = new ArrayList<>();
                            c.getQuestions().forEach(q -> {
                                if (!questIds.contains(q.getQuesId())) {
                                    questIds.add(q.getQuesId());
                                    FaqFaqsService faqFaqsService = new FaqFaqsService(t.getId(), null, objectDraftId, objectType, t.getType(), t.getPriority(),
                                        c.getCatalogId(), q.getQuesId(), c.getStatus(), q.getStatus());
                                    faqFaqsServices.add(faqFaqsService);
                                }
                            });
                        } else {
                            FaqFaqsService faqFaqsService = new FaqFaqsService(t.getId(), null, objectDraftId, objectType, t.getType(), t.getPriority(),
                                0L, c.getQuestId(), c.getStatus(), c.getStatus());
                            faqFaqsServices.add(faqFaqsService);
                        }
                    });
                }

            }
        });
        faqsServiceRepository.saveAll(faqFaqsServices);
    }

    @Override
    public void approvedTopicProductSolution(Long objectDraftId, Long id, Integer objectType) {
        // lấy danh sách theo draftId và objectType
        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByObjectDraftIdAndObjectTypeAndObjectIdIsNull(objectDraftId, objectType);
        // tạo ra bản chính
        List<FaqFaqsService> approvedFaqs =  faqsServices.stream().map(draft -> {
            FaqFaqsService mapping = new FaqFaqsService();
            BeanUtils.copyProperties(draft, mapping);
            mapping.setObjectId(id);
            return mapping;
        }).collect(Collectors.toList());

        // lưu vào db
        faqsServiceRepository.saveAll(approvedFaqs);
    }

    @Override
    public List<FaqServiceDetailResDTO> getListTopicByService(Long idService, Integer objectType) {
        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByObjectIdAndObjectTypeOrderByPriorityAsc(idService, objectType);
        List<FaqServiceDetailResDTO> resp = new ArrayList<>();
        if (faqsServices != null && !faqsServices.isEmpty()) {
            Map<Long, List<FaqFaqsService>> mapTopicId = faqsServices.stream()
                    .collect(Collectors.groupingBy(FaqFaqsService::getTopicId));
            Set<Map.Entry<Long, List<FaqFaqsService>>> entrySet = mapTopicId.entrySet();
            for (Map.Entry<Long, List<FaqFaqsService>> entry : entrySet) {
                Optional<TopicFaq> faq = topicFaqRepository.getById(entry.getKey());
                if (!faq.isPresent()) {
                    continue;
                }
                TopicFaq topicFaq = faq.get();
                FaqServiceDetailResDTO faqDetailResDTO = new FaqServiceDetailResDTO();
                BeanUtils.copyProperties(topicFaq, faqDetailResDTO);
                FaqFaqsService faqFaqsService = entry.getValue().get(0);
                faqDetailResDTO.setPriority(faqFaqsService.getPriority());
                faqDetailResDTO.setTopicServiceType(faqFaqsService.getType());
                List<FaqFaqsService> faqFaqsServices = entry.getValue();
                if(faqFaqsServices.size() == 1){
                    if(Objects.isNull(faqFaqsServices.get(0).getCatalogId())){
                        resp.add(faqDetailResDTO);
                        continue;
                    }
                }
                Map<Long, List<FaqFaqsService>> mapCatalog = faqFaqsServices.stream()
                        .collect(Collectors.groupingBy(FaqFaqsService::getCatalogId));
                List<ListCatalogDTO> listCatalogDTOS = faqsQuestionRepository.getCatalogByIdIn(faqFaqsServices.stream().map(FaqFaqsService::getCatalogId).collect(Collectors.toList()), topicFaq.getId(), 0);
                List<ListCatalogServiceRes> catalogs = new ArrayList<>();
                for (ListCatalogDTO listCatalog : listCatalogDTOS) {
                    ListCatalogServiceRes catalogRes = new ListCatalogServiceRes();
                    catalogRes.setCatalogId(listCatalog.getCatalogId());
                    catalogRes.setCatalogName(listCatalog.getCatalogName());
                    catalogRes.setStatus(mapCatalog.get(listCatalog.getCatalogId()).get(0).getCatalogStatus());
                    catalogRes.setPriority(listCatalog.getPriority());
                    catalogRes.setType(listCatalog.getType());
                    List<FaqFaqsService> listQuest = mapCatalog.get(listCatalog.getCatalogId());
                    List<ListCatalogDTO> listCatalogQuestion = faqsQuestionRepository.getQuestionByIdIn(listQuest.stream().map(FaqFaqsService::getQuestionId).collect(Collectors.toList()), topicFaq.getId(), listCatalog.getCatalogId(), 0);
                    List<ListQuestionService> questions = new ArrayList<>();
                    Map<Long, FaqFaqsService> topicMap = listQuest.stream()
                            .collect(Collectors.toMap(FaqFaqsService::getQuestionId, Function.identity()));
                    for (ListCatalogDTO question : listCatalogQuestion) {
                        ListQuestionService listQuestionService = new ListQuestionService();
                        listQuestionService.setQuesId(question.getQuestId());
                        listQuestionService.setQuesName(question.getQuestName());
                        listQuestionService.setQuesAnswer(question.getQuesAnswer());
                        listQuestionService.setPriority(question.getPriority());
                        listQuestionService.setStatus(topicMap.get(question.getQuestId()).getQuestionStatus());
                        questions.add(listQuestionService);
                    }
                    catalogRes.setQuestions(questions);
                    catalogs.add(catalogRes);
                }
                // TODO: 1/9/2023  xử lý question lẻ
                List<FaqFaqsService> listQuestionNotCatalog = mapCatalog.get(0L);
                if (listQuestionNotCatalog != null && !listQuestionNotCatalog.isEmpty()) {
                    Map<Long, FaqFaqsService> topicMap = listQuestionNotCatalog.stream()
                            .collect(Collectors.toMap(FaqFaqsService::getQuestionId, Function.identity()));
                    List<ListCatalogDTO> listQuestion = faqsQuestionRepository.getQuestionByIdInNotCatalog(listQuestionNotCatalog.stream().map(FaqFaqsService::getQuestionId).collect(Collectors.toList()), topicFaq.getId(), 0);
                    for (ListCatalogDTO question : listQuestion) {
                        ListCatalogServiceRes catalogRes = new ListCatalogServiceRes();
                        catalogRes.setCatalogId(null);
                        catalogRes.setCatalogName(null);
                        catalogRes.setQuestId(question.getQuestId());
                        catalogRes.setQuestName(question.getQuestName());
                        catalogRes.setQuestAnswer(question.getQuesAnswer());
                        catalogRes.setStatus(topicMap.get(question.getQuestId()).getCatalogStatus());
                        catalogRes.setPriority(question.getPriority());
                        catalogRes.setType(question.getType());
                        catalogs.add(catalogRes);

                    }
                }
                faqDetailResDTO.setCatalogs(catalogs);
                resp.add(faqDetailResDTO);
            }
        }
        resp.sort(Comparator.comparing(FaqServiceDetailResDTO::getPriority));
        return resp;
    }

//    @Override
//    public List<FaqDetailResDTO> getListTopicByServiceForSME(Long idService, Integer objectType) {
//        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByServiceIdAndObjectTypeOrderByPriorityAsc(idService, objectType);
//        List<FaqDetailResDTO> resp = new ArrayList<>();
//        for (FaqFaqsService faqFaqsService : faqsServices) {
//            TopicFaq topicFaq = topicFaqRepository.findFirstByIdAndApproveAndStatus(faqFaqsService.getTopicId(), ApproveStatusEnum.APPROVED, 1);
//            if (topicFaq != null) {
//                FaqDetailResDTO faqDetailResDTO = getFaqDraftBasicOrFaq(topicFaq, null, false);
//                faqDetailResDTO.setPriority(faqFaqsService.getPriority());
//                faqDetailResDTO.setTopicServiceType(faqFaqsService.getType());
//                resp.add(faqDetailResDTO);
//            }
//        }
//
//        return resp;
//    }

    @Override
    public List<FaqServiceDetailResDTO> getListTopicByServiceForSME(Long idService, Integer objectType) {
        List<FaqFaqsService> faqsServices = faqsServiceRepository.findAllByObjectIdAndObjectTypeOrderByPriorityAsc(idService, objectType);
        List<FaqServiceDetailResDTO> resp = new ArrayList<>();
        if (faqsServices != null && !faqsServices.isEmpty()) {
            Map<Long, List<FaqFaqsService>> mapTopicId = faqsServices.stream()
                    .collect(Collectors.groupingBy(FaqFaqsService::getTopicId));
            Set<Map.Entry<Long, List<FaqFaqsService>>> entrySet = mapTopicId.entrySet();
            for (Map.Entry<Long, List<FaqFaqsService>> entry : entrySet) {
                Optional<TopicFaq> faq = topicFaqRepository.getById(entry.getKey());
                if (!faq.isPresent()) {
                    continue;
                }
                TopicFaq topicFaq = faq.get();
                if (topicFaq.getApprove().equals(ApproveStatusEnum.APPROVED) && topicFaq.getStatus() == 1) {
                    FaqServiceDetailResDTO faqDetailResDTO = new FaqServiceDetailResDTO();
                    BeanUtils.copyProperties(topicFaq, faqDetailResDTO);
                    FaqFaqsService faqFaqsService = entry.getValue().get(0);
                    faqDetailResDTO.setPriority(faqFaqsService.getPriority());
                    faqDetailResDTO.setTopicServiceType(faqFaqsService.getType());
                    List<FaqFaqsService> faqFaqsServices = entry.getValue();
                    if(faqFaqsServices.size() == 1){
                        if(Objects.isNull(faqFaqsServices.get(0).getCatalogId())){
                            resp.add(faqDetailResDTO);
                            continue;
                        }
                    }
                    Map<Long, List<FaqFaqsService>> mapCatalog = faqFaqsServices.stream()
                            .collect(Collectors.groupingBy(FaqFaqsService::getCatalogId));
                    List<ListCatalogDTO> listCatalogDTOS = faqsQuestionRepository.getCatalogByIdIn(faqFaqsServices.stream().map(FaqFaqsService::getCatalogId).collect(Collectors.toList()), topicFaq.getId(), 0);
                    List<ListCatalogServiceRes> catalogs = new ArrayList<>();
                    for (ListCatalogDTO listCatalog : listCatalogDTOS) {
                        if (mapCatalog.get(listCatalog.getCatalogId()).get(0).getCatalogStatus() == 0) {
                            continue;
                        }
                        ListCatalogServiceRes catalogRes = new ListCatalogServiceRes();
                        catalogRes.setCatalogId(listCatalog.getCatalogId());
                        catalogRes.setCatalogName(listCatalog.getCatalogName());
                        catalogRes.setStatus(mapCatalog.get(listCatalog.getCatalogId()).get(0).getCatalogStatus());
                        catalogRes.setPriority(listCatalog.getPriority());
                        List<FaqFaqsService> listQuest = mapCatalog.get(listCatalog.getCatalogId());
                        List<ListCatalogDTO> listCatalogQuestion = faqsQuestionRepository.getQuestionByIdIn(listQuest.stream().map(FaqFaqsService::getQuestionId).collect(Collectors.toList()), topicFaq.getId(), listCatalog.getCatalogId(), 0);
                        List<ListQuestionService> questions = new ArrayList<>();
                        Map<Long, FaqFaqsService> topicMap = listQuest.stream()
                                .collect(Collectors.toMap(FaqFaqsService::getQuestionId, Function.identity()));
                        for (ListCatalogDTO question : listCatalogQuestion) {
                            if (topicMap.get(question.getQuestId()).getQuestionStatus() == 0) {
                                continue;
                            }
                            ListQuestionService listQuestionService = new ListQuestionService();
                            listQuestionService.setQuesId(question.getQuestId());
                            listQuestionService.setQuesName(question.getQuestName());
                            listQuestionService.setQuesAnswer(question.getQuesAnswer());
                            listQuestionService.setPriority(question.getPriority());
                            listQuestionService.setStatus(topicMap.get(question.getQuestId()).getQuestionStatus());
                            questions.add(listQuestionService);
                        }
                        catalogRes.setQuestions(questions);
                        catalogs.add(catalogRes);
                    }
                    // TODO: 1/9/2023  xử lý question lẻ
                    List<FaqFaqsService> listQuestionNotCatalog = mapCatalog.get(0L);
                    if (listQuestionNotCatalog != null && !listQuestionNotCatalog.isEmpty()) {
                        Map<Long, FaqFaqsService> topicMap = listQuestionNotCatalog.stream()
                                .collect(Collectors.toMap(FaqFaqsService::getQuestionId, Function.identity()));
                        List<ListCatalogDTO> listQuestion = faqsQuestionRepository.getQuestionByIdInNotCatalog(listQuestionNotCatalog.stream().map(FaqFaqsService::getQuestionId).collect(Collectors.toList()), topicFaq.getId(), 0);
                        for (ListCatalogDTO question : listQuestion) {
                            if (topicMap.get(question.getQuestId()).getCatalogStatus() == 0) {
                                continue;
                            }
                            ListCatalogServiceRes catalogRes = new ListCatalogServiceRes();
                            catalogRes.setCatalogId(null);
                            catalogRes.setCatalogName(null);
                            catalogRes.setQuestId(question.getQuestId());
                            catalogRes.setQuestName(question.getQuestName());
                            catalogRes.setQuestAnswer(question.getQuesAnswer());
                            catalogRes.setStatus(topicMap.get(question.getQuestId()).getCatalogStatus());
                            catalogRes.setPriority(question.getPriority());
                            catalogs.add(catalogRes);
                        }
                    }
                    faqDetailResDTO.setCatalogs(catalogs);
                    resp.add(faqDetailResDTO);
                }
            }
        }
        resp.sort(Comparator.comparing(FaqServiceDetailResDTO::getPriority));
        return resp;
    }

    @Override
    @Transactional
    public void updateStatus(Long id, ServiceStatusEnum status) {
        if (status != ServiceStatusEnum.VISIBLE && status != ServiceStatusEnum.INVISIBLE) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, Faq, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        topicFaqRepository.changeStatus(id, status.value);
        topicFaqRepository.changeStatusDraft(id, status.value);
    }

    @Override
    public Page<ListTopicService> searchTopicAndCatalog(String topicName, String catalogName, Integer topicType, PortalType portal, Pageable pageable) {
        Long userId = AuthUtil.getCurrentParentId();
        if (portal == PortalType.ADMIN) {
            userId = -1L;
        }
        String escapedTopicName = SqlUtils.optimizeSearchLike(topicName);
        String escapedCatalogName = SqlUtils.optimizeSearchLike(catalogName);
        Page<ListTopicIdDTO> allTopicId = topicFaqRepository.getAllTopicId(escapedTopicName, escapedCatalogName, topicType, userId, pageable);
        List<TopicFaq> topics = topicFaqRepository.findAllByIdIn(allTopicId.getContent().stream().map(ListTopicIdDTO::getTopicId).collect(Collectors.toList()));
        List<ListTopicServiceDTO> listTopicService = topicFaqRepository.getAllByTopicId(allTopicId.getContent().stream().map(ListTopicIdDTO::getTopicId).collect(Collectors.toList()), escapedCatalogName, topicType, userId);

        Map<Long, List<ListTopicServiceDTO>> mapTopicId = listTopicService.stream()
                .collect(Collectors.groupingBy(ListTopicServiceDTO::getTopicId));
        Map<Long, TopicFaq> topicMap = topics.stream()
                .collect(Collectors.toMap(TopicFaq::getId, Function.identity()));
        return allTopicId.map(item -> convertToListTopicServiceDTO(item.getTopicId(), mapTopicId.get(item.getTopicId()), topicMap.get(item.getTopicId())));
    }

    private ListTopicService convertToListTopicServiceDTO(Long topicId, List<ListTopicServiceDTO> listTopicService, TopicFaq item) {
        ListTopicService resDTO = new ListTopicService();
        if (listTopicService == null || listTopicService.isEmpty()) {
            resDTO.setTopicId(item.getId());
            resDTO.setTopicType(item.getTopicType());
            resDTO.setTopicName(item.getName());
            return resDTO;
        }
        ListTopicServiceDTO dto = listTopicService.get(0);
        resDTO.setTopicId(topicId);
        BeanUtils.copyProperties(dto, resDTO);
        Map<Long, List<ListTopicServiceDTO>> catalogMap = listTopicService.stream()
                .collect(Collectors.groupingBy(ListTopicServiceDTO::getCatalogId));
        Set<Map.Entry<Long, List<ListTopicServiceDTO>>> entrySet = catalogMap.entrySet();
        for (Map.Entry<Long, List<ListTopicServiceDTO>> entry : entrySet) {
            ListTopicService.ListCatalog listCatalog = new ListTopicService.ListCatalog();
            ListTopicServiceDTO listTopicServiceDTO = entry.getValue().get(0);
            listCatalog.setCatalogId(listTopicServiceDTO.getCatalogId());
            listCatalog.setCatalogName(listTopicServiceDTO.getCatalogName());
            listCatalog.setPriority(listTopicServiceDTO.getPriority());
            resDTO.getCatalogs().add(listCatalog);
        }

        return resDTO;
    }
//
//    @Override
//    public Page<ListTopicService> searchTopicAndCatalog(String topicName, String catalogName, Integer topicType, PortalType portal, Pageable pageable) {
//        Long userId = AuthUtil.getCurrentParentId();
//        if (portal == PortalType.ADMIN) {
//            userId = -1L;
//        }
//        Page<ListTopicIdDTO> allTopicId = topicFaqRepository.getAllTopicId(topicName, catalogName, topicType, userId, pageable);
//
//        List<ListTopicServiceDTO> listTopicService = topicFaqRepository.getAllByTopicId(allTopicId.getContent().stream().map(ListTopicIdDTO::getTopicId).collect(Collectors.toList()), catalogName, topicType, userId);
//        List<ListTopicServiceDTO> listTopicService1 = topicFaqRepository.getAllQuestionByTopicId(allTopicId.getContent().stream().map(ListTopicIdDTO::getTopicId).collect(Collectors.toList()), topicType, userId);
//        listTopicService.addAll(listTopicService1);
//        Map<Long, List<ListTopicServiceDTO>> mapTopicId = listTopicService.stream()
//                .collect(Collectors.groupingBy(ListTopicServiceDTO::getTopicId));
//
//        return allTopicId.map(item -> convertToListTopicServiceDTO(item.getTopicId(), mapTopicId.get(item.getTopicId())));
//    }
//
//    private ListTopicService convertToListTopicServiceDTO(Long topicId, List<ListTopicServiceDTO> listTopicService) {
//        ListTopicService resDTO = new ListTopicService();
//        if (listTopicService.isEmpty()) {
//            return resDTO;
//        }
//        ListTopicServiceDTO dto = listTopicService.get(0);
//        resDTO.setTopicId(topicId);
//        BeanUtils.copyProperties(dto, resDTO);
//        Map<Long, Map<Long, List<ListTopicServiceDTO>>> catalogMap = listTopicService.stream()
//                .collect(Collectors.groupingBy(ListTopicServiceDTO::getCatalogId, Collectors.groupingBy(ListTopicServiceDTO::getQuestId)));
//        Set<Map.Entry<Long, Map<Long, List<ListTopicServiceDTO>>>> entrySet = catalogMap.entrySet();
//        for (Map.Entry<Long, Map<Long, List<ListTopicServiceDTO>>> entry : entrySet) {
//            ListTopicService.ListCatalog listCatalog = new ListTopicService.ListCatalog();
//            Map<Long, List<ListTopicServiceDTO>> listMap = entry.getValue();
//            Set<Map.Entry<Long, List<ListTopicServiceDTO>>> entrySet1 = listMap.entrySet();
//            for (Map.Entry<Long, List<ListTopicServiceDTO>> entry1 : entrySet1) {
//                ListTopicServiceDTO listTopicServiceDTO = (ListTopicServiceDTO) entry1.getValue().get(0);
//                listCatalog.setCatalogId(listTopicServiceDTO.getCatalogId());
//                listCatalog.setCatalogName(listTopicServiceDTO.getCatalogName());
//                listCatalog.setPriority(listTopicServiceDTO.getPriority());
//                listCatalog.setQuestId(listTopicServiceDTO.getQuestId());
//                listCatalog.setQuestName(listTopicServiceDTO.getQuestName());
//                listCatalog.setType(listTopicServiceDTO.getType());
//                resDTO.getCatalogs().add(listCatalog);
//            }
//        }
//
//        return resDTO;
//    }
}
