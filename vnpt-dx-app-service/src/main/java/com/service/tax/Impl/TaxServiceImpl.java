package com.service.tax.Impl;

import com.constant.EntitiesConstant;
import com.onedx.common.exception.MessageKeyConstant;
import com.constant.enums.coupon.IsVATEnum;
import com.constant.enums.tax.AllowDeletedEnum;
import com.constant.enums.tax.TaxStatusEnum;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.dto.tax.TaxDTO;
import com.dto.tax.TaxListDTO;
import com.dto.tax.TaxReqDTO;
import com.dto.tax.TaxResDTO;
import com.dto.tax.TaxsDTO;
import com.entity.tax.Tax;
import com.enums.DisplayStatus;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.repository.tax.TaxRepository;
import com.service.tax.TaxService;
import com.util.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 13/5/2021
 */
@Service
@Transactional
public class TaxServiceImpl implements TaxService {

    @Autowired
    private TaxRepository taxRepository;

    @Autowired
    MessageSource messageSource;

    final String[] errorKeyTax = {"tax"};

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createTax(TaxDTO taxDTO) {
        Tax tax = new Tax();
        tax.setName(taxDTO.getName());
        tax.setStatus(taxDTO.getStatus() == null ? 1 : taxDTO.getStatus().value);
        tax.setDescription(taxDTO.getDescription());
        tax.setCreatedBy(AuthUtil.getCurrentUser().getId());
        tax.setIsVAT(IsVATEnum.NO.value);
        checkExistTax(tax);
        return taxRepository.save(tax).getId();
    }

    /**
     * Kiểm tra xem thuế đã tồn tại trong Database hay chưa
     *
     */
    private void checkExistTax(Tax tax) {
        String messageExist = messageSource.getMessage(MessageKeyConstant.DATA_EXISTS, this.errorKeyTax,
            LocaleContextHolder.getLocale());
        // Trường hợp truyền Id = null thì check thuế tồn tại khi tạo mới
        if (tax.getId() == null) {
            Optional<Tax> taxOption = taxRepository.findByName(tax.getName().toUpperCase());
            if (taxOption.isPresent()) {
                throw new BadRequestException(messageExist, Resources.TAX, ErrorKey.ID,
                    MessageKeyConstant.DATA_EXISTS);
            }
        } else {
            Optional<Tax> taxOption = taxRepository
                .findByNameAndIdNot(tax.getName().toUpperCase(), tax.getId());
            if (taxOption.isPresent()) {
                throw new BadRequestException(messageExist, Resources.TAX, ErrorKey.ID,
                    MessageKeyConstant.DATA_EXISTS);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long updateTax(TaxDTO taxDTO, Long id) {
        Tax tax = taxRepository.findByIdNotDelete(id).orElseThrow(() -> {
            String messageNotFound = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, this.errorKeyTax, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.TAX, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });
        // VAT là thuế mặc định trong hệ thống ko cho đổi tên
        if (Objects.equals(tax.getIsVAT(), IsVATEnum.YES.value) && !Objects
            .equals(tax.getName(), taxDTO.getName())) {
            String messageNotChange = messageSource
                .getMessage(MessageKeyConstant.CANNOT_CHANGE, this.errorKeyTax,
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(messageNotChange, Resources.TAX, ErrorKey.NAME,
                MessageKeyConstant.CANNOT_CHANGE);
        }
        tax.setName(taxDTO.getName());
        tax.setStatus(taxDTO.getStatus() == null ? 1 : taxDTO.getStatus().value);
        tax.setDescription(taxDTO.getDescription());
        tax.setModifiedBy(AuthUtil.getCurrentUser().getId());
        checkExistTax(tax);
        return taxRepository.save(tax).getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTax(Long id) {
        Tax tax = taxRepository.findByIdNotDelete(id).orElseThrow(() -> {
            String messageNotFound = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, this.errorKeyTax, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.TAX, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });
        if (!checkTaxAllowDelete(tax)) {
            String messageNotDelete = messageSource
                .getMessage(MessageKeyConstant.CANNOT_DELETE, this.errorKeyTax,
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(messageNotDelete, Resources.TAX, ErrorKey.ID,
                MessageKeyConstant.CANNOT_DELETE);
        }
        tax.setModifiedBy(AuthUtil.getCurrentUser().getId());
        tax.setDeletedFlag(0);
        taxRepository.save(tax);
    }

    @Override
    public Page<TaxsDTO> getAllTax(TaxReqDTO taxReqDTO, Pageable pageable) {
        taxReqDTO.setName(Objects.isNull(taxReqDTO.getName()) ? "" : taxReqDTO.getName());
        taxReqDTO.setDisplayed(Objects.isNull(taxReqDTO.getDisplayed()) ? TaxStatusEnum.UNSET : taxReqDTO.getDisplayed());
        return taxRepository.getAllTax(taxReqDTO, pageable);
    }

    /**
     * Kiểm tra xem thuế có được phép xóa hay không
     *
     */
    private boolean checkTaxAllowDelete(Tax tax) {
        // Không cho phép xóa thuế VAT và thuế đã đc gán cho dịch vụ và addon
         return !Objects.equals(tax.getIsVAT(), IsVATEnum.YES.value)
             && !taxRepository.checkTaxUsed(tax.getId()) ;
    }

    @Transactional(readOnly = true)
    @Override
    public TaxResDTO checkAllowDelete(Long id) {
        Tax tax = taxRepository.findByIdNotDelete(id).orElseThrow(() -> {
            String messageNotFound = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, this.errorKeyTax, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.TAX, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });
        TaxResDTO taxResDTO = new TaxResDTO();
        taxResDTO.setId(tax.getId());
        taxResDTO.setIsDeleted(checkTaxAllowDelete(tax) ? AllowDeletedEnum.YES.name()
            : AllowDeletedEnum.NO.name());
        return taxResDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponseDTO updateStatus(Long id, DisplayStatus status) {
        Tax tax = taxRepository
                .findByIdAndDeletedFlag(id, EntitiesConstant.DeleteFlag.ACTIVE)
                .orElseThrow(() -> {
                    String messageNotFound = messageSource.getMessage(
                            MessageKeyConstant.NOT_FOUND, this.errorKeyTax,
                            LocaleContextHolder.getLocale());
                    return new ResourceNotFoundException(messageNotFound,
                            Resources.TAX, ErrorKey.ID,
                            MessageKeyConstant.NOT_FOUND);
                });
        tax.setStatus(status.value);
        tax.setModifiedAt(new Date());
        taxRepository.save(tax);
        return new BaseResponseDTO(id);
    }

    @Override
    public List<TaxListDTO> getListTax() {
        return taxRepository.getListTax();
    }

    @Override
    public List<TaxResDTO> getTaxDropList(String name) {
        return taxRepository.getTaxDropList(name);
    }

    @Override
    public void deleteTaxByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        taxRepository.deleteTaxByIds(ids, AuthUtil.getCurrentUser().getId());
    }
}
