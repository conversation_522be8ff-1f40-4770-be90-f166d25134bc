package com.service.product_solutions.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.common.Constant.SOLUTION_PACKAGE;
import com.constant.SeoTypeCodeConstant;
import com.dto.product_solustions.ApprovedReqDTO;
import com.dto.product_solustions.IGetListPackageReqDTO;
import com.dto.product_solustions.IGetListSolutionPopup;
import com.dto.product_solustions.IGetListSolutionReqDTO;
import com.dto.product_solustions.IGetPackItemDTO;
import com.dto.product_solustions.IGetSolutionDetailDTO;
import com.dto.product_solustions.PackageListResDTO;
import com.dto.product_solustions.PackageListResDTO.ProductAddonDTO;
import com.dto.product_solustions.SolutionCreateDTO;
import com.dto.product_solustions.SolutionDetailResDTO;
import com.dto.product_solustions.SolutionDetailSMEResDTO;
import com.dto.product_solustions.SolutionListResDTO;
import com.dto.product_solustions.ValueDTO;
import com.entity.categories.Category;
import com.entity.product_solutions.ProductSolution;
import com.entity.product_solutions.ProductSolutionDraft;
import com.entity.product_solutions.SolutionDomain;
import com.entity.seo.Seo;
import com.enums.ApproveStatusEnum;
import com.enums.DisplayStatus;
import com.enums.ServiceViewEnum.ServiceViewTypeEnum;
import com.enums.product_solutions.ObjectTypeEnum;
import com.exception.ErrorKey;
import com.exception.ErrorKey.ProductSolutionError;
import com.exception.Resources;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.values.ExceptionConstants;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant.ProductSolutions;
import com.repository.categories.CategoryRepository;
import com.repository.feature.FeatureRepository;
import com.repository.product_solutions.PackageRepository;
import com.repository.product_solutions.ProductSolutionDraftRepository;
import com.repository.product_solutions.ProductSolutionRepository;
import com.repository.product_solutions.SectionRepository;
import com.repository.product_solutions.SolutionDomainRepository;
import com.repository.seo.SeoRepository;
import com.repository.subscriptions.SubscriptionMetadataRepository;
import com.service.faq.TopicFaqService;
import com.service.feature.impl.FeatureServiceImpl;
import com.service.product_solutions.FeatureMappingService;
import com.service.product_solutions.ProductApprovalHistoryService;
import com.service.product_solutions.ProductSolutionService;
import com.service.product_solutions.SectionMappingService;
import com.service.product_solutions.SolutionPackageService;
import com.service.rating.ServiceViewService;
import com.service.seo.SeoService;
import com.service.suggestions.SuggestionMappingService;
import com.util.AuthUtil;
import com.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSolutionServiceImpl implements ProductSolutionService {

    private final ProductSolutionDraftRepository productSolutionDraftRepository;
    private final ProductSolutionRepository productSolutionRepository;
    private final SolutionPackageService solutionPackageService;
    private final PackageRepository packageRepository;
    private final TopicFaqService topicFaqService;
    private final SuggestionMappingService suggestionMappingService;
    private final SectionMappingService sectionMappingService;
    private final FeatureMappingService featureMappingService;
    private final FeatureServiceImpl featureService;
    private final SeoService seoService;
    private final SolutionDomainRepository solutionDomainRepository;
    private final SubscriptionMetadataRepository subscriptionMetadataRepository;
    private final CategoryRepository categoryRepository;
    private final FeatureRepository featureRepository;
    private final SectionRepository sectionRepository;
    private final SeoRepository seoRepository;
    private final ServiceViewService serviceViewService;
    private final ExceptionFactory exceptionFactory;
    private final ProductApprovalHistoryService productApprovalHistoryService;

    private static final String CAU_HINH_GIAP_PHAP = "CAU_HINH_GIAP_PHAP";

    @Override
    public Page<SolutionListResDTO> listSolutions(String value, Integer isName, Integer isCode, List<Long> categoryIds,
        ApproveStatusEnum approveStatus, Integer displayStatus, CustomerTypeEnum customerType,
        List<Long> createdIds, List<Long> packageIds, List<Long> ids, Pageable pageable) {
        // TODO: Dev sẽ chỉ được xem và quản lý các Giải pháp do mình tạo

        if (Objects.isNull(categoryIds) || categoryIds.isEmpty()) {
            categoryIds = new ArrayList<>(Collections.singletonList(-1L));
        }
        if (Objects.isNull(createdIds) || createdIds.isEmpty()) {
            createdIds = new ArrayList<>(Collections.singletonList(-1L));
        }
        if (Objects.isNull(packageIds) || packageIds.isEmpty()) {
            packageIds = new ArrayList<>(Collections.singletonList(-1L));
        }

        Long userId = AuthUtil.getCurrentParentId();
        userId = AuthUtil.isDev(userId) ? userId : -1L;

        BigDecimal[] categoryId = categoryIds.stream()
                .sorted().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);

        // Lấy danh sách các giải pháp~
        Page<IGetListSolutionReqDTO> list = productSolutionDraftRepository.searchProductSolution(value, isName, isCode, categoryId,
            approveStatus.value, displayStatus,
            customerType.name(), createdIds, packageIds, userId, ids, pageable);
        Set<Long> packageIdValue = new HashSet<>();
        list.getContent().forEach(item -> packageIdValue.addAll(item.getPackageIdValue()));

        List<IGetListPackageReqDTO> listPackageReqDTOS = productSolutionDraftRepository.getListPackageByIds(packageIdValue);

        Map<Long, List<IGetListPackageReqDTO>> map = listPackageReqDTOS.stream()
            .collect(Collectors.groupingBy(IGetListPackageReqDTO::getSolutionDraftId));
        List<SolutionListResDTO> listResDTOPage = list.stream().map(solutionReqDTO -> {
            SolutionListResDTO dto = new SolutionListResDTO();
            dto.setId(solutionReqDTO.getId());
            dto.setName(solutionReqDTO.getName());
            dto.setCustomerTypes(solutionReqDTO.getCustomerTypeValue());
            dto.setApprovalStatus(ApproveStatusEnum.valueOf(solutionReqDTO.getApprovalStatus()).name());
            dto.setVisibility(DisplayStatus.valueOf(solutionReqDTO.getVisibility()));
            dto.setModifiedAt(solutionReqDTO.getModifiedAt());
            dto.setRevenue(solutionReqDTO.getRevenue());
            dto.setPromotionCount(solutionReqDTO.getPromotionCount());
            dto.setSolutionDomains(solutionReqDTO.getSolutionDomainValue());
            dto.setCategories(solutionReqDTO.getCategoriesValue());
            dto.setAvatarUrl(solutionReqDTO.getAvatarUrl());
            return dto;
        }).collect(Collectors.toList());
        for (SolutionListResDTO solutionListResDTO : listResDTOPage) {
            List<IGetListPackageReqDTO> packages = map.get(solutionListResDTO.getId());
            solutionListResDTO.setPackages(packages);
        }

        return new PageImpl<>(listResDTOPage, list.getPageable(), list.getTotalElements());
    }


    @Override
    @Transactional
    public Long createSolution(SolutionCreateDTO request) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentId = AuthUtil.getCurrentParentId();
        boolean isDev = AuthUtil.getPortalOfUserRoles().equals(PortalType.DEV);
        // Check trùng mã giải pháp
        validateCodeProductSolution(request.getCode(), -1L);
        // DEV chỉ tạo giải pháp từ các gói bundling mà mình tạo
        if (Objects.nonNull(request.getPackageIds()) && !request.getPackageIds().isEmpty() && AuthUtil.isDev()) {
            if (packageRepository.existsByIdInAndProviderIdNot(request.getPackageIds(), currentParentId)) {
                throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
            }
        }

        ProductSolutionDraft productSolutionDraft = new ProductSolutionDraft();
        BeanUtils.copyProperties(request, productSolutionDraft);
        if (isDev) {
            productSolutionDraft.setProviderId(currentParentId);
        } else {
            productSolutionDraft.setProviderId(currentUserId);
        }
        productSolutionDraft.setCreatedBy(currentUserId);
        productSolutionDraft.setModifiedBy(currentUserId);
        productSolutionDraft.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
        productSolutionDraft.setState(ApproveStatusEnum.UNAPPROVED.value);
        productSolutionDraft.setCategoryIds(request.getCategoryIds() != null ? request.getCategoryIds().toArray(new Long[0]) : new Long[0]);
        productSolutionDraft.setDomainIds(request.getDomainIds() != null ? request.getDomainIds().toArray(new Long[0]) : new Long[0]);
        if (isDev) {
            productSolutionDraft.setProviderId(currentUserId);
        }
        ProductSolutionDraft save = productSolutionDraftRepository.save(productSolutionDraft);
        // Lưu thông tin chi tiết
        createOrUpdateProductSolution(request, save, isDev, productSolutionDraft);

        //Lưu log khi tạo giải pháp
        if (isDev){
            productApprovalHistoryService.addLog(null, ObjectTypeEnum.SOLUTION, productSolutionDraft.getName(),
                    null, save.getId(), null, ApproveStatusEnum.UNAPPROVED.value);
        }
        return save.getId();
    }

    /**
     * Cập nhật các thông tin chi tiết của giải pháp
     */
    private void createOrUpdateProductSolution(SolutionCreateDTO request, ProductSolutionDraft solutionDraft, boolean isDev,
        ProductSolutionDraft productSolutionDraft) {
        //seo
        Seo seo = saveSeo(request);
        solutionDraft.setSeoId(seo.getId());

        Long draftId = solutionDraft.getId();

        // bố cục
        sectionMappingService.saveSectionMappingDraft(solutionDraft, request.getSections(), ObjectTypeEnum.SOLUTION);

        // tính năng
        featureService.saveFeatureMapping(draftId, request.getFeatures(), ObjectTypeEnum.SOLUTION);

        // gói bundling đi kèm
        if (Objects.nonNull(request.getPackageIds())) {
            solutionPackageService.createSolutionPackages(draftId, SOLUTION_PACKAGE.SOLUTION, request.getPackageIds(),
                request.getPackageDefaultId());
        }

        // topic
        if (request.getTopics() != null) {
            topicFaqService.updateTopicProductSolution(draftId, ObjectTypeEnum.SOLUTION.value, request.getTopics());
        }

        if (!isDev) {
            // nếu là admin thì phê duyệt luôn
            Set<Long> ids = new HashSet<>(Collections.singletonList(draftId));
            approveSolution(new ApprovedReqDTO(ids, ApproveStatusEnum.APPROVED, null, true));
        }
    }

    /**
     * Lưu thông tin SEO của solution
     */
    private Seo saveSeo(SolutionCreateDTO solutionCreateDTO) {
        return Objects.nonNull(solutionCreateDTO.getSeo()) ?
            seoService.saveSeoSolution(CAU_HINH_GIAP_PHAP, solutionCreateDTO.getSeo()) :
            seoService.saveSeoDefault(StringUtil.replace(solutionCreateDTO.getName()),
                solutionCreateDTO.getName() + SeoTypeCodeConstant.TITLE_PAGE_PRICING, solutionCreateDTO.getDescriptions(), null,
                null, CAU_HINH_GIAP_PHAP, true);
    }

    /**
     * Check trùng mã giải pháp cập nhật được trùng với chính nó, khi tạo check toàn hệ thống dùng boolean existsBy
     */
    private void validateCodeProductSolution(String code, Long solutionId) {
        if (productSolutionDraftRepository.existsByCodeAndDeletedFlagAndIdNot(code, DeletedFlag.NOT_YET_DELETED.getValue(), solutionId)) {
            throw exceptionFactory.badRequest(ProductSolutions.SOLUTION_CODE_EXIST, ErrorKey.PRODUCT_SOLUTION,
                ProductSolutionError.SOLUTION_CODE);
        }
    }

    @Override
    public Long updateSolution(Long solutionDraftId, SolutionCreateDTO request) {
        CustomUserDetails userDetails = AuthUtil.getLoggedInUser();
        boolean isDev = AuthUtil.isDev(userDetails.getId());
        // Check trùng mã giải pháp
        validateCodeProductSolution(request.getCode(), solutionDraftId);

        ProductSolutionDraft productSolutionDraft = productSolutionDraftRepository.findById(solutionDraftId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRODUCT_SOLUTION, ErrorKey.DRAFT_ID,
                String.valueOf(solutionDraftId)));
        // Chỉ được cập nhật các giải pháp do bản thân tạo
        if (!Objects.equals(productSolutionDraft.getProviderId(), AuthUtil.getCurrentParentId()) && // Không cùng doanh nghiệp dev
            !Objects.equals(productSolutionDraft.getCreatedBy(), userDetails.getId())) { // Không phải người tạo trực tiếp
            throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
        }
        BeanUtils.copyProperties(request, productSolutionDraft);
        productSolutionDraft.setModifiedBy(userDetails.getId());
        productSolutionDraft.setState(ApproveStatusEnum.UNAPPROVED.value);
        ProductSolutionDraft save = productSolutionDraftRepository.save(productSolutionDraft);
        // Cập nhật thông tin chi tiết
        createOrUpdateProductSolution(request, save, isDev, productSolutionDraft);
        if (isDev){
            productApprovalHistoryService.addLog(null, ObjectTypeEnum.SOLUTION,
                    save.getName(), null, save.getId(), null, ApproveStatusEnum.UNAPPROVED.value);
        }
        return save.getId();
    }

    @Override
    @Transactional
    public void deleteSolution(List<Long> solutionDraftIds) {
        // Không được phép xóa solution đã được đăng ký thuê bao
        if (subscriptionMetadataRepository.existsBySolutionDraftIdIn(solutionDraftIds)) {
            throw exceptionFactory.badRequest(ProductSolutions.SOLUTION_EXISTS_FOR_SUBSCRIPTION, Resources.PRODUCT_SOLUTION, ErrorKey.DRAFT_ID,
                solutionDraftIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentId = AuthUtil.getCurrentParentId();
        Date now = new Date();
        solutionDraftIds.forEach(solutionDraftId -> {
            var solutionDraft = productSolutionDraftRepository.findById(solutionDraftId)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRODUCT_SOLUTION, ErrorKey.ID, String.valueOf(solutionDraftId)));
            // Chỉ được xóa các giải pháp do bản thân tạo
            if (!Objects.equals(solutionDraft.getProviderId(), currentParentId) && // Không cùng doanh nghiệp dev
                !Objects.equals(solutionDraft.getCreatedBy(), currentUserId)) { // Không phải người tạo trực tiếp
                throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
            }
            // Xóa solution draft
            solutionDraft.setDeletedFlag(DeletedFlag.DELETED.getValue());
            solutionDraft.setModifiedBy(currentUserId);
            solutionDraft.setModifiedAt(now);
            productSolutionDraftRepository.save(solutionDraft);
            // Xóa solution
            productSolutionRepository.findByDraftId(solutionDraftId)
                .forEach(solution -> {
                    solution.setDeletedFlag(DeletedFlag.DELETED.getValue());
                    solution.setModifiedBy(currentUserId);
                    solution.setModifiedAt(now);
                    productSolutionRepository.save(solution);
                });
        });
    }

    @Override
    public SolutionDetailResDTO getSolutionById(Long solutionId, ApproveStatusEnum type) {
        var userDetail = AuthUtil.getLoggedInUser();
        var solution = productSolutionDraftRepository.findById(solutionId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRODUCT_SOLUTION, ErrorKey.ID, String.valueOf(solutionId)));
        // DEV chỉ được xem thông tin các giải pháp mà mình tạo
        if (AuthUtil.isDev() &&
            !Objects.equals(solution.getProviderId(), AuthUtil.getCurrentParentId()) && // Không cùng doanh nghiệp dev
            !Objects.equals(solution.getCreatedBy(), userDetail.getId())) { // Không phải người tạo trực tiếp
            throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
        }
        SolutionDetailResDTO response = new SolutionDetailResDTO();
        IGetSolutionDetailDTO productSolution;
        if (ApproveStatusEnum.UNAPPROVED.equals(type)) {
            productSolution = productSolutionRepository.getSolutionProgressDraft(solutionId);
            response.setApproveStatus(ApproveStatusEnum.valueOf(productSolution.getApprovalStatus()));
        } else {
            productSolution = productSolutionRepository.getSolutionProgress(solutionId);
        }
        if (Objects.isNull(productSolution)) {
            throw exceptionFactory.resourceNotFound(Resources.PRODUCT_SOLUTION, ErrorKey.ID, String.valueOf(solutionId));
        }
        BeanUtils.copyProperties(productSolution, response);
        response.setLstCategory(productSolution.getLstCategory());
        response.setLstSeo(productSolution.getLstSeo());
        response.setLstSection(productSolution.getLstSection());
        response.setLstFeature(productSolution.getLstFeature());
        response.setLstCustomerType(productSolution.getLstCustomerType());
        response.setLstDomain(productSolution.getLstDomain());
        if (Objects.equals(DisplayStatus.VISIBLE.value, productSolution.getVisibility())) {
            response.setVisibility(DisplayStatus.VISIBLE);
        } else {
            response.setVisibility(DisplayStatus.INVISIBLE);
        }

        // lấy danh sách gói bundling
        List<PackageListResDTO> packageListResDTOS = getListPackageBySolutionId(solutionId);
        response.setProducts(packageListResDTOS);

        // lấy danh sách topic
        response.setLstTopic(topicFaqService.getLstTopic(solutionId, ObjectTypeEnum.SOLUTION.value));

        return response;
    }

    public List<PackageListResDTO> getListPackageBySolutionId(Long solutionId) {
        List<IGetPackItemDTO> packItems = productSolutionRepository.getPackItemsBySolutionId(solutionId);
        List<PackageListResDTO> packageListResDTOS = new ArrayList<>();
        packItems.forEach(item -> {
            PackageListResDTO packageListResDTO = new PackageListResDTO();
            BeanUtils.copyProperties(item, packageListResDTO);
            packageListResDTO.setLstProduct(item.getLstProduct());
            // tính tổng tiền của package item = totalAmount trong item + totalAmount trong item addon
            packageListResDTO.getLstProduct().forEach(product -> {
                BigDecimal totalAmountAddon = product.getAddons().stream().map(ProductAddonDTO::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                product.setTotalAmount(product.getTotalAmount().add(totalAmountAddon));
            });
            packageListResDTOS.add(packageListResDTO);
        });
        return packageListResDTOS;
    }

    @Override
    public SolutionDetailSMEResDTO getDetailSolutionSME(Long solutionId) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        SolutionDetailSMEResDTO resDTO = new SolutionDetailSMEResDTO();
        ProductSolution productSolution = productSolutionRepository.findByIdAndDeletedFlagAndVisibility(solutionId,
                DeletedFlag.NOT_YET_DELETED.getValue(), DisplayStatus.VISIBLE)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRODUCT_SOLUTION, ErrorKey.ID, String.valueOf(solutionId)));
        // set các thông tin cơ bản
        BeanUtils.copyProperties(productSolution, resDTO);
        resDTO.setApproveStatus(ApproveStatusEnum.APPROVED);
        resDTO.setLstCustomerType(productSolution.getCustomerTypes().stream().map(CustomerTypeEnum::valueOf).collect(
            Collectors.toList()));

        // lĩnh vực áp dụng
        List<SolutionDomain> domains = solutionDomainRepository.findAllById(Arrays.asList(productSolution.getDomainIds()));
        resDTO.setLstDomain(
            domains.stream().map(item -> new ValueDTO(item.getId(), item.getName())).collect(Collectors.toList()));

        // danh mục
        List<Category> categories = categoryRepository.findAllByIdIn(Arrays.asList(productSolution.getCategoryIds()));
        resDTO.setLstCategory(
            categories.stream().map(item -> new ValueDTO(item.getId(), item.getName())).collect(Collectors.toList()));

        // tính năng
        resDTO.setLstFeature(
            featureRepository.getListFeatureByObjectIdAndObjectType(productSolution.getId(), ObjectTypeEnum.SOLUTION.name()));

        // Bố cục
        resDTO.setLstSection(
            sectionRepository.getListSectionByObjectIdAndObjectType(productSolution.getId(), ObjectTypeEnum.SOLUTION.name()));

        // topics
        resDTO.setLstTopic(
            topicFaqService.getListTopicByServiceForSME(productSolution.getId(), ObjectTypeEnum.SOLUTION.value));

        // thông tin seo
        resDTO.setSeo(seoRepository.getSeoDetailById(productSolution.getSeoId()));

        // SPDV gợi ý
        resDTO.setSuggestedProducts(suggestionMappingService.getSuggestionsByIdForDev(ObjectTypeEnum.SOLUTION, productSolution.getId()));

        // Lưu lại lịch sử xem giải pháp của người dùng
        if (Objects.nonNull(userLogin)) {
            serviceViewService.create(solutionId, userLogin.getId(), ServiceViewTypeEnum.SOLUTION.getValue());
        }

        return resDTO;
    }


    @Override
    public void approveSolution(ApprovedReqDTO reqDTO) {
        List<ProductSolutionDraft> productSolutionDrafts = productSolutionDraftRepository.findAllById(reqDTO.getIds());
        if (Objects.equals(ApproveStatusEnum.APPROVED, reqDTO.getApprovedStatus())) {
            productSolutionDrafts.forEach(productSolutionDraft -> {
                productSolutionDraft.setState(ApproveStatusEnum.APPROVED.value);
                productSolutionDraftRepository.save(productSolutionDraft);

                // tạo phiên bản chính
                ProductSolution productSolution = new ProductSolution();
                BeanUtils.copyProperties(productSolutionDraft, productSolution);
                productSolution.setId(null);
                productSolution.setDraftId(productSolutionDraft.getId());
                ProductSolution save = productSolutionRepository.save(productSolution);

                // Tạo solution package theo bản được approved
                solutionPackageService.approvedMappingSolutionPackages(productSolutionDraft.getId(), SOLUTION_PACKAGE.SOLUTION, save.getId());

                // tạo section_mappings
                sectionMappingService.approvedSectionMapping(productSolutionDraft.getId(), save.getId(), ObjectTypeEnum.SOLUTION);

                // tạo feature_mappings
                featureMappingService.approvedFeatureMapping(productSolutionDraft.getId(), save.getId(), ObjectTypeEnum.SOLUTION);

                // topic
                topicFaqService.approvedTopicProductSolution(productSolutionDraft.getId(), save.getId(), ObjectTypeEnum.SOLUTION.value);

                //Lưu log khi phê duyệt gói
                if (reqDTO.isCreate()){
                    int maxVersion = productApprovalHistoryService.findMaxVersionByDraftId(productSolutionDraft.getId(),
                        ObjectTypeEnum.SOLUTION.name());
                    productApprovalHistoryService.addLog(maxVersion + 1, ObjectTypeEnum.SOLUTION, productSolutionDraft.getName(),
                            save.getId(), productSolutionDraft.getId(), reqDTO.getComment(), ApproveStatusEnum.APPROVED.value);
                }
            });
        } else {
            if (StringUtils.isEmpty(reqDTO.getComment())) {
                throw exceptionFactory.badRequest(ProductSolutions.SOLUTION_REJECT_COMMENT_MUST_NOT_EMPTY, ErrorKey.PRODUCT_SOLUTION,
                    ProductSolutionError.SOLUTION, ErrorKey.APPROVE_COMMENT);
            }
            productSolutionDrafts.forEach(productSolutionDraft -> {
                productSolutionDraft.setState(reqDTO.getApprovedStatus().value);
                productSolutionDraft.setApproveReason(reqDTO.getComment());
                productSolutionDraftRepository.save(productSolutionDraft);
                //Lưu log khi từ chối
                productApprovalHistoryService.addLog(null, ObjectTypeEnum.SOLUTION, productSolutionDraft.getName(),
                        null, productSolutionDraft.getId(), reqDTO.getComment(), ApproveStatusEnum.REJECTED.value);
            });
        }

    }

    @Override
    public void submitSolution(List<Long> solutionDraftIds) {
        Long currentUserId = AuthUtil.getCurrentUserId();

        // admin gui ycpd sẽ duyệt luôn
        if (!AuthUtil.isDev()) {
            approveSolution(new ApprovedReqDTO(new HashSet<>(solutionDraftIds), ApproveStatusEnum.APPROVED, null));
            return;
        }
        // Không được phép yêu cầu phê duyệt cho các solution không phải mình tạo
        if (productSolutionDraftRepository.existsByProviderIdNotAndIdIn(AuthUtil.getCurrentParentId(), solutionDraftIds)) {
            throw exceptionFactory.badRequest(ProductSolutions.SOLUTION_NOT_ALLOWED, ErrorKey.PRODUCT_SOLUTION, ProductSolutionError.SOLUTION);
        }
        // Cập nhật trạng thái phê duyệt
        productSolutionDraftRepository.updateStateByIds(solutionDraftIds, ApproveStatusEnum.AWAITING_APPROVAL.value, currentUserId);
        // TODO: Gửi thông báo yêu cầu phê duyệt
        //Lưu log khi yêu cầu phê duyệt gói
        List<ProductSolutionDraft> productSolutionDrafts = productSolutionDraftRepository.findAllById(solutionDraftIds);
        productSolutionDrafts.forEach(productSolutionDraft -> {
            productApprovalHistoryService.addLog(null, ObjectTypeEnum.SOLUTION, productSolutionDraft.getName(),
                    null, productSolutionDraft.getId(), null, ApproveStatusEnum.AWAITING_APPROVAL.value);
        });
    }

    @Override
    public List<SolutionDomain> getSolutionDomains() {
        return solutionDomainRepository.findAll();
    }

    @Override
    public List<IGetListSolutionPopup> listSolutionPopUp(List<String> customerTypeCondition, String value, Integer isSolution,
        Integer isPackage, CustomerTypeEnum customerTypeSearch, List<Long> domainIds, List<Long> categoryIds, Integer displayStatus) {

        BigDecimal[] domainIdsConvert = domainIds.stream()
                .sorted().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);
        BigDecimal[] categoryIdsConvert = categoryIds.stream()
                .sorted().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);

        return productSolutionDraftRepository.getListSolutionPopup(customerTypeCondition, value, isSolution, isPackage,
        customerTypeSearch.name() , domainIdsConvert, categoryIdsConvert, displayStatus);
    }

}
