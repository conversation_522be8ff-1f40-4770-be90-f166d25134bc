package com.service.product_solutions;

import java.util.List;

import com.dto.product_solustions.*;
import com.dto.services.sugesstion.ServiceCommonDetailDTO;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.dto.product_solustions.ApprovedReqDTO;
import com.dto.product_solustions.CalculatePriceInfoRequestDTO;
import com.dto.product_solustions.PackageBundlingCreateDTO;
import com.dto.product_solustions.PackageDetailResDTO;
import com.dto.product_solustions.PackageDetailSmeResDTO;
import com.dto.product_solustions.PackageListResDTO;
import com.dto.product_solustions.PriceInfoDTO;
import com.enums.ApproveStatusEnum;
import com.onedx.common.constants.enums.ProductSortEnum;
import com.onedx.common.constants.enums.services.ProductClassificationEnum;
import com.onedx.common.constants.enums.PortalType;

public interface PackageBundlingService {

    void approvePackages(ApprovedReqDTO reqDTO);

    void submitPackage(List<Long> packageDraftIds);

    Long createPackage(PackageBundlingCreateDTO request);

    PackageDetailResDTO getPackageById(Long packageId, ApproveStatusEnum type);

    /**
     * Chi tiét gói bundling trên SME portal
     *
     * @param packageId id bảng package
     * @return PackageDetailSmeResDTO
     */
    PackageDetailSmeResDTO getPackageDetailSme(Long packageId);

    Long updatePackage(Long packageId, PackageBundlingCreateDTO request);

    void deletePackages(List<Long> packageId);

    Page<PackageListResDTO> getListSolutionPackage(Long solutionId, Pageable pageable);

    List<SolutionByDomainPageDTO> getListSolutionByDomain(String search, ProductSortEnum sort, CustomerTypeEnum customerType, List<Long> domainIds);

    SolutionByDomainPageDTO getListSolutionByDomainId(String search, CustomerTypeEnum customerType, Long domainId, ProductSortEnum order, Pageable pageable);

    Page<PackageListResDTO> listPackages(String value, Integer isName, Integer isCode, ApproveStatusEnum approveStatus,
                                         Integer displayStatus, CustomerTypeEnum customerType, List<Long> createdIds,
                                         List<Long> productIds, List<String> customerTypeCondition, Pageable pageable);

    List<IGetListCreatedBy> listCreatedByInSearch();

    List<IGetListProduct> listProductInSearch();

    Page<GetSPDVBundlingDTO> getSPDVBundling(String value, Integer isNameService, Integer isNamePricing, List<Long> categoryIds,
        Long providerId, String manufactureName, List<String> customerTypes, String paymentCycle,
        ProductClassificationEnum classification, Integer serviceType, Pageable pageable);

    List<ServiceCommonDetailDTO> getListPackageSuggestion(Long packageId, PortalType portalType,
        PricingDetailInputEnum pricingDetailInputEnum);

    /**
     * Tính toán thông tin giá trước thuế/sau thuế/km/phí/thuế khi chọn SPDV trong gói bundling
     *
     * @param request Thông tin các gói dịch vụ cần tính toán
     * @return Thông tin giá đã được tính toán
     */
    PriceInfoDTO calculatePriceInfo(CalculatePriceInfoRequestDTO request);

    /**
     * Xử lý khi component thay đổi - tìm và tính toán lại giá cho các packages có chứa component
     * @param componentId ID của component bị thay đổi (coupon, addon, pricing, variant)
     * @param type Loại thay đổi: COUPON, ADDON, PRICING, VARIANT
     * @param eventType Loại event cụ thể để xác định hành động (EXPIRED, APPLY_EXCEED, UPGRADED, STATUS_CHANGED)
     */
    void handleComponentChanged(Long componentId, String type, String eventType);
}

