package com.service.subscriptions.impl;

import static com.constant.MarketingCampaignConstant.ENABLE_CAMPAIGN_SUBSCRIPTION;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.component.BaseController.ListRequest;
import com.constant.AddonsConstant;
import com.constant.BillConstant;
import com.constant.ComboConst;
import com.constant.CouponConst;
import com.constant.CreditNoteConst;
import com.constant.FileAttachConst;
import com.constant.PaymentConstant.MerchantData;
import com.constant.PricingConst;
import com.constant.SeoTypeCodeConstant;
import com.constant.SubscriptionConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.addon.AddonChangeQuantityEnum;
import com.constant.enums.addons.AddonPopUpTypeEnum;
import com.constant.enums.combo.ComboChangeEnum;
import com.constant.enums.coupon.AddonTypeEnum;
import com.constant.enums.coupon.CouponPricingApplyTypeEnum;
import com.constant.enums.coupon.EnterpriseTypeEnum;
import com.constant.enums.coupon.TotalBillTypeEnum;
import com.constant.enums.credit_note.CreditNoteDetailStatusEnum;
import com.constant.enums.credit_note.CreditNoteType;
import com.constant.enums.orders.SmeProgressEnum;
import com.constant.enums.pricing.PricingCancelTimeActiveEnum;
import com.constant.enums.report.InstalledStatusEnum;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.constant.enums.subscription.ActionTypeEnum;
import com.constant.enums.subscription.RegTypeEnum;
import com.constant.enums.subscription.StatusVersionEnum;
import com.constant.enums.subscription.SubTypeEnum;
import com.constant.enums.transactionLog.ActivityCodeEnum;
import com.constant.enums.transactionLog.TransactionLogStatusEnum;
import com.constant.enums.userSubscription.UserSubscriptionEnum;
import com.constant.integration.apigwkhcn.ApiGwKHCNConstant;
import com.dto.combo.ComboDetailDTO;
import com.dto.coupons.AddonCouponResDTO;
import com.dto.coupons.CouponAddonsDetailDTO;
import com.dto.coupons.CouponEnterpriseAndUserDetailDTO;
import com.dto.coupons.CouponEnterpriseDetailDTO;
import com.dto.coupons.CouponIdAndNameDTO;
import com.dto.coupons.ICountCouponDTO;
import com.dto.feature.FeatureReqDTO;
import com.dto.marketingCampaign.smePortal.ProductTagDTO;
import com.dto.payment.ClueDTO;
import com.dto.pricing.PricingDetailResDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.pricing.PricingTaxRes;
import com.dto.pricing.multiplePeriod.PricingMultiplePeriodResDTO;
import com.dto.pricing.multiplePeriod.PricingMultiplePeriodTransDTO;
import com.dto.services.ServiceResponseDTO;
import com.dto.services.ServiceSuggestionResDTO;
import com.dto.shoppingCart.ISubCodePageDTO;
import com.dto.shoppingCart.SubscriptionsOrderDTO;
import com.dto.shoppingCart.SubscriptionsOrderITF;
import com.dto.subscriptionUser.SubscriptionCustomerDTO;
import com.dto.subscriptionUser.SubscriptionPaymentDTO;
import com.dto.subscriptionUser.SubscriptionUserResponseDTO;
import com.dto.subscriptions.CouponPopupDTO;
import com.dto.subscriptions.CustomerDTO;
import com.dto.subscriptions.ISubscriptionDetailDTO;
import com.dto.subscriptions.SubscriptionActiveResponseDTO;
import com.dto.subscriptions.SubscriptionAddonResDTO;
import com.dto.subscriptions.SubscriptionAddonResITFDTO;
import com.dto.subscriptions.SubscriptionByDevOrAdminDTONew;
import com.dto.subscriptions.SubscriptionCalculateFirstStepResDTO;
import com.dto.subscriptions.SubscriptionComboBasicDTO;
import com.dto.subscriptions.SubscriptionDTO;
import com.dto.subscriptions.SubscriptionDevCompanyResponseDTO;
import com.dto.subscriptions.SubscriptionFirstStepReqDTO;
import com.dto.subscriptions.SubscriptionForPricingDetailDTO;
import com.dto.subscriptions.SubscriptionGeneralInformationDTO;
import com.dto.subscriptions.SubscriptionInfoDTO;
import com.dto.subscriptions.SubscriptionIntegrationResDTO;
import com.dto.subscriptions.SubscriptionOfficialJointScreenDTO;
import com.dto.subscriptions.SubscriptionPricingChosenResDTO;
import com.dto.subscriptions.SubscriptionPricingChosenResDTO.SetupFeeTax;
import com.dto.subscriptions.SubscriptionPricingPopupResDTO;
import com.dto.subscriptions.SubscriptionPricingResDTO;
import com.dto.subscriptions.SubscriptionPricingResDTO.MultiPlanCustomerTypeCode;
import com.dto.subscriptions.SubscriptionRangeDTO;
import com.dto.subscriptions.SubscriptionRequestDTO;
import com.dto.subscriptions.SubscriptionServiceDTO;
import com.dto.subscriptions.SubscriptionServiceResponseDTO;
import com.dto.subscriptions.SubscriptionSmeCompaniesRequestDTO;
import com.dto.subscriptions.SubscriptionSmeCompaniesResponseDTO;
import com.dto.subscriptions.SubscriptionSmeCreditNoteDTO;
import com.dto.subscriptions.SubscriptionSubPlanResponseDTO;
import com.dto.subscriptions.SubscriptionsDetailDTO;
import com.dto.subscriptions.calculate.CalUnitLimitedCustomDTO;
import com.dto.subscriptions.calculate.UnitLimitedNewDTO;
import com.dto.subscriptions.combo.ComboPricingDetailResDTO;
import com.dto.subscriptions.combo.FilterReqDefault;
import com.dto.subscriptions.combo.PaymentCycleDTO;
import com.dto.subscriptions.combo.SubComboFilterDevRes;
import com.dto.subscriptions.combo.SubComboFilterDevResDTO;
import com.dto.subscriptions.combo.SubComboFilterReqDTO;
import com.dto.subscriptions.combo.SubComboFilterResDTO;
import com.dto.subscriptions.combo.SubComboFilterResInterfaceDTO;
import com.dto.subscriptions.combo.SubDetailComboResDTO;
import com.dto.subscriptions.combo.SubscriptionComboPlanPopupResDTO;
import com.dto.subscriptions.common.IServiceOwnerInfoDTO;
import com.dto.subscriptions.detail.SubscriptionCheckedAddonCouponDTO;
import com.dto.subscriptions.detail.SubscriptionDetailDTO;
import com.dto.subscriptions.detail.SubscriptionDetailRespDTO;
import com.dto.subscriptions.detail.SubscriptionEnterpriseDTO;
import com.dto.subscriptions.detail.SubscriptionPricingAddonCouponDTO;
import com.dto.subscriptions.detail.SubscriptionProgressDTO;
import com.dto.subscriptions.formula.ServiceCalculateDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaReqDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaTax;
import com.dto.subscriptions.formula.VariantCalculateDTO;
import com.dto.subscriptions.responseDTO.AmountOfCycleNext;
import com.dto.subscriptions.responseDTO.GetSubscriptionServiceDTO;
import com.dto.subscriptions.responseDTO.ISubscriptionProgressDTO;
import com.dto.subscriptions.responseDTO.MyServiceSubscriptionResDTO;
import com.dto.subscriptions.responseDTO.SubscriptionCreditNoteResDTO;
import com.dto.subscriptions.responseDTO.SubscriptionDetailCommonDTO;
import com.dto.subscriptions.responseDTO.SubscriptionDetailCommonInterfaceDTO;
import com.dto.subscriptions.responseDTO.SubscriptionSaaSResDTO;
import com.dto.system.param.Wallet3rdPartyDTO;
import com.dto.transaction_log.IOnlineServiceTimeLineDTO;
import com.dto.transaction_log.OnlineServiceProgressDTO;
import com.dto.transaction_log.OnlineServiceTimeLineDTO;
import com.dto.users.ITransactionInfo;
import com.entity.addons.Addon;
import com.entity.address.Address;
import com.entity.bills.BillItem;
import com.entity.bills.BillMcPrivate;
import com.entity.bills.BillMcTotal;
import com.entity.bills.Bills;
import com.entity.combo.Combo;
import com.entity.combo.ComboPlan;
import com.entity.combo.ComboPricing;
import com.entity.couponSet.CouponSet;
import com.entity.coupons.Coupon;
import com.entity.file.attach.FileAttach;
import com.entity.marketingCampaign.MarketingCampaign;
import com.entity.marketingCampaign.McActivity;
import com.entity.orderServiceReceive.OrderServiceReceive;
import com.entity.orderServiceReceive.OrderServiceReceiveLog;
import com.entity.orderServiceReceive.SmeProgress;
import com.entity.payment.VNPTPayResponse;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingAddon;
import com.entity.pricing.PricingDraft;
import com.entity.pricing.PricingMultiPlan;
import com.entity.pricing.PricingPlanDetail;
import com.entity.pricing.PricingSetupInfo;
import com.entity.pricing.PricingTax;
import com.entity.rating.ServiceReaction;
import com.entity.serviceGroup.ServiceGroup;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.Subscription;
import com.entity.subscriptions.SubscriptionMetadata;
import com.entity.unitLimited.UnitLimited;
import com.enums.ApproveStatusEnum;
import com.enums.PermissionNameEnum;
import com.enums.ProductTypeEnum;
import com.enums.SystemParamEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.mapper.EnumFieldMapper;
import com.mapper.SubscriptionMapper;
import com.mapper.pricing.PricingSubscriptionDevMapper;
import com.mapper.pricing.SubscriptionPricingMapper;
import com.mapper.subscription.SubscriptionAddonCouponMapper;
import com.mapper.subscription.SubscriptionDetailComboMapper;
import com.mapper.subscription.SubscriptionDetailMapper;
import com.mapper.subscription.SubscriptionOfficialMapper;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.billings.BillStatusEnum;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.coupons.TimeUsedTypeEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.customFields.CustomFieldCategoryEnum;
import com.onedx.common.constants.enums.customFields.EntityTypeEnum;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.constants.enums.migration.MigrationServiceTypeEnum;
import com.onedx.common.constants.enums.migration.RepeatTypeEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.pricings.PeriodTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.services.ComboTypeEnum;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerTypeEnum;
import com.onedx.common.constants.enums.services.ServiceTypeEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeQuantityEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionSetupFeeTypeEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.enums.subscriptions.TypeReActiveEnum;
import com.onedx.common.constants.enums.systemParams.SystemParamTypeEnum;
import com.onedx.common.constants.enums.theme.ThemeEnum;
import com.onedx.common.constants.values.AutomationRuleConstant;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.constants.values.ExceptionConstants;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.onedx.common.dto.integration.backend.IntegrationSubsDetailDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingPeriodAddonDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.IntegrateSubsTaxDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionAddonOfPricingDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionCouponTotalBillDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionMcDetailDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionOneTimeFee;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionPricingAddonsDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionPricingCouponDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionSummaryDTO;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.dto.subscriptions.orders.DHSXKDTrackingResDTO;
import com.onedx.common.dto.subscriptions.orders.TrackingOrderServiceReqDTO;
import com.onedx.common.dto.subscriptions.orders.TrackingOrderServiceResDTO;
import com.onedx.common.dto.users.UserDepartmentDTO;
import com.onedx.common.entity.subscriptions.SubscriptionHistory;
import com.onedx.common.entity.subscriptions.SubscriptionSetupFee;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.repository.subscriptions.SubscriptionHistoryRepository;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.Province.ProvinceRepository;
import com.repository.addons.AddonRepository;
import com.repository.addons.AddonsTaxRepository;
import com.repository.bank.BankRepository;
import com.repository.bills.BillItemRepository;
import com.repository.bills.BillMcPrivateRepository;
import com.repository.bills.BillMcTotalRepository;
import com.repository.bills.BillsRepository;
import com.repository.categories.CategoryRepository;
import com.repository.collect_info_preorder.dto.IGetDetailPricing;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboPricingRepository;
import com.repository.combo.ComboRepository;
import com.repository.combo.ComboSetupFeeTaxRepository;
import com.repository.combo.ComboTaxRepository;
import com.repository.combo.SubscriptionComboAddonRepository;
import com.repository.couponSet.CouponSetRepository;
import com.repository.coupons.CouponAddonRepository;
import com.repository.coupons.CouponEnterpriseRepository;
import com.repository.coupons.CouponRepository;
import com.repository.credit_note.CreditNoteRepository;
import com.repository.currency.CurrencyRepository;
import com.repository.customField.CustomFieldRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.error_intergration.ErrorIntergrationRepository;
import com.repository.feature.FeatureRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.marketingCampaign.MarketingCampaignRepository;
import com.repository.marketingCampaign.McActivityRepository;
import com.repository.orderService.SmeProgressRepository;
import com.repository.payment.VNPTPayResponseRepository;
import com.repository.pricing.PricingAddonRepository;
import com.repository.pricing.PricingDraftRepository;
import com.repository.pricing.PricingMultiPlanAddonRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingPlanDetailRepository;
import com.repository.pricing.PricingRepository;
import com.repository.pricing.PricingSetupFeeTaxRepository;
import com.repository.pricing.PricingSetupInfoRepository;
import com.repository.pricing.PricingTaxRepository;
import com.repository.rating.ServiceEvaluationRepository;
import com.repository.rating.ServiceReationRepository;
import com.repository.services.ServiceRepository;
import com.repository.shoppingCart.ShoppingCartRepository;
import com.repository.subscriptions.CustomFeeRepository;
import com.repository.subscriptions.SubscriptionAddonsRepository;
import com.repository.subscriptions.SubscriptionCouponsRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.subscriptions.SubscriptionSetupFeeRepository;
import com.repository.subscriptions.SubscriptionMetadataRepository;
import com.repository.transactionLog.ActivityLogRepository;
import com.repository.transactionLog.TransactionLogRepository;
import com.repository.unitLimited.UnitLimitedRepository;
import com.repository.units.UnitRepository;
import com.repository.users.UserRepository;
import com.service.address.AddressService;
import com.service.calculator.SetupFeeCalculator;
import com.service.calculator.SubscriptionCalculator;
import com.service.combo.ComboPlanService;
import com.service.combo.SubComboService;
import com.service.coupon.CouponService;
import com.service.crm.automationRule.impl.AutomationRuleServiceImpl;
import com.service.crm.dataPartition.impl.CrmObjectPermissionUtil;
import com.service.customField.impl.CustomFieldManager;
import com.service.integrated.ExecutiveProducerService;
import com.service.integration.apigwkhcn.IntegrationApiGwKHCNSmeService;
import com.service.marketingCampaign.MarketingCampaignSmeService;
import com.service.migration.SubscriptionMigration;
import com.service.multiplePeriod.SubMultiplePeriod;
import com.service.orderServiceReceive.OrderServiceReceiveService;
import com.service.pricing.PricingService;
import com.service.rating.ServiceReactionService;
import com.service.seo.SeoService;
import com.service.serviceGroup.ServiceGroupService;
import com.service.serviceSuggestion.ServiceSuggestionService;
import com.service.services.ServicesService;
import com.service.shoppingCart.ShoppingCartSmeService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.subscriptions.SubscriptionCalculateService;
import com.service.subscriptions.SubscriptionDetailService;
import com.service.subscriptions.SubscriptionHelperService;
import com.service.system.param.SystemParamService;
import com.service.users.UserService;
import com.util.AuthUtil;
import com.util.CheckUtil;
import com.util.SpringContextUtils;
import com.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;

@Service
@Slf4j
public class SubscriptionDetailServiceImpl implements SubscriptionDetailService {

    private static final Long DEFAULT_ID = -1L;
    private static final Long DEFAULT_SUCCESS_ID = 0L;
    private static final Integer OTHER = 3;
    private static final Long COMPLETE = 4L;
    private static final Long CANCEL = 3L;
    private static final Integer WAIT_CHANGE_PLAN = 0; // chờ đổi gói
    private static final int flagNotDeleted = DeletedFlag.NOT_YET_DELETED.getValue();
    private static final Integer TYPE_DEFAULT = 0;
    private static final Integer DEFAULT_USED_QUANTITY = 1;
    private static final Long STATUS_DEV_PROVINCE_ALL = -2L;
    private static final Long STATUS_ADMIN_PROVINCE_ALL = -1L;
    private static final String SERVICE_NAME = "serviceName";
    private static final String PRICING_NAME = "pricingName";
    private static final String TYPE = "type";
    private static final String PAYMENT_CYCLE = "paymentCycle";
    private static final String BATCH = "batch";
    private static final String AM = "AM";
    private static final String OS = "OS";
    private static final String ON = "ON";
    private static final String[] orderStatusMessage = {"orderStatus_message"};
    private static final String[] billMessage = {"bill"};
    private static final String[] pricingMessage = {"pricing"};
    private static final String[] subscriptionMessage = {"subscription"};
    public static final int KHCN_MIGRATE_SOURCE = 2;
    private static final Integer DELETED_FLAG = 1;
    private static final Integer STATUS_UN_SEARCHING = -1;
    private static final long IS_EMPTY = 0L;
    private static final Integer STATUS_SEARCHING = 1;
    private static final String[] userMessage = {"user"};
    private static final String[] creditNotes = {"credit_note"};

    @Autowired
    private ServiceReationRepository serviceReactionRepository;
    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    private CategoryRepository categoriesRepository;
    @Autowired
    private ServiceSuggestionService serviceSuggestionService;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private SubscriptionMetadataRepository metadataRepository;
    @Autowired
    private ShoppingCartSmeService shoppingCartSmeService;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private UnitLimitedRepository unitLimitedRepository;
    @Autowired
    private PricingPlanDetailRepository pricingPlanDetailRepository;
    @Autowired
    private PricingTaxRepository pricingTaxRepository;
    @Autowired
    private AddonsTaxRepository addonsTaxRepository;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private AddonRepository addonRepository;
    @Autowired
    private ComboPlanService comboPlanService;
    @Autowired
    private ComboTaxRepository comboTaxRepository;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private SubMultiplePeriod subMultiplePeriod;
    @Autowired
    private SubscriptionPricingMapper subscriptionPricingMapper;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CouponService couponService;
    @Autowired
    private CouponSetRepository couponSetRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private CurrencyRepository currencyRepository;
    @Autowired
    private PricingSetupFeeTaxRepository pricingSetupFeeTaxRepository;
    @Autowired
    private CustomFieldRepository customFieldRepository;
    @Autowired
    private CustomFieldManager customFieldManager;
    @Autowired
    private ShoppingCartRepository shoppingCartRepository;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private SeoService seoService;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private CouponAddonRepository couponAddonRepository;
    @Autowired
    private ComboRepository comboRepository;
    @Autowired
    private CouponEnterpriseRepository couponEnterpriseRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private CrmObjectPermissionUtil crmObjectPermissionUtil;
    @Autowired
    private SubscriptionDetailComboMapper subscriptionDetailComboMapper;
    @Autowired
    private ComboPricingRepository comboPricingRepository;
    @Autowired
    private ComboSetupFeeTaxRepository comboSetupFeeTaxRepository;
    @Autowired
    private SubscriptionComboAddonRepository subscriptionComboAddonRepository;
    @Autowired
    private SubscriptionAddonCouponMapper subscriptionAddonCouponMapper;
    @Autowired
    private CustomFeeRepository customFeeRepository;
    @Autowired
    private FileAttachRepository fileAttachRepository;
    @Autowired
    private ServicesService servicesService;
    @Autowired
    private OrderServiceReceiveService orderServiceReceiveService;
    @Autowired
    private ExecutiveProducerService executiveProducerService;
    @Autowired
    private SmeProgressRepository smeProgressRepository;
    @Autowired
    private SubscriptionHistoryRepository subscriptionHistoryRepository;
    @Autowired
    private BillsRepository billsRepository;
    @Autowired
    private SubscriptionDetailMapper subscriptionDetailMapper;
    @Autowired
    private ErrorIntergrationRepository errorIntergrationRepository;
    @Autowired
    private ActivityLogRepository activityLogRepository;
    @Autowired
    private SubComboService subComboService;
    @Autowired
    private ProvinceRepository provinceRepository;
    @Autowired
    private AddressService addressService;
    @Autowired
    private SubscriptionMigration subscriptionMigration;
    @Autowired
    private VNPTPayResponseRepository vnptPayResponseRepository;
    @Autowired
    private BankRepository bankRepository;
    @Autowired
    private SubscriptionSetupFeeRepository subscriptionSetupFeeRepository;
    @Autowired
    private SubscriptionHelperService subscriptionHelperService;
    @Autowired
    private BillItemRepository billItemRepository;
    @Autowired
    private BillMcPrivateRepository billMcPrivateRepository;
    @Autowired
    private BillMcTotalRepository billMcTotalRepository;
    @Autowired
    private ServiceEvaluationRepository serviceEvaluationRepository;
    @Autowired
    private SubscriptionCalculateService subscriptionCalculateService;
    @Autowired
    private MarketingCampaignRepository campaignRepository;
    @Autowired
    private McActivityRepository mcActivityRepository;
    @Autowired
    private SubscriptionAddonsRepository subscriptionAddonsRepository;
    @Autowired
    private SubscriptionCouponsRepository subscriptionCouponsRepository;
    @Autowired
    private PricingSetupInfoRepository pricingSetupInfoRepository;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private PricingAddonRepository pricingAddonRepository;
    @Autowired
    private PricingSubscriptionDevMapper pricingSubscriptionDevMapper;
    @Autowired
    private PricingMultiPlanAddonRepository pricingMultiPlanAddonRepository;
    @Autowired
    private TransactionLogRepository transactionLogRepository;
    @Autowired
    @Lazy
    private ServiceGroupService serviceGroupService;
    @Autowired
    private IntegrationApiGwKHCNSmeService integrationApiGwKHCNSmeService;
    @Autowired
    private AutomationRuleServiceImpl automationRuleService;
    @Autowired
    private MarketingCampaignSmeService campaignSmeService;
    @Autowired
    private SubscriptionMapper subscriptionMapper;
    @Autowired
    private ServiceReactionService serviceReactionService;
    @Autowired
    private PricingDraftRepository pricingDraftRepository;
    @Autowired
    private CreditNoteRepository creditNoteRepository;
    @Autowired
    private SubscriptionOfficialMapper subscriptionOfficialMapper;

    @Value("${transaction.base_url}")
    private String baseUrl;

    @Override
    public ITransactionInfo getTransactionInfoBySubscriptionId(Long subscriptionId) {
        return subscriptionRepository.getTransactionInfoBySubscriptionId(subscriptionId);
    }

    @Transactional(readOnly = true)
    @Override
    public IGetDetailPricing getPricingDetailPreOrder(Long id, Long pricingMultiPlanId) {
        if (Objects.nonNull(pricingMultiPlanId)) {
            return pricingMultiPlanRepository.getPricingDetailPreOrder(pricingMultiPlanId);
        }
        return pricingRepository.getPricingDetailPreOrder(id);
    }

    @Transactional(readOnly = true)
    @Override
    public SubscriptionPricingResDTO getSubscription(Long id, Long subscriptionId, Long pricingMultiPlanId) {
        String customerTypeCurrentUser = AuthUtil.getCurrentUser().getCustomerType();
        Pricing pricing = pricingRepository.findByIdAndDeletedFlagAndApproveAndStatus(id, DeletedFlag.NOT_YET_DELETED.getValue(),
                ApproveStatusEnum.APPROVED.value, StatusEnum.ACTIVE.value)
            .orElseThrow(
                () -> exceptionFactory.badRequest(MessageKeyConstant.OBJECT_EXISTS, Resources.PRICING, ErrorKey.ID, String.valueOf(id)));
        Pricing pricingClone = new Pricing();
        BeanUtils.copyProperties(pricing, pricingClone);
        PricingMultiPlan pricingMultiPlan = null;
        if (Objects.nonNull(pricingMultiPlanId) && pricingMultiPlanId != -1L) {
            pricingMultiPlan = pricingMultiPlanRepository
                .findByIdAndPricingIdAndDeletedFlag(pricingMultiPlanId, id, DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> exceptionFactory.badRequest(MessageKeyConstant.OBJECT_EXISTS, Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                    String.valueOf(pricingMultiPlanId)));
            pricingClone.setPricingPlan(pricingMultiPlan.getPricingPlan());
            pricingClone.setPaymentCycle(pricingMultiPlan.getPaymentCycle() != null ? pricingMultiPlan.getPaymentCycle().intValue() : null);
            pricingClone.setNumberOfCycles(pricingMultiPlan.getNumberOfCycle());
            pricingClone.setCycleType(pricingMultiPlan.getCircleType());
            pricingClone.setUnitId(pricingMultiPlan.getUnitId());
            pricingClone.setCurrencyId(pricingMultiPlan.getCurrencyId());
            pricingClone.setPrice(pricingMultiPlan.getPrice());
            pricingClone.setTrialType(pricingMultiPlan.getTrialType() != null ? pricingMultiPlan.getTrialType().intValue() : null);
            pricingClone.setNumberOfTrial(pricingMultiPlan.getNumberOfTrial());
            pricingClone.setFreeQuantity(pricingMultiPlan.getFreeQuantity() != null ? Long.valueOf(pricingMultiPlan.getFreeQuantity()) : null);
            pricingClone.setEstimateQuantity(pricingMultiPlan.getEstimateQuantity());
        } else {
            // Check có là gói multi period
            subMultiplePeriod.validatePricingMultiPlanByPricingId(pricingClone.getId(), null);
        }

        validateCustomerTypeCode(pricingClone, pricingMultiPlan,
            Objects.nonNull(customerTypeCurrentUser) ? customerTypeCurrentUser : CustomerTypeEnum.ENTERPRISE.getValue());

        Long pricingOwnerId = pricingRepository.findOwnerPricing(id);
        // Lấy coupon của pricing
        Set<SubscriptionPricingAddonDTO.CouponList> couponList = new LinkedHashSet<>();
        Optional<SubscriptionForPricingDetailDTO> subscriptionInfo = subscriptionRepository
            .getRegTypeOfSubscription(AuthUtil.getCurrentUserId(), id);
        SubscriptionPricingResDTO resSubscription = subscriptionPricingMapper.toDto(pricingClone);
        if (pricingMultiPlan != null) {
            resSubscription.setMinimumQuantity(
                Objects.nonNull(pricingMultiPlan.getMinimumQuantity()) ? pricingMultiPlan.getMinimumQuantity() : null);
            resSubscription.setMaximumQuantity(
                Objects.nonNull(pricingMultiPlan.getMaximumQuantity()) ? pricingMultiPlan.getMaximumQuantity() : null);
        }
        if (Objects.nonNull(pricingClone.getServiceId())) {
            serviceRepository.findByIdAndDeletedFlag(pricingClone.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue())
                .ifPresent(serviceEntity -> {
                    if (Objects.nonNull(serviceEntity.getServiceOwner())) {
                        resSubscription.setServiceOwner(ServiceTypeEnum.valueOf(serviceEntity.getServiceOwner()));
                        // gói 1 lần của service ON luôn là thanh toán trả trước
                        if (Objects.equals(pricing.getIsOneTime(), RepeatTypeEnum.ONCE.getValue()) &&
                            Objects.equals(serviceEntity.getOnOsType().getValue(), OnOsTypeEnum.ON.getValue())) {
                            resSubscription.setPaymentMethod(PaymentMethodEnum.VNPTPAY);
                        } else {
                            resSubscription.setPaymentMethod(PaymentMethodEnum.valueOf(serviceEntity.getPaymentMethod()));
                        }
                    }
                });
        }
        // Lấy thông tin province
        Long provinceId;
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue()))) {
            provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
        } else {
            provinceId = userRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
        }
        // Thông tin khuyến mại mới của gói dịch vụ
        provinceId = Objects.nonNull(provinceId) ? provinceId : SubscriptionConstant.GUEST_USER;
        Long parentId = Objects.isNull(AuthUtil.getCurrentUser()) ? SubscriptionConstant.GUEST_USER : AuthUtil.getCurrentParentId();
        Set<Coupon> couponSet = pricingMultiPlanId == null ?
            pricingRepository.getCouponByPricing(id, provinceId, parentId) :
            pricingRepository.getCouponByPricingMultiPlanId(pricingMultiPlanId, provinceId, parentId);
        Set<Long> idCoupons = couponSet.stream().map(Coupon::getId).collect(Collectors.toSet());
        //lay thong tin subscription cu
        if (Objects.nonNull(subscriptionId) && !subscriptionId.equals(-1L)) {
            Subscription subscription = validateSubscription(subscriptionId, PortalType.SME);
            Pricing pricingOld = validatePricing(subscription.getPricingId());
            List<SubscriptionPricingCouponDTO> listCoupons = pricingRepository.getCouponByPricingSubscription(subscriptionId);
            listCoupons.stream().filter(c -> idCoupons.contains(c.getCouponId())).forEach(c -> {
                boolean isValid = (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value))
                    && ((!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && !Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
                    || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    || !Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    || !Objects.equals(c.getAddonsType(), AddonTypeEnum.NONE.value));
                //nếu coupon ko chọn áp dụng với enterprise nào thì ko đc áp dụng
                boolean checkEnterpriseType = Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value);
                if (!isValid && !checkEnterpriseType) {
                    SubscriptionPricingAddonDTO.CouponList coupon = new SubscriptionPricingAddonDTO.CouponList();
                    coupon.setCouponId(c.getCouponId());
                    coupon.setCode(c.getCode());
                    coupon.setCouponName(c.getCouponName());
                    coupon.setCode(c.getPromotionCode());
                    coupon.setTimesUsedType(c.getTimesUsedType());
                    coupon.setLimitedQuantity(c.getLimitedQuantity());
                    coupon.setMinimum(c.getMinimum());
                    coupon.setMinimumAmount(c.getMinimumAmount());
                    coupon.setPromotionType(c.getPromotionType());
                    coupon.setDiscountValue(c.getDiscountValue());
                    coupon.setDiscountType(c.getDiscountType());
                    coupon.setMaximum((c.getMinimum()));
                    coupon.setAddonDepend(getDependAddonByCouponId(c));
                    coupon.setChecked(true);
                    coupon.setType(Objects.nonNull(c.getTimeType()) ? TimeTypeEnum.valueOf(c.getTimeType()) : null);
                    coupon.setConditions(couponService.getCouponConditions(c.getCouponId(), CouponConst.COUPON_OF_PRICING));
                    coupon.setVisibleStatus(c.getVisibleStatus());
                    //set pricings của mỗi couponType = PRODUCT
                    if (PromotionTypeEnum.PRODUCT.equals(c.getPromotionType())) {
                        coupon.setPricing(getPricingByCoupon(c.getCouponId()));
                    } else {
                        coupon.setPricing(null);
                    }
                    couponList.add(coupon);
                }
            });
            resSubscription.setChangeDate(
                Objects.equals(pricingOld.getChangePricingDate(), ComboChangeEnum.END_OF_PERIOD.value) ? DateUtil
                    .convertLocalDateToDate(DateUtil.toLocalDate(subscription.getEndCurrentCycle()).plusDays(1L)) : new Date());
            if (Objects.equals(pricingOld.getChangePricingDate(), ComboChangeEnum.END_OF_PERIOD.value)) {
                resSubscription.setChangeDate(
                    DateUtil.convertLocalDateToDate(DateUtil.toLocalDate(subscription.getEndCurrentCycle()).plusDays(1L)));
                resSubscription.setChangePricing(PricingCancelTimeActiveEnum.END_OF_PERIOD);
            } else {
                resSubscription.setChangeDate(new Date());
                resSubscription.setChangePricing(PricingCancelTimeActiveEnum.NOW);
            }
            resSubscription.setStartDateSubscription(subscription.getStartedAt());
            resSubscription.setEndDateSubscription(subscription.getExpiredTime());
        } else if (subscriptionInfo.isPresent()) {
            resSubscription.setStartDateSubscription(subscriptionInfo.get().getStartDateSubscription());
            resSubscription.setEndDateSubscription(subscriptionInfo.get().getEndDateSubscription());
        } else {
            resSubscription.setStartDateSubscription(null);
            resSubscription.setEndDateSubscription(null);
        }

        Pricing pricingOpt = pricingRepository.getNewPricing(pricingClone.getPricingDraftId())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.DRAFT_ID,
                String.valueOf(pricingClone.getPricingDraftId())));

        if (pricingOpt.getId().equals(id)) {
            resSubscription.setStatusVersion(StatusVersionEnum.UP_TO_DATE);
        } else {
            resSubscription.setStatusVersion(StatusVersionEnum.OUT_OF_DATE);
        }

        //Set đơn vị tính
        if (Objects.nonNull(resSubscription.getUnitId())) {
            unitRepository.findById(resSubscription.getUnitId()).ifPresent(unit -> resSubscription.setUnitName(unit.getName()));
        }

        //Set đơn vị tiền tệ
        if (Objects.nonNull(resSubscription.getCurrencyId())) {
            currencyRepository.findById(resSubscription.getCurrencyId()).ifPresent(
                currency -> resSubscription.setCurrencyName(currency.getCurrencyType())
            );
        }
        // Set thong tin thue
        List<PricingDetailResDTO.Tax> taxList = new ArrayList<>();
        AtomicBoolean checkHavetax = new AtomicBoolean(false);
        List<PricingTaxRes> pricingTax = pricingTaxRepository.getPricingTax(id);
        pricingTax.forEach(t -> {
            PricingDetailResDTO.Tax tax = new PricingDetailResDTO.Tax();
            tax.setTaxId(t.getTaxId());
            tax.setTaxName(t.getTaxName());
            tax.setHasTax(t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()));
            tax.setPercent(t.getPercent());
            taxList.add(tax);
            if (Objects.equals(YesNoEnum.valueOf(t.getHasTax()), YesNoEnum.YES)) {
                checkHavetax.set(true);
            }
        });

        // Thông tin thuế phí thiết lập
        List<PricingDetailResDTO.Tax> setupFeeTaxList = new ArrayList<>();
        List<PricingTaxRes> setupFeeTax = pricingSetupFeeTaxRepository.getPricingSetupFeeTax(id);
        setupFeeTax.forEach(t -> {
            PricingDetailResDTO.Tax tax = new PricingDetailResDTO.Tax();
            tax.setTaxId(t.getTaxId());
            tax.setTaxName(t.getTaxName());
            tax.setHasTax(t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()));
            tax.setPercent(t.getPercent());
            setupFeeTaxList.add(tax);
        });

        // Thực hiện lấy thông tin của level cho gói, kiểm tra xem có phải gói dạng Multiple Period
        if (Objects.isNull(pricingMultiPlanId)) {
            List<UnitLimited> unitLimitedList = unitLimitedRepository.findByPricingIdAndQuantity(id);
            if (Objects.nonNull(unitLimitedList)) {
                List<CalUnitLimitedCustomDTO> limiteds =
                    unitLimitedList.stream().map(x -> new CalUnitLimitedCustomDTO(x.getUnitFrom(), x.getUnitTo(),
                            subscriptionFormula.priceBeforeTax(x.getPrice(), pricingTax)))
                        .collect(Collectors.toList());
                resSubscription.setUnitLimitedList(limiteds);
                if (!CollectionUtils.isEmpty(limiteds)) {
                    resSubscription.setMinimumQuantity(limiteds.stream()
                            .map(CalUnitLimitedCustomDTO::getUnitFrom)
                            .filter(Objects::nonNull)
                            .min(Long::compare)
                            .orElse(-1L));
                    resSubscription.setMaximumQuantity((limiteds.stream()
                            .map(CalUnitLimitedCustomDTO::getUnitTo)
                            .filter(Objects::nonNull)
                            .max((a, b) -> {
                                if (a.equals(-1L)) return 1;
                                if (b.equals(-1L)) return -1;
                                return a.compareTo(b);
                            })
                            .orElse(-1L)));
                }
            }
        } else {
            List<CalUnitLimitedCustomDTO> limiteds = new ArrayList<>();
            pricingPlanDetailRepository.findByPricingMultiPlanIdOrderByUnitFromAsc(pricingMultiPlanId).ifPresent(
                unitLimits -> unitLimits.forEach(unitLimited -> {
                    CalUnitLimitedCustomDTO limited = new CalUnitLimitedCustomDTO();
                    limited.setUnitFrom(Long.valueOf(unitLimited.getUnitFrom()));
                    limited.setUnitTo(unitLimited.getUnitTo() == -1 ? null : Long.valueOf(unitLimited.getUnitTo()));
                    limited.setPrice(subscriptionFormula.priceBeforeTax(unitLimited.getPrice(), pricingTax));
                    limiteds.add(limited);
                }));
            resSubscription.setUnitLimitedList(limiteds);
            if (!CollectionUtils.isEmpty(limiteds)) {
                resSubscription.setMinimumQuantity(limiteds.stream()
                        .map(CalUnitLimitedCustomDTO::getUnitFrom)
                        .filter(Objects::nonNull)
                        .min(Long::compare)
                        .orElse(-1L));
                resSubscription.setMaximumQuantity((limiteds.stream()
                        .map(CalUnitLimitedCustomDTO::getUnitTo)
                        .filter(Objects::nonNull)
                        .max((a, b) -> {
                            if (a.equals(-1L)) return 1;
                            if (b.equals(-1L)) return -1;
                            return a.compareTo(b);
                        })
                        .orElse(-1L)));
            }
        }

        resSubscription.setTaxList(taxList);
        resSubscription.setSetupFeeTaxList(setupFeeTaxList);

        // Nếu pricing là FLAT_RATE hoặc UNIT và đã bao gồm thuế thì đơn giá hiển thị giá tiền trước thuế
        if (checkHavetax.get() && (Objects.equals(resSubscription.getPricingPlan(), PricingPlanEnum.FLAT_RATE) ||
            Objects.equals(resSubscription.getPricingPlan(), PricingPlanEnum.UNIT))) {
            resSubscription.setPrice(subscriptionFormula.priceBeforeTax(pricingClone.getPrice(), pricingTax));
        }

        // List<String> roles = AuthUtil.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority)
        //    .collect(Collectors.toList());

        // chỉ hiện ra cac coupon có cùng đối tượng khách hàng với user đăng nhập
        Set<Coupon> couponSetFilter = couponSet.stream().filter(cs -> cs.getCustomerTypeCode().contains(customerTypeCurrentUser)).collect(
            Collectors.toSet());

        // set thông tin khuyến mại
        couponSetFilter.forEach(c -> {
            boolean checkValid = (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value))
                && ((!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                && !Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || !Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value))
                && (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || !Objects.equals(c.getAddonsType(), AddonTypeEnum.NONE.value));
            boolean checkEnterpriseType = Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value);
            if (checkValid && !checkEnterpriseType
                && validateCoupons(c, null, CouponConst.COUPON_OF_PRICING)
                && (Objects.equals(c.getPortal(), PortalType.ADMIN.getType())
                || (Objects.equals(c.getPortal(), PortalType.DEV.getType()) && Objects.equals(c.getUserId(), pricingOwnerId)))) {
                SubscriptionPricingAddonDTO.CouponList coupon = new SubscriptionPricingAddonDTO.CouponList();
                coupon.setCouponId(c.getId());
                coupon.setCouponName(c.getName());
                coupon.setCode(c.getPromotionCode());
                coupon.setTimesUsedType(
                    TimeUsedTypeEnum.valueOf(ObjectUtil.getOrDefault(c.getTimesUsedType(), TimeUsedTypeEnum.UNLIMITED.value)));
                coupon.setLimitedQuantity((c.getLimitedQuantity()));
                coupon.setMinimum(c.getMinimum());
                coupon.setMinimumAmount(c.getMinimumAmount());
                coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                coupon.setDiscountValue(c.getDiscountValue());
                coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                coupon.setDiscountAmount(c.getDiscountAmount());
                coupon.setMaximum((c.getMinimum()));
                coupon.setType(Objects.nonNull(c.getType()) ? TimeTypeEnum.valueOf(c.getType()) : null);
                coupon.setEndDate(c.getEndDate());
                coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_PRICING));
                coupon.setOriginCode(c.getCode());
                coupon.setVisibleStatus(c.getVisibleStatus());
                List<CouponSet> couponSets = couponSetRepository.findAllByCouponId(c.getId());
                if (!couponSets.isEmpty()) {
                    coupon.setExistCouponSet(true);
                }
                coupon.setAddonDepend(getDependAddonByCouponId(c));
                //set pricings của mỗi couponType = PRODUCT
                if (PromotionTypeEnum.valueOf(c.getPromotionType()).equals(PromotionTypeEnum.PRODUCT)) {
                    coupon.setPricing(getPricingByCoupon(c.getId()));
                } else {
                    coupon.setPricing(null);
                }
                if (couponList.stream().noneMatch(cp -> cp.getCouponId().equals(c.getId()))) {
                    couponList.add(coupon);
                }
            }
        });

        // Set creationLayoutId và danh sách các custom field value
        pricingRepository.findById(id).ifPresent(pricingDraft -> {
            Long layoutId = pricingDraft.getCreationLayoutId();
            layoutId = layoutId != null ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.PRICING.getValue());
            List<CustomFieldValueDTO> lstFieldValueDTO = customFieldManager.getListFieldValue(layoutId, EntityTypeEnum.PRICING.getValue(),
                pricingDraft.getId());
            resSubscription.setCreationLayoutId(layoutId);
            resSubscription.setLstCustomFields(lstFieldValueDTO);
        });

        resSubscription.setCouponList(couponList);

        // kiểm tra quyền đăng ký gói dịch vụ
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            Integer numSub = shoppingCartRepository.getNumSubOfServiceUser(false, pricingClone.getServiceId(), AuthUtil.getCurrentParentId());
            Optional<ServiceEntity> service = serviceRepository.findByIdAndDeletedFlag(pricingClone.getServiceId(), 1);
            if (Objects.nonNull(subscriptionId) && subscriptionId.equals(-1L) && service.get().getAllowMultiSub() == 0 && numSub > 0) {
                throw exceptionFactory.badRequest(MessageKeyConstant.DATA_EXISTS, Resources.SERVICES, ErrorKey.ID,
                    String.valueOf(pricingClone.getServiceId()));
            }
        }
        resSubscription.setAllowSubscript(yesNoEnum);

        // lấy thông tin Addon
        List<SubscriptionPricingAddonDTO> lstAddonDb = getAddonSubscription(pricingClone, subscriptionId, pricingMultiPlanId);
        lstAddonDb = lstAddonDb.stream().filter(x -> Objects.nonNull(x.getPricingPlan())).collect(Collectors.toList());
        //lọc addon theo đối tượng khách hàng
        resSubscription.setAddonList(filterAddonByCustomerType(lstAddonDb));

        resSubscription.setCoupons(getSubscriptionCoupon(id));
        String paramValue = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
        if (Objects.nonNull(paramValue)) {
            resSubscription.setCouponConfig(SystemParamEnum.findByStatusId(Integer.parseInt(paramValue)));
        }

        if (subscriptionInfo.isPresent()) {
            String versionSubscription = subscriptionInfo.get().getRegType() != null ?
                RegTypeEnum.valueOf(subscriptionInfo.get().getRegType()).toString() : null;
            resSubscription.setVersionSubscription(versionSubscription);
            resSubscription.setSubscriptionId(subscriptionInfo.get().getId());
        }

        if (systemParamService.checkCanOfflinePayment()) {
            resSubscription.setOfflinePaymentConfig(YesNoEnum.YES);
        } else {
            resSubscription.setOfflinePaymentConfig(YesNoEnum.NO);
        }
        // Lấy thông tin urlPreOrder
        Set<ComboDetailDTO> urlPreOrder = subscriptionRepository.getPricingAndUrlPreOrder(pricingClone.getId())
            .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
        resSubscription.setUrlPreOrder(urlPreOrder);
        resSubscription.setSeoDTO(seoService.getSeoDetailSme(pricingClone.getPricingDraftId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU));
        return resSubscription;
    }


    /**
     * validate pricing khi đăng ký subscription
     */
    private void validateCustomerTypeCode(Pricing pricing, PricingMultiPlan pricingMultiPlan, String customerType) {
        String message = messageSource
            .getMessage(MessageKeyConstant.NO_HAVE_ACCESS, pricingMessage, LocaleContextHolder.getLocale());
        if (!AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.DEVELOPER.getValue()))
            && Objects.nonNull(pricingMultiPlan)
            && !CollectionUtils.isEmpty(pricingMultiPlan.getCustomerTypeCode())
            && !pricingMultiPlan.getCustomerTypeCode().contains(customerType)) {
            throw new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.Services.CUSTOMER_TYPE, MessageKeyConstant.NO_HAVE_ACCESS);
        } else if (!Objects.equals(CustomerTypeEnum.UNSET.name(), customerType) && !CollectionUtils.isEmpty(pricing.getCustomerTypeCode())
            && !pricing.getCustomerTypeCode().contains(customerType)) {
            throw new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.Services.CUSTOMER_TYPE, MessageKeyConstant.NO_HAVE_ACCESS);
        }
    }

    /**
     * Kiem tra subcription hop le
     */
    private Subscription validateSubscription(Long id, PortalType portalType) {
        String msg = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
            new Long[]{id}, LocaleContextHolder.getLocale());
        if (PortalType.ADMIN.equals(portalType) && AuthUtil.checkUserRoles(
            Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue()))) {
            Long provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
            //neu provinceId not null -> la admin tinh
            if (Objects.nonNull(provinceId)) {
                return subscriptionRepository.getSubscriptionBySMEProvinceId(id, provinceId)
                    .orElseThrow(() -> new BadRequestException(msg, Resources.SUBSCRIPTION,
                        ErrorKey.Subscription.ID, MessageKeyConstant.NOT_FOUND));
            }
            return findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        } else if (AuthUtil.checkUserRoles(
            Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_BUSINESS
                .getValue(), RoleType.DEVELOPER_OPERATOR.getValue())) && PortalType.DEV.equals(portalType)) {
            Subscription subscription = findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
            Long provider;
            if (Objects.isNull(subscription.getPricingId()) && Objects.isNull(subscription.getComboPlanId())) { // thiết bị
                provider = serviceRepository.findOwnerService(subscription.getServiceId());
            } else {
                provider = Objects.nonNull(subscription.getPricingId()) ? pricingRepository.findOwnerPricing(subscription.getPricingId())
                    : comboRepository.getComboPlanOwnerId(subscription.getComboPlanId());
            }
            if (!AuthUtil.getCurrentParentId().equals(provider)) {
                throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
            }
            return subscription;
        } else if (PortalType.SME.equals(portalType)) {
            return subscriptionRepository.getSubscriptionByIdAndUserId(id, AuthUtil.getCurrentParentId())
                .orElseThrow(() -> exceptionFactory.permissionDenied(ExceptionConstants.NEED_PERMISSION));
        } else {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
    }

    /**
     * Kiem tra pricing hop le
     */
    private Pricing validatePricing(Long id) {
        return pricingService.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
    }

    /**
     * Gets depend addon by coupon id.
     *
     * @param c the c
     * @return the depend addon by coupon id
     */
    private List<Long> getDependAddonByCouponId(Coupon c) {
        List<CouponAddonsDetailDTO> couponAddon = new ArrayList<>();
        if (Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value)) {
            couponAddon = couponAddonRepository.getCouponAddon(c.getId());
        } else if (Objects.equals(c.getAddonsType(), AddonTypeEnum.ALL.value)) {
            couponAddon = couponAddonRepository.getCouponAddonAllOption(c.getId());
        }
        return couponAddon.stream().map(CouponAddonsDetailDTO::getAddonsId).collect(Collectors.toList());
    }

    /**
     * Gets depend addon by coupon id.
     *
     * @param c the c
     * @return the depend addon by coupon id
     */
    private List<Long> getDependAddonByCouponId(SubscriptionPricingCouponDTO c) {
        List<CouponAddonsDetailDTO> couponAddon = new ArrayList<>();
        if (Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value)) {
            couponAddon = couponAddonRepository.getCouponAddon(c.getCouponId());
        } else if (Objects.equals(c.getAddonsType(), AddonTypeEnum.ALL.value)) {
            couponAddon = couponAddonRepository.getCouponAddonAllOption(c.getCouponId());
        }
        return couponAddon.stream().map(CouponAddonsDetailDTO::getAddonsId).collect(Collectors.toList());
    }

    /**
     * Lấy thông tin pricing bởi coupon_id
     */
    private List<SubscriptionPricingAddonDTO.PricingByCouponId> getPricingByCoupon(Long couponId) {
        List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupons = new ArrayList<>();
        couponRepository.getPricingByCoupon(couponId).forEach(p -> {
            SubscriptionPricingAddonDTO.PricingByCouponId pricingCoupon = new SubscriptionPricingAddonDTO.PricingByCouponId();
            pricingCoupon.setPricingId(p.getPricingId());
            pricingCoupon.setPricingMultiPlanId(p.getPricingMultiPlanId());
            pricingCoupon.setType(p.getType());
            pricingCoupon.setServiceName(p.getServiceName());
            pricingCoupon.setPricingName(p.getPricingName());
            pricingCoupon.setType(p.getType());
            pricingCoupons.add(pricingCoupon);
        });
        return pricingCoupons;
    }

    /**
     * Validate coupons.
     *
     * @param coupon the coupon
     * @return the boolean
     */
    private boolean validateCoupons(Coupon coupon, Long userId, String classify) {
        boolean isValid = true;
        Long currentUserId = Objects.nonNull(userId) ? userId : AuthUtil.getCurrentParentId();
        //Neu ap dung cho doanh nghiep can check user login
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)) {
            List<CouponEnterpriseDetailDTO> couponEnterprise = couponEnterpriseRepository
                .getCouponEnterprise(coupon.getId());
            long count = couponEnterprise.stream()
                .filter(cou -> Objects.equals(cou.getUserId(), currentUserId))
                .count();
            if (count == 0) {
                isValid = false;
            }
        }

        //Neu dich vu bo sung OPTION -> can phai mua ca dich vu bo sung va goi moi duoc khuyen mai
//        if (isValid && Objects.equals(coupon.getAddonsType(), EnterpriseTypeEnum.OPTION.value)) {
//            List<CouponAddon> couponAddons = couponAddonRepository.findByAddonsId(coupon.getId());
//            List<Long> addonIds = couponAddons.stream().map(x -> x.getAddonsId()).collect(Collectors.toList());
//
//            List<Long> currentAddon = subscriptionFirstStepReqDTO.getAddons().stream()
//                                                                 .filter(x -> Objects.nonNull(x.getQuantity()) && x.getQuantity() > 0)
//                                                                 .map(x -> x.getId()).collect(Collectors.toList());
//            if (!Objects.equals(addonIds, currentAddon)) {
//                isValid = false;
//            }
//        } else if (isValid && Objects.equals(coupon.getAddonsType(), EnterpriseTypeEnum.NONE.value)) {
//            isValid = false;
//        }

        //DK1: Check số lần áp dụng
        if (isValid && Objects.nonNull(coupon.getMaximumPromotion())) {
            Long companyUsedCoupon;
            if (currentUserId != null) {
                companyUsedCoupon = subscriptionRepository.countNumberOfTimeTheCompanyUsedCoupon(currentUserId, coupon.getId());
            } else {
                companyUsedCoupon = null;
            }
            if (companyUsedCoupon != null && companyUsedCoupon >= coupon.getMaximumPromotion()) {
                isValid = false;
            }
        }

        //DK: Nếu đều
        //Nếu 4 cái đều ko chọn thì false
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            isValid = false;
        }
        //Nếu là KM của pricing mà ko chọn doanh nghiệp hoạc ko chọn gói thì true
        else if ((Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN))
            && ((Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value))
            || ((Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
            || Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
            || (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)))) {
            isValid = false;
        } else if ((Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN)
            || Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING)
            || Objects.equals(classify, CouponConst.COUPON_TOTAL))
            && !(Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.YES.value))) {
            isValid = false;
        }
        //Nếu là KM của addon  mà ko chọn doanh nghiệp hoạc ko chọn DVBS thì true
        else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            isValid = false;
        }
        //DK2: check thời gian
        LocalDate startDate = Objects.isNull(coupon.getStartDate()) ? SubscriptionConstant.LOCAL_DATE_MIN_DATE
            : coupon.getStartDate();
        LocalDate endDate = Objects.isNull(coupon.getEndDate()) ? SubscriptionConstant.LOCAL_DATE_MAX_DATE
            : coupon.getEndDate();
        if (isValid && (LocalDate.now().isBefore(startDate)
            || LocalDate.now().isAfter(endDate))) {
            isValid = false;
        }

        //DK3: check số lượng tối thiểu
//        if (isValid) {
//            List<SubscriptionFirstStepReqDTO.Addon> addonList = subscriptionFirstStepReqDTO.getAddons().stream().
//                    filter(x -> Objects.nonNull(x.getQuantity()) && x.getQuantity() > 0).collect(Collectors.toList());
//            int count = 0;
//            for (SubscriptionFirstStepReqDTO.Addon addon : addonList) {
//                count += addon.getQuantity();
//            }
//            Long minimum = Objects.isNull(coupon.getMinimum()) ? 0 : coupon.getMinimum();
//            if (count > minimum) {
//                isValid = false;
//            }
//        }

        //DK4: Check số lần hưởng khuyến mại tối đa
        if (isValid && Objects.nonNull(coupon.getMaxUsed())) {
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(coupon.getId());
            if (subscriptionUsedCoupon >= coupon.getMaxUsed()) {
                isValid = false;
            }
        }

        //DK5: Check số tiền nhỏ nhất áp dụng
//        if (isValid) {
//            BigDecimal minimumAmount = Objects.isNull(coupon.getMinimumAmount()) ? BigDecimal.ZERO : new BigDecimal(coupon.getMinimumAmount());
//            if (totalAmount.compareTo(minimumAmount) < 0) {
//                isValid = false;
//            }
//        }

        //DK6, DK7: CHi kiem tra khi thuc hien thanh toan voi chu ky
        return isValid;
    }

    /**
     * Lấy thông tin chi tiết Addon
     *
     * @param subscriptionId id subscription
     */
    private List<SubscriptionPricingAddonDTO> getAddonSubscription(Pricing pricing, Long subscriptionId, Long pricingMultiPlanId) {
        //lay thong tin nguoi tao pricing
        userService.findByIdAndDeletedFlag(pricing.getCreatedBy(), DeletedFlag.NOT_YET_DELETED.getValue());
        List<SubscriptionPricingPeriodAddonDTO> periodAddonDTOS = pricingRepository
            .getAddonByPricingIdAndPricingMultiPlanId(pricing.getId(), pricingMultiPlanId != null ? pricingMultiPlanId : DEFAULT_ID);

        List<SubscriptionPricingAddonDTO> resAddon = new ArrayList<>();
        for (SubscriptionPricingPeriodAddonDTO dto : periodAddonDTOS) {
            SubscriptionPricingAddonDTO res = new SubscriptionPricingAddonDTO();
            res.setId(dto.getId());
            res.setAddonMultiPlanId(dto.getPricingMultiPlanId() > DEFAULT_SUCCESS_ID ? dto.getPricingMultiPlanId() : DEFAULT_ID);
            res.setName(dto.getName());
            res.setCode(dto.getCode());
            res.setDescription(dto.getDescription());
            res.setIsRequired(dto.getIsRequired() == null ? YesNoEnum.NO : YesNoEnum.valueOf(dto.getIsRequired()));
            res.setPricingPlan(dto.getPricingPlan() == null ? null : PricingPlanEnum.valueOf(dto.getPricingPlan()));
            res.setBonusType(dto.getBonusType() == null ? null : BonusTypeEnum.valueOf(dto.getBonusType()));
            res.setBonusValue(dto.getBonusValue());
            res.setUnitId(dto.getUnitId());
            res.setUnitName(dto.getUnitName());
            res.setType(dto.getType() == null ? null : PeriodTypeEnum.valueOf(dto.getType()));
            res.setFreeQuantity(dto.getFreeQuantity());
            res.setSetupFee(dto.getSetupFee());
            res.setPrice(dto.getPrice());
            res.setMinimumQuantity(Objects.nonNull(dto.getMinimumQuantity()) ? dto.getMinimumQuantity() : null);
            res.setMaximumQuantity(Objects.nonNull(dto.getMaximumQuantity()) ? dto.getMaximumQuantity() : null);
            resAddon.add(res);
        }

        List<SubscriptionPricingAddonDTO> oldAddon = new ArrayList<>();
        Long pricingOwnerId = pricingRepository.findOwnerPricing(pricing.getId());
        if (Objects.nonNull(subscriptionId) && subscriptionId > DEFAULT_SUCCESS_ID) {
            Long paymentCycle = Objects.nonNull(pricing.getPaymentCycle()) ? Long.valueOf(pricing.getPaymentCycle()) : DEFAULT_ID;
            Integer cycleType = Objects.nonNull(pricing.getCycleType()) ? pricing.getCycleType() : -1;
            Long pricingId = Objects.nonNull(pricing.getId()) ? pricing.getId() : DEFAULT_ID;
            Set<SubscriptionAddonOfPricingDTO> subOldAddon = addonRepository
                .getAddonBySubscriptionId(subscriptionId, pricingId,
                    paymentCycle, cycleType, pricingMultiPlanId != null ? pricingMultiPlanId : DEFAULT_ID);
            // Lấy paymentCycle mới nhất của gói mưới nhất
            Long pricingDraftId = Objects.nonNull(pricing.getPricingDraftId()) ? pricing.getPricingDraftId() : DEFAULT_ID;
            Long lastPricingDBId = pricingRepository.findMaxPricingId(pricingDraftId).orElse(DEFAULT_ID);
            Long multiPlanDBIdNew = pricingMultiPlanRepository.findMaxPricingMultiPlanById(
                ObjectUtil.getOrDefault(pricingMultiPlanId, DEFAULT_ID)).orElse(DEFAULT_ID);
            subOldAddon.addAll(
                addonRepository.getAddonByPricingMultiPlan(subscriptionId, pricingMultiPlanId != null ? pricingMultiPlanId : DEFAULT_ID,
                    paymentCycle, cycleType, pricingId, lastPricingDBId, multiPlanDBIdNew));
            subOldAddon.forEach(s -> {
                SubscriptionPricingAddonDTO subPricingAddon = new SubscriptionPricingAddonDTO();
                BeanUtils.copyProperties(s, subPricingAddon);
                subPricingAddon.setAddonMultiPlanId(Objects.nonNull(s.getAddonMultiPlanId()) ? s.getAddonMultiPlanId() : DEFAULT_ID);
                oldAddon.add(subPricingAddon);
            });
            resAddon.addAll(oldAddon);
        }

        resAddon = resAddon.stream()
            .filter(CheckUtil.distinctByKeys(SubscriptionPricingAddonDTO::getId, SubscriptionPricingAddonDTO::getAddonMultiPlanId))
            .collect(Collectors.toList());

        List<Long> addonIds = resAddon.stream().map(SubscriptionPricingAddonDTO::getId).collect(Collectors.toList());
        List<PricingTaxRes> listTaxs = addonsTaxRepository.getAddonTax(addonIds);
        List<PricingTaxRes> addonSetupFeeList = addonsTaxRepository.getAddonSetupFeeTaxByIds(addonIds);

        // Phân loại addon id
        List<Long> addonOnceIDs = new ArrayList<>();
        List<Long> addonMultiIDs = new ArrayList<>();

        resAddon.forEach(x -> {
            if (Objects.isNull(x.getAddonMultiPlanId()) || Objects.equals(x.getAddonMultiPlanId(), DEFAULT_ID)) {
                addonOnceIDs.add(x.getId());
            } else {
                addonMultiIDs.add(x.getAddonMultiPlanId());
            }
        });
        List<AddonCouponResDTO> addonOnceCoupon;

        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            addonOnceCoupon = addonRepository.getCouponAddonByAddonIds(addonOnceIDs, AuthUtil.getCurrentParentId(), addonMultiIDs);
        } else {
            addonOnceCoupon = null;
        }

        List<AddonCouponResDTO> finalAddonOnceCoupon = addonOnceCoupon;
        resAddon.forEach(i -> {
//            set Tax by addon_id
            List<SubscriptionPricingAddonDTO.Tax> taxList = new ArrayList<>();
            List<SubscriptionPricingAddonDTO.Tax> taxSetupFeeList = new ArrayList<>();
            List<SubscriptionPricingAddonsDTO.UnitLimited> unitLimitedList = new ArrayList<>();
            List<PricingTaxRes> pricingTax = new ArrayList<>();
            if (!CollectionUtils.isEmpty(listTaxs)) {
                pricingTax = listTaxs.stream().filter(t -> i.getId()
                    .equals(t.getAddonId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pricingTax)) {
                    pricingTax.forEach(t -> {
                        SubscriptionPricingAddonDTO.Tax tax = new SubscriptionPricingAddonDTO.Tax();
                        tax.setTaxId(t.getTaxId());
                        tax.setTaxName(t.getTaxName());
                        tax.setPercent(t.getPercent());
                        tax.setHasTax(Objects.isNull(t.getHasTax()) ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()));
                        taxList.add(tax);
                    });
                }
            }
            if (!CollectionUtils.isEmpty(addonSetupFeeList)) {
                List<PricingTaxRes> addonSetupFee = addonSetupFeeList.stream().filter(t -> Objects.equals(i.getId(), t.getAddonId()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(addonSetupFee)) {
                    addonSetupFee.forEach(
                        t -> taxSetupFeeList.add(new SubscriptionPricingAddonDTO.Tax(t.getTaxId(), t.getTaxName(), t.getPercent(),
                            t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()))));
                }
            }
            List<PricingTaxRes> finalPricingTax = pricingTax;
            if (Objects.equals(PricingPlanEnum.TIER, i.getPricingPlan()) ||
                Objects.equals(PricingPlanEnum.VOLUME, i.getPricingPlan()) ||
                Objects.equals(PricingPlanEnum.STAIR_STEP, i.getPricingPlan())) {
                if (i.getAddonMultiPlanId() == null || i.getAddonMultiPlanId() == -1L) {
                    // Loại addon 1 lần
                    List<UnitLimited> addonUnit = unitLimitedRepository.findByAddonsIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromAsc(
                        i.getId());
                    if (!CollectionUtils.isEmpty(addonUnit)) {
                        addonUnit.forEach(au -> {
                            if (Objects.isNull(au.getSubscriptionSetupFeeId())) {
                                SubscriptionPricingAddonsDTO.UnitLimited unitLimited = new SubscriptionPricingAddonsDTO.UnitLimited();
                                unitLimited.setUnitTo(au.getUnitTo() == -1 ? null : au.getUnitTo());
                                unitLimited.setUnitFrom(au.getUnitFrom());
                                unitLimited.setAddonsId(au.getAddonsId());
                                unitLimited.setPricingId(au.getPricingId());
                                unitLimited.setPrice(subscriptionFormula.priceBeforeTax(au.getPrice(), finalPricingTax));
                                unitLimitedList.add(unitLimited);
                            }
                        });
                    }
                } else {
                    // Loại addon riêng dạng multi period
                    pricingPlanDetailRepository.findByPricingMultiPlanIdOrderByUnitFromAsc(i.getAddonMultiPlanId()).ifPresent(
                        unitLimits -> unitLimits.forEach(unitLimited -> {
                            if (Objects.isNull(unitLimited.getSubscriptionSetupFeeId())) {
                                SubscriptionPricingAddonsDTO.UnitLimited limited = new SubscriptionPricingAddonsDTO.UnitLimited();
                                limited.setUnitFrom(Long.valueOf(unitLimited.getUnitFrom()));
                                limited.setUnitTo(unitLimited.getUnitTo() == -1 ? null : Long.valueOf(unitLimited.getUnitTo()));
                                limited.setAddonsId(i.getId());
                                limited.setPricingId(pricing.getId());
                                limited.setAddonMultiPeriodId(i.getAddonMultiPlanId());
                                limited.setPrice(subscriptionFormula.priceBeforeTax(unitLimited.getPrice(), finalPricingTax));
                                unitLimitedList.add(limited);
                            }
                        }));
                }
            }
            i.setUnitLimitedList(unitLimitedList);
            if (!CollectionUtils.isEmpty(unitLimitedList)) {
                i.setMinimumQuantity(unitLimitedList.stream()
                        .map(SubscriptionPricingAddonsDTO.UnitLimited::getUnitFrom)
                        .filter(Objects::nonNull)
                        .min(Long::compare)
                        .orElse(-1L));
                i.setMaximumQuantity((unitLimitedList.stream()
                        .map(SubscriptionPricingAddonsDTO.UnitLimited::getUnitTo)
                        .filter(Objects::nonNull)
                        .max((a, b) -> {
                            if (a.equals(-1L)) return 1;
                            if (b.equals(-1L)) return -1;
                            return a.compareTo(b);
                        })
                        .orElse(-1L)));
            }
            if (!unitLimitedList.isEmpty()) {
                BigDecimal getPriceFirstUnitLimited = unitLimitedList.get(0).getPrice();
                i.setPrice(getPriceFirstUnitLimited);
            }

//          Set coupon by addon_id
            List<SubscriptionPricingAddonDTO.CouponList> couponList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(finalAddonOnceCoupon)) {
                List<AddonCouponResDTO> coupons = new ArrayList<>();
                List<AddonCouponResDTO> addonOnceList = finalAddonOnceCoupon.stream()
                    .filter(cp -> (i.getId().equals(cp.getAddonId()) || Objects.equals(cp.getAddonsType(), AddonTypeEnum.ALL.value))
                        && (Objects.equals(cp.getPortal(), PortalType.ADMIN.getType())
                        || (Objects.equals(cp.getPortal(), PortalType.DEV.getType()) && Objects.equals(cp.getUserId(), pricingOwnerId))))
                    .collect(Collectors.toList());
                coupons.addAll(addonOnceList);
                if (!CollectionUtils.isEmpty(coupons)) {
                    coupons.forEach(c -> {
                        boolean checkEnterpriseType = Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value);
                        if (validateCouponDTO(c) && !checkEnterpriseType) {
                            SubscriptionPricingAddonDTO.CouponList coupon = new SubscriptionPricingAddonDTO.CouponList();
                            coupon.setCouponId(c.getCouponId());
                            coupon.setCouponName(c.getCouponName());
                            coupon.setCode(c.getCode());
                            coupon.setTimesUsedType(TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
                            coupon.setLimitedQuantity(c.getLimitedQuantity());
                            coupon.setMinimum(c.getMinimum());
                            coupon.setMinimumAmount(c.getMinimumAmount());
                            coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                            coupon.setDiscountValue(c.getDiscountValue());
                            coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                            coupon.setDiscountAmount(c.getDiscountAmount());
                            coupon.setMaximum(c.getMinimum());
                            coupon.setType(Objects.nonNull(c.getType()) ? TimeTypeEnum.valueOf(c.getType()) : null);
                            coupon.setEndDate(Objects.nonNull(c.getEndDate()) ?
                                LocalDate.parse(c.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null);
                            coupon.setConditions(couponService.getCouponConditions(c.getCouponId(), CouponConst.COUPON_OF_ADDON));
                            coupon.setOriginCode(c.getCode());
                            coupon.setVisibleStatus(c.getVisibleStatus());
                            coupon.setAddonDepend(getDependAddonByCouponIdDTO(c));
                            //set pricings của mỗi couponType = PRODUCT
                            if (PromotionTypeEnum.valueOf(c.getPromotionType()).equals(PromotionTypeEnum.PRODUCT)) {
                                coupon.setPricing(getPricingByCoupon(c.getCouponId()));
                            } else {
                                coupon.setPricing(null);
                            }
                            couponList.add(coupon);
                        }
                    });
                }
            }
            i.setTax(taxList);
            i.setTaxSetupFee(taxSetupFeeList);
            i.setCouponList(couponList);
            i.setPrice(subscriptionFormula.getPriceBeforeTax(i.getPrice(), taxList));
            if (Objects.isNull(i.getIsRequired())) {
                i.setIsRequired(YesNoEnum.NO);
            }
        });

        Comparator<SubscriptionPricingAddonDTO> comp = Comparator.comparing(item -> item.getIsRequired().value);
        resAddon.sort(comp.reversed());
        return resAddon;
    }

    private List<SubscriptionPricingAddonDTO> filterAddonByCustomerType(List<SubscriptionPricingAddonDTO> input) {
        List<SubscriptionPricingAddonDTO> lstAddonResult = new ArrayList<>(input);
        if (!input.isEmpty()) {
            Set<Long> resultId = new HashSet<>();
            Set<Long> lstIdAddon = new HashSet<>();
            for (SubscriptionPricingAddonDTO e : input) {
                lstIdAddon.add(e.getId());
            }
            Set<Addon> listAddon = addonRepository.findByIdIn(lstIdAddon);
            String customerTypeCurrent = userRepository.getCustomerTypeById(AuthUtil.getCurrentUserId());
            if (!listAddon.isEmpty()) {
                for (Addon addon : listAddon) {
                    if (addon.getCustomerTypeCode().contains(customerTypeCurrent)) {
                        resultId.add(addon.getId());
                    }
                }
            }
            if (!resultId.isEmpty()) {
                for (SubscriptionPricingAddonDTO e : input) {
                    if (!resultId.contains(e.getId())) {
                        lstAddonResult.remove(e);
                    }
                }
            }
        }
        return lstAddonResult;
    }

    /**
     * Lấy danh sách coupon của Subscription trên tổng hóa đơn
     *
     * @return subscription coupon
     */
    private List<SubscriptionPricingAddonDTO.CouponList> getSubscriptionCoupon(Long pricingId) {
        Long pricingOwnerId = pricingRepository.findOwnerPricing(pricingId);
        List<SubscriptionPricingAddonDTO.CouponList> coupons = new ArrayList<>();
        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            couponRepository.getCouponSubscription(AuthUtil.getCurrentParentId()).stream()
                .filter(cs -> cs.getCustomerTypeCode().contains(AuthUtil.getCurrentUser().getCustomerType())).forEach(sa -> {
                    SubscriptionPricingAddonDTO.CouponList coupon = new SubscriptionPricingAddonDTO.CouponList();
                    coupon.setCouponId(sa.getId());
                    coupon.setCouponName(sa.getName());
                    coupon.setCode(sa.getPromotionCode());
                    coupon.setTimesUsedType(TimeUsedTypeEnum.valueOf(sa.getTimesUsedType()));
                    coupon.setLimitedQuantity((sa.getLimitedQuantity()));
                    coupon.setMinimum(sa.getMinimum());
                    coupon.setMinimumAmount(sa.getMinimumAmount());
                    coupon.setPromotionType(sa.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(sa.getPromotionType()));
                    coupon.setDiscountValue(sa.getDiscountValue());
                    coupon.setDiscountType(sa.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(sa.getDiscountType()));
                    coupon.setDiscountAmount(sa.getDiscountAmount());
                    coupon.setMaximum((sa.getMaximumPromotion()));
                    coupon.setType(Objects.nonNull(sa.getType()) ? TimeTypeEnum.valueOf(sa.getType()) : null);
                    coupon.setEndDate(sa.getEndDate());
                    coupon.setConditions(couponService.getCouponConditions(sa, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING));
                    coupon.setOriginCode(sa.getCode());
                    coupon.setVisibleStatus(sa.getVisibleStatus());
                    List<CouponSet> couponSets = couponSetRepository.findAllByCouponId(sa.getId());
                    if (!couponSets.isEmpty()) {
                        coupon.setExistCouponSet(true);
                    }
                    boolean checkValid = !Objects.equals(sa.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                        || !Objects.equals(sa.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                        || !Objects.equals(sa.getTotalBillType(), TotalBillTypeEnum.NO.value)
                        || !Objects.equals(sa.getAddonsType(), AddonTypeEnum.NONE.value);
                    if (checkValid
                        && validateCoupons(sa, null, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING)
                        && (Objects.equals(sa.getPortal(), PortalType.ADMIN.getType())
                        || (Objects.equals(sa.getPortal(), PortalType.DEV.getType()) && Objects.equals(sa.getUserId(), pricingOwnerId)))) {
                        coupon.setAddonDepend(getDependAddonByCouponId(sa));
                        //set pricings của mỗi couponType = PRODUCT
                        if (PromotionTypeEnum.valueOf(sa.getPromotionType()).equals(PromotionTypeEnum.PRODUCT)) {
                            coupon.setPricing(getPricingByCoupon(sa.getId()));
                        } else {
                            coupon.setPricing(null);
                        }
                        coupons.add(coupon);
                    }
                });
        }
        return coupons;
    }

    /**
     * Validate couponDTO.
     *
     * @param coupon the coupon
     * @return the boolean
     */
    private boolean validateCouponDTO(AddonCouponResDTO coupon) {
        boolean isValid = true;

        //Neu ap dung cho doanh nghiep can check user login
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)) {
            List<CouponEnterpriseDetailDTO> couponEnterprise = couponEnterpriseRepository.getCouponEnterprise(coupon.getCouponId());
            long count = couponEnterprise.stream().filter(cou -> Objects.equals(cou.getUserId(), AuthUtil.getCurrentUserId())).count();
            if (count == 0) {
                isValid = false;
            }
        }
        //DK1: Check số lần áp dụng
        if (isValid && Objects.nonNull(coupon.getMaximumPromotion())) {
            Long companyUsedCoupon = subscriptionRepository.countNumberOfTimeTheCompanyUsedCoupon(AuthUtil.getCurrentUserId(),
                coupon.getCouponId());
            if (companyUsedCoupon >= coupon.getMaximumPromotion()) {
                isValid = false;
            }
        }

        //DK2: check thời gian
        LocalDate startDate = Objects.isNull(coupon.getStartDate()) ? SubscriptionConstant.LOCAL_DATE_MIN_DATE
            : DateUtil.toLocalDate(coupon.getStartDate(), DateUtil.FORMAT_YYYY_MM_DD);
        LocalDate endDate = Objects.isNull(coupon.getEndDate()) ? SubscriptionConstant.LOCAL_DATE_MAX_DATE
            : DateUtil.toLocalDate(coupon.getEndDate(), DateUtil.FORMAT_YYYY_MM_DD).plusDays(1L);
        if (isValid && (LocalDate.now().isBefore(startDate) || LocalDate.now().isAfter(endDate))) {
            isValid = false;
        }

        //DK4: Check số lần hưởng khuyến mại tối đa
        if (isValid && Objects.nonNull(coupon.getMaxUsed())) {
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(coupon.getCouponId());
            if (subscriptionUsedCoupon >= coupon.getMaxUsed()) {
                isValid = false;
            }
        }
        return isValid;
    }

    /**
     * Gets depend addon by coupon id (DTO).
     *
     * @param c the c
     * @return the depend addon by coupon id
     */
    private List<Long> getDependAddonByCouponIdDTO(AddonCouponResDTO c) {
        List<CouponAddonsDetailDTO> couponAddon = new ArrayList<>();
        if (Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value)) {
            couponAddon = couponAddonRepository.getCouponAddon(c.getCouponId());
        } else if (Objects.equals(c.getAddonsType(), AddonTypeEnum.ALL.value)) {
            couponAddon = couponAddonRepository.getCouponAddonAllOption(c.getCouponId());
        }
        return couponAddon.stream().map(CouponAddonsDetailDTO::getAddonsId).collect(Collectors.toList());
    }

    private Subscription findByIdAndDeletedFlag(Long subscriptionId, Integer deletedFlag) {
        return subscriptionRepository.findByIdAndDeletedFlag(subscriptionId, deletedFlag)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SUBSCRIPTION, ErrorKey.ID, String.valueOf(subscriptionId)));
    }

    @Override
    public SubDetailComboResDTO getDetailSubsCombo(Long id) {
        // Check view permission
        crmObjectPermissionUtil.checkViewPermission(CrmObjectTypeEnum.SUBSCRIPTION, id);

        Subscription subscription = subscriptionRepository.findByIdAndDeletedFlagAndUserId(id, DeletedFlag.NOT_YET_DELETED.getValue(),
                AuthUtil.getCurrentParentId())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SUBSCRIPTION, ErrorKey.ID, String.valueOf(id)));

        ComboPlan currentComboPlan = comboPlanService.findByIdAndDeletedFlag(subscription.getComboPlanId(),
            DeletedFlag.NOT_YET_DELETED.getValue());

        SubDetailComboResDTO res = subscriptionDetailComboMapper.toDto(currentComboPlan);

        if (Objects.nonNull(res.getUnitId())) {
            unitRepository.findById(res.getUnitId()).ifPresent(unit -> res.setUnitName(unit.getName()));
        }
        res.setComboPricingList(setComboPricingListDetail(currentComboPlan.getId()));

        //danh sach CTKM cua goi
        List<SubscriptionPricingCouponDTO> comboCoupon = getSubscriptionComboCoupon(id);

        //danh sach addon cua goi
        Set<SubscriptionPricingAddonsDTO> comboAddon = getSubscriptionComboAddon(currentComboPlan.getId(), subscription);

        res.setQuantity(subscription.getQuantity());
        res.setTotalAmount(subscription.getTotalAmount());
        if (Objects.nonNull(currentComboPlan.getCurrencyId())) {
            res.setCurrencyName(currencyRepository.getCurrencyNameById(currentComboPlan.getCurrencyId()));
        }
        res.setComboCouponList(comboCoupon);

        res.setTaxList(comboTaxRepository.getSubscriptionComboTax(currentComboPlan.getId()));
        res.setCouponList(getSubscriptionCouponTotalBill(id));
        res.setComboName(getCurrentCombo(currentComboPlan.getComboId()).getComboName());
        res.setComboId(currentComboPlan.getComboId());
        res.setStartCurrentCycle(subscription.getStartCurrentCycle());
        res.setEndCurrentCycle(subscription.getEndCurrentCycle());
        res.setEndCurrentCycleNew(subscription.getEndCurrentCycleNew());
        res.setExpiredDate(subscription.getExpiredTime());
        res.setSubsPaymentMethod(Objects.isNull(subscription.getPaymentMethod())
            ? null : PaymentMethodEnum.valueOf(subscription.getPaymentMethod()));

        SubscriptionStatusEnum subStatus = subscription.getStatus() == null ? null :
            SubscriptionStatusEnum.valueOf(subscription.getStatus());
        res.setSubscriptionStatus(subStatus);
        res.setDhsxkdSubCode(subscription.getSubCodeDHSXKD());
        //
        VNPTPayResponse vnptPayResponse = vnptPayResponseRepository.findBySubscriptionId(id).orElse(null);
        if (Objects.nonNull(vnptPayResponse)) {
            res.setPaymentCode(Objects.nonNull(vnptPayResponse.getVnptpayTransactionId()) ? vnptPayResponse.getVnptpayTransactionId() : null);
            String bankName = bankRepository.getNameByCode(vnptPayResponse.getPaymentMethod());
            res.setBankName(Objects.nonNull(bankName) ? bankName : null);
        }
        //neu subscription co trang thai hien tai la da huy
        //-> kiem tra xem co duoc phep kich hoat hay khong
        if (SubscriptionStatusEnum.CANCELED.equals(subStatus)) {
            //check hoa don va thoi gian active
            if (checkTimeActiveDate(subscription)) {
                res.setCanActive(YesNoEnum.YES);
            } else {
                res.setCanActive(YesNoEnum.NO);
            }
        }

        //thong tin khach hang
        SubscriptionEnterpriseDTO enterprise = subscriptionRepository.getEnterprise(subscription.getUserId());
        if (!Objects.isNull(enterprise)) {
            res.setSmeName(enterprise.getName());
            res.setSmeId(enterprise.getId());
        }

        //lay ngay bat dau su dung va ngay yeu cau thanh toan
        res.setStartedAt(subscription.getStartedAt());
        res.setStartChargedAt(subscription.getStartChargeAt());

        res.setAddonsList(comboAddon);

        //one time fee
        res.setOnceTimeFee(getOnceTimeFee(id));

        //summary
        SubscriptionSummaryDTO summary = new SubscriptionSummaryDTO();
        summary.setCouponList(getSubscriptionCouponDiscount(id));
        res.setSummary(summary);

        // Lấy thông tin urlPreOrder
        Set<ComboDetailDTO> urlPreOrder = comboPricingRepository.findPricingByComboPlanId(currentComboPlan.getId())
            .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
        res.setUrlPreOrder(urlPreOrder);
        res.setChangeSubscription(Objects.nonNull(currentComboPlan.getIsUpdateNow()) ? ComboChangeEnum.valueOf(currentComboPlan.getIsUpdateNow())
            : ComboChangeEnum.END_OF_PERIOD);
        // Phí thiết lập Tax của combo_plan
        res.setSetupFeeTaxList(comboSetupFeeTaxRepository.findHasTaxByComboPlanId(subscription.getComboPlanId()));
        // Bổ sung phần chi tiết combo common
        if (Objects.nonNull(currentComboPlan.getCombo())) {
            res.setServiceOwner(currentComboPlan.getCombo().getOnOsTypeEnum());
            res.setProvider(currentComboPlan.getCombo().getProvider());
        }
        res.setSubCode(subscription.getFinalSubCode());
        res.setCreatedAt(subscription.getCreatedAt());
        res.setCurrentCycle(subscription.getCurrentCycle());
        List<FileAttach> fileAttachList = fileAttachRepository.findByComboIdAndObjectType(res.getComboId(), FileAttachConst.ICON_OF_COMBO);
        if (!fileAttachList.isEmpty()) {
            res.setIcon(ObjectUtil.getOrDefault(fileAttachList.get(0).getFilePath(), fileAttachList.get(0).getFileName()));
            res.setIconExternalLink(fileAttachList.get(0).getExternalLink());
        }
        // Bổ sung amount Cycle
        Optional<AmountOfCycleNext> listAmount = subscriptionRepository.getAmountCycleNextNew(id);
        if (!listAmount.isPresent()) {
            return res;
        }
        AmountOfCycleNext amount = listAmount.get();
        // Sub-combo trả sau: lấy thông tin chu kỳ hien tại
        if (Objects.nonNull(res.getComboPlanType()) && res.getComboPlanType().equals(PricingTypeEnum.POSTPAID)) {
            res.setAmountOfCycle(new AmountOfCycleNext(amount.getAmountCurrentCycle(), amount.getCurrencyName(), amount.getStartCurrentCycle(),
                amount.getEndCurrentCycle(), amount.getCurrentPaymentDate()));
        } else {
            // Sub-combo trả trước: Lấy thông tin của chu kỳ tiếp theo
            // number_of_cycle == null là dùng vĩnh viễn
            if (!SubscriptionStatusEnum.IN_TRIAL.equals(res.getSubscriptionStatus()) &&
                    (Objects.isNull(res.getNumberOfCycles()) || res.getNumberOfCycles().equals(-1) ||
                            res.getCurrentCycle() < res.getNumberOfCycles())) {
                AmountOfCycleNext cycleNext = new AmountOfCycleNext(amount.getCurrencyName(),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(res.getStartedAt(), res.getPaymentCycle(), res.getCycleType(),
                        false, res.getCurrentCycle())),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(res.getStartedAt(), res.getPaymentCycle(), res.getCycleType(),
                        true, (res.getCurrentCycle() + 1))),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(res.getStartedAt(), res.getPaymentCycle(), res.getCycleType(),
                        false, (res.getCurrentCycle() + 1))),
                    amount.getAmountCurrentCycle());
                res.setAmountOfCycle(cycleNext);
            }
        }
        return res;
    }

    /**
     * Lay coupon cua tong hoa don
     */
    private List<SubscriptionPricingAddonsDTO.CouponList> getSubscriptionCouponTotalBill(Long id) {
        List<SubscriptionPricingAddonsDTO.CouponList> result = new ArrayList<>();
        List<Coupon> coupons = subscriptionRepository.getCouponBySubscription(id, PromotionTypeEnum.ALL.value);
        if (!CollectionUtils.isEmpty(coupons)) {
            coupons.forEach(c -> {
                SubscriptionPricingAddonsDTO.CouponList coupon = new SubscriptionPricingAddonsDTO.CouponList();
                coupon.setCouponId(c.getId());
                coupon.setCouponName(c.getName());
                coupon.setMinimum(c.getMinimum());
                coupon.setMinimumAmount(c.getMinimumAmount());
                coupon.setMinimum(c.getMaximumPromotion());
                coupon.setTimesUsedType(c.getTimesUsedType() == null ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
                coupon.setLimitedQuantity(c.getLimitedQuantity());
                coupon.setStartDate(DateUtil.convertLocalDateToDate(c.getStartDate()));
                coupon.setEndDate(DateUtil.convertLocalDateToDate(c.getEndDate()));
                coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                coupon.setTimeType(c.getType() == null ? null : TimeTypeEnum.valueOf(c.getType()));
                coupon.setCode(c.getCode());
                coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_COMBO_PLAN));
                // set pricings cua couponType = PRODUCT
                if (Objects.nonNull(c.getPromotionType()) && Objects.equals(PromotionTypeEnum.PRODUCT.value, c.getPromotionType())) {
                    List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupons = getPricingByCoupon(c.getId());
                    coupon.setPricing(pricingCoupons);
                    BigDecimal cValue = new BigDecimal(CollectionUtils.isEmpty(pricingCoupons) ? 0 : pricingCoupons.size());
                    coupon.setCouponValue(cValue);
                } else {
                    coupon.setPricing(null);
                    coupon.setCouponValue(c.getDiscountAmount());
                    coupon.setDiscountAmount(c.getDiscountValue());
                }
                result.add(coupon);
            });
        }
        return result;
    }

    /**
     * Sets combo pricing list detail.
     *
     * @param comboPlanId the combo plan id
     * @return the combo pricing list detail
     */
    private List<ComboPricingDetailResDTO> setComboPricingListDetail(Long comboPlanId) {
        List<ComboPricing> comboPricings = comboPricingRepository.findByComboPlanId(comboPlanId);
        if (CollectionUtils.isEmpty(comboPricings)) return new ArrayList<>();
        return comboPricings.stream().map(e -> {
            ComboPricingDetailResDTO dto = new ComboPricingDetailResDTO();
            dto.setQuantity(Objects.nonNull(e.getQuantity()) && !e.getQuantity().equals(0L) ? e.getQuantity() : 1);
            dto.setFreeQuantity(e.getFreeQuantity());
            dto.setObjectType(e.getObjectType());
            switch (e.getObjectType()) {
                case "PRICING":
                    dto.setPricingName(e.getPricing().getPricingName());
                    dto.setServiceName(e.getPricing().getServiceEntity().getServiceName());
                    break;
                case "DEVICE_VARIANT":
                    dto.setVariantName(e.getVariant().getFullName());
                    var service = serviceRepository.findById(e.getVariant().getServiceId()).orElse(null);
                    if (Objects.nonNull(service)) {
                        dto.setServiceName(service.getServiceName());
                    }
                    break;
                case "DEVICE_NO_VARIANT":
                    dto.setServiceName(e.getService().getServiceName());
                    break;
                default:
                    break;
            }

            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * Lay danh sach CTKM cua goi
     */
    private List<SubscriptionPricingCouponDTO> getSubscriptionComboCoupon(Long id) {

        List<SubscriptionPricingCouponDTO> result = new ArrayList<>();
        List<Coupon> pricingCoupons = couponRepository.getCouponByComboSubscription(id);

        // set thông tin khuyến mại
        pricingCoupons.forEach(c -> {
            SubscriptionPricingCouponDTO coupon = new SubscriptionPricingCouponDTO();
            coupon.setChecked(true);
            coupon.setCouponId(c.getId());
            coupon.setCouponName(c.getName());
            coupon.setMinimum(c.getMinimum());
            coupon.setMinimumAmount(c.getMinimumAmount());
            coupon.setDiscountAmount(c.getDiscountAmount());
            coupon.setStartDate(c.getStartDate());
            coupon.setEndDate(c.getEndDate());
            coupon.setPromotionType(c.getPromotionType() == null ? null
                : PromotionTypeEnum.valueOf(c.getPromotionType()));
            coupon.setDiscountType(
                c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
            coupon.setTimesUsedType(Objects.isNull(c.getTimesUsedType()) ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
            coupon.setCode(c.getCode());
            coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_COMBO_PLAN));
            if (validateCoupons(c, null, CouponConst.COUPON_OF_COMBO_PLAN) ||
                Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)) {
                //set pricings của mỗi couponType = PRODUCT
                if (PromotionTypeEnum.PRODUCT.value == c.getPromotionType()) {
                    List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupon = getPricingByCoupon(c.getId());
                    coupon.setPricing(pricingCoupon);
                    BigDecimal cValue = new BigDecimal(CollectionUtils.isEmpty(pricingCoupon) ? 0 : pricingCoupon.size());
                    coupon.setCouponValue(cValue);
                } else {
                    coupon.setPricing(null);
                    coupon.setCouponValue(c.getDiscountValue());
                }
                result.add(coupon);
            }
        });
        return result;
    }

    /**
     * Lay danh sach addon
     *
     * @param comboPlanId  the combo plan id
     * @param subscription the subscription id
     * @return subscription combo addon
     */
    @Override
    public Set<SubscriptionPricingAddonsDTO> getSubscriptionComboAddon(Long comboPlanId, Subscription subscription) {
        Set<Long> addons = new HashSet<>();
        subscriptionComboAddonRepository.findAllBySubscriptionId(subscription.getId()).forEach(addon -> addons.add(addon.getAddonId()));
        List<SubscriptionPricingAddonsDTO> resAddon = addonRepository.getAddonComboMultiPeriod(comboPlanId, subscription.getId());
        resAddon.addAll(addonRepository.getAddonBySubscriptionIdAndComboPlan(comboPlanId, subscription.getId()));
        Comparator<SubscriptionPricingAddonsDTO> comp = Comparator.comparing(
            item -> Objects.nonNull(item.getChecked()) ? item.getChecked() : Boolean.FALSE);
        resAddon.sort(comp.reversed());
        Set<SubscriptionPricingAddonsDTO> addonFinal = resAddon.stream()
            .filter(x -> x.getQuantity() != 0L || !addons.contains(x.getId())).collect(Collectors.toSet());
        //danh sach addonId
        Set<Long> addonIds = resAddon.stream().map(SubscriptionPricingAddonsDTO::getId).collect(Collectors.toSet());

        //danh sach thue cua addon theo goi
        List<PricingTaxRes> addonTaxs = addonsTaxRepository.getAddonTaxByIds(addonIds);

        //danh sach unitLimited theo addon
        List<UnitLimited> addonUnitLimited = unitLimitedRepository.getUnitLimitedByAddonsIdIn(addonIds);
        // Danh sach taxSetUpFee cua addon
        List<PricingTaxRes> addonSetupFeeList =
            !CollectionUtils.isEmpty(addonIds) ? addonsTaxRepository.getAddonSetupFeeTaxByIds(addonIds)
                : new ArrayList<>();
        resAddon.forEach(addon -> {
            //danh sach ctkm cua addon theo goi
            List<SubscriptionPricingAddonCouponDTO> pricingAddonCoupons =
                addonRepository.getCouponByAddonComboIds(addon.getId(), subscription.getUserId(), subscription.getId());
            List<Long> comboAddonCouponIds = addonRepository.getCouponIdsUnionByAddonComboId(addon.getId(), subscription.getId());
            if (!CollectionUtils.isEmpty(comboAddonCouponIds)) {
                comboAddonCouponIds.forEach(i -> pricingAddonCoupons.forEach(coupon -> {
                    if (Objects.equals(coupon.getId(), i)) {
                        coupon.setChecked(true);
                    }
                }));
            }
            List<SubscriptionPricingAddonDTO.Tax> taxList = new ArrayList<>();
            List<SubscriptionPricingAddonsDTO.CouponList> couponList = new ArrayList<>();
            List<SubscriptionPricingAddonsDTO.UnitLimited> unitLimitedList = new ArrayList<>();
            List<SubscriptionPricingAddonDTO.Tax> taxSetupFeeList = new ArrayList<>();
            List<PricingTaxRes> addonTax = new ArrayList<>();

            if (!CollectionUtils.isEmpty(addonTaxs)) {
                addonTax = addonTaxs.stream().filter(t ->
                    addon.getId().equals(t.getAddonId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(addonTax)) {
                    addonTax.forEach(t -> {
                        SubscriptionPricingAddonDTO.Tax tax = new SubscriptionPricingAddonDTO.Tax();
                        tax.setTaxId(t.getTaxId());
                        tax.setTaxName(t.getTaxName());
                        tax.setPercent(t.getPercent());
                        tax.setHasTax(YesNoEnum.valueOf(t.getHasTax()));
                        taxList.add(tax);
                    });
                }
            }
            //            Set coupon by pricing_id
            if (!CollectionUtils.isEmpty(pricingAddonCoupons)) {
                List<SubscriptionPricingAddonCouponDTO> pricingAddonCoupon = pricingAddonCoupons.stream().filter(c ->
                    addon.getId().equals(c.getAddonId()) || !addonIds.contains(c.getAddonId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pricingAddonCoupon)) {
                    pricingAddonCoupon.forEach(c -> {
                        boolean checkEnterpriseType = Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value);
                        if (validateCoupons(subscriptionAddonCouponMapper.toEntity(c), null, CouponConst.COUPON_OF_ADDON) &&
                            !checkEnterpriseType) {
                            SubscriptionPricingAddonsDTO.CouponList coupon = new SubscriptionPricingAddonsDTO.CouponList();
                            coupon.setCouponId(c.getId());
                            coupon.setCouponName(c.getName());
                            coupon.setMinimum(c.getMinimum());
                            coupon.setMinimumAmount(c.getMinimumAmount());
                            coupon.setTimesUsedType(c.getTimesUsedType() == null ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
                            coupon.setLimitedQuantity(c.getLimitedQuantity());
                            coupon.setStartDate(DateUtil.convertLocalDateToDate(c.getStartDate()));
                            coupon.setEndDate(DateUtil.convertLocalDateToDate(c.getEndDate()));
                            coupon.setCode(c.getCode());
                            coupon.setConditions(couponService.getCouponConditions(c.getId(), CouponConst.COUPON_OF_ADDON));
                            coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                            coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                            coupon.setDiscountAmount(c.getDiscountAmount());
                            coupon.setTimeType(c.getType() == null ? null : TimeTypeEnum.valueOf(c.getType()));
                            coupon.setChecked(c.getChecked());
                            //set pricings của mỗi couponType = PRODUCT
                            if (Objects.nonNull(c.getPromotionType())
                                && PromotionTypeEnum.PRODUCT.value == c.getPromotionType()) {
                                List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupons = getPricingByCoupon(c.getId());
                                coupon.setPricing(pricingCoupons);
                                BigDecimal cValue = new BigDecimal(CollectionUtils.isEmpty(pricingCoupons) ? 0 : pricingCoupons.size());
                                coupon.setCouponValue(cValue);
                            } else {
                                coupon.setPricing(null);
                                coupon.setCouponValue(c.getDiscountAmount());
                                coupon.setDiscountValue(c.getDiscountValue());
                            }
                            couponList.add(coupon);
                        }
                    });
                }
            }
            // lay danh sach unitlimited list cua addon
            if (!CollectionUtils.isEmpty(addonUnitLimited)) {
                List<UnitLimited> addonUnit = addonUnitLimited.stream().filter(a ->
                    addon.getId().equals(a.getAddonsId())).collect(Collectors.toList());
                List<PricingTaxRes> addonTaxFiltered = new ArrayList<>(addonTax);
                if (!CollectionUtils.isEmpty(addonUnit)) {
                    addonUnit.forEach(au -> {
                        SubscriptionPricingAddonsDTO.UnitLimited unitLimited = new SubscriptionPricingAddonsDTO.UnitLimited();
                        unitLimited.setUnitTo(au.getUnitTo());
                        unitLimited.setUnitFrom(au.getUnitFrom());
                        unitLimited.setAddonsId(au.getAddonsId());
                        unitLimited.setPricingId(au.getPricingId());
                        unitLimited.setPrice(subscriptionFormula.priceBeforeTax(au.getPrice(), addonTaxFiltered));
                        unitLimitedList.add(unitLimited);
                    });
                }
            }
            // setupFee của addon
            subscriptionRepository.getAddonSetupFee(addon.getId(), subscription.getId(),
                Objects.isNull(addon.getAddonMultiPlanId()) ? -1L : addon.getAddonMultiPlanId()).ifPresent(addonSetupFee -> {
                addon.setSetupFee(addonSetupFee.getPrice());
                if (!addonSetupFee.getIsEdited()) {
                    addon.setSetupFee(subscriptionFormula.getPriceBeforeTax(addonSetupFee.getPrice(),
                        taxSetupFeeList.stream().map(x ->
                                new SubscriptionPricingAddonDTO.Tax(x.getTaxId(), x.getTaxName(), x.getPercent(), x.getHasTax()))
                            .collect(Collectors.toList())));
                }
            });
            // taxSetupFee của addon
            if (!CollectionUtils.isEmpty(addonSetupFeeList)) {
                List<PricingTaxRes> addonSetupFee = addonSetupFeeList.stream().filter(t -> Objects.equals(addon.getId(), t.getAddonId()))
                    .collect(Collectors.toList());
                addonSetupFee.forEach(t -> taxSetupFeeList.add(new SubscriptionPricingAddonDTO.Tax(t.getTaxId(), t.getTaxName(), t.getPercent(),
                    t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()))));
            }
            addon.setTaxSetupFeeList(taxSetupFeeList);
            addon.setTaxList(taxList);
            addon.setCouponList(couponList);
            addon.setUnitLimitedList(unitLimitedList);
            addon.setPrice(subscriptionFormula.priceBeforeTax(addon.getPrice(), addonTax));
        });
        return addonFinal;
    }

    private Combo getCurrentCombo(Long comboId) {
        return comboRepository.findByIdAndDeletedFlag(comboId, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.COMBO, ErrorKey.ID, String.valueOf(comboId)));
    }

    /**
     * Kiem tra thoi gian kich hoat lai
     */
    private boolean checkTimeActiveDate(Subscription subscription) {
        Integer activeDate = pricingRepository.getActiveDateBySubscription(subscription.getId());
        if (Objects.isNull(activeDate) || Objects.isNull(subscription.getCancelledTime())) {
            return false;
        }
        LocalDate cancelTime = subscription.getCancelledTime().toLocalDate();
        // LocalDate.now() - cancelTime
        Period diff = Period.between(cancelTime, LocalDate.now());
        return activeDate == -1 || diff.getDays() <= activeDate;
    }


    /**
     * Lay danh sach phi mot lan cua subscription
     */
    private List<SubscriptionOneTimeFee> getOnceTimeFee(Long id) {
        List<SubscriptionOneTimeFee> result = new ArrayList<>();
        customFeeRepository.findAllBySubscriptionId(id).forEach(c -> {
            SubscriptionOneTimeFee oneTime = new SubscriptionOneTimeFee();
            oneTime.setId(c.getId());
            oneTime.setName(c.getName());
            oneTime.setDateFee(c.getFeeDate());
            oneTime.setPrice(c.getPrice());
            oneTime.setDescription(c.getDescription());
            result.add(oneTime);
        });
        return result;
    }

    /**
     * Lay danh sach CTKM theo chiet khau cua subscription
     */
    private List<SubscriptionPricingCouponDTO> getSubscriptionCouponDiscount(Long id) {
        List<SubscriptionPricingCouponDTO> result = new ArrayList<>();
        Subscription subscription = findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        // set thông tin khuyến mại
        subscriptionRepository.getCouponBySubscription(id, PromotionTypeEnum.DISCOUNT.value)
            .forEach(c -> {
                SubscriptionPricingCouponDTO coupon = new SubscriptionPricingCouponDTO();
                coupon.setCouponId(c.getId());
                coupon.setCouponName(c.getName());
                coupon.setCode(c.getCode());
                coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING));
                coupon.setDiscountAmount(c.getDiscountAmount());
                coupon.setStartDate(c.getStartDate());
                coupon.setEndDate(c.getEndDate());
                coupon.setPromotionType(c.getPromotionType() == null ? null
                    : PromotionTypeEnum.valueOf(c.getPromotionType()));
                coupon.setDiscountType(c.getDiscountType() == null ? null
                    : DiscountTypeEnum.valueOf(c.getDiscountType()));
                coupon.setTimesUsedType(Objects.isNull(c.getTimesUsedType()) ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
                coupon.setLimitedQuantity(c.getLimitedQuantity());
                TimeTypeEnum typeEnum = c.getType() == null ? null : TimeTypeEnum.valueOf(c.getType());
                coupon.setTimeType(typeEnum == null ? null : typeEnum.name());
                if (validateCoupons(c, subscription.getUserId(), CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING)) {
                    coupon.setPricing(null);
                    coupon.setCouponValue(c.getDiscountAmount());
                    coupon.setDiscountValue(c.getDiscountValue());
                    result.add(coupon);
                }
            });
        return result;
    }

    @Transactional(readOnly = true)
    @Override
    public SubscriptionPricingResDTO getSubscriptionPermitAll(Long id, Long subscriptionId, Long pricingMultiPlanId) {
        Optional<Pricing> pricingDetail = pricingRepository.findByDraftIdApproved(id);
        if (!pricingDetail.isPresent()) {
            return null;
        }
        id = pricingDetail.get().getId();
        Pricing pricing = pricingRepository
            .findByIdAndDeletedFlagAndApproveAndStatus(id, DeletedFlag.NOT_YET_DELETED.getValue(),
                ApproveStatusEnum.APPROVED.value, StatusEnum.ACTIVE.value)
            .orElseThrow(() -> {
                String msg = messageSource
                    .getMessage(MessageKeyConstant.OBJECT_EXISTS, null,
                        LocaleContextHolder.getLocale());
                return new BadRequestException(msg, Resources.PRICING, ErrorKey.ID,
                    MessageKeyConstant.OBJECT_EXISTS);
            });
        SubscriptionPricingResDTO resSubscription = subscriptionPricingMapper.toDto(pricing);
        resSubscription.setDescription(pricing.getDescription());
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerType = Objects.isNull(userLogin) ? StringUtils.EMPTY : userLogin.getCustomerType();
        List<PricingMultiplePeriodResDTO> pricingCustomerType = pricingMultiPlanRepository.getPricingMultiPeriodById(id);
        resSubscription.setPlanCustomerTypeCode(pricingCustomerType.stream().map(x ->
            new MultiPlanCustomerTypeCode(x.getIdPricingPeriod(), x.getCustomerTypeCode())).collect(Collectors.toSet()));
        PricingMultiPlan pricingMultiPlan = null;
        if (Objects.nonNull(pricingMultiPlanId)) {
            pricingMultiPlan = pricingMultiPlanRepository
                .findByIdAndPricingIdAndDeletedFlag(pricingMultiPlanId, id, DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> {
                    String msg = messageSource
                        .getMessage(MessageKeyConstant.OBJECT_EXISTS, null,
                            LocaleContextHolder.getLocale());
                    return new BadRequestException(msg, Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                        MessageKeyConstant.OBJECT_EXISTS);
                });
            resSubscription.setPricingPlan(PricingPlanEnum.valueOf(pricingMultiPlan.getPricingPlan()));
            pricing.setPricingPlan(pricingMultiPlan.getPricingPlan());
            pricing.setPaymentCycle(pricingMultiPlan.getPaymentCycle() != null ? pricingMultiPlan.getPaymentCycle().intValue() : null);
            pricing.setNumberOfCycles(pricingMultiPlan.getNumberOfCycle());
            pricing.setCycleType(pricingMultiPlan.getCircleType());
            pricing.setUnitId(pricingMultiPlan.getUnitId());
            pricing.setCurrencyId(pricingMultiPlan.getCurrencyId());
            pricing.setPrice(pricingMultiPlan.getPrice());
            pricing.setTrialType(pricingMultiPlan.getTrialType() != null ? pricingMultiPlan.getTrialType().intValue() : null);
            pricing.setNumberOfTrial(pricingMultiPlan.getNumberOfTrial());
            pricing.setFreeQuantity(pricingMultiPlan.getFreeQuantity() != null ? Long.valueOf(pricingMultiPlan.getFreeQuantity()) : null);
            pricing.setEstimateQuantity(pricingMultiPlan.getEstimateQuantity());
        } else {
            // Check có là gói multi period
            subMultiplePeriod.validatePricingMultiPlanByPricingId(pricing.getId(), null);
        }

        Long pricingOwnerId = pricingRepository.findOwnerPricing(id);
        // Lấy coupon của pricing
        Set<SubscriptionPricingAddonDTO.CouponList> couponList = new LinkedHashSet<>();
        Optional<SubscriptionForPricingDetailDTO> subscriptionInfo;
        CustomUserDetails userDetails = AuthUtil.getCurrentUser();
        if (userDetails != null) {
            subscriptionInfo = subscriptionRepository
                .getRegTypeOfSubscription(AuthUtil.getCurrentUserId(), id);
        } else {
            subscriptionInfo = Optional.empty();
        }

        if (Objects.nonNull(pricing.getServiceId())) {
            serviceRepository.findByIdAndDeletedFlag(pricing.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue()).ifPresent(serviceEntity -> {
                if (Objects.nonNull(serviceEntity.getServiceOwner())) {
                    resSubscription.setServiceOwner(ServiceTypeEnum.valueOf(serviceEntity.getServiceOwner()));
                }
                // Lấy thông tin sản phẩm liên quan
                if(serviceEntity.getSuggestionType() != null && serviceEntity.getSuggestionType()== 1 ){
                    resSubscription.setServiceSuggestions(serviceSuggestionService.getAllServiceSuggestionsByServiceSmePortal(pricing.getServiceId(),1));
                }else if (serviceEntity.getSuggestionType() != null && serviceEntity.getSuggestionType() == 0 && Objects.nonNull(serviceEntity.getCategoriesId())){
                    List<Long> categoriesId = categoriesRepository.getListCategoryIdByServiceId(serviceEntity.getId());
                    if (!Objects.equals(ServiceProductTypeEnum.DEVICE, serviceEntity.getProductType())) {
                        resSubscription.setServiceSuggestions(serviceSuggestionService.getAutoServiceSuggestionsByServiceSmePortal(categoriesId, pricing.getServiceId(), customerType));
                    } else {
                        resSubscription.setServiceSuggestions(serviceSuggestionService.getAutoDeviceSuggestionsByServiceSmePortal(categoriesId, pricing.getServiceId(), customerType));
                    }
                }
            });
        }

        List<ServiceSuggestionResDTO> lstSuggestionService = resSubscription.getServiceSuggestions();
        if (Objects.nonNull(lstSuggestionService) && !lstSuggestionService.isEmpty()) {
            List<ProductTagDTO> lstBannerInfo = lstSuggestionService.stream()
                    .map(suggestionDTO -> new ProductTagDTO(suggestionDTO.getId(),
                            (suggestionDTO.getServiceSuggestionType() == 1) ? ProductTypeEnum.SERVICE : ProductTypeEnum.COMBO)) // from KienNH : 1 (service), 2 (combo)
                    .collect(Collectors.toList());
            campaignSmeService.getListProductTag(lstBannerInfo);
            lstSuggestionService.forEach(serviceDTO -> {
                ProductTypeEnum dtoType = serviceDTO.getServiceSuggestionType() == 1 ? ProductTypeEnum.SERVICE : ProductTypeEnum.COMBO;
                Optional<ProductTagDTO> oBanner = lstBannerInfo.stream()
                        .filter(banner -> Objects.equals(banner.getObjectId(), serviceDTO.getId()) && banner.getObjectType() == dtoType)
                        .findFirst();
                oBanner.ifPresent(banner -> {
                    serviceDTO.setDiscountInfo(banner.getDiscountInfo());
                    serviceDTO.setGiftInfo(banner.getGiftInfo());
                });
            });
        }

        //lay thong tin subscription cu
        if (Objects.nonNull(subscriptionId) && !subscriptionId.equals(-1L)) {
            Subscription subscription = validateSubscription(subscriptionId, PortalType.SME);
            Pricing pricingOld = validatePricing(subscription.getPricingId());
            pricingRepository.getCouponByPricingSubscription(subscriptionId).forEach(c -> {
                SubscriptionPricingAddonDTO.CouponList coupon = new SubscriptionPricingAddonDTO.CouponList();
                coupon.setCouponId(c.getCouponId());
                coupon.setCode(c.getCode());
                coupon.setCouponName(c.getCouponName());
                coupon.setCode(c.getPromotionCode());
                coupon.setTimesUsedType(c.getTimesUsedType());
                coupon.setLimitedQuantity(c.getLimitedQuantity());
                coupon.setMinimum(c.getMinimum());
                coupon.setMinimumAmount(c.getMinimumAmount());
                coupon.setPromotionType(c.getPromotionType());
                coupon.setDiscountValue(c.getDiscountValue());
                coupon.setDiscountType(c.getDiscountType());
                coupon.setMaximum((c.getMinimum()));
                coupon.setAddonDepend(getDependAddonByCouponId(c));
                coupon.setChecked(true);
                coupon.setType(Objects.nonNull(c.getTimeType()) ? TimeTypeEnum.valueOf(c.getTimeType()) : null);
                coupon.setConditions(couponService.getCouponConditions(c.getCouponId(), CouponConst.COUPON_OF_PRICING));
                //set pricings của mỗi couponType = PRODUCT
                if (PromotionTypeEnum.PRODUCT.equals(c.getPromotionType())) {
                    coupon.setPricing(getPricingByCoupon(c.getCouponId()));
                } else {
                    coupon.setPricing(null);
                }
                couponList.add(coupon);
            });
            resSubscription.setChangeDate(
                Objects.equals(pricingOld.getChangePricingDate(), ComboChangeEnum.END_OF_PERIOD.value) ? DateUtil
                    .convertLocalDateToDate(DateUtil.toLocalDate(subscription.getEndCurrentCycle()).plusDays(1L)) : new Date());
            resSubscription.setStartDateSubscription(subscription.getStartedAt());
            resSubscription.setEndDateSubscription(subscription.getExpiredTime());
        } else if (subscriptionInfo.isPresent()) {
            resSubscription.setStartDateSubscription(subscriptionInfo.get().getStartDateSubscription());
            resSubscription.setEndDateSubscription(subscriptionInfo.get().getEndDateSubscription());
        } else {
            resSubscription.setStartDateSubscription(null);
            resSubscription.setEndDateSubscription(null);
        }

        Pricing pricingOpt = pricingRepository.getNewPricing(pricing.getPricingDraftId()).orElseThrow(() -> {
            String msg = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, pricingMessage, LocaleContextHolder.getLocale());
            return new BadRequestException(msg, Resources.PRICING, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        });

        if (pricingOpt.getId().equals(id)) {
            resSubscription.setStatusVersion(StatusVersionEnum.UP_TO_DATE);
        } else {
            resSubscription.setStatusVersion(StatusVersionEnum.OUT_OF_DATE);
        }

        //Set đơn vị tính
        if (Objects.nonNull(resSubscription.getUnitId())) {
            unitRepository.findById(resSubscription.getUnitId()).ifPresent(unit -> resSubscription.setUnitName(unit.getName()));
        }

        //Set đơn vị tiền tệ
        if (Objects.nonNull(resSubscription.getCurrencyId())) {
            currencyRepository.findById(resSubscription.getCurrencyId()).ifPresent(
                currency -> resSubscription.setCurrencyName(currency.getCurrencyType())
            );
        }
        // Set thong tin thue
        List<PricingDetailResDTO.Tax> taxList = new ArrayList<>();
        AtomicBoolean checkHavetax = new AtomicBoolean(false);
        List<PricingTaxRes> pricingTax = pricingTaxRepository.getPricingTax(id);
        pricingTax.forEach(t -> {
            PricingDetailResDTO.Tax tax = new PricingDetailResDTO.Tax();
            tax.setTaxId(t.getTaxId());
            tax.setTaxName(t.getTaxName());
            tax.setHasTax(t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()));
            tax.setPercent(t.getPercent());
            taxList.add(tax);
            if (Objects.equals(YesNoEnum.valueOf(t.getHasTax()), YesNoEnum.YES)) {
                checkHavetax.set(true);
            }
        });

        // Thông tin thuế phí thiết lập
        List<PricingDetailResDTO.Tax> setupFeeTaxList = new ArrayList<>();
        List<PricingTaxRes> setupFeeTax = pricingSetupFeeTaxRepository.getPricingSetupFeeTax(id);
        setupFeeTax.forEach(t -> {
            PricingDetailResDTO.Tax tax = new PricingDetailResDTO.Tax();
            tax.setTaxId(t.getTaxId());
            tax.setTaxName(t.getTaxName());
            tax.setHasTax(t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()));
            tax.setPercent(t.getPercent());
            setupFeeTaxList.add(tax);
        });

        // Thực hiện lấy thông tin của level cho gói, kiểm tra xem có phải gói dạng Multiple Period
        if (Objects.isNull(pricingMultiPlanId)) {
            List<UnitLimited> unitLimitedList = unitLimitedRepository.findByPricingIdAndQuantity(id);
            if (Objects.nonNull(unitLimitedList)) {
                List<CalUnitLimitedCustomDTO> limiteds =
                    unitLimitedList.stream().map(x -> new CalUnitLimitedCustomDTO(x.getUnitFrom(), x.getUnitTo(),
                            subscriptionFormula.priceBeforeTax(x.getPrice(), pricingTax)))
                        .collect(Collectors.toList());
                resSubscription.setUnitLimitedList(limiteds);
                if (!CollectionUtils.isEmpty(limiteds)) {
                    resSubscription.setMinimumQuantity(limiteds.stream()
                            .map(CalUnitLimitedCustomDTO::getUnitFrom)
                            .filter(Objects::nonNull)
                            .min(Long::compare)
                            .orElse(-1L));
                    resSubscription.setMaximumQuantity((limiteds.stream()
                            .map(CalUnitLimitedCustomDTO::getUnitTo)
                            .filter(Objects::nonNull)
                            .max((a, b) -> {
                                if (a.equals(-1L)) return 1;
                                if (b.equals(-1L)) return -1;
                                return a.compareTo(b);
                            })
                            .orElse(-1L)));
                }
            }
        } else {
            List<CalUnitLimitedCustomDTO> limiteds = new ArrayList<>();
            pricingPlanDetailRepository.findByPricingMultiPlanIdOrderByUnitFromAsc(pricingMultiPlanId).ifPresent(
                unitLimits -> unitLimits.forEach(unitLimited -> {
                    CalUnitLimitedCustomDTO limited = new CalUnitLimitedCustomDTO();
                    limited.setUnitFrom(Long.valueOf(unitLimited.getUnitFrom()));
                    limited.setUnitTo(unitLimited.getUnitTo() == -1 ? null : Long.valueOf(unitLimited.getUnitTo()));
                    limited.setPrice(subscriptionFormula.priceBeforeTax(unitLimited.getPrice(), pricingTax));
                    limiteds.add(limited);
                }));
            resSubscription.setUnitLimitedList(limiteds);
            if (!CollectionUtils.isEmpty(limiteds)) {
                resSubscription.setMinimumQuantity(limiteds.stream()
                        .map(CalUnitLimitedCustomDTO::getUnitFrom)
                        .filter(Objects::nonNull)
                        .min(Long::compare)
                        .orElse(-1L));
                resSubscription.setMaximumQuantity((limiteds.stream()
                        .map(CalUnitLimitedCustomDTO::getUnitTo)
                        .filter(Objects::nonNull)
                        .max((a, b) -> {
                            if (a.equals(-1L)) return 1;
                            if (b.equals(-1L)) return -1;
                            return a.compareTo(b);
                        })
                        .orElse(-1L)));
            }
        }

        resSubscription.setTaxList(taxList);
        resSubscription.setSetupFeeTaxList(setupFeeTaxList);

        Double totalPriceTaxPercent = Double.valueOf(0);
        if (taxList.size() > 0) {
            totalPriceTaxPercent = taxList.stream().mapToDouble(PricingDetailResDTO.Tax::getPercent).sum();
        }

        // Nếu pricing là FLAT_RATE hoặc UNIT và đã bao gồm thuế thì đơn giá hiển thị giá tiền trước thuế
        if (checkHavetax.get() && (Objects.equals(resSubscription.getPricingPlan(), PricingPlanEnum.FLAT_RATE) ||
            Objects.equals(resSubscription.getPricingPlan(), PricingPlanEnum.UNIT))) {
            BigDecimal priceBeforeTax = subscriptionFormula.priceBeforeTax(pricing.getPrice(), pricingTax);
            resSubscription.setPrice(priceBeforeTax);
        }

        // Lấy giá nhỏ nhất khi UnitLimitedList tồn lại
        if (Objects.nonNull(resSubscription.getUnitLimitedList()) && resSubscription.getUnitLimitedList().size() > 0) {
            BigDecimal getFirstPrice = resSubscription.getUnitLimitedList().get(0).getPrice();
            resSubscription.setPrice(getFirstPrice);
        }

        // Lấy giá tiên sau thuế
        if (taxList.size() > 0 && totalPriceTaxPercent > Double.valueOf(0) && Objects.nonNull(resSubscription.getPrice())) {
            resSubscription.setPriceAfterTax(resSubscription.getPrice().multiply(BigDecimal.valueOf(1).add(BigDecimal.valueOf(totalPriceTaxPercent).divide(BigDecimal.valueOf(100)))));
        } else {
            resSubscription.setPriceAfterTax(resSubscription.getPrice());
        }

        // set thông tin khuyến mại
        Long provinceId;
        Long parentId;
        if (userDetails != null) {
            provinceId = userRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
            parentId = AuthUtil.getCurrentParentId();
        } else {
            provinceId = SubscriptionConstant.GUEST_USER;
            parentId = SubscriptionConstant.GUEST_USER;
        }
        Set<Coupon> couponSet = pricingMultiPlanId == null ?
            pricingRepository.getCouponByPricing(id, provinceId, parentId) :
            pricingRepository.getCouponByPricingMultiPlanId(pricingMultiPlanId, provinceId, parentId);
        couponSet.forEach(c -> {
            boolean checkValid = (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || !Objects.equals(c.getTotalBillType(), TotalBillTypeEnum.NO.value)
                || !Objects.equals(c.getAddonsType(), AddonTypeEnum.NONE.value))
                && (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || !Objects.equals(c.getTotalBillType(), TotalBillTypeEnum.NO.value)
                || !Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value));

            //nếu coupon ko chọn áp dụng với enterprise nào thì ko đc áp dụng
            boolean checkEnterpriseType = Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value);
            if (checkValid && !checkEnterpriseType
                && validateCoupons(c, null, CouponConst.COUPON_OF_PRICING)
                && (Objects.equals(c.getPortal(), PortalType.ADMIN.getType())
                || (Objects.equals(c.getPortal(), PortalType.DEV.getType()) && Objects.equals(c.getUserId(), pricingOwnerId)))) {
                SubscriptionPricingAddonDTO.CouponList coupon = new SubscriptionPricingAddonDTO.CouponList();
                coupon.setCouponId(c.getId());
                coupon.setCouponName(c.getName());
                coupon.setCode(c.getPromotionCode());
                coupon.setTimesUsedType(
                    TimeUsedTypeEnum.valueOf(ObjectUtil.getOrDefault(c.getTimesUsedType(), TimeUsedTypeEnum.UNLIMITED.value)));
                coupon.setLimitedQuantity((c.getLimitedQuantity()));
                coupon.setMinimum(c.getMinimum());
                coupon.setMinimumAmount(c.getMinimumAmount());
                coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                coupon.setDiscountValue(c.getDiscountValue());
                coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                coupon.setDiscountAmount(c.getDiscountAmount());
                coupon.setMaximum((c.getMinimum()));
                coupon.setType(Objects.nonNull(c.getType()) ? TimeTypeEnum.valueOf(c.getType()) : null);
                coupon.setEndDate(c.getEndDate());
                coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_PRICING));
                coupon.setOriginCode(c.getCode());
                coupon.setVisibleStatus(c.getVisibleStatus());
                List<CouponSet> couponSets = couponSetRepository.findAllByCouponId(c.getId());
                if (!couponSets.isEmpty()) {
                    coupon.setExistCouponSet(true);
                }
                coupon.setAddonDepend(getDependAddonByCouponId(c));
                //set pricings của mỗi couponType = PRODUCT
                if (PromotionTypeEnum.valueOf(c.getPromotionType()).equals(PromotionTypeEnum.PRODUCT)) {
                    coupon.setPricing(getPricingByCoupon(c.getId()));
                } else {
                    coupon.setPricing(null);
                }
                if (couponList.stream().noneMatch(cp -> cp.getCouponId().equals(c.getId()))) {
                    couponList.add(coupon);
                }
            }
        });

        resSubscription.setCouponList(couponList);

        // kiểm tra quyền đăng ký gói dịch vụ
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            Optional<Long> allowSubscript = subscriptionRepository.getAllowSubscript(pricing.getServiceId(), AuthUtil.getCurrentUserId());
            if (allowSubscript.isPresent()) {
                yesNoEnum = YesNoEnum.NO;
            }
        }
        resSubscription.setAllowSubscript(yesNoEnum);

        // set thông tin Addon
        resSubscription.setAddonList(getAddonSubscription(pricing, subscriptionId, pricingMultiPlanId));
        resSubscription.setCoupons(getSubscriptionCoupon(id));

        String paramValue = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
        if (Objects.nonNull(paramValue)) {
            resSubscription.setCouponConfig(SystemParamEnum.findByStatusId(Integer.parseInt(paramValue)));
        }

        if (subscriptionInfo.isPresent()) {
            String versionSubscription = subscriptionInfo.get().getRegType() != null ? RegTypeEnum
                .valueOf(subscriptionInfo.get().getRegType()).toString() : null;
            resSubscription.setVersionSubscription(versionSubscription);
            resSubscription.setSubscriptionId(subscriptionInfo.get().getId());
        }

        if (systemParamService.checkCanOfflinePayment()) {
            resSubscription.setOfflinePaymentConfig(YesNoEnum.YES);
        } else {
            resSubscription.setOfflinePaymentConfig(YesNoEnum.NO);
        }
        // Lấy thông tin urlPreOrder
        Set<ComboDetailDTO> urlPreOrder = subscriptionRepository.getPricingAndUrlPreOrder(pricing.getId())
            .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
        resSubscription.setUrlPreOrder(urlPreOrder);
        resSubscription.setSeoDTO(seoService.getSeoDetailSme(pricingDetail.get().getPricingDraftId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU));
        // Lấy thông tin phụ phí
        if (Objects.nonNull(pricing.getSetupFee())) {
            List<PricingTax> planSetupFeeTaxList = pricingSetupFeeTaxRepository.findAllByPricingId(pricing.getId())
                    .stream().map(PricingTax::new).collect(Collectors.toList());
            SubscriptionFormulaResDTO.SetupFee setupFee = new SetupFeeCalculator(planSetupFeeTaxList, pricing.getSetupFee(), null).calc();
            resSubscription.setSetUpFee(setupFee.getPrice());
        }
        // lấy giá lắp đặt sau thuế
        if (setupFeeTaxList.size() > 0 && Objects.nonNull(resSubscription.getSetUpFee())) {
            double totalSetupTaxPercent = setupFeeTaxList.stream().mapToDouble(PricingDetailResDTO.Tax::getPercent).sum();
            resSubscription.setSetupFeeAfterTax(resSubscription.getSetUpFee().multiply(BigDecimal.valueOf(100 + totalSetupTaxPercent)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }
        // lấy thông tin khuyến khích
        resSubscription.setRecommended(pricing.getRecommendedStatus());
        // reaction pricing
        if (Objects.nonNull(userLogin)) {
            resSubscription.setReaction(serviceReactionRepository.existsByUserIdAndServiceIdAndType(userLogin.getId(), pricing.getPricingDraftId(), 3));
        } else {
            resSubscription.setReaction(false);
        }
        // Lấy thông tin gói cước khác
        CustomerTypeEnum customerTypeEnum = userDetails != null ? CustomerTypeEnum.fromValue(userDetails.getCustomerType()) : null;

        // Lấy thông tin gói cước khác
        List<PricingSaaSResDTO> pricingOther = new ArrayList<>();
        List<PricingSaaSResDTO> getPricingInTheSameService = pricingService.findAllPricingByService(pricing.getServiceId(), null, customerTypeEnum, -1L);
        if (Objects.nonNull(getPricingInTheSameService) && !getPricingInTheSameService.isEmpty()) {
            List<PricingSaaSResDTO> getSuggestPricing = getPricingInTheSameService.stream().filter(x -> !x.getId().equals(pricing.getId())).collect(Collectors.toList());
            Collections.shuffle(getSuggestPricing);
            pricingOther.addAll(getSuggestPricing);
        }
        if (Objects.nonNull(resSubscription.getServiceSuggestions()) && !resSubscription.getServiceSuggestions().isEmpty() && pricingOther.size() < 10) {
            List<Long> serviceIdsWithTheSameCategory = resSubscription.getServiceSuggestions().stream().map(ServiceSuggestionResDTO::getId).collect(Collectors.toList());
            Collections.shuffle(serviceIdsWithTheSameCategory);
            for (Long serviceId : serviceIdsWithTheSameCategory) {
                if (pricingOther.size() > 10) break;
                pricingOther.addAll(pricingService.findAllPricingByService(serviceId, null, customerTypeEnum, -1L));
            }
        }

        resSubscription.setPricingSuggestions(pricingOther.size() >= 10 ? pricingOther.subList(0, 10) : pricingOther);

        // Set gia tri cho featureList
        String listFeatureId = pricing.getListFeatureId();
        resSubscription.setFeatureList(getFeatureDetaiolByIds(listFeatureId));
        // lấy chu kỳ thanh toán
        List<PricingMultiplePeriodResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodForPricing(pricing.getId(), customerTypeEnum, pricing.getServiceEntity());
        List<PricingMultiplePeriodResDTO> multiplePeriodResponse = new ArrayList<>();
        for (PricingMultiplePeriodResDTO mul : multiplePeriods) {
            if (customerTypeEnum == null || customerTypeEnum == CustomerTypeEnum.UNSET ||
                    mul.getCustomerTypeCode() == null || mul.getCustomerTypeCode().contains(customerTypeEnum)) {
                if (Objects.nonNull(mul.getPrice())) {
                    mul.setPriceAfterTax(mul.getPrice().multiply(BigDecimal.valueOf(1).add(BigDecimal.valueOf(totalPriceTaxPercent).divide(BigDecimal.valueOf(100)))));
                } else if (Objects.nonNull(mul.getPriceFrom())) {
                    mul.setPriceAfterTax(mul.getPriceFrom().multiply(BigDecimal.valueOf(1).add(BigDecimal.valueOf(totalPriceTaxPercent).divide(BigDecimal.valueOf(100)))));
                }
                multiplePeriodResponse.add(mul);
            }
        }
        resSubscription.setPricingMultiplePeriods(multiplePeriodResponse);

        if (multiplePeriodResponse.size() > 1) {
            resSubscription.setPrice(null);
            resSubscription.setPriceAfterTax(null);
        }

        return resSubscription;
    }


    @Override
    public SubscriptionDetailCommonDTO getSubscriptionCommon(Long id) {
        // Kiểm tra subscription có tồn tại?
        log.info("=================== start getSubscriptionCommon API ===================");

        ISubscriptionDetailDTO sub = subscriptionRepository.findSubDetailBySubId(id)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SUBSCRIPTION, ErrorKey.ID, String.valueOf(id)));

        SubscriptionsDetailDTO subDetailDTO = new SubscriptionsDetailDTO(sub);

        // SME chỉ xem được subscription của chính nó /dev và admin tạo hộ
        if (!AuthUtil.getCurrentParentId()
            .equals(subDetailDTO.getUserId())) {
            String messageNotFound = messageSource.getMessage(
                MessageKeyConstant.USER_NOT_OWN_SUBSCRIPTION,
                subscriptionMessage, LocaleContextHolder.getLocale());
            throw new BadRequestException(messageNotFound,
                Resources.SUBSCRIPTION, ErrorKey.CREATED_BY,
                MessageKeyConstant.USER_NOT_OWN_SUBSCRIPTION);
        }

        // Lấy thông tin chi tiết của subscription
        SubscriptionDetailCommonDTO subCommon = new SubscriptionDetailCommonDTO();
        subCommon.setIsRated(serviceEvaluationRepository.existsBySubId(id));
        SubscriptionDetailCommonInterfaceDTO subInterFaceCommon = subscriptionRepository
            .getSubscriptionCommonInterface(id);
        if (Objects.nonNull(subInterFaceCommon)) {
            BeanUtils.copyProperties(subInterFaceCommon, subCommon);
        }
        // Lay thong tin icon
        if (Objects.nonNull(subCommon.getServiceId())) {
            FileAttach fileAttach = fileAttachRepository.getFileAttach(subCommon.getServiceId()).orElse(null);
            String filePath = Objects.nonNull(fileAttach) ? fileAttach.getFilePath() : CharacterConstant.BLANK;
            String fileName = Objects.nonNull(fileAttach) ? fileAttach.getFileName() : CharacterConstant.BLANK;
            subCommon.setServiceIcon(Objects.isNull(filePath) ? fileName : filePath);
        }
        if (Boolean.TRUE.equals(subCommon.getIsOnlyService())) {
            return subCommon;
        }

        // lấy sim số meta data của sub
        SubscriptionMetadata metadata = metadataRepository.findBySubscriptionId(id).orElse(null);
        if (Objects.nonNull(metadata)) {
            subCommon.setSimMetadata(metadata.getKhcnMetadata());
        }
        subCommon.setMigrationServiceType(MigrationServiceTypeEnum.valueOf(subscriptionRepository.getCategoriesMigrationIdBySubId(id)));

        // Lấy thông tin pricing
        Pricing pricing = pricingRepository.findByIdAndDeletedFlag(subCommon.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        if (Objects.isNull(pricing)) {
            return subCommon;
        }
        subCommon.setDhsxkdSubCode(Objects.isNull(subDetailDTO.getSubCodeDHSXKD()) ? pricing.getPricingName() : subDetailDTO.getSubCodeDHSXKD());

        PricingMultiPlan pricingMultiPlan = null;
        if (Objects.nonNull(subDetailDTO.getPricingMultiPlanId())) {
            pricingMultiPlan = pricingMultiPlanRepository.findMaxPricingMultiPlanById(subDetailDTO.getPricingMultiPlanId(),
                    subCommon.getPricingId())
                .orElseThrow(() -> {
                    String messageNotFound = messageSource.getMessage(
                        MessageKeyConstant.NOT_FOUND, pricingMessage,
                        LocaleContextHolder.getLocale());
                    return new ResourceNotFoundException(messageNotFound,
                        Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                        MessageKeyConstant.NOT_FOUND);
                });
        }

        // tích hợp KHCN: lấy thông tin categories migration id
        subCommon.setMigrationServiceType(MigrationServiceTypeEnum.valueOf(subscriptionRepository.getCategoriesMigrationIdBySubId(id)));

        if (Objects.nonNull(pricingMultiPlan)) {
            subCommon.setPaymentCycle(pricingMultiPlan.getPaymentCycle() == null ? null : pricingMultiPlan.getPaymentCycle().intValue());
            subCommon.setCycleType(
                pricingMultiPlan.getCircleType() == null ? null : CycleTypeEnum.valueOf(pricingMultiPlan.getCircleType()).name());
            if (Objects.nonNull(pricingMultiPlan.getPricingPlan())) {
                subCommon.setPricingPlan(PricingPlanEnum.valueOf(pricingMultiPlan.getPricingPlan()).name());
            }
            if (Objects.isNull(subCommon.getNumberOfCycles())) {
                subCommon.setNumberOfCycles(pricingMultiPlan.getNumberOfCycle());
            }
            subCommon.setPricingMultiPlanId(subDetailDTO.getPricingMultiPlanId());
        }
        // Set trạng thái hiển thị nút kích hoạt
        List<String> roles = userRepository.getRole(Objects.nonNull(subCommon.getCanceledBy())
            && !subCommon.getCanceledBy().equals(BATCH) ? Long.parseLong(subCommon.getCanceledBy()) : -1);
        subCommon.setIsActivateSub(Boolean.FALSE);
        if (roles.contains(RoleType.SME.getValue())
            || roles.contains(RoleType.FULL_SME.getValue())
            || roles.contains(RoleType.EMPLOYEE.getValue())) {
            subCommon.setIsActivateSub(Boolean.TRUE);
        }
        //====set status is oderService====
        ServiceEntity serviceOwner = servicesService.findByIdAndDeletedFlag(subDetailDTO.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
        if (Objects.nonNull(serviceOwner)) {
            subCommon.setRegisterEcontract(serviceOwner.getRegisterEcontract());
        }
        if (Objects.nonNull(serviceOwner.getServiceOwner()) && Objects.equals(serviceOwner.getServiceOwner(), OTHER)) {
            // check orderServiceReceive có tồn tại
            OrderServiceReceive orderServiceReceive = orderServiceReceiveService.findBySubscriptionId(id);
            // lấy provinceId user su dung sub
            User user = userService.findByIdAndDeletedFlag(subDetailDTO.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
            Long provinceId = user.getProvinceId();
            // cập nhật lấy province_id_setup trong bảng sub (KHDN ko lay thong tin trong nay)
            Long provinceIdSetup = subDetailDTO.getProvinceIdSetup();
            provinceId =
                Objects.equals(user.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue()) && provinceIdSetup != null ? provinceIdSetup
                    : provinceId;
            //Neu CreatedSourceMigration khong phai la DHSXKD thi call TrackingOrderServiceReqDTO
            TrackingOrderServiceResDTO resDTO = null;
            if (!Objects.equals(subDetailDTO.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
                // lay du lieu ben DHSXKD
                TrackingOrderServiceReqDTO requestToDHSXKD = new TrackingOrderServiceReqDTO(orderServiceReceive.getTransactionCode(),
                    provinceId);

                DHSXKDTrackingResDTO trackingOrderResponse = executiveProducerService
                    .smeTrackingOrderDHSXKD(requestToDHSXKD, false, subDetailDTO.getId(), orderServiceReceive, null, null);

                //lay date DHSX tra ve
                resDTO = trackingOrderResponse.getData().get(0);
                log.info("====================Get data in response resDTO in method getProgress================".concat(Objects.nonNull(resDTO) ?
                    resDTO.toString() : null));
            }

            SmeProgress orderStatus = null;
            String serviceReceiveOrderStatus = orderServiceReceive.getOrderStatus();
            Long orderStatusId = (Objects.nonNull(resDTO) && Objects.nonNull(resDTO.getOrderStatusId())) ? resDTO.getOrderStatusId()
                : (serviceReceiveOrderStatus == null ? null : Long.valueOf(serviceReceiveOrderStatus));
            if (Objects.nonNull(orderStatusId)) {
                // lấy order status trong DB
                orderStatus = smeProgressRepository.getStatusByStatusId(orderStatusId)
                    .orElseThrow(() -> {
                        String messageNotFound = messageSource.getMessage(
                            MessageKeyConstant.NOT_FOUND,
                            orderStatusMessage,
                            LocaleContextHolder.getLocale()
                        );
                        return new ResourceNotFoundException(
                            messageNotFound,
                            Resources.ORDER_SERVICE,
                            ErrorKey.ID,
                            MessageKeyConstant.NOT_FOUND
                        );
                    });
            }
            if (orderStatus != null) {
                if (Objects.equals(orderStatus.getId(), COMPLETE)) {
                    subCommon.setStatus(SubscriptionStatusEnum.ACTIVE.name());
                } else if (Objects.equals(orderStatus.getId(), CANCEL)) {
                    subCommon.setStatus(SubscriptionStatusEnum.CANCELED.name());
                } else {
                    subCommon.setStatus(SubscriptionStatusEnum.FUTURE.name());
                }
            } else {
                subCommon.setStatus(SubscriptionStatusEnum.FUTURE.name());
            }
        }

        // Set giá trị chu kỳ thanh toán
        Integer currentCycle = subDetailDTO.getCurrentCycle();
        Integer numberOfCycles =
            Objects.nonNull(subDetailDTO.getNumberOfCycles()) ? subDetailDTO.getNumberOfCycles()
                : pricing.getNumberOfCycles();
        subCommon.setIsFinalCycle(YesNoEnum.NO.toString());
        if (Objects.nonNull(currentCycle) && Objects.nonNull(numberOfCycles)
            && checkSubsInFinalCycle(pricing, numberOfCycles, currentCycle)) {
            subCommon.setIsFinalCycle(YesNoEnum.YES.toString());
        }
        // Nếu thuê bao do SME lần đầu đăng ký => numberOfCycle lấy trong bảng pricing
        if (Objects.isNull(subCommon.getNumberOfCycles()) && Objects.nonNull(subCommon.getPricingId())) {
            subCommon.setNumberOfCycles(pricing.getNumberOfCycles());
        }
        // nếu numberOfCycles null thì ngày kết thúc cx phải null
        subCommon.setEndDateSubscription(Objects.isNull(subCommon.getNumberOfCycles()) ? null : subCommon.getEndDateSubscription());

        // Set giá trị cancelDate
        if (SubTypeEnum.PERIODIC.value.equals(pricing.getIsOneTime())) {
            Date canCelDate = Objects.nonNull(subDetailDTO.getEndCurrentCycle()) ? subDetailDTO.getEndCurrentCycle() :
                DateUtil.toDate(
                    DateUtil.calculateCycleDate(
                        new Date(),
                        Objects.nonNull(pricing.getPaymentCycle()) ? pricing.getPaymentCycle() : pricingMultiPlan.getPaymentCycle().intValue(),
                        Objects.nonNull(pricing.getCycleType()) ? CycleTypeEnum.valueOf(pricing.getCycleType())
                            : CycleTypeEnum.valueOf(pricingMultiPlan.getCircleType()),
                        true,
                        subDetailDTO.getCurrentCycle())
                );
            subCommon.setCancelDate(canCelDate);
        }

        // Kiểm tra service được đã đánh giá chưa?
        boolean isEvaluationExits = serviceRepository
            .isCheckHaveEvaluation(subDetailDTO.getServiceId(), AuthUtil.getCurrentUserId(), id);
        subCommon.setIsEvaluation(YesNoEnum.NO);
        if (isEvaluationExits) {
            subCommon.setIsEvaluation(YesNoEnum.YES);
        }
        // Lấy số ngày nhắc nhở gia hạn
        subCommon.setNumberOfDayRemind(systemParamService.getNumberOfDayRemindByParamValue());

        // Lấy thông tin đã có hợp đồng chưa
        subCommon.setIsContract(YesNoEnum.NO);
        if (subscriptionRepository.getContractBySubscriptionId(id) == 0) {
            subCommon.setIsContract(YesNoEnum.YES);
        }
        // Đếm số lượng gói dịch vụ của service
        if (Objects.nonNull(subCommon.getServiceId())) {
            subCommon.setCountPricing(pricingRepository.countPricingByServiceId(subCommon.getServiceId()));
        }
        // Nếu PricingPlan = FLAT_RATE thì usedQuantity = 1, Quantity = 1
        if (Objects.nonNull(subCommon.getPricingPlan()) && Objects.equals(PricingPlanEnum.FLAT_RATE.name(), subCommon.getPricingPlan())) {
            subCommon.setUsedQuantity(1);
            subCommon.setQuantity(1L);
        }

        if (Objects.equals(subDetailDTO.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            subCommon.setCreatedAt(Objects.nonNull(subDetailDTO.getStartedAt()) ? subDetailDTO.getStartedAt() : null);
        } else {
            subCommon.setCreatedAt(Objects.nonNull(subDetailDTO.getCreatedAt()) ? subDetailDTO.getCreatedAt() : null);
        }

        // Subscription trạng thái dùng thử hoặc đã kết thúc
        if (RegTypeEnum.TRIAL.value.equals(subDetailDTO.getRegType())
            || SubscriptionStatusEnum.IN_TRIAL.toString().equals(subCommon.getStatus())
            || SubscriptionStatusEnum.NON_RENEWING.toString().equals(subCommon.getStatus())) {
            return subCommon;
        }

        // Subscription đang hoạt động
        // PricingType = trả sau => Thông tin hóa đơn của chu kỳ hiện tại
        // PricingType = trả trước => Thông tin hóa đơn của chu kỳ tiếp theo

        if (Objects.isNull(subCommon.getCurrentCycle()) && SubTypeEnum.PERIODIC.value.equals(subCommon.getIsOneTime())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.SUBSCRIPTION,
                ErrorKey.Subscription.CURRENT_CYCLE);
        }
        if (Objects.isNull(subCommon.getCycleType()) && SubTypeEnum.PERIODIC.value.equals(subCommon.getIsOneTime())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.COMBO_PLAN, ErrorKey.ComboPlan.CYCLE_TYPE);
        }
        Optional<AmountOfCycleNext> listAmount = subscriptionRepository.getAmountCyclePricingNext(id);
        if (!listAmount.isPresent()) {
            return subCommon;
        }
        AmountOfCycleNext amount = listAmount.get();
        //Nếu là subs đã có change_subs và thay đổi thông tin vào cuối kỳ
        if ((Objects.nonNull(subDetailDTO.getChangeDate()) && Objects.equals(subDetailDTO.getChangeStatus(), YesNoEnum.NO.value))
            || (Objects.nonNull(subDetailDTO.getUpdateDate()) && Objects.equals(subDetailDTO.getUpdateStatus(), YesNoEnum.NO.value))) {
            List<AmountOfCycleNext> amountChangeSubs = subscriptionRepository.getCycleNextWhenExistChangeSubs(id);
            if (!amountChangeSubs.isEmpty()) {
                amount = amountChangeSubs.get(0);
            }
        }

        Date currentDateReactive = DateUtil.toDate(LocalDate.now());
        if (Objects.nonNull(subDetailDTO.getReactiveDate()) && subDetailDTO.getReactiveDate().compareTo(currentDateReactive) > 0) {
            subCommon.setAwaitingReactive(YesNoEnum.YES.value);
        } else {
            subCommon.setAwaitingReactive(YesNoEnum.NO.value);
        }

        int pCycle = Objects.nonNull(subDetailDTO.getCurrentCycleSwap()) ? subDetailDTO.getCurrentCycleSwap() :
            Objects.nonNull(subDetailDTO.getCurrentCycleNonExtend()) ? subDetailDTO.getCurrentCycleNonExtend() : subCommon.getCurrentCycle();
        Date startedAt =
            Objects.nonNull(subDetailDTO.getStartedAtSwap()) ? subDetailDTO.getStartedAtSwap() : subCommon.getStartDateSubscription();
        // lần thay đổi gần nhất là gia hạn thuê bao => currentCycleRenew
        SubscriptionHistory history = subscriptionHistoryRepository.findHistoryBySubscriptionId(id);
        BigDecimal nextAmountCycle = subDetailDTO.getNextPaymentAmount(); // khi đổi gói đã tính toán và lưu vào trường next_payment_amount rồi
        if (!Objects.equals(sub.getCreatedSourceMigration(), CreatedSourceMigrationEnum.BAN_KHCN.getValue())) {
            if ((amount.getAmountCurrentCycle().compareTo(BigDecimal.ZERO) == 0 ||
                PricingTypeEnum.valueOf(subCommon.getPricingType()).equals(PricingTypeEnum.PREPAY)) && Objects.nonNull(history) &&
                SubscriptionHistoryConstant.RENEWAL_SUBSCRIPTION.equalsIgnoreCase(history.getContent()) &&
                Objects.nonNull(subDetailDTO.getCurrentCycleRenew())) {
                pCycle = Objects.nonNull(subDetailDTO.getCurrentCycleNonExtend()) ? subDetailDTO.getCurrentCycleNonExtend() :
                    Objects.nonNull(subDetailDTO.getCurrentCycleRenew()) ? subDetailDTO.getCurrentCycleRenew() : subCommon.getCurrentCycle();
            }

            //gói 1 lần thì ko cần tính
            if (SubTypeEnum.PERIODIC.value.equals(subDetailDTO.getIsOneTime())) {
                LocalDate paymentTimeLocalDate = DateUtil.convertDateToLocalDate(subDetailDTO.getEndCurrentCycle()).plusDays(1);
                Date paymentTimeDate = Date.from(paymentTimeLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

                LocalDate endPaymentLocalDate =
                    Objects.nonNull(subDetailDTO.getNextPaymentTime()) ? DateUtil.convertDateToLocalDate(subDetailDTO.getNextPaymentTime())
                        .minusDays(1)
                        : DateUtil.convertDateToLocalDate(subDetailDTO.getEndCurrentCycle());
                Date endPaymentTimeDate = Date.from(endPaymentLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                Bills newestBillOfSub = billsRepository.findLatestBillBySubscriptionId(subDetailDTO.getId());
                checkExistBill(newestBillOfSub);
                Integer actionType = newestBillOfSub.getActionType();
                boolean isRenewSub = actionType != null && (actionType.equals(ActionTypeEnum.RENEW_SUBSCRIPTION.getValue()) || actionType
                    .equals(ActionTypeEnum.CREATE_SUBSCRIPTION.getValue()));
                boolean isChangePlanPayNowInCurrent = WAIT_CHANGE_PLAN.equals(subDetailDTO.getChangeStatus()); // Đang trong chu kỳ hiện tại
//      Khi gia hạn sub cần tính toán tiền bỏ đi các addon 1 lần, KM hết số lần, phí thiết lập. Nên cần hiển thị giá trị được tín toán ở subscription.getNextPaymentAmount
                boolean checkSubReactive = Objects.nonNull(subDetailDTO.getTypeReactive());
                if (Objects.isNull(subDetailDTO.getReactiveStatus()) &&
                    Objects.equals(subDetailDTO.getStatus(), SubscriptionStatusEnum.CANCELED.value)) {
                    checkSubReactive = Boolean.FALSE;
                }
                if (checkSubReactive) {
                    if (PricingTypeEnum.valueOf(subCommon.getPricingType())
                        .equals(PricingTypeEnum.POSTPAID)) {
                        subCommon.setAmountOfCycle(
                            new AmountOfCycleNext(nextAmountCycle,
                                amount.getCurrencyName(),
                                DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDatePostPaid(
                                    Objects.isNull(subDetailDTO.getNextPaymentTime()) ? paymentTimeDate : subDetailDTO.getNextPaymentTime(),
                                    subCommon.getPaymentCycle(),
                                    CycleTypeEnum.valueOf(subCommon.getCycleType()),
                                    false,
                                    1)),
                                endPaymentTimeDate,
                                Objects.isNull(subDetailDTO.getNextPaymentTime()) ? paymentTimeDate : subDetailDTO.getNextPaymentTime()));
                    } else if (Objects.isNull(subCommon.getNumberOfCycles())
                        || subCommon.getNumberOfCycles() == -1
                        || (Objects.nonNull(subCommon.getNumberOfCycles()) && subCommon
                        .getCurrentCycle() < subCommon.getNumberOfCycles())) {
                        // Tính ngày của chu kỳ tiếp theo
                        AmountOfCycleNext cycleNext = new AmountOfCycleNext(
                            amount.getCurrencyName(),
                            subDetailDTO.getNextPaymentTime(),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                                subDetailDTO.getNextPaymentTime(),
                                subCommon.getPaymentCycle(),
                                CycleTypeEnum.valueOf(subCommon.getCycleType()),
                                true,
                                1)),
                            subDetailDTO.getNextPaymentTime(),
                            nextAmountCycle);
                        subCommon.setAmountOfCycle(cycleNext);
                    }
                } else {
                    if (PricingTypeEnum.valueOf(subCommon.getPricingType())
                        .equals(PricingTypeEnum.POSTPAID)) {
                        Bills newestBillOfCurrentCycleSub = billsRepository.findNewestBillOfCurrentCycleSub(id, sub.getCurrentCycle());
                        checkExistBill(newestBillOfCurrentCycleSub);
                        if (newestBillOfCurrentCycleSub.getStatus() == BillStatusEnum.PAID.value) { // đã thanh toán
                            pCycle = pCycle + 1; // lấy thông tin của chu kỳ kết tiếp
                        }
                        AtomicReference<Date> startCurrentCycle = new AtomicReference<>(DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                            startedAt,
                            subCommon.getPaymentCycle(),
                            CycleTypeEnum.valueOf(subCommon.getCycleType()),
                            false,
                            pCycle - 1)));
                        AtomicReference<Date> endCurrentCycle = new AtomicReference<>(DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                            startedAt,
                            subCommon.getPaymentCycle(),
                            CycleTypeEnum.valueOf(subCommon.getCycleType()),
                            true,
                            (pCycle))));
                        AtomicReference<Date> currentPaymentDate = new AtomicReference<>(DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                            startedAt,
                            subCommon.getPaymentCycle(),
                            CycleTypeEnum.valueOf(subCommon.getCycleType()),
                            false,
                            pCycle)));
                        AtomicReference<BigDecimal> amountPayment = new AtomicReference<>(nextAmountCycle);

                        // case gia han thue bao
                        List<Bills> billLst = billsRepository.findListBillOfSub(id, subCommon.getCurrentCycle());
                        billLst.stream().filter(bl -> bl.getStatus().equals(BillStatusEnum.PAID.value)).findFirst().ifPresent(b -> {
                            billLst.removeIf(bi -> Objects.equals(bi.getId(), b.getId()));
                            Optional<Bills> billOpt = billLst.stream()
                                .filter(bi -> bi.getActionType().equals(ActionTypeEnum.RENEW_SUBSCRIPTION.getValue())).findFirst();
                            if (billOpt.isPresent()) {
                                startCurrentCycle.set(billOpt.get().getStartDateNewRenewal());
                                endCurrentCycle.set(billOpt.get().getEndDateNewRenewal());
                                currentPaymentDate.set(
                                    Objects.nonNull(billOpt.get().getEndDateNewRenewal()) ? addDate(billOpt.get().getEndDateNewRenewal(), 1) : null);
                                amountPayment.set(subDetailDTO.getNextPaymentAmount());
                            }
                        });
                        AmountOfCycleNext amountOfCycle = new AmountOfCycleNext(amountPayment,
                            amount.getCurrencyName(),
                            startCurrentCycle,
                            endCurrentCycle,
                            currentPaymentDate);
                        subCommon.setAmountOfCycle(amountOfCycle);
                    } else if ((amount.getAmountCurrentCycle().compareTo(BigDecimal.ZERO) == 0 ||
                        PricingTypeEnum.valueOf(subCommon.getPricingType()).equals(PricingTypeEnum.PREPAY))
                        && (Objects.isNull(subCommon.getNumberOfCycles())
                        || subCommon.getNumberOfCycles() == -1
                        || (Objects.nonNull(subCommon.getNumberOfCycles()) && pCycle < subCommon.getNumberOfCycles()))) {
                        // Tính ngày của chu kỳ tiếp theo
                        AmountOfCycleNext cycleNext = new AmountOfCycleNext(
                            amount.getCurrencyName(),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                                startedAt,
                                subCommon.getPaymentCycle(),
                                CycleTypeEnum.valueOf(subCommon.getCycleType()),
                                false,
                                pCycle)),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                                startedAt,
                                subCommon.getPaymentCycle(),
                                CycleTypeEnum.valueOf(subCommon.getCycleType()),
                                true,
                                (pCycle + 1))),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                                startedAt,
                                subCommon.getPaymentCycle(),
                                CycleTypeEnum.valueOf(subCommon.getCycleType()),
                                false,
                                pCycle)),
                            nextAmountCycle);
                        subCommon.setAmountOfCycle(cycleNext);
                    }
                }
            }
        } else {
            // TH là dv ban KHCN
            if (Objects.isNull(subCommon.getNumberOfCycles()) || subCommon.getNumberOfCycles() == -1 || pCycle < subCommon.getNumberOfCycles()) {
                // Tính ngày của chu kỳ tiếp theo
                AmountOfCycleNext cycleNext = new AmountOfCycleNext(
                    amount.getCurrencyName(),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                        startedAt,
                        subCommon.getPaymentCycle(),
                        CycleTypeEnum.valueOf(subCommon.getCycleType()),
                        false,
                        pCycle)),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                        startedAt,
                        subCommon.getPaymentCycle(),
                        CycleTypeEnum.valueOf(subCommon.getCycleType()),
                        true,
                        (pCycle + 1))),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(
                        startedAt,
                        subCommon.getPaymentCycle(),
                        CycleTypeEnum.valueOf(subCommon.getCycleType()),
                        false,
                        pCycle)),
                    nextAmountCycle);
                subCommon.setAmountOfCycle(cycleNext);
            }
        }

        if (Objects.equals(serviceOwner.getServiceOwner(), ServiceOwnerEnum.SAAS.value) || Objects
            .equals(serviceOwner.getServiceOwner(), ServiceOwnerEnum.VNPT.value)) {
            subCommon.setInstalled(
                Objects.nonNull(subDetailDTO.getInstalled()) ? InstalledStatusEnum
                    .valueOf(subDetailDTO.getInstalled()).name() : null);
        }
        // Tich hop api tra cuu don hang
        if (Objects.nonNull(subDetailDTO) &&
            !Objects.equals(subDetailDTO.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            OrderServiceReceiveLog orderServiceReceiveLog = executiveProducerService.callApiTrackingOrderDHSXKD(subDetailDTO);
            if (Objects.nonNull(orderServiceReceiveLog)) {
                subCommon.setSetupDate(orderServiceReceiveLog.getDateSetupContract());
            }
        }
        // Kiểm tra thông tin của đối tượng khách hàng có thỏa mãn

        // Check Pre-condition display button renewal
        Integer hasRenew = pricing.getHasRenew();
        Date endDateSub = subCommon.getEndDateSubscription();
        if (Objects.nonNull(endDateSub) && Objects.equals(subCommon.getStatus(), SubscriptionStatusEnum.CANCELED.name())) {
            Date finalDate = new Date();
            // lấy ra thời gian thanh toán không thành công từ cấu hình hệ thống
            Optional<Long> paymentDateFailOn = systemParamService.getPaymentDateFailOn(SystemParamConstant.PARAM_BILL);
            Calendar c = Calendar.getInstance();
            c.setTime(endDateSub);
            c.add(Calendar.DATE, Math.toIntExact(paymentDateFailOn.get()));
            finalDate = c.getTime();
            Date currentDate = new Date();
            if (!endDateSub.after(currentDate) && !finalDate.before(currentDate) && Objects.equals(hasRenew,
                EnumFieldMapper.convertYesNoToEntity(YesNoEnum.YES))) {
                subCommon.setIsRenewal("YES");
            }
        } else if (Objects.equals(subCommon.getStatus(), SubscriptionStatusEnum.ACTIVE.name())) {
            subCommon.setIsRenewal("YES");
        }
        if (!serviceOwner.getCustomerTypeCode().containsAll(pricing.getCustomerTypeCode()) || CollectionUtils.isEmpty(
            pricing.getCustomerTypeCode())) {
            subCommon.setIsRenewal("NO");
        }
        subCommon.setActiveDate(pricing.getActiveDate());


        log.info("=================== end getSubscriptionCommon API ===================");
        return subCommon;
    }

    public AmountOfCycleNext getAmountOfCycle(Integer createdSourceMigration, SubscriptionDetailDTO subCommon,
        SubscriptionsDetailDTO subDetailDTO) {
        Long subscriptionId = subDetailDTO.getId();
        Optional<AmountOfCycleNext> listAmount = subscriptionRepository.getAmountCyclePricingNext(subscriptionId);
        if (!listAmount.isPresent()) {
            return null;
        }
        Integer currentCycle = subCommon.getCurrentCycle();
        Integer pricingType = subCommon.getPricingType().getValue();
        Integer paymentCycle = subCommon.getPaymentCycle();
        CycleTypeEnum cycleType = subCommon.getCycleType();
        Integer numberOfCycles = subCommon.getNumberOfCycles();
        AmountOfCycleNext amount = listAmount.get();
        //Nếu là subs đã có change_subs và thay đổi thông tin vào cuối kỳ
        if ((Objects.nonNull(subDetailDTO.getChangeDate()) && Objects.equals(subDetailDTO.getChangeStatus(), YesNoEnum.NO.value))
            || (Objects.nonNull(subDetailDTO.getUpdateDate()) && Objects.equals(subDetailDTO.getUpdateStatus(), YesNoEnum.NO.value))) {
            List<AmountOfCycleNext> amountChangeSubs = subscriptionRepository.getCycleNextWhenExistChangeSubs(subscriptionId);
            if (!amountChangeSubs.isEmpty()) {
                amount = amountChangeSubs.get(0);
            }
        }

        int pCycle = Objects.nonNull(subDetailDTO.getCurrentCycleSwap()) ? subDetailDTO.getCurrentCycleSwap() :
            Objects.nonNull(subDetailDTO.getCurrentCycleNonExtend()) ? subDetailDTO.getCurrentCycleNonExtend() : currentCycle;
        Date startedAt =
            Objects.nonNull(subDetailDTO.getStartedAtSwap()) ? subDetailDTO.getStartedAtSwap() : subCommon.getStartedAt();
        // lần thay đổi gần nhất là gia hạn thuê bao => currentCycleRenew
        SubscriptionHistory history = subscriptionHistoryRepository.findHistoryBySubscriptionId(subscriptionId);
        BigDecimal nextAmountCycle = subDetailDTO.getNextPaymentAmount(); // khi đổi gói đã tính toán và lưu vào trường next_payment_amount rồi
        if (!Objects.equals(createdSourceMigration, CreatedSourceMigrationEnum.BAN_KHCN.getValue())) {
            if ((amount.getAmountCurrentCycle().compareTo(BigDecimal.ZERO) == 0 ||
                PricingTypeEnum.valueOf(pricingType).equals(PricingTypeEnum.PREPAY)) && Objects.nonNull(history) &&
                SubscriptionHistoryConstant.RENEWAL_SUBSCRIPTION.equalsIgnoreCase(history.getContent()) &&
                Objects.nonNull(subDetailDTO.getCurrentCycleRenew())) {
                pCycle = Objects.nonNull(subDetailDTO.getCurrentCycleNonExtend()) ? subDetailDTO.getCurrentCycleNonExtend() :
                    subDetailDTO.getCurrentCycleRenew();
            }

            //gói 1 lần thì ko cần tính
            if (SubTypeEnum.PERIODIC.value.equals(subDetailDTO.getIsOneTime())) {
                LocalDate paymentTimeLocalDate = DateUtil.convertDateToLocalDate(subDetailDTO.getEndCurrentCycle()).plusDays(1);
                Date paymentTimeDate = Date.from(paymentTimeLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

                LocalDate endPaymentLocalDate =
                    Objects.nonNull(subDetailDTO.getNextPaymentTime()) ? DateUtil.convertDateToLocalDate(subDetailDTO.getNextPaymentTime())
                        .minusDays(1)
                        : DateUtil.convertDateToLocalDate(subDetailDTO.getEndCurrentCycle());
                Date endPaymentTimeDate = Date.from(endPaymentLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                Bills newestBillOfSub = billsRepository.findLatestBillBySubscriptionId(subDetailDTO.getId());
                checkExistBill(newestBillOfSub);
                boolean checkSubReactive = Objects.nonNull(subDetailDTO.getTypeReactive());
                if (Objects.isNull(subDetailDTO.getReactiveStatus()) &&
                    Objects.equals(subDetailDTO.getStatus(), SubscriptionStatusEnum.CANCELED.value)) {
                    checkSubReactive = Boolean.FALSE;
                }
                if (checkSubReactive) {
                    if (PricingTypeEnum.valueOf(pricingType).equals(PricingTypeEnum.POSTPAID)) {
                        return new AmountOfCycleNext(nextAmountCycle, amount.getCurrencyName(),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDatePostPaid(
                                Objects.isNull(subDetailDTO.getNextPaymentTime()) ? paymentTimeDate : subDetailDTO.getNextPaymentTime(),
                                paymentCycle, cycleType, false, 1)), endPaymentTimeDate,
                            Objects.isNull(subDetailDTO.getNextPaymentTime()) ? paymentTimeDate : subDetailDTO.getNextPaymentTime());
                    } else if (Objects.isNull(numberOfCycles) || numberOfCycles == -1 || currentCycle < numberOfCycles) {
                        // Tính ngày của chu kỳ tiếp theo
                        return new AmountOfCycleNext(amount.getCurrencyName(), subDetailDTO.getNextPaymentTime(),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subDetailDTO.getNextPaymentTime(), paymentCycle,
                                cycleType, true, 1)), subDetailDTO.getNextPaymentTime(), nextAmountCycle);
                    }
                } else {
                    if (PricingTypeEnum.valueOf(pricingType).equals(PricingTypeEnum.POSTPAID)) {
                        Bills newestBillOfCurrentCycleSub = billsRepository.findNewestBillOfCurrentCycleSub(subscriptionId,
                            subDetailDTO.getCurrentCycle());
                        checkExistBill(newestBillOfCurrentCycleSub);
                        if (newestBillOfCurrentCycleSub.getStatus() == BillStatusEnum.PAID.value) { // đã thanh toán
                            pCycle = pCycle + 1; // lấy thông tin của chu kỳ kết tiếp
                        }
                        AtomicReference<Date> startCurrentCycle = new AtomicReference<>(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle,
                                cycleType, false, pCycle - 1)));
                        AtomicReference<Date> endCurrentCycle = new AtomicReference<>(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, true, (pCycle))));
                        AtomicReference<Date> currentPaymentDate = new AtomicReference<>(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, false, pCycle)));
                        AtomicReference<BigDecimal> amountPayment = new AtomicReference<>(nextAmountCycle);

                        // case gia han thue bao
                        List<Bills> billLst = billsRepository.findListBillOfSub(subscriptionId, currentCycle);
                        billLst.stream().filter(bl -> bl.getStatus().equals(BillStatusEnum.PAID.value)).findFirst().ifPresent(b -> {
                            billLst.removeIf(bi -> Objects.equals(bi.getId(), b.getId()));
                            Optional<Bills> billOpt = billLst.stream()
                                .filter(bi -> bi.getActionType().equals(ActionTypeEnum.RENEW_SUBSCRIPTION.getValue())).findFirst();
                            if (billOpt.isPresent()) {
                                startCurrentCycle.set(billOpt.get().getStartDateNewRenewal());
                                endCurrentCycle.set(billOpt.get().getEndDateNewRenewal());
                                currentPaymentDate.set(
                                    Objects.nonNull(billOpt.get().getEndDateNewRenewal()) ? addDate(billOpt.get().getEndDateNewRenewal(), 1)
                                        : null);
                                amountPayment.set(subDetailDTO.getNextPaymentAmount());
                            }
                        });
                        return new AmountOfCycleNext(amountPayment, amount.getCurrencyName(), startCurrentCycle, endCurrentCycle,
                            currentPaymentDate);
                    } else if ((amount.getAmountCurrentCycle().compareTo(BigDecimal.ZERO) == 0 ||
                        PricingTypeEnum.valueOf(pricingType).equals(PricingTypeEnum.PREPAY)) &&
                        (Objects.isNull(numberOfCycles) || numberOfCycles == -1 || pCycle < numberOfCycles)) {
                        // Tính ngày của chu kỳ tiếp theo
                        return new AmountOfCycleNext(amount.getCurrencyName(),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, false, pCycle)),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, true, (pCycle + 1))),
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, false, pCycle)),
                            nextAmountCycle);
                    }
                }
            }
        } else {
            // TH là dv ban KHCN
            if (Objects.isNull(numberOfCycles) || numberOfCycles == -1 || pCycle < numberOfCycles) {
                // Tính ngày của chu kỳ tiếp theo
                return new AmountOfCycleNext(amount.getCurrencyName(),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, false, pCycle)),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, true, (pCycle + 1))),
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(startedAt, paymentCycle, cycleType, false, pCycle)),
                    nextAmountCycle);
            }
        }
        return null;
    }

    /**
     * kiểm tra subscription có đang trong chu ký cuối cùng không
     */
    private Boolean checkSubsInFinalCycle(Pricing pricing, Integer numberOfCycle, Integer currentCycle) {
        int changePricing =
            Objects.isNull(pricing.getChangePricingDate()) ? PricingCancelTimeActiveEnum.END_OF_PERIOD.value : pricing.getChangePricingDate();
        return PricingCancelTimeActiveEnum.END_OF_PERIOD.equals(PricingCancelTimeActiveEnum.valueOf(changePricing)) &&
            currentCycle.equals(numberOfCycle);
    }

    /**
     * throws an exception if bill is null
     */
    private void checkExistBill(Bills newestBillOfSub) {
        if (newestBillOfSub == null) {
            String messageNotFound = messageSource.getMessage(
                MessageKeyConstant.NOT_FOUND,
                billMessage,
                LocaleContextHolder.getLocale()
            );
            throw new ResourceNotFoundException(
                messageNotFound,
                Resources.BILL,
                ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND
            );
        }
    }

    /**
     * Cộng thêm 1 ngày cho ngày đến
     */
    private Date addDate(Date date, Integer amountDate) {
        // convert date to calendar
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, amountDate);
        return c.getTime();
    }

    public static final Set<Long> internetTVServiceIds = new HashSet<>();
    static {
        internetTVServiceIds.add(Long.valueOf(MigrationServiceTypeEnum.INTERNET_CAP_QUANG.getValue()));
        internetTVServiceIds.add(Long.valueOf(MigrationServiceTypeEnum.INTERNET_TRUYEN_HINH.getValue()));
        internetTVServiceIds.add(Long.valueOf(MigrationServiceTypeEnum.INTERNET_TRUYEN_HINH_VA_DI_DONG.getValue()));
    }

    /**
     * Lấy thông tin chi tiết thuê bao dịch vụ
     */
    @Override
    public SubscriptionDetailDTO getSubscriptionDetail(Long id, PortalType portalType) {
        CustomUserDetails currentUser = AuthUtil.getLoggedInUser();
        Long currentUserId = currentUser.getId();
        Long currentParentId = AuthUtil.getCurrentParentId();
        boolean isAdmin = AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue()));
        if (isAdmin) {
            portalType = PortalType.ADMIN;
        }
        Subscription subscription = validateSubscription(id, portalType);
        User sme = userService.findByIdAndDeletedFlag(subscription.getUserId(), flagNotDeleted);
        if (Objects.equals(subscription.getCreatedSourceMigration(), CreatedSourceMigrationEnum.BAN_KHCN.getValue())) {
            return getDetailSubBanKHCN(subscription,sme);
        }
        // Lấy thông tin các thành phần thuộc thuê bao
        Pricing pricing = pricingService.findByIdAndDeletedFlag(subscription.getPricingId(), flagNotDeleted);
        ServiceEntity service = servicesService.findByIdAndDeletedFlag(pricing.getServiceId(), flagNotDeleted);

        pricing = subMultiplePeriod.transformPricingMultiPlanIdToPricing(pricing, subscription.getPricingMultiPlanId());
        Integer currentCycle = subscription.getCurrentCycle();
        Integer numberOfCycles =
            Objects.nonNull(subscription.getNumberOfCycles()) ? subscription.getNumberOfCycles() : pricing.getNumberOfCycles();

        SubscriptionDetailDTO res = subscriptionDetailMapper.toDto(pricing);
        // Optional<Integer> installationStatusOpt = subscriptionHelperService.getInstallationStatusOfSubscription(subscription);
        res.setInstallationStatus(subscription.getInstalled());
        if (Objects.nonNull(subscription.getIsOnlyService())) {
            res.setIsBuyService(subscription.getIsOnlyService() || subscription.getIsBuyService());
        } else {
            res.setIsBuyService(subscription.getIsBuyService());
        }
        if (Objects.nonNull(res.getInstallationStatus()) && res.getInstallationStatus().equals(2)) {
            String content = errorIntergrationRepository.getErrorContentBySubId(subscription.getId());
            res.setErrorContent(content);
        }
        String errorActivity = activityLogRepository.getErrorMessageBySubId(subscription.getId());
        res.setErrorActivity(errorActivity);
        // res.setCanUpdateSub((Objects.equals(installationStatusOpt.orElse(null), InstalledStatusEnum.INSTALLED.value) && isSwapPricingOrUpdateSub(subscription)) ? YesNoEnum.YES : YesNoEnum.NO);
        if (Objects.equals(subscription.getInstalled(), InstalledStatusEnum.INSTALLED.value) && isSwapPricingOrUpdateSub(subscription)) {
            res.setCanUpdateSub(YesNoEnum.YES);
        } else {
            res.setCanUpdateSub(YesNoEnum.NO);
        }
        // find service group
        ServiceGroup serviceGroup = serviceGroupService.getServiceGroupBySubscriptionId(id);
        if (Objects.nonNull(serviceGroup)) {
            res.setServiceGroupId(serviceGroup.getId());
            res.setServiceGroupName(serviceGroup.getName());
        }

        // mã nhân viên tư vấn trên Portal Admin
        res.setEmployeeCode(subscription.getEmployeeCode());
        res.setMaDV(subscription.getMaDv());
        //Ngày kết thúc chu kỳ sau gia hạn
        res.setEndCurrentCycleNew(Objects.nonNull(subscription.getEndCurrentCycleNew()) ? subscription.getEndCurrentCycleNew() : null);

        List<String> nameRoles = userRepository.getRole(subscription.getCreatedBy());
        res.setIsAM(!CollectionUtils.isEmpty(nameRoles) && nameRoles.contains(AM));

        // trạng thái hủy thuê bao
        res.setAwaitingCancel(subscription.getAwaitingCancel() != null ? YesNoEnum.valueOf(subscription.getAwaitingCancel()) : YesNoEnum.NO);

        //Role nguoi tao sub
        res.setRoleCreatedBy(subComboService.getRoleUserCreated(subscription.getRegistedBy()));

        res.setIsFinalCycle(YesNoEnum.NO.toString());
        if (Objects.nonNull(currentCycle) && Objects.nonNull(numberOfCycles)
            && checkSubsInFinalCycle(pricing, numberOfCycles, currentCycle)) {
            res.setIsFinalCycle(YesNoEnum.YES.toString());
        }

        // Danh sách CTKM của gói dịch vụ trong thuê bao đã chọn
        List<SubscriptionPricingCouponDTO> couponPricing = new ArrayList<>(getSubscriptionPricingCoupon(id));
        // Danh sách CTKM
        // Long userOfService = pricingRepository.findOwnerPricing(subscription.getPricingId());
        Long userOfService = service.getUserId();
        Integer numSub = shoppingCartRepository.getNumSubOfServiceUser(true, pricing.getServiceId(), subscription.getUserId());

        Long provinceUser;
        if (isAdmin) {
            provinceUser = departmentsRepository.getProvinceIdByUserId(currentUserId);
        } else {
            provinceUser = userRepository.getProvinceIdByUserId(currentUserId);
        }
        // Thông tin khuyến mại mới của gói dịch vụ
        provinceUser = Objects.nonNull(provinceUser) ? provinceUser : SubscriptionConstant.GUEST_USER;
        Long parentId = Objects.isNull(currentUser) ? SubscriptionConstant.GUEST_USER : currentParentId;
        Set<Coupon> couponSet = subscription.getPricingMultiPlanId() == null ?
            pricingRepository.getCouponByPricing(subscription.getPricingId(), provinceUser, parentId) :
            pricingRepository.getCouponByPricingMultiPlanId(subscription.getPricingMultiPlanId(), provinceUser, parentId);
        // set thông tin khuyến mại
        List<Long> lstCouponId = couponSet.stream().map(Coupon::getId).collect(Collectors.toList());
        List<CouponIdAndNameDTO> lstCouponName = couponRepository.getServicePlanPromotionNameByLstCouponId(lstCouponId).stream().map(coupon ->
        {
            CouponIdAndNameDTO couponName = new CouponIdAndNameDTO();
            BeanUtils.copyProperties(coupon, couponName);
            return couponName;
        }).collect(Collectors.toList());

        HashMap<Long, List<CouponIdAndNameDTO>> couponIdToCouponName = new HashMap<>();
        for (CouponIdAndNameDTO coupon : lstCouponName) {
            couponIdToCouponName.computeIfAbsent(coupon.getCouponId(), c -> new ArrayList<>()).add(coupon);
        }
        HashMap<Long, List<SubscriptionPricingAddonDTO.PricingByCouponId>> couponIdToCouponPricing = getPricingByLstCouponId(lstCouponId);

        HashMap<Long, List<CouponEnterpriseAndUserDetailDTO>> couponIdToCouponEnterpriseDetail = new HashMap<>();
        List<CouponEnterpriseAndUserDetailDTO> lstCouponEnterpriseDetail = couponEnterpriseRepository.getCouponEnterpriseByListCouponId(
            lstCouponId);
        for (CouponEnterpriseAndUserDetailDTO detail : lstCouponEnterpriseDetail) {
            couponIdToCouponEnterpriseDetail.computeIfAbsent(detail.getCouponId(), c -> new ArrayList<>()).add(detail);
        }

        HashMap<Long, Long> couponIdToCompanyUsedCount = new HashMap<>();
        List<ICountCouponDTO> lstCountCompanyCoupon = subscriptionRepository.countCompanyUsedCouponByLstCouponId(currentParentId, lstCouponId);
        for (ICountCouponDTO count : lstCountCompanyCoupon) {
            couponIdToCompanyUsedCount.put(count.getCouponId(), count.getCount());
        }

        HashMap<Long, Long> couponIdToUsedCount = new HashMap<>();
        List<ICountCouponDTO> lstCountCoupon = subscriptionRepository.countUsedCouponByLstCouponId(lstCouponId);
        for (ICountCouponDTO count : lstCountCoupon) {
            couponIdToUsedCount.put(count.getCouponId(), count.getCount());
        }

        couponSet.forEach(c -> {
            SubscriptionPricingCouponDTO coupon = new SubscriptionPricingCouponDTO();
            boolean checkValid = (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value))
                && ((!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                && !Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || !Objects.equals(c.getAddonsType(), AddonTypeEnum.OPTION.value))
                && (!Objects.equals(c.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                || !Objects.equals(c.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || !Objects.equals(c.getAddonsType(), AddonTypeEnum.NONE.value));
            if (checkValid
                && validateCoupons(couponIdToCouponEnterpriseDetail, couponIdToCompanyUsedCount, couponIdToUsedCount,
                c, null, CouponConst.COUPON_OF_PRICING)
                && (Objects.equals(c.getPortal(), PortalType.ADMIN.getType())
                || (Objects.equals(c.getPortal(), PortalType.DEV.getType()) && Objects.equals(c.getUserId(), userOfService)))) {
                //set pricings của mỗi couponType = PRODUCT
                if (PromotionTypeEnum.valueOf(c.getPromotionType()).equals(PromotionTypeEnum.PRODUCT)) {
                    coupon.setPricing(couponIdToCouponPricing.get(c.getId()));
                } else {
                    coupon.setPricing(null);
                }
                if (couponPricing.stream().noneMatch(cp -> cp.getCouponId().equals(c.getId()))) {
                    coupon.setCouponId(c.getId());
                    coupon.setCouponName(c.getName());
                    coupon.setTimesUsedType(
                        TimeUsedTypeEnum.valueOf(ObjectUtil.getOrDefault(c.getTimesUsedType(), TimeUsedTypeEnum.UNLIMITED.value)));
                    coupon.setLimitedQuantity((c.getLimitedQuantity()));
                    coupon.setMinimum(c.getMinimum());
                    coupon.setMinimumAmount(c.getMinimumAmount());
                    coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                    coupon.setDiscountValue(c.getDiscountValue());
                    coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                    coupon.setDiscountAmount(c.getDiscountAmount());
                    coupon.setMinimum((c.getMinimum()));
                    coupon.setTimeType(Objects.nonNull(c.getType()) ? TimeTypeEnum.valueOf(c.getType()).toString() : null);
                    coupon.setEndDate(c.getEndDate());
                    coupon.setConditions(couponService.getCouponConditions(couponIdToCouponName, c, CouponConst.COUPON_OF_PRICING));
                    coupon.setCode(c.getCode());
                    couponPricing.add(coupon);
                }
            }
        });
        //danh sach addon cua goi
        Set<SubscriptionPricingAddonsDTO> addonPricing = getSubscriptionPricingAddon(pricing, subscription, portalType);

        // Lay thong tin pricingMultiPlan
        PricingMultiPlan pricingMultiPlan = null;
        Optional<PricingMultiPlan> pricingMultiPlanOpt = pricingMultiPlanRepository.findMaxPricingMultiPlanById
            (Objects.nonNull(subscription.getPricingMultiPlanId()) ? subscription.getPricingMultiPlanId() : DEFAULT_ID, pricing.getId());
        if (pricingMultiPlanOpt.isPresent()) {
            pricingMultiPlan = pricingMultiPlanOpt.get();
            res.setMinimumQuantity(Objects.nonNull(pricingMultiPlan.getMinimumQuantity()) ? pricingMultiPlan.getMinimumQuantity() : null);
            res.setMaximumQuantity(Objects.nonNull(pricingMultiPlan.getMaximumQuantity()) ? pricingMultiPlan.getMaximumQuantity() : null);
        }
        // Danh sách thuế của phí thiết lập
        List<PricingTaxRes> setupFeeTax = pricingTaxRepository.getSubscriptionPricingFeeTax(pricing.getId());
        List<SubscriptionCouponTotalBillDTO> couponLists = getCouponTotalBillBySubscription(id);
        res.setQuantity(subscription.getQuantity());
        res.setTotalAmount(subscription.getTotalAmount());
        Bills bill = billsRepository.findFirstBySubscriptionsIdOrderByIdDesc(id);
        if (Objects.nonNull(bill)) {
            res.setTotalAmountAfterRefund(Objects.nonNull(bill.getTotalAmountAfterAdjustment()) ? bill.getTotalAmountAfterAdjustment() : null);
            res.setBillingStatus(bill.getStatus());
            res.setBillingActionType(Objects.nonNull(bill.getActionType()) ? bill.getActionType() : -1);
        }
        res.setCurrencyName(Objects.nonNull(pricing.getCurrencyId()) ? currencyRepository.getCurrencyNameById(pricing.getCurrencyId()) : null);
        res.setCouponPricings(couponPricing.stream().distinct().collect(Collectors.toList()));
        res.setTaxList(pricingTaxRepository.getSubscriptionPricingTax(pricing.getId()));
        res.setCouponList(couponLists);
        res.setServiceName(service.getServiceName());
        res.setServiceStatus(StatusEnum.valueOf(service.getStatus()));
        res.setAllowMultiSub(service.getAllowMultiSub());
        res.setNumSub(numSub);
        res.setServiceId(pricing.getServiceId());
        res.setStartCurrentCycle(subscription.getStartCurrentCycle());
        res.setEndCurrentCycle(subscription.getEndCurrentCycle());
        res.setExpiredDate(subscription.getExpiredTime());
        res.setMigrateCode(subscription.getMigrateCode());
        if (Objects.nonNull(subscription.getMigrateTime())) {
            res.setMigrateTime(DateUtil.convertLocalDateTimeToDate(subscription.getMigrateTime()));
        }
        res.setInstalledTime(subscription.getInstalledTime());
        if (Objects.isNull(res.getNumberOfCycles())) {
            res.setNumberOfCycles(pricingMultiPlan.getNumberOfCycle());
            if (Objects.isNull(pricingMultiPlan.getNumberOfCycle()) && Objects.nonNull(res.getPricingId())) {
                res.setNumberOfCycles(pricing.getNumberOfCycles());
            }
        }
        if (Objects.nonNull(pricingMultiPlan) && Objects.nonNull(pricingMultiPlan.getNumberOfCycle())) {
            res.setNumberOfCyclesPricing(pricingMultiPlan.getNumberOfCycle());
        } else if (Objects.nonNull(pricing.getNumberOfCycles())) {
            res.setNumberOfCyclesPricing(pricing.getNumberOfCycles());
        } else {
            res.setNumberOfCyclesPricing(subscription.getNumberOfCycles());
        }
        res.setNumberOfCycles(subscription.getNumberOfCycles());
        res.setNumberOfCyclesPopup(subscription.getNumberOfCycles());
        res.setCycleTypeTry(Objects.nonNull(subscription.getCycleType()) ? CycleTypeEnum.valueOf(subscription.getCycleType()).name() : null);
        res.setTrialDay(subscription.getTrialDay());
        res.setRegType(Objects.isNull(subscription.getRegType()) ? null : RegTypeEnum.valueOf(subscription.getRegType()));
        res.setPaymentMethod(
            Objects.isNull(subscription.getPaymentMethod()) ? null : PaymentMethodEnum.valueOf(subscription.getPaymentMethod()));
//        res.setListUnitLimited(subscriptionFormula.getPricingPriceBeforeTax(pricing, id, pricingMultiPlan));
        BigDecimal pricingSetupFee = getPricingSetupFee(pricing, id, setupFeeTax);
        res.setSetupFee(pricingSetupFee);
        setSetupFeeLabel(res, pricingSetupFee, service);
        res.setSetupFeeTaxList(setupFeeTax);
        res.setCurrentCycle(subscription.getCurrentCycle());
        res.setDhsxkdSubCode(subscription.getSubCodeDHSXKD());
        res.setChangeDate(subscription.getChangeDate());
        res.setProvinceCreatedBy(Objects.nonNull(subscription.getRegistedBy()) ?
            departmentsRepository.getProvinceIdByUserId(subscription.getRegistedBy())
            : null);
        res.setPricingMultiPlanId(Objects.isNull(pricingMultiPlan) ? DEFAULT_ID : pricingMultiPlan.getId());
        res.setPricingMultiPlanStatus(Objects.isNull(pricingMultiPlan) ? StatusEnum.UNSET : StatusEnum.valueOf(pricingMultiPlan.getDisplayStatus()));
        res.setHasCouponList(!CollectionUtils.isEmpty(couponLists) ? YesNoEnum.YES : YesNoEnum.NO);
        res.setHasCouponListPricing(!CollectionUtils.isEmpty(couponPricing) ? YesNoEnum.YES : YesNoEnum.NO);
        res.setHasRenew(Objects.isNull(pricing.getHasRenew()) ? YesNoEnum.YES : YesNoEnum.valueOf(pricing.getHasRenew()));
        // Lấy thông tin đã có hợp đồng chưa
        res.setIsContract(YesNoEnum.NO);

        // Trạng thái thuê bao
        res.setSubscriptionStatus(SubscriptionStatusEnum.fromValue(subscription.getStatus()));
        // Trạng thái order service
        Integer orderServiceStatus = subscriptionRepository.getStatusOrderService(id);
        if (service.getServiceOwnerPartner() == null && Objects.equals(service.getServiceOwner(), ServiceOwnerEnum.NONE.getValue())) {
            // SPC_ONEDXINTERNAL-10382: Nhà phát triển 3rd party cập nhật trạng thái đơn hàng OS bằng tay
            orderServiceStatus = subscription.getOs3rdStatus().intValue();
        }
        res.setOrderServiceStatus(SmeProgressEnum.fromValue(orderServiceStatus));

        //neu subscription co trang thai hien tai la da huy
        //-> kiem tra xem co duoc phep kich hoat hay khong
        if (SubscriptionStatusEnum.CANCELED.equals(res.getSubscriptionStatus())) {
            //check hoa don va thoi gian active
            if (checkTimeActiveDate(subscription)) {
                res.setCanActive(YesNoEnum.YES);
            } else {
                res.setCanActive(YesNoEnum.NO);
            }
            Bills billsOutOfDate = billsRepository.findBillOutOfDate(id);
            Date currentDate = DateUtil.toDate(LocalDate.now());
            boolean isTimeInPaymentCycle =
                currentDate.compareTo(res.getStartCurrentCycle()) >= 0 && (currentDate.before(res.getEndCurrentCycle())
                    || Objects.equals(currentDate.getTime(), res.getEndCurrentCycle().getTime()));
            if (Objects.isNull(billsOutOfDate) && isTimeInPaymentCycle) {
                res.setTypeReActiveEnum(TypeReActiveEnum.IN_PAYMENT);
            } else {
                res.setTypeReActiveEnum(TypeReActiveEnum.OUT_OF_DATE);
            }
        }

        //====set status is oderService====
        // ServiceEntity serviceOwner = servicesService.findByIdAndDeletedFlag(subscription.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
        Integer serviceOwner = service.getServiceOwner();
        if (subscriptionRepository.getContractBySubscriptionId(id) == 0 && !CheckUtil.checkIsThirdParty(serviceOwner)) {
            res.setIsContract(YesNoEnum.YES);
        }
        if (Objects.nonNull(serviceOwner) && Objects.equals(serviceOwner, OTHER)) {
            // check orderServiceReceive có tồn tại
            OrderServiceReceive orderServiceReceive = orderServiceReceiveService.findBySubscriptionId(id);
            // lấy provinceId user su dung sub
            Long provinceId = sme.getProvinceId();

            //Neu CreatedSourceMigration khong phai la DHSXKD thi call TrackingOrderServiceReqDTO
            TrackingOrderServiceResDTO resDTO = null;
            if (!Objects.equals(subscription.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
                // lay du lieu ben DHSXKD
                TrackingOrderServiceReqDTO requestToDHSXKD = new TrackingOrderServiceReqDTO(orderServiceReceive.getTransactionCode(),
                    provinceId);

                DHSXKDTrackingResDTO trackingOrderResponse = executiveProducerService.smeTrackingOrderDHSXKD(requestToDHSXKD, false,
                    subscription.getId(), orderServiceReceive, null, null);

                //lay date DHSX tra ve
                resDTO = trackingOrderResponse.getData().get(0);
                if (Objects.nonNull(resDTO)) {
                    res.setSetupDate(resDTO.getSetupContractUpdatedDate());
                    if (Objects.nonNull(resDTO.getEndContractDate()) &&
                        (Objects.isNull(subscription.getEndCurrentCycleContract()) ||
                            resDTO.getEndContractDate().compareTo(subscription.getEndCurrentCycleContract()) != 0)) {
                        subscriptionRepository.updateEndCurrentCycleContract(subscription.getId(), resDTO.getEndContractDate());
                    }
                }
                log.info("====================Get data in response resDTO in method getProgress================".concat(Objects.nonNull(resDTO) ?
                    resDTO.toString() : null));
            }
        }

        //thong tin khach hang
        String firtName = Objects.nonNull(sme.getFirstName()) ? sme.getFirstName() : "";
        String lastName = Objects.nonNull(sme.getLastName()) ? sme.getLastName() : "";
        String repFullName = Objects.nonNull(sme.getRepName()) ? sme.getRepName() : "";
        String customerType = Objects.nonNull(sme.getCustomerType()) ? sme.getCustomerType() : "";
        String province = provinceRepository.getProvinceNameByUserId(sme.getId());
        res.setSmeName(sme.getName());
        res.setUserCode(sme.getUserCode());
        res.setSmeEmail(sme.getEmail());
        res.setSmeId(sme.getId());
        res.setSmeTaxCode(Objects.nonNull(sme.getCorporateTaxCode()) ? sme.getCorporateTaxCode() : "");
        res.setSmeProvince(Objects.nonNull(province) ? province : "");
        if (!repFullName.equals("")) {
            res.setSmeFullName(repFullName);
        } else {
            res.setSmeFullName((customerType.equals(CustomerTypeEnum.PERSONAL.getValue())) ? lastName + " " + firtName : "");
        }
        res.setCreatedSourceMigration(Objects.nonNull(sme.getCreatedSourceMigration()) ? CreatedSourceMigrationEnum
            .valueOf(sme.getCreatedSourceMigration()) : CreatedSourceMigrationEnum.UNSET);
        Address address = addressService.findFirstByUserIdAndTypeAndDefaultLocation(sme.getId(), TYPE_DEFAULT, DEFAULT_USED_QUANTITY);
        if (Objects.nonNull(address)) {
            res.setSmeNameDefault(Objects.nonNull(address.getSmeName()) ? address.getSmeName() : CharacterConstant.BLANK);
            res.setSmeAddressDefault(Objects.nonNull(address.getAddress()) ? address.getAddress() : CharacterConstant.BLANK);
            res.setSmeTaxCodeDefault(Objects.nonNull(address.getTin()) ? address.getTin() : CharacterConstant.BLANK);
        }
        Address setupAddress = addressService.findFirstByUserIdAndTypeAndDefaultLocation(sme.getId(), 1, DEFAULT_USED_QUANTITY);
        if (Objects.nonNull(setupAddress)) {
            res.setSmeSetupAddress(Objects.nonNull(setupAddress.getAddress()) ? setupAddress.getAddress() : CharacterConstant.BLANK);
        }
        res.setSmePersonalCertNumber(sme.getRepPersonalCertNumber());
        res.setSmeCustomerType(CustomerTypeEnum.getValueOf(sme.getCustomerType()));
        if (res.getSmeCustomerType() == CustomerTypeEnum.ENTERPRISE || res.getSmeCustomerType() == CustomerTypeEnum.HOUSE_HOLD) {
            res.setSmeSocialInsuranceNumber(sme.getSocialInsuranceNumber());
        }
        res.setCanChange(pricingRepository.countByServiceIdAndCustomerTypeCode(pricing.getServiceId(),
            sme.getCustomerType() != null ? sme.getCustomerType() : CustomerTypeEnum.ENTERPRISE.getValue(),
            pricing.getId()) > 0 && isSwapPricingOrUpdateSub(subscription) ? YesNoEnum.YES : YesNoEnum.NO);
        // Nếu customerType ko thoa man thi cũng ko cho đổi
        if (!service.getCustomerTypeCode().containsAll(pricing.getCustomerTypeCode()) ||
            CollectionUtils.isEmpty(pricing.getCustomerTypeCode())) {
            res.setCanChange(YesNoEnum.NO);
            res.setCanUpdateSub(YesNoEnum.NO);
            res.setHasRenew(YesNoEnum.NO);
            res.setCanActive(YesNoEnum.NO);
        }

        res.setEmployeeCode(subscription.getEmployeeCode());
        //lay ngay bat dau su dung va ngay yeu cau thanh toan
        res.setCreatedAt(subscription.getCreatedAt());
        res.setStartedAt(subscription.getStartedAt());
        res.setStartChargedAt(subscription.getCurrentPaymentDate());

        res.setAddonsList(addonPricing);

        //one time fee
        res.setOnceTimeFee(getOnceTimeFee(id));

        //summary
        SubscriptionSummaryDTO summary = new SubscriptionSummaryDTO();
        summary.setCouponList(getSubscriptionCouponDiscount(id));
        res.setSummary(summary);

        // Lấy thông tin urlPreOrder
        Set<ComboDetailDTO> urlPreOrder = subscriptionRepository.getPricingAndUrlPreOrder(pricing.getId())
            .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
        res.setUrlPreOrder(urlPreOrder);
        // Set service owner vào sub
        if (Objects.nonNull(subscription.getPricingId())) {
            Integer owner = service.getServiceOwner();
            res.setServiceOwnerType(owner == null ? ServiceOwnerEnum.NONE : ServiceOwnerEnum.valueOf(owner));
            if (Objects.equals(owner, ServiceOwnerEnum.VNPT.value) || Objects.equals(owner, ServiceOwnerEnum.SAAS.value)) {
                res.setServiceOwner(owner == null ? ServiceOwnerTypeEnum.UNSET : ServiceOwnerTypeEnum.valueOf(ServiceOwnerTypeEnum.ON.value));
            } else {
                res.setServiceOwner(owner == null ? ServiceOwnerTypeEnum.UNSET : ServiceOwnerTypeEnum.valueOf(ServiceOwnerTypeEnum.OS.value));
            }
        }
        String couponConfig = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
        if (Objects.nonNull(couponConfig)) {
            res.setCouponConfig(SystemParamEnum.valueOf(Integer.valueOf(couponConfig)));
        }
        if (ENABLE_CAMPAIGN_SUBSCRIPTION) {
            fillMcAppliedInfo(res, subscription);
        }
        SubscriptionFormulaResDTO formulaResDTO;
        List<UnitLimitedNewDTO.UnitLimitedNew> listUnitLimited = subscriptionFormula.getPricingPriceBeforeTax(pricing, id, pricingMultiPlan);
        res.setListUnitLimited(listUnitLimited);
        // Bổ sung thông tin tính toán chi phí vào thông tin đầu ra
        if (Objects.equals(CreatedSourceMigrationEnum.DHSXKD.getValue(), subscription.getCreatedSourceMigration()) ||
            subscription.getMigrateTime() != null) {
            res.setCreatedSourceSubMigration(CreatedSourceMigrationEnum.DHSXKD);
            formulaResDTO = subscriptionMigration.calculateSubsHasMigrate(subscription.getId());
            res.setFormulaResDTO(formulaResDTO);
        } else {
            res.setCreatedSourceSubMigration(CreatedSourceMigrationEnum.ONE_SME);
            formulaResDTO = collectCalculationInfo(subscription, res);
            // Gộp thuế phí để hiển thị theo UI mới
            formulaResDTO.setServiceName(service.getServiceName());
            if (Objects.nonNull(formulaResDTO.getObject())) {
                subscriptionFormula.calculatePricePrePayment(formulaResDTO);
            }
            subscriptionFormula.convertTaxFeePaymentOne(formulaResDTO, res.getOnceTimeFee().stream()
                .map(x -> new SubscriptionFormulaReqDTO.FormulaCustomFee(x.getId(), x.getName(), x.getPrice())).collect(Collectors.toList()));
            res.setFormulaResDTO(formulaResDTO);
            // Thông tin phí vận chuyển
            res.setShippingFee(formulaResDTO.getShippingFee());
        }
        if (Objects.nonNull(subscription.getGroupCode())) {
            boolean isSwap = Objects.equals(subscription.getIsSwap(), 1);
            if (!isSwap) {
                formulaResDTO.getObject().setOriginQuantity(
                        subscriptionRepository.getOriginQuantityByServiceGroupIdAndPricingId(
                                subscription.getServiceGroupId(), shoppingCartSmeService.getLstPricingMultiPlanIdByReferenceId(subscription.getPricingMultiPlanId())));
            }
        }
        //
        if (Objects.nonNull(formulaResDTO.getObject()) && !CollectionUtils.isEmpty(formulaResDTO.getObject().getPlanPriceList())) {
            listUnitLimited = formulaResDTO.getObject().getPlanPriceList().stream()
                    .map(e -> {
                        UnitLimitedNewDTO.UnitLimitedNew unitLimitedNew = new UnitLimitedNewDTO.UnitLimitedNew();
                        unitLimitedNew.setUnitTo(e.getUnitTo());
                        unitLimitedNew.setUnitFrom(e.getUnitFrom());
                        unitLimitedNew.setPrice(e.getUnitPrice());
                        return unitLimitedNew;
                    })
                    .collect(Collectors.toList());
        } else if (!Objects.equals(SubscriptionStatusEnum.IN_TRIAL, res.getSubscriptionStatus())) {
            listUnitLimited = new ArrayList<>();
            UnitLimitedNewDTO.UnitLimitedNew unitLimitedNew = new UnitLimitedNewDTO.UnitLimitedNew();
            unitLimitedNew.setPrice(formulaResDTO.getObject().getInputPrice());
            listUnitLimited.add(unitLimitedNew);
        }
        res.setListUnitLimited(listUnitLimited);
        //bổ sung thông tin reactiveDate
        res.setReActiveDate(subscription.getReactiveDate());
        res.setReactiveStatus(subscription.getReactiveStatus());
        // bổ sung thông tin canceledBy
        res.setCanceledBy(subscription.getCanceledBy());

        Date currentDate = DateUtil.toDate(LocalDate.now());
        if (Objects.nonNull(subscription.getReactiveDate()) && subscription.getReactiveDate().compareTo(currentDate) >= 0) {
            res.setAwaitingReactive(YesNoEnum.YES.value);
        } else {
            res.setAwaitingReactive(YesNoEnum.NO.value);
        }
        Date endDateSub = res.getExpiredDate();
        if (Objects.nonNull(endDateSub) && Objects.equals(res.getSubscriptionStatus(), SubscriptionStatusEnum.CANCELED)) {
            Date finalDate = new Date();
            // Lấy ra thời gian thanh toán không thành công từ cấu hình hệ thống
            Optional<Long> paymentDateFailOn = systemParamService.getPaymentDateFailOn(SystemParamConstant.PARAM_BILL);
            Calendar c = Calendar.getInstance();
            c.setTime(endDateSub);
            c.add(Calendar.DATE, Math.toIntExact(paymentDateFailOn.get()));
            finalDate = c.getTime();
            if (!endDateSub.after(currentDate) && !finalDate.before(currentDate) && Objects
                .equals(pricing.getHasRenew(), EnumFieldMapper.convertYesNoToEntity(YesNoEnum.YES))) {
                res.setHasRenew(YesNoEnum.YES);
            } else {
                res.setHasRenew(YesNoEnum.NO);
            }
        }
        if (Objects.nonNull(serviceOwner) && Objects.equals(serviceOwner, OTHER) && Objects.nonNull(subscription.getEndCurrentCycle())) {
            if (Objects.nonNull(subscription.getEndCurrentCycleContract()) &&
                subscription.getEndCurrentCycleContract().before(subscription.getEndCurrentCycle())) {
                res.setHasRenew(YesNoEnum.YES);
            } else {
                res.setHasRenew(YesNoEnum.NO);
            }
        }
        // Chỉ gia hạn thuê bao ON,thuê bao OS thực hiện ở giai đoạn sau
        // với admin/dev thì cho gia hạn với gói không có cấu hình gia hạn
        if ((Objects.nonNull(serviceOwner) && !Objects.equals(serviceOwner, OTHER)) &&
            (portalType == PortalType.ADMIN || portalType == PortalType.DEV)) {
            res.setHasRenew(YesNoEnum.YES);
        }
        res.setActiveDate(pricing.getActiveDate());
        getInfoUpdatePrice(subscription.getPricingMultiPlanId(), pricing.getPricingDraftId(), res);
        res.setIsOneTime(pricing.getIsOneTime());
        res.setPricingPlan(pricing.getPricingPlan() != null ? PricingPlanEnum.valueOf(pricing.getPricingPlan())
            : PricingPlanEnum.valueOf(pricingMultiPlan.getPricingPlan()));
        //Bổ sung thông tin customField
        List<CustomFieldValueDTO> lstCustomField = customFieldManager.getListSubscriptionFieldValue(res.getSmeCustomerType().getValue(), id,
            portalType);
        res.setLstCustomField(lstCustomField);
        res.setVariantId(subscription.getVariantId());
        res.setVariantName(subscription.getVariantName());
        res.setServiceDraftId(subscription.getServiceDraftId());
        // SPC_SUPPORTONESME-328: VNPT PAY - Cập nhật thông tin phương thức thanh toán
        VNPTPayResponse vnptPayResponse = vnptPayResponseRepository.findBySubscriptionId(id).orElse(null);
        if (Objects.nonNull(vnptPayResponse)) {
            res.setPaymentCode(Objects.nonNull(vnptPayResponse.getVnptpayTransactionId()) ? vnptPayResponse.getVnptpayTransactionId() : null);
            String bankName = bankRepository.getNameByCode(vnptPayResponse.getPaymentMethod());
            res.setBankName(Objects.nonNull(bankName) ? bankName : null);
        }

        // lấy sim số meta data của sub
        SubscriptionMetadata metadata = metadataRepository.findBySubscriptionId(id).orElse(null);
        if (Objects.nonNull(metadata)) {
            res.setSimMetadata(metadata.getKhcnMetadata());
        }
        // Lưu thông tin tương tác gần nhất
        if (Objects.isNull(subscription.getCartCode())) {
            automationRuleService.saveHistoryAssigneeInteractive(AuthUtil.getCurrentUserId(), subscription.getAssigneeId(), subscription.getId(),
                CrmObjectTypeEnum.SUBSCRIPTION, AutomationRuleConstant.ACTION_VIEWED);
        } else {
            Set<Long> lstSubId = subscriptionRepository.findListSubIdByCartCode(subscription.getCartCode());
            automationRuleService.saveListHistoryAssigneeInteractive(AuthUtil.getCurrentUserId(), subscription.getAssigneeId(), lstSubId,
                CrmObjectTypeEnum.SUBSCRIPTION, AutomationRuleConstant.ACTION_VIEWED);
        }
        log.info("=============== end getSubscriptionDetail API =================");
        // Bổ sung thông tin subCommon
        if (Objects.nonNull(subscription.getServiceId())) {
            FileAttach fileAttach = fileAttachRepository.getFileAttach(subscription.getServiceId()).orElse(null);
            String filePath = Objects.nonNull(fileAttach) ? fileAttach.getFilePath() : CharacterConstant.BLANK;
            String fileName = Objects.nonNull(fileAttach) ? fileAttach.getFileName() : CharacterConstant.BLANK;
            res.setIcon(Objects.isNull(filePath) ? fileName : filePath);
        }
        res.setProvider(service.getProvider());
        res.setSubCode(subscription.getFinalSubCode());
        // Lấy thông tin amountOfCycle
        res.setIsRated(serviceEvaluationRepository.existsBySubId(id));
        subscriptionRepository.findSubDetailBySubId(subscription.getId()).ifPresent(iSubscriptionDetailDTO -> res.setAmountOfCycle(
            getAmountOfCycle(res.getCreatedSourceMigration().getValue(), res, new SubscriptionsDetailDTO(iSubscriptionDetailDTO))));
        return res;
    }

    @Override
    public SubscriptionDetailRespDTO getSubscriptionInfo(Long subscriptionId, PortalType portalType) {
        Subscription subscription = findByIdAndDeletedFlag(subscriptionId, DeletedFlag.NOT_YET_DELETED.getValue());
        if (subscription.getComboPlanId() != null) {
            return SubscriptionDetailRespDTO.builder().comboSubscription(getDetailSubsCombo(subscriptionId)).build();
        } else {
            return SubscriptionDetailRespDTO.builder().serviceSubscription(getSubscriptionDetail(subscriptionId, portalType)).build();
        }
    }

    private SubscriptionDetailDTO getDetailSubBanKHCN(Subscription subscription, User sme) {
        SubscriptionDetailDTO res = null;
        Pricing pricing = pricingRepository.findByIdAndDeletedFlag(subscription.getPricingId(), flagNotDeleted).orElse(null);

        // ------------- thông tin pricing ---------------
        if (Objects.nonNull(pricing)) {
            pricing = subMultiplePeriod.transformPricingMultiPlanIdToPricing(pricing, subscription.getPricingMultiPlanId());
            res = subscriptionDetailMapper.toDto(pricing);

            //danh sach addon cua goi
            Set<SubscriptionPricingAddonsDTO> addonPricing = getSubscriptionPricingAddon(pricing, subscription, PortalType.ADMIN);
            res.setAddonsList(addonPricing);
            PricingMultiPlan pricingMultiPlan = null;
            Optional<PricingMultiPlan> pricingMultiPlanOpt = pricingMultiPlanRepository.findMaxPricingMultiPlanById
                (Objects.nonNull(subscription.getPricingMultiPlanId()) ? subscription.getPricingMultiPlanId() : DEFAULT_ID, pricing.getId());
            if (pricingMultiPlanOpt.isPresent()) {
                pricingMultiPlan = pricingMultiPlanOpt.get();
                res.setMinimumQuantity(Objects.nonNull(pricingMultiPlan.getMinimumQuantity()) ? pricingMultiPlan.getMinimumQuantity() : null);
                res.setMaximumQuantity(Objects.nonNull(pricingMultiPlan.getMaximumQuantity()) ? pricingMultiPlan.getMaximumQuantity() : null);
                res.setPricingMultiPlanId(pricingMultiPlan.getId());
            }
            List<UnitLimitedNewDTO.UnitLimitedNew> listUnitLimited = subscriptionFormula.getPricingPriceBeforeTax(pricing, subscription.getId(), pricingMultiPlan);
            res.setListUnitLimited(listUnitLimited);
        } else {
            res = new SubscriptionDetailDTO();
        }
        res.setPaymentMethod(Objects.isNull(subscription.getPaymentMethod()) ? null : PaymentMethodEnum.valueOf(subscription.getPaymentMethod()));
        res.setEmployeeCode(subscription.getEmployeeCode());

        // ---------- thông tin khách hàng -------------
        if (Objects.nonNull(sme)) {
            if (Objects.equals(sme.getEmail(), ApiGwKHCNConstant.EMAIL_KHCN_VANG_LAI)) {
                setSmeDetail(sme, res, subscription.getProvinceIdSetup());
            } else {
                setSmeDetail(sme, res, sme.getProvinceId());
            }
        }

        //  ------------ lấy sim số meta data của sub --------------
        SubscriptionMetadata metadata = metadataRepository.findBySubscriptionId(subscription.getId()).orElse(null);
        if (Objects.nonNull(metadata)) {
            res.setSimMetadata(metadata.getKhcnMetadata());
            // Đồng bộ thông tin trạng thái giao dịch với ban KHCN
            integrationApiGwKHCNSmeService.syncOrder(metadata.getKhcnTransactionId(), metadata.getKhcnServiceId());
        }

        // ------------- Thong tin dich vu -----------------
        ServiceEntity service = subscription.getService();
        res.setServiceId(subscription.getServiceId());
        res.setServiceDraftId(subscription.getServiceDraftId());
        res.setServiceName(service.getServiceName()); // ten dich vu
        res.setInstallationStatus(subscription.getInstalled());

        // Trạng thái thuê bao
        res.setSubscriptionStatus(SubscriptionStatusEnum.fromValue(subscription.getStatus()));
        // Trạng thái order service
        Integer orderServiceStatus = subscriptionRepository.getStatusOrderService(subscription.getId());
        res.setOrderServiceStatus(SmeProgressEnum.fromValue(orderServiceStatus));

        // trạng thái hủy thuê bao
        res.setAwaitingCancel(subscription.getAwaitingCancel() != null ? YesNoEnum.valueOf(subscription.getAwaitingCancel()) : YesNoEnum.NO);
        //Role nguoi tao sub
        res.setRoleCreatedBy(subComboService.getRoleUserCreated(subscription.getRegistedBy()));

        //one time fee
        res.setOnceTimeFee(getOnceTimeFee(subscription.getId()));

        // -------- formula res DTO ----------
        res.setCouponPricings(new ArrayList<>());
        res.setCouponList(new ArrayList<>());

        SubscriptionFormulaResDTO formulaResDTO = new SubscriptionFormulaResDTO();

        MigrationServiceTypeEnum migrationServiceType = MigrationServiceTypeEnum
            .valueOf(subscriptionRepository.getCategoriesMigrationIdBySubId(subscription.getId()));

        if (Objects.equals(migrationServiceType, MigrationServiceTypeEnum.SIM_KEM_GOI) && Objects.isNull(subscription.getPricingId())) {
            // nếu là sim số không gói
            formulaResDTO = subscriptionFormula.calculateOnlySim(subscription, res);
        } else if (Objects.equals(migrationServiceType, MigrationServiceTypeEnum.SIM_KEM_GOI) && Objects.nonNull(subscription.getPricingId())) {
            // nếu là sim số có gói
            formulaResDTO = collectCalculationInfo(subscription, res);
            subscriptionFormula.calculateSimWithPricing(subscription, res, formulaResDTO);
        } else {
            // nếu là Gói cước di động, Truyền hình, MyTV, Dịch vụ (giữ nguyên luồng cũ)
            formulaResDTO = collectCalculationInfo(subscription, res);
            // Gộp thuế phí để hiển thị theo UI mới
            formulaResDTO.setServiceName(service.getServiceName());
            if (Objects.nonNull(formulaResDTO.getObject())) {
                subscriptionFormula.calculatePricePrePayment(formulaResDTO);
            }

            subscriptionFormula.convertTaxFeePaymentOne(formulaResDTO, res.getOnceTimeFee().stream()
                .map(x -> new SubscriptionFormulaReqDTO.FormulaCustomFee(x.getId(), x.getName(), x.getPrice())).collect(Collectors.toList()));
        }
        res.setFormulaResDTO(formulaResDTO);
        BigDecimal pricingSetupFee = res.getSetupFee();
        setSetupFeeLabel(res, pricingSetupFee, service);
        res.setMigrationServiceType(MigrationServiceTypeEnum.valueOf(subscriptionRepository.getCategoriesMigrationIdBySubId(subscription.getId())));
        if (Objects.nonNull(subscription.getServiceId())) {
            FileAttach fileAttach = fileAttachRepository.getFileAttach(subscription.getServiceId()).orElse(null);
            String filePath = Objects.nonNull(fileAttach) ? fileAttach.getFilePath() : CharacterConstant.BLANK;
            String fileName = Objects.nonNull(fileAttach) ? fileAttach.getFileName() : CharacterConstant.BLANK;
            res.setIcon(Objects.isNull(filePath) ? fileName : filePath);
        }
        res.setProvider(service.getProvider());
        res.setSubCode(subscription.getFinalSubCode());
        // Lấy thông tin amountOfCycle
        res.setIsRated(serviceEvaluationRepository.existsBySubId(subscription.getId()));
        Optional<ISubscriptionDetailDTO> sub = subscriptionRepository.findSubDetailBySubId(subscription.getId());
        if (sub.isPresent()) {
            res.setAmountOfCycle(getAmountOfCycle(res.getCreatedSourceMigration().getValue(), res, new SubscriptionsDetailDTO(sub.get())));
        }
        return res;
    }

    private void setSetupFeeLabel(SubscriptionDetailDTO res, BigDecimal pricingSetupFee, ServiceEntity service) {
        res.setSetupFeeLabel(BillConstant.LABEL_SETUP_FEE);
        if(pricingSetupFee != null){
            Integer createdSourceMigration = service.getCreatedSourceMigration();
            Long migrationId = service.getMigrationId();
            if(Objects.equals(createdSourceMigration, KHCN_MIGRATE_SOURCE) && Objects.nonNull(migrationId) && internetTVServiceIds.contains(migrationId)){
                res.setSetupFeeLabel(ApiGwKHCNConstant.SETUP_FEE);
            }
        }
    }

    private BigDecimal priceBeforeTax(BigDecimal price, List<FormulaTax> taxes) {
        if (Objects.isNull(price))
            return null;
        if (CollectionUtils.isEmpty(taxes)) {
            return price;
        } else {
            taxes.size();
        }
        BigDecimal totalPercentTax = BigDecimal.ZERO;
        for (FormulaTax tax : taxes) {
            //nếu có thuế chưa bao gồm -> addon chưa bao gồm thuế, trả về đơn giá ban đầu
            if (Objects.equals(YesNoEnum.NO.value, tax.getHasTax()))
                return price;
            totalPercentTax = totalPercentTax.add(tax.getPercent());
        }
        if (totalPercentTax.compareTo(BigDecimal.ZERO) < 1) {
            //tính đơn giá trước thuế
            price = price.divide(BigDecimal.valueOf(1)
                .add(totalPercentTax.divide(BigDecimal.valueOf(100))), 0 , RoundingMode.HALF_UP);
        }
        return price;
    }

    private void setSmeDetail(User enterprise, SubscriptionDetailDTO res, Long provinceId) {
        log.info("=== start setSmeDetail ====");
        String repFullName = Objects.nonNull(enterprise.getRepName()) ? enterprise.getRepName() : "";
        String customerType = Objects.nonNull(enterprise.getCustomerType()) ? enterprise.getCustomerType() : "";
        String province = provinceRepository.getProvinceNameById(provinceId);
        res.setSmeName(enterprise.getName());
        res.setSmeEmail(enterprise.getEmail());
        res.setSmeId(enterprise.getId());
        res.setSmeTaxCode(Objects.nonNull(enterprise.getCorporateTaxCode()) ? enterprise.getCorporateTaxCode() : "");
        res.setSmeProvince(Objects.nonNull(province) ? province : "");
        if (!customerType.equals(CustomerTypeEnum.PERSONAL.getValue()) && !repFullName.isEmpty()) {
            res.setSmeFullName(repFullName);
        } else {
            res.setSmeFullName(enterprise.getFullName());
        }
        res.setCreatedSourceMigration(Objects.nonNull(enterprise.getCreatedSourceMigration()) ? CreatedSourceMigrationEnum
            .valueOf(enterprise.getCreatedSourceMigration()) : CreatedSourceMigrationEnum.UNSET);
        Address address = addressService.findFirstByUserIdAndTypeAndDefaultLocation(enterprise.getId(), TYPE_DEFAULT,
            DEFAULT_USED_QUANTITY);
        if (Objects.nonNull(address)) {
            res.setSmeNameDefault(Objects.nonNull(address.getSmeName()) ? address.getSmeName() : CharacterConstant.BLANK);
            res.setSmeAddressDefault(Objects.nonNull(address.getAddress()) ? address.getAddress() : CharacterConstant.BLANK);
            res.setSmeTaxCodeDefault(Objects.nonNull(address.getTin()) ? address.getTin() : CharacterConstant.BLANK);
        }
        Address setupAddress = addressService.findFirstByUserIdAndTypeAndDefaultLocation(enterprise.getId(), 1, DEFAULT_USED_QUANTITY);
        if (Objects.nonNull(setupAddress)) {
            res.setSmeSetupAddress(Objects.nonNull(setupAddress.getAddress()) ? setupAddress.getAddress() : CharacterConstant.BLANK);
        }
        res.setSmePersonalCertNumber(enterprise.getRepPersonalCertNumber());
        res.setSmeCustomerType(CustomerTypeEnum.getValueOf(enterprise.getCustomerType()));
        if (res.getSmeCustomerType() == CustomerTypeEnum.ENTERPRISE || res.getSmeCustomerType() == CustomerTypeEnum.HOUSE_HOLD) {
            res.setSmeSocialInsuranceNumber(enterprise.getSocialInsuranceNumber());
        }
        log.info("=== end setSmeDetail ====");
    }

    /**
     * subscription có đang trong giữa chu kỳ để được đổi gói hoặc chỉnh sửa thuê bao không
     */
    private boolean isSwapPricingOrUpdateSub(Subscription subscription) {
        if (Objects.isNull(subscription.getCurrentCycle())) {
            return true;
        }
        Bills currentBill = billsRepository.getFirstByOfCycle(subscription.getId(), subscription.getCurrentCycle());
        if (currentBill != null && Objects.equals(currentBill.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            Date currentDate = DateUtil.toDate(LocalDate.now());
            Date endCurrentCycle = subscription.getEndCurrentCycle();
            return currentDate.compareTo(subscription.getStartCurrentCycle()) < 0 || // Gói sử dụng trong tương lai
                (endCurrentCycle == null) || // Gói một lần
                (currentDate.before(endCurrentCycle) && !Objects.equals(currentDate.getTime(), endCurrentCycle.getTime())); // Gói đang sử dụng
        }
        return true;
    }

    /**
     * Lay danh sach CTKM cua goi
     */
    private List<SubscriptionPricingCouponDTO> getSubscriptionPricingCoupon(Long id) {
        List<SubscriptionPricingCouponDTO> pricingCoupons = pricingRepository.getCouponByPricingSubscription(id);
        List<SubscriptionPricingCouponDTO> couponNotValid = new ArrayList<>();
        // : pricingRepository.getCouponByPricingId(id);
        // set thông tin khuyến mại
        pricingCoupons.forEach(c -> {
            //set pricings của mỗi couponType = PRODUCT
            if (Objects.nonNull(c.getPromotionType()) && PromotionTypeEnum.PRODUCT.value == c.getPromotionType().value) {
                List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupon = getPricingByCoupon(c.getCouponId());
                c.setPricing(pricingCoupon);
                BigDecimal cValue = new BigDecimal(CollectionUtils.isEmpty(pricingCoupon) ? 0 : pricingCoupon.size());
                c.setCouponValue(cValue);
            } else {
                c.setPricing(null);
                c.setCouponValue(c.getDiscountAmount());
                c.setDiscountValue(c.getDiscountValue());
            }
            c.setConditions(couponService.getCouponConditions(c.getCouponId(), CouponConst.COUPON_OF_PRICING));
        });
        return pricingCoupons;
    }

    private HashMap<Long, List<SubscriptionPricingAddonDTO.PricingByCouponId>> getPricingByLstCouponId(List<Long> lstCouponId) {
        HashMap<Long, List<SubscriptionPricingAddonDTO.PricingByCouponId>> couponIdToPricing = new HashMap<>();
        couponRepository.getPricingByLstCouponId(lstCouponId).forEach(p -> {
            SubscriptionPricingAddonDTO.PricingByCouponId pricingCoupon = new SubscriptionPricingAddonDTO.PricingByCouponId();
            BeanUtils.copyProperties(p, pricingCoupon);
            couponIdToPricing.computeIfAbsent(p.getCouponId(), pc -> new ArrayList<>()).add(pricingCoupon);
        });
        return couponIdToPricing;
    }

    private boolean validateCoupons(HashMap<Long, List<CouponEnterpriseAndUserDetailDTO>> couponIdToCouponEnterpriseDetail,
        HashMap<Long, Long> couponIdToCompanyUsedCount, HashMap<Long, Long> couponIdToUsedCount, Coupon coupon, Long userId, String classify) {
        boolean isValid = true;
        Long currentUserId = Objects.nonNull(userId) ? userId : AuthUtil.getCurrentParentId();
        //Neu ap dung cho doanh nghiep can check user login
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)) {
            List<CouponEnterpriseAndUserDetailDTO> couponEnterprise = couponIdToCouponEnterpriseDetail.get(coupon.getId());
            long count = couponEnterprise.stream()
                .filter(cou -> Objects.equals(cou.getUserId(), currentUserId))
                .count();
            if (count == 0) {
                isValid = false;
            }
        }

        //DK1: Check số lần áp dụng
        if (isValid && Objects.nonNull(coupon.getMaximumPromotion())) {
            Long companyUsedCoupon;
            if (currentUserId != null) {
                companyUsedCoupon = couponIdToCompanyUsedCount.get(coupon.getId());
            } else {
                companyUsedCoupon = null;
            }
            if (companyUsedCoupon != null && companyUsedCoupon >= coupon.getMaximumPromotion()) {
                isValid = false;
            }
        }

        //DK: Nếu đều
        //Nếu 4 cái đều ko chọn thì false
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            isValid = false;
        }
        //Nếu là KM của pricing mà ko chọn doanh nghiệp hoạc ko chọn gói thì true
        else if ((Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN))
            && ((Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value))
            || ((Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
            || Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
            || (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)))) {
            isValid = false;
        } else if ((Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN)
            || Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING)
            || Objects.equals(classify, CouponConst.COUPON_TOTAL))
            && !(Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.YES.value))) {
            isValid = false;
        }
        //Nếu là KM của addon  mà ko chọn doanh nghiệp hoạc ko chọn DVBS thì true
        else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            isValid = false;
        }
        //DK2: check thời gian
        LocalDate startDate = Objects.isNull(coupon.getStartDate()) ? SubscriptionConstant.LOCAL_DATE_MIN_DATE
            : coupon.getStartDate();
        LocalDate endDate = Objects.isNull(coupon.getEndDate()) ? SubscriptionConstant.LOCAL_DATE_MAX_DATE
            : coupon.getEndDate();
        if (isValid && (LocalDate.now().isBefore(startDate)
            || LocalDate.now().isAfter(endDate))) {
            isValid = false;
        }

        //DK4: Check số lần hưởng khuyến mại tối đa
        if (isValid && Objects.nonNull(coupon.getMaxUsed())) {
            Long subscriptionUsedCoupon = couponIdToUsedCount.get(coupon.getId());
            if (subscriptionUsedCoupon != null && subscriptionUsedCoupon >= coupon.getMaxUsed()) {
                isValid = false;
            }
        }

        return isValid;
    }


    /**
     * Lay danh sach CTKM tren tong hoa don cua subscription
     */
    private List<SubscriptionCouponTotalBillDTO> getCouponTotalBillBySubscription(Long subsId) {
        List<SubscriptionCouponTotalBillDTO> result = new ArrayList<>();
        subscriptionRepository.getCouponBySubscription(subsId, PromotionTypeEnum.ALL.value).forEach(c -> {
            SubscriptionCouponTotalBillDTO coupon = new SubscriptionCouponTotalBillDTO();
            coupon.setId(c.getId());
            coupon.setName(c.getName());
            coupon.setMinimum(c.getMinimum());
            coupon.setMinimumAmount(c.getMinimumAmount());
            coupon.setMaximum(c.getMaximumPromotion());
            coupon.setTimesUsedType(c.getTimesUsedType() == null ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
            coupon.setLimitedQuantity(c.getLimitedQuantity());
            coupon.setType(c.getType() == null ? null : TimeTypeEnum.valueOf(c.getType()));
            coupon.setCode(c.getCode());
            coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_PRICING));
            result.add(coupon);
        });
        return result;
    }

    /**
     * Lấy thông tin khuyến mại từ CDQC của thuê bao
     *
     * @param response     Thông tin chi tiết thuê bao (đầu ra)
     * @param subscription Thông tin thuê bao cần lấy chi tiết khuyến mại từ CDQC
     */
    private void fillMcAppliedInfo(SubscriptionDetailDTO response, Subscription subscription) {
        // Lọc các billing thuộc cùng chu kỳ thanh toán với thuê bao
        List<Bills> lstBilling = subscription.getBills().stream()
            .filter(bill -> Objects.equals(bill.getCurrentCycle(), subscription.getCurrentCycle()))
            .collect(Collectors.toList());
        lstBilling.forEach(billing -> {
            List<BillItem> lstBillItem = billItemRepository.findByBillingId(billing.getId());
            lstBillItem.forEach(billItem -> {
                if (billItem.getObjectType() == null) {
                    return;
                }
                switch (billItem.getObjectType()) {
                    case 0: // PRICING
                    case 1: // COMBO PLAN
                        List<BillMcPrivate> lstBillMcPrivate = billMcPrivateRepository.findByBillItemIdIn(
                            Collections.singleton(billItem.getId()));
                        response.getLstMcPrivatePrice().addAll(lstBillMcPrivate.stream().filter(item -> item.getAmountByCash() != null)
                            .map(this::convertBillMcPrivate).collect(Collectors.toList()));
                        response.getLstMcPrivatePercent().addAll(lstBillMcPrivate.stream().filter(item -> item.getAmountByPercent() != null)
                            .map(this::convertBillMcPrivate).collect(Collectors.toList()));
                        List<BillMcTotal> lstBillMcTotal = billMcTotalRepository.findByBillItemIdIn(Collections.singleton(billItem.getId()));
                        response.getLstMcInvoicePrice().addAll(lstBillMcTotal.stream().filter(item -> item.getAmountByCash() != null)
                            .map(this::convertBillMcTotal).collect(Collectors.toList()));
                        response.getLstMcInvoicePercent().addAll(lstBillMcTotal.stream().filter(item -> item.getAmountByPercent() != null)
                            .map(this::convertBillMcTotal).collect(Collectors.toList()));
                        break;
                    case 2: // ADDON
                        if (response.getAddonsList() == null) {
                            return;
                        }
                        response.getAddonsList().stream().filter(item -> Objects.equals(item.getId(), billItem.getObjectId())).forEach(addon -> {
                            List<BillMcPrivate> lstBillMcAddonPrivate = billMcPrivateRepository.findByBillItemIdIn(
                                Collections.singleton(billItem.getId()));
                            addon.getLstMcAddonPrice().addAll(lstBillMcAddonPrivate.stream().filter(item -> item.getAmountByCash() != null)
                                .map(this::convertBillMcPrivate).collect(Collectors.toList()));
                            addon.getLstMcAddonPercent().addAll(lstBillMcAddonPrivate.stream().filter(item -> item.getAmountByPercent() != null)
                                .map(this::convertBillMcPrivate).collect(Collectors.toList()));
                            List<BillMcTotal> lstBillMcAddonTotal = billMcTotalRepository.findByBillItemIdIn(
                                Collections.singleton(billItem.getId()));
                            addon.getLstMcInvoicePrice().addAll(lstBillMcAddonTotal.stream().filter(item -> item.getAmountByCash() != null)
                                .map(this::convertBillMcTotal).collect(Collectors.toList()));
                            addon.getLstMcInvoicePercent().addAll(lstBillMcAddonTotal.stream().filter(item -> item.getAmountByPercent() != null)
                                .map(this::convertBillMcTotal).collect(Collectors.toList()));
                        });
                        break;
                }
            });
        });
    }

    /**
     * Lấy thông tin tính toán chi phí của 1 thuê bao đã đăng kí từ DB
     *
     * @param detailDTO Thông tin chi tiết thuê bao
     * @return Thông tin chi phí của thuê bao đã đăng kí
     */
    private SubscriptionFormulaResDTO collectCalculationInfo(Subscription subscription, SubscriptionDetailDTO detailDTO) {
        try {
            SubscriptionFormula subscriptionFormula = SpringContextUtils.getBean(SubscriptionFormula.class);
            SubscriptionCalculator calculator = subscriptionFormula.createCalculation(subscription, detailDTO);
            return calculator.calc();
        } catch (Exception e) {
            e.printStackTrace();
            return new SubscriptionFormulaResDTO();
        }
    }

    /**
     * lấy thông tin pricingId mới nhất, pricingMultiPlanId mới nhất để cập nhật giá gói mới nhất cho popup gia hạn
     */
    public void getInfoUpdatePrice(Long curPricingMultiPlanId, Long curPricingDraftId, SubscriptionDetailDTO res) {
        PricingMultiPlan newestPMP = getNewestPricingMultiPlan(curPricingMultiPlanId, curPricingDraftId);
        if (newestPMP != null) {
            res.setNewestPricingMultiPlanId(newestPMP.getId());
            res.setNewestPricingId(newestPMP.getPricingId());
            res.setPricingStatus(StatusEnum.valueOf(pricingRepository.getPricingStatus(newestPMP.getPricingId())));
        } else {
            res.setNewestPricingMultiPlanId(res.getPricingMultiPlanId());
            res.setNewestPricingId(res.getPricingId());
            res.setPricingStatus(StatusEnum.valueOf(pricingRepository.getPricingStatus(res.getPricingId())));
        }
    }

    private SubscriptionMcDetailDTO convertBillMcPrivate(BillMcPrivate bill) {
        MarketingCampaign campaign = campaignRepository.findFirstById(bill.getMcId());
        McActivity activity = mcActivityRepository.findFirstByMcIdAndIndex(bill.getMcId(), bill.getActivityIdx());

        boolean discountByCash = bill.getAmountByCash() != null;
        return SubscriptionMcDetailDTO.builder()
            .mcName(campaign == null ? null : campaign.getName())
            .activityName(activity == null ? null : activity.getName())
            .mcId(bill.getMcId())
            .activityIdx(bill.getActivityIdx())
            .ruleIdx(bill.getRuleIdx())
            .effectSetIdx(bill.getEffectSetIdx())
            .effectItemIdx(bill.getEffectItemIdx())
            .price(discountByCash ? bill.getAmountByCash() : bill.getAmountByPercent())
            .discountType(discountByCash ? DiscountTypeEnum.PRICE : DiscountTypeEnum.PERCENT)
            .billItemId(bill.getBillItemId())
            .percent(bill.getPercent())
            .maxAmount(bill.getMaxAmount())
            .amount(bill.getAmount())
            .numberApplyUnit(bill.getNumberApplyUnit())
            .build();
    }

    private SubscriptionMcDetailDTO convertBillMcTotal(BillMcTotal bill) {
        MarketingCampaign campaign = campaignRepository.findFirstById(bill.getMcId());
        McActivity activity = mcActivityRepository.findFirstByMcIdAndIndex(bill.getMcId(), bill.getActivityIdx());

        boolean discountByCash = bill.getAmountByCash() != null;
        return SubscriptionMcDetailDTO.builder()
            .mcName(campaign == null ? null : campaign.getName())
            .activityName(activity == null ? null : activity.getName())
            .mcId(bill.getMcId())
            .activityIdx(bill.getActivityIdx())
            .ruleIdx(bill.getRuleIdx())
            .effectSetIdx(bill.getEffectSetIdx())
            .effectItemIdx(bill.getEffectItemIdx())
            .price(discountByCash ? bill.getAmountByCash() : bill.getAmountByPercent())
            .discountType(discountByCash ? DiscountTypeEnum.PRICE : DiscountTypeEnum.PERCENT)
            .billItemId(bill.getBillItemId())
            .percent(bill.getPercent())
            .maxAmount(bill.getMaxAmount())
            .amount(bill.getAmount())
            .numberApplyUnit(bill.getNumberApplyUnit())
            .build();
    }

    /**
     * Lay danh sach addon
     */
    @Override
    public Set<SubscriptionPricingAddonsDTO> getSubscriptionPricingAddon(Pricing pricing,
        Subscription subscription, PortalType portalType) {
        Long multiPlanId = Objects.isNull(subscription.getPricingMultiPlanId()) ? DEFAULT_ID : subscription.getPricingMultiPlanId();
        PricingMultiPlan pricingMultiPlan = null;
        pricingMultiPlan = pricingMultiPlanRepository.findByIdAndDeletedFlag(multiPlanId, flagNotDeleted).orElse(null);
        Long paymentCycle = Objects.nonNull(pricing.getPaymentCycle()) ? Long.valueOf(pricing.getPaymentCycle())
            : Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getPaymentCycle() : DEFAULT_ID;
        Integer cycleType = Objects.nonNull(pricing.getCycleType()) ? pricing.getCycleType()
            : Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getCircleType() : -1;
        Long pricingId = Objects.nonNull(pricing.getId()) ? pricing.getId() : DEFAULT_ID;
        Set<SubscriptionPricingAddonsDTO> resAddon = new HashSet<>();
        // Lấy paymentCycle mới nhất của gói mới nhất
        Long pricingDraftId = Objects.nonNull(pricing.getPricingDraftId()) ? pricing.getPricingDraftId() : DEFAULT_ID;
        Long lastPricingDBId = pricingRepository.findMaxPricingId(pricingDraftId).orElse(DEFAULT_ID);
        Long multiPlanDBIdNew = pricingMultiPlanRepository.findMaxPricingMultiPlanById(multiPlanId).orElse(DEFAULT_ID);

        Set<SubscriptionAddonOfPricingDTO> addons = addonRepository.getAddonBySubscriptionId(subscription.getId(), pricingId
            , paymentCycle, cycleType, multiPlanId);
        addons.addAll(
            addonRepository.getAddonByPricingMultiPlan(subscription.getId(), multiPlanId, paymentCycle, cycleType, pricingId, lastPricingDBId,
                multiPlanDBIdNew));
        convertInterfaceToEntity(resAddon, addons);
        resAddon.forEach(
            a -> a.setAddonMultiPlanId(Objects.equals(AuthUtil.PARENT_ID, a.getAddonMultiPlanId()) ? null : a.getAddonMultiPlanId()));
        Comparator<SubscriptionPricingAddonsDTO> comp = Comparator.comparing(SubscriptionPricingAddonsDTO::getIsRequired);
        new ArrayList<>(resAddon).sort(comp.reversed());
        //danh sach addonId
        Set<Long> addonIds = resAddon.stream().map(SubscriptionPricingAddonsDTO::getId).collect(Collectors.toSet());

        //danh sach thue cua addon theo goi
        List<PricingTaxRes> addonTaxs = addonsTaxRepository.getAddonTaxByIds(addonIds);
        List<PricingTaxRes> addonSetupFeeList = !CollectionUtils.isEmpty(addonIds) ? addonsTaxRepository.getAddonSetupFeeTaxByIds(
            new ArrayList<>(addonIds)) : new ArrayList<>();

        //danh sach ctkm cua addon theo goi
        User customer = subscription.getUser();
        if (Objects.isNull(subscription.getService())) {
            return new HashSet<>();
        }
        List<SubscriptionPricingAddonCouponDTO> pricingAddonCoupons = addonRepository
            .getCouponByAddonIdsAndCustomerId(addonIds, customer.getId(), subscription.getService().getUserId());
        pricingAddonCoupons.addAll(addonRepository.getCouponByAddonPeriods(addonIds, subscription.getUserId(), subscription.getId()));
        pricingAddonCoupons.addAll(addonRepository.getCouponByAddonSubIds(addonIds, subscription.getUserId(), subscription.getId()));

        Set<Long> couponIds = pricingAddonCoupons.stream().map(SubscriptionPricingAddonCouponDTO::getId).collect(Collectors.toSet());

        List<SubscriptionCheckedAddonCouponDTO> couponChecked =
            subscriptionRepository.getCouponAddonChecked(subscription.getId(), addonIds, couponIds);

        resAddon.forEach(addon -> {
            List<SubscriptionPricingAddonDTO.Tax> taxList = new ArrayList<>();
            List<SubscriptionPricingAddonDTO.Tax> taxSetupFeeList = new ArrayList<>();
            List<SubscriptionPricingAddonsDTO.CouponList> couponList = new ArrayList<>();
            List<SubscriptionPricingAddonsDTO.UnitLimited> unitLimitedList = new ArrayList<>();
            Addon ad = new Addon();
            ad.setId(addon.getId());
            ad.setPricingPlan(getPricingPlanValue(addon.getPricingPlan()));
            ad.setPrice(addon.getPrice());

            if (!CollectionUtils.isEmpty(addonTaxs)) {
                List<PricingTaxRes> addonTax = addonTaxs.stream().filter(t ->
                    addon.getId().equals(t.getAddonId())).collect(Collectors.toList());
                ad.setPrice(subscriptionFormula.priceBeforeTax(addon.getPrice(), addonTax));
                if (!CollectionUtils.isEmpty(addonTax)) {
                    addonTax.forEach(t -> {
                        SubscriptionPricingAddonDTO.Tax tax = new SubscriptionPricingAddonDTO.Tax();
                        tax.setTaxId(t.getTaxId());
                        tax.setTaxName(t.getTaxName());
                        tax.setPercent(t.getPercent());
                        tax.setHasTax(YesNoEnum.valueOf(t.getHasTax()));
                        taxList.add(tax);
                    });
                }
            }
            if (!CollectionUtils.isEmpty(addonSetupFeeList)) {
                List<PricingTaxRes> addonSetupFee = addonSetupFeeList.stream().filter(t -> Objects.equals(addon.getId(), t.getAddonId()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(addonSetupFee)) {
                    addonSetupFee.forEach(
                        t -> taxSetupFeeList.add(new SubscriptionPricingAddonDTO.Tax(t.getTaxId(), t.getTaxName(), t.getPercent(),
                            t.getHasTax() == null ? YesNoEnum.NO : YesNoEnum.valueOf(t.getHasTax()))));
                }
            }
            //            Set coupon by pricing_id
            if (!CollectionUtils.isEmpty(pricingAddonCoupons)) {
                List<SubscriptionPricingAddonCouponDTO> pricingAddonCoupon = pricingAddonCoupons.stream().filter(
                    c -> ((Objects.equals(addon.getId(), c.getAddonId()) || Objects.isNull(c.getAddonId()))
                        && (Objects.equals(AddonTypeEnum.ALL.value, c.getAddonsType())
                        || Objects.equals(EnterpriseTypeEnum.ALL.value, c.getEnterpriseType())
                        || Objects.equals(customer.getId(), c.getEnterpriseId()))
                        && c.getCustomerTypeCode().contains(customer.getCustomerType()))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pricingAddonCoupon)) {
                    pricingAddonCoupon.forEach(c -> {
                        SubscriptionPricingAddonsDTO.CouponList coupon = new SubscriptionPricingAddonsDTO.CouponList();
                        coupon.setCouponId(c.getId());
                        coupon.setCouponName(c.getName());
                        coupon.setCode(c.getCode());
                        coupon.setMinimum(c.getMinimum());
                        coupon.setMinimumAmount(c.getMinimumAmount());
                        coupon.setTimesUsedType(c.getTimesUsedType() == null ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
                        coupon.setLimitedQuantity(c.getLimitedQuantity());
                        coupon.setStartDate(DateUtil.convertLocalDateToDate(c.getStartDate()));
                        coupon.setEndDate(DateUtil.convertLocalDateToDate(c.getEndDate()));
                        coupon.setPromotionType(c.getPromotionType() == null ? null : PromotionTypeEnum.valueOf(c.getPromotionType()));
                        coupon.setDiscountType(c.getDiscountType() == null ? null : DiscountTypeEnum.valueOf(c.getDiscountType()));
                        coupon.setDiscountAmount(c.getDiscountAmount());
                        coupon.setTimeType(c.getType() == null ? null : TimeTypeEnum.valueOf(c.getType()));
                        coupon.setCode(c.getCode());
                        coupon.setConditions(couponService.getCouponConditions(c.getId(), CouponConst.COUPON_OF_ADDON));
                        //set pricings của mỗi couponType = PRODUCT
                        if (PromotionTypeEnum.PRODUCT.value ==
                            (Objects.isNull(c.getPromotionType()) ? -1 : c.getPromotionType())) {
                            List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupons = getPricingByCoupon(c.getId());
                            coupon.setPricing(pricingCoupons);
                            BigDecimal cValue = new BigDecimal(CollectionUtils.isEmpty(pricingCoupons) ? 0 : pricingCoupons.size());
                            coupon.setCouponValue(cValue);
                        } else {
                            coupon.setPricing(null);
                            coupon.setCouponValue(c.getDiscountAmount());
                            coupon.setDiscountValue(c.getDiscountValue());
                        }
                        setCheckedCouponAddon(couponChecked, addon, couponList, c, coupon);
                    });
                }
            }
            subMultiplePeriod.transformAddonMultiPlanIdToAddon(addon);

            // lay danh sach unitlimited list cua addon
            List<SubscriptionRangeDTO> rageOfAddons = subscriptionRepository.getAddonRangePrice(addon.getId(), subscription.getId());
            //Xóa phần tử chưa được sửa giá nếu phần tử đó có sửa giá
            rageOfAddons.removeIf(range -> !range.getIsEdited() &&
                rageOfAddons.stream().anyMatch(r -> r.getIsEdited() && r.getUnitFrom().equals(range.getUnitFrom())));

            //Lấy danh sách thuế
            List<PricingTaxRes> addonTaxes = pricingTaxRepository.getAddonTax(addon.getId());

            if (!CollectionUtils.isEmpty(rageOfAddons)) {
                rageOfAddons.forEach(au -> {
                    SubscriptionPricingAddonsDTO.UnitLimited unitLimited = new SubscriptionPricingAddonsDTO.UnitLimited();
                    unitLimited.setUnitTo(Objects.nonNull(au.getUnitTo()) ? (long) au.getUnitTo() : null);
                    unitLimited.setUnitFrom(Objects.nonNull(au.getUnitFrom()) ? (long) au.getUnitFrom() : null);
                    unitLimited.setAddonsId(addon.getId());
                    unitLimited.setPrice(au.getIsEdited() ? au.getPrice() : subscriptionFormula.priceBeforeTax(au.getPrice(), addonTaxes));
                    unitLimitedList.add(unitLimited);
                });
            }
            if (Objects.nonNull(addon.getAddonMultiPlanId())) {
                unitLimitedList.clear(); // Only return unit limited entries from matching multiplan id
                // lay danh sach unitlimited list cua addon trong bang pricingMultiPlan detail
                List<SubscriptionRangeDTO> rageOfAddonsMultiPlan = subscriptionRepository
                    .getAddonMultiPlanRange(addon.getAddonMultiPlanId(), subscription.getId());
                //Xóa phần tử chưa được sửa giá nếu phần tử đó có sửa giá
                rageOfAddonsMultiPlan.removeIf(range -> !range.getIsEdited() &&
                    rageOfAddonsMultiPlan.stream().anyMatch(r -> r.getIsEdited() && r.getUnitFrom().equals(range.getUnitFrom())));

                if (!CollectionUtils.isEmpty(rageOfAddonsMultiPlan)) {
                    rageOfAddonsMultiPlan.forEach(au -> {
                        SubscriptionPricingAddonsDTO.UnitLimited unitLimited = new SubscriptionPricingAddonsDTO.UnitLimited();
                        unitLimited.setUnitTo(Objects.nonNull(au.getUnitTo()) ? (long) au.getUnitTo() : null);
                        unitLimited.setUnitFrom(Objects.nonNull(au.getUnitFrom()) ? (long) au.getUnitFrom() : null);
                        unitLimited.setAddonsId(addon.getId());
                        unitLimited.setPrice(au.getIsEdited() ? au.getPrice() : subscriptionFormula.priceBeforeTax(au.getPrice(), addonTaxes));
                        unitLimited.setAddonMultiPeriodId(addon.getAddonMultiPlanId());
                        unitLimitedList.add(unitLimited);
                    });
                }
            }

            addon.setUnitLimitedList(unitLimitedList);
            if (!CollectionUtils.isEmpty(unitLimitedList)) {
                addon.setMinimumQuantity(unitLimitedList.stream()
                        .map(SubscriptionPricingAddonsDTO.UnitLimited::getUnitFrom)
                        .filter(Objects::nonNull)
                        .min(Long::compare)
                        .orElse(-1L)
                        .intValue());
                addon.setMaximumQuantity((unitLimitedList.stream()
                        .map(SubscriptionPricingAddonsDTO.UnitLimited::getUnitTo)
                        .filter(Objects::nonNull)
                        .max((a, b) -> {
                            if (a.equals(-1L)) return 1;
                            if (b.equals(-1L)) return -1;
                            return a.compareTo(b);
                        })
                        .orElse(-1L)
                        .intValue()));
            }
            //phí thiết lập của addon
            subscriptionRepository.getAddonSetupFee(addon.getId(), subscription.getId(),
                Objects.isNull(addon.getAddonMultiPlanId()) ? -1L : addon.getAddonMultiPlanId()).ifPresent(addonSetupFee -> {
                addon.setSetupFee(addonSetupFee.getPrice());
                if (!addonSetupFee.getIsEdited()) {
                    addon.setSetupFee(subscriptionFormula.getPriceBeforeTax(addonSetupFee.getPrice(),
                        taxSetupFeeList.stream().map(x ->
                                new SubscriptionPricingAddonDTO.Tax(x.getTaxId(), x.getTaxName(), x.getPercent(), x.getHasTax()))
                            .collect(Collectors.toList())));
                }
            });
            addon.setTaxList(taxList);
            addon.setTaxSetupFeeList(taxSetupFeeList);
            addon.setCouponList(couponList.stream().distinct().collect(Collectors.toList()));
            addon.setHasCouponList(!CollectionUtils.isEmpty(couponList) ? YesNoEnum.YES : YesNoEnum.NO);
            addon.setPrice(getAddonPrice(ad, subscription.getId(), addon.getAddonMultiPlanId()));
        });
        return resAddon;
    }

    /**
     * Convert interface to entity
     */
    private Set<SubscriptionPricingAddonsDTO> convertInterfaceToEntity(Set<SubscriptionPricingAddonsDTO> resAddon,
        Set<SubscriptionAddonOfPricingDTO> addons) {
        addons.forEach(a -> {
            SubscriptionPricingAddonsDTO addon = new SubscriptionPricingAddonsDTO();
            BeanUtils.copyProperties(a, addon);
            if (Objects.isNull(a.getHasChangeQuantity())) {
                addon.setHasChangeQuantity(ChangeQuantityEnum.NONE);
            } else {
                int allowIncrease = a.getHasChangeQuantity().indexOf("1");
                int allowDecrease = a.getHasChangeQuantity().indexOf("2");
                if (allowIncrease != -1 && allowDecrease != -1) {
                    addon.setHasChangeQuantity(ChangeQuantityEnum.ALL);
                } else if (allowIncrease != -1) {
                    addon.setHasChangeQuantity(ChangeQuantityEnum.INCREASE);
                } else if (allowDecrease != -1) {
                    addon.setHasChangeQuantity(ChangeQuantityEnum.DECREASE);
                }
            }
            resAddon.add(addon);
        });
        return resAddon;
    }

    /**
     * Convert pricing plan name sang pricing plan value
     *
     * @param pricingPlan string pricing plan
     * @return gia tri pricing plan
     */
    private Integer getPricingPlanValue(String pricingPlan) {
        switch (pricingPlan) {
            case "FLAT_RATE":
                return 0;
            case "UNIT":
                return 1;
            case "TIER":
                return 2;
            case "VOLUME":
                return 3;
            case "STAIR_STEP":
                return 4;
        }
        return -1;
    }

    /**
     * set giá trị checked cho coupon và add vào list coupon của addon
     */
    private void setCheckedCouponAddon(List<SubscriptionCheckedAddonCouponDTO> couponChecked, SubscriptionPricingAddonsDTO addon,
        List<SubscriptionPricingAddonsDTO.CouponList> couponList, SubscriptionPricingAddonCouponDTO c,
        SubscriptionPricingAddonsDTO.CouponList coupon) {
        if ((Objects.nonNull(addon.getChecked()) ? addon.getChecked() : Boolean.FALSE) && !couponChecked.isEmpty()) {
            couponChecked.stream().filter(cop -> cop.getCouponId().equals(c.getId()) && cop.getId().equals(addon.getId()))
                .findFirst().ifPresent(checkedCoupon -> {
                    if (checkedCoupon.getChecked()) {
                        coupon.setChecked(true);
                    }
                    couponList.add(coupon);
                });
        } else {
            couponList.add(coupon);
        }
    }


    /**
     * Lấy đơn giá của addon theo kế hoạnh định giá
     */
    @Override
    public BigDecimal getAddonPrice(Addon addon, Long subscriptionId, Long multiPlanId) {
        Optional<SubscriptionSetupFee> setupFees =
            subscriptionSetupFeeRepository.findAllBySubscriptionIdAndAddonIdAndPriceIsNotNull(subscriptionId, addon.getId())
                .stream().findFirst();

        BigDecimal price = subscriptionCalculateService.calculatePriceAddon(addon, multiPlanId);
        if (setupFees.isPresent()) {
            price = setupFees.get().getPrice();
        }
        return price;
    }

    /**
     * Lấy phí thiết lập của pricing
     */
    @Override
    public BigDecimal getPricingSetupFee(Pricing pricing, Long subscriptionId, List<PricingTaxRes> setupFeeTax) {
        Optional<BigDecimal> setupFees =
            subscriptionSetupFeeRepository.findSetupFeeBySubscriptionAndPricing(subscriptionId, pricing.getId());
        return setupFees.orElseGet(() -> subscriptionFormula.priceBeforeTax(pricing.getSetupFee(), setupFeeTax));
    }

    /**
     * Lấy PricingMultiPlan mới nhất (nếu có cập nhật service/gói dịch vụ)
     *
     * @return PricingMultiPlan mới nhất nếu có cập nhật; NULL nếu không có cập nhật ảnh hưởng tới dịch vụ hoặc gói dịch vụ
     */
    @Override
    public PricingMultiPlan getNewestPricingMultiPlan(Long curPricingMultiPlanId, Long curPricingDraftId) {
        List<PricingMultiPlan> pricingMultiPlanLst = pricingMultiPlanRepository
            .findAllByPricingDraftIdAndDeletedFlagAndPricingIdIsNotNull(curPricingDraftId, DeletedFlag.NOT_YET_DELETED
                .getValue());
        Map<Long, PricingMultiPlan> pricingMultiPlanMap = new HashMap<>();
        pricingMultiPlanLst.forEach(item -> {
            if (item.getPricing() != null && item.getReferenceId() != null) {
                pricingMultiPlanMap.put(item.getReferenceId(), item);
            }
        });
        PricingMultiPlan itemPMP = pricingMultiPlanMap.get(curPricingMultiPlanId);
        PricingMultiPlan newestPMP = null;
        while (itemPMP != null) {
            curPricingMultiPlanId = itemPMP.getId();
            newestPMP = itemPMP;
            itemPMP = pricingMultiPlanMap.get(curPricingMultiPlanId);
        }
        return newestPMP;
    }

    @Override
    public IntegrationSubsDetailDTO getSubscriptionDetailIntegrate(Long id, Subscription subscription, Pricing pricing) {
        boolean isDevOrAdmin = false;
        SubscriptionDetailDTO res = subscriptionDetailMapper.toDto(pricing);

        //danh sach CTKM cua goi
        List<SubscriptionPricingCouponDTO> couponPricing = getSubscriptionPricingCoupon(
            isDevOrAdmin ? id : pricing.getId());
        //danh sach addon cua goi
        Set<SubscriptionPricingAddonsDTO> addonPricing = getSubscriptionPricingAddon(
            pricing, subscription, isDevOrAdmin ? PortalType.DEV : PortalType.SME);

        // lay sme cua sub
        User smeOfSubscription = userRepository.findByIdAndDeletedFlag(subscription.getUserId(), flagNotDeleted).orElse(null);

        res.setQuantity(subscription.getQuantity());
        res.setTotalAmount(subscription.getTotalAmount());
        res.setCurrencyName(
            currencyRepository.getCurrencyNameById(pricing.getCurrencyId()));
        res.setCouponPricings(couponPricing);
        res.setTaxList(pricingTaxRepository.getSubscriptionPricingTax(pricing.getId()));
        res.setCouponList(getSubscriptionCouponProduct(id));
        res.setServiceName(serviceRepository.getServiceNameById(pricing.getServiceId()));
        res.setServiceId(pricing.getServiceId());
        res.setStartCurrentCycle(subscription.getStartCurrentCycle());
        res.setEndCurrentCycle(subscription.getEndCurrentCycle());
        res.setExpiredDate(subscription.getExpiredTime());

        // Trạng thái thuê bao
        res.setSubscriptionStatus(SubscriptionStatusEnum.fromValue(subscription.getStatus()));
        //neu subscription co trang thai hien tai la da huy
        //-> kiem tra xem co duoc phep kich hoat hay khong
        if (SubscriptionStatusEnum.CANCELED.equals(res.getSubscriptionStatus())) {
            //check hoa don va thoi gian active
            if (!billsRepository.checkBillsOutOfDate(id) && checkTimeActiveDate(
                subscription)) {
                res.setCanActive(YesNoEnum.YES);
            } else {
                res.setCanActive(YesNoEnum.NO);
            }
        }

        //thong tin khach hang
        SubscriptionEnterpriseDTO enterprise = isDevOrAdmin ? subscriptionRepository
            .getEnterprise(subscription.getUserId()) : null;
        if (!Objects.isNull(enterprise)) {
            res.setSmeName(enterprise.getName());
            res.setSmeId(enterprise.getId());
        }

        //lay ngay bat dau su dung va ngay yeu cau thanh toan
        res.setStartedAt(subscription.getStartedAt());
        res.setStartChargedAt(subscription.getStartChargeAt());

        SubscriptionFirstStepReqDTO inputCalculate = setCaculateSubscriptionReq(pricing,
            subscription.getQuantity(),
            subscription.getId(), couponPricing, addonPricing);
        inputCalculate.setPricingMultiPlanId(subscription.getPricingMultiPlanId());
        SubscriptionCalculateFirstStepResDTO calculate = subscriptionCalculateService.calculateFirstStep(inputCalculate,
            null);

        // set thanh tien theo chu ki addon va pricing
        addonPricing.forEach(a -> calculate.getAddons().forEach(c -> {
            if (c.getId().equals(a.getId())) {
                a.setTotalAddonAmount(c.getPreAmountTax());
                a.setTotalPricingAmount(c.getIntoAmountPreTaxPricingCycle());
            }
        }));

        res.setAddonsList(addonPricing);

        //one time fee
        res.setOnceTimeFee(getOnceTimeFee(id));

        //summary
        SubscriptionSummaryDTO summary = new SubscriptionSummaryDTO();
        summary.setTotalAmountGross(calculate.getTotalAmountPreTaxFinal());
        summary.setCouponList(getSubscriptionCouponDiscount(id));
        summary.setTotalAmountNet(calculate.getTotalAmountAfterTaxFinal());
        res.setSummary(summary);
        IntegrationSubsDetailDTO result = new IntegrationSubsDetailDTO();
        BeanUtils.copyProperties(res, result);

        PricingMultiPlan pricingMultiPlan = null;
        if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
            pricingMultiPlan = pricingMultiPlanRepository.findByIdAndDeletedFlag(subscription.getPricingMultiPlanId(), flagNotDeleted)
                .orElse(null);
        }
        // lấy pricing plan
        PricingPlanEnum pricingPlan = Objects.nonNull(pricing.getPricingPlan()) ? PricingPlanEnum.valueOf(pricing.getPricingPlan())
            : Objects.nonNull(pricingMultiPlan) ? PricingPlanEnum.valueOf(pricingMultiPlan.getPricingPlan()) : null;

        Integer freeQuantity = Objects.nonNull(pricing.getFreeQuantity()) ? pricing.getFreeQuantity().intValue()
            : Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getFreeQuantity() : null;

        Integer paymentCycle = Objects.nonNull(pricing.getPaymentCycle()) ? pricing.getPaymentCycle()
            : Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getPaymentCycle().intValue() : null;

        CycleTypeEnum cycleType = Objects.nonNull(pricing.getCycleType()) ? CycleTypeEnum.valueOf(pricing.getCycleType())
            : Objects.nonNull(pricingMultiPlan) ? CycleTypeEnum.valueOf(pricingMultiPlan.getCircleType()) : null;

        Integer numberOfTrail = Objects.nonNull(pricing.getNumberOfTrial()) ? pricing.getNumberOfTrial()
            : Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getNumberOfTrial() : null;

        Integer numberOfCycles = Objects.nonNull(pricing.getNumberOfCycles()) ? pricing.getNumberOfCycles()
            : Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getNumberOfCycle() : null;

        CycleTypeEnum trailType = Objects.nonNull(pricing.getTrialType()) ? CycleTypeEnum.valueOf(pricing.getTrialType())
            : Objects.nonNull(pricingMultiPlan) && Objects.nonNull(pricingMultiPlan.getTrialType()) ? CycleTypeEnum
                .valueOf(pricingMultiPlan.getTrialType().intValue()) : null;

        result.setDhsxkdCusCode(subscription.getSubCodeDHSXKD());
        result.setServiceType(subscription.getService().getServiceCode());
        result.setSubscriptionCode(subscription.getSubCode());
        result
            .setSubscriptionStatus(Objects.nonNull(subscription.getStatus()) ? SubscriptionStatusEnum.valueOf(subscription.getStatus()) : null);
        result.setSmeName(Objects.nonNull(smeOfSubscription) ? smeOfSubscription.getLastName().concat(CharacterConstant.SPACE)
            .concat(smeOfSubscription.getFirstName()) : null);
        result.setSmeId(subscription.getUserId());
        result.setEffectiveTime(subscription.getStartedAt());
        result.setExpiredDate(subscription.getExpiredTime());
        result.setUsedQuantity(subscription.getUsedQuantity());
        result.setPrice(pricing.getPrice());
        result.setPricingPlan(pricingPlan);
        result.setFreeQuantity(freeQuantity);
        result.setPaymentCycle(paymentCycle);
        result.setCycleType(cycleType);
        result.setNumberOfCycles(subscription.getNumberOfCycles());
        result.setNumberOfTrial(numberOfTrail);
        result.setTrialType(trailType);
        result.setCouponList(couponRepository
            .getAllSubscriptionCouponTotalBill(subscription.getId()));
        result.setAddonsList(new ArrayList<>(addonPricing));
        result.setPeriodRemain(numberOfCycles != -1 ? numberOfCycles
            - subscription
            .getCurrentCycle() : -1);
        result.setMultiPlanId(subscription.getPricingMultiPlanId());

        List<IntegrateSubsTaxDTO> taxDTOList = new ArrayList<>();
        res.getTaxList().forEach(i -> {
            IntegrateSubsTaxDTO taxDTO = new IntegrateSubsTaxDTO(i.getTaxId(),
                i.getTaxName(), i.getPercent());
            taxDTOList.add(taxDTO);
        });
        result.setId(id);
        result.setTaxList(taxDTOList);
        result.setAfterTax(calculate.getPricing().getIntoAmountAfterTax());
        result.setBeforeTax(calculate.getPricing().getIntoAmountPreTax());
        return result;
    }


    /**
     * Danh sach thue bao tao boi dev/admin
     *
     * @param searchText     searchText
     * @param status         status
     * @param portalTypeName portalTypeName
     * @param developerName  developerName
     * @param cusName        cusName
     * @param pageable       pageable
     * @return khi PortalType là DEV thì trả về page subscription của cty đó,khi PortalType là ADMIN thì trả về tất cả subscription.
     */
    @Override
    public Page<SubscriptionByDevOrAdminDTONew> findAllByPortalType(String searchText, CustomerTypeEnum customerType,
        SubscriptionStatusEnum status, String portalTypeName, String cusName, String developerName, String tin,
        String email, String phone, String representative, Integer businessAreaId, Integer businessSizeId,
        LocalDateTime createdFrom, LocalDateTime createdTo, String paymentCycleStr, Integer createdSource, LocalDate foundingFrom,
        LocalDate foundingTo, LocalDateTime nextPaymentDateFrom, LocalDateTime nextPaymentDateTo, LocalDateTime paymentDateFrom,
        LocalDateTime paymentDateTo, Long provinceId, Pageable pageable) {
        Integer subscriptionStatus =
            Objects.isNull(status) ? SubscriptionStatusEnum.NOT_SET.value : status.value;
        Long parentId = AuthUtil.getCurrentParentId();
        Sort realSort = pageable.getSort();
        if (pageable.getSort().stream().anyMatch(e -> SERVICE_NAME.equals(e.getProperty()))) {
            //sort theo serviceName thì sort thêm theo điều kiện pricingName
            val direction = pageable.getSort().stream().map(Order::getDirection)
                .collect(Collectors.toList()).get(0);
            val sorts = Arrays.asList(
                new Sort.Order(direction, SERVICE_NAME),
                new Sort.Order(direction, PRICING_NAME)
            );
            realSort = Sort.by(sorts);
        } else if (pageable.getSort().stream().anyMatch(e -> TYPE.equals(e.getProperty()))) {
            //sort theo type thì sort thêm theo điều kiện paymentCycle
            val direction = pageable.getSort().stream().map(Order::getDirection)
                .collect(Collectors.toList()).get(0);
            val sorts = Arrays.asList(
                new Sort.Order(direction, TYPE),
                new Sort.Order(direction, PAYMENT_CYCLE)
            );
            realSort = Sort.by(sorts);
        }
        pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), realSort);
        long createdBy = -1L;
        PortalType portalType = PortalType.getByName(portalTypeName);
        if (portalType != PortalType.ADMIN && portalType != PortalType.DEV) {
            // Khi  PortalType khác DEV và ADMIN
            String msg = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, new String[]{portalTypeName},
                    LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(msg, Resources.SUBSCRIPTION,
                ErrorKey.Subscription.PORTAL_TYPE,
                MessageKeyConstant.NOT_FOUND);
        }
        if ((PortalType.ADMIN.equals(portalType) && !AuthUtil.checkUserRolesByPortal(Collections.singletonList(PortalType.ADMIN))) ||
            (PortalType.DEV.equals(portalType) && !AuthUtil.checkUserRolesByPortal(Arrays.asList(PortalType.ADMIN, PortalType.DEV)))) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }

        if (portalType == PortalType.ADMIN) {
            // khi PortalType là ADMIN thì trả về tất cả subscription.
            if (Objects.nonNull(AuthUtil.getDepartment().getProvinceId())) {
                provinceId = AuthUtil.getDepartment().getProvinceId();
            }

            List<String> roles = userRepository.getRole(AuthUtil.getCurrentUserId());
            // nếu là AM thì chỉ hiện data AM tạo
            createdBy = !CollectionUtils.isEmpty(roles) && roles.contains(RoleType.AM.name()) ? AuthUtil.getCurrentUserId() : -1L;
        }

        String tinReplace = StringUtils.isNotEmpty(tin) ? tin.replace("-", "") : tin;
        PaymentCycleDTO paymentCycleDTO = FilterReqDefault.parsePaymentCycle(paymentCycleStr);

        return subscriptionRepository
            .findAllByPortalTypeDevAdmin(searchText.toLowerCase(), subscriptionStatus, customerType.getValue(), parentId,
                cusName.toLowerCase(), developerName.toLowerCase(), portalType.getType(), provinceId, createdBy,
                tinReplace, email, phone, representative, businessAreaId, businessSizeId, createdFrom, createdTo,
                CreditNoteConst.SqlVar.MIN_DATE_DEFAULT, CreditNoteConst.SqlVar.MAX_DATE_DEFAULT,
                CreditNoteConst.SqlVar.MIN_DATE_TIME_DEFAULT, CreditNoteConst.SqlVar.MAX_DATE_TIME_DEFAULT,
                paymentCycleDTO.getPaymentCycle(), paymentCycleDTO.getCycleType(),
                createdSource, foundingFrom, foundingTo, nextPaymentDateFrom, nextPaymentDateTo, paymentDateFrom,
                paymentDateTo, pageable);
    }

    @Override
    public Page<SubscriptionAddonResDTO> getSubscriptionAddon(Long developerId, Long serviceId, Long addonId, Long periodId,
        PortalType portalType, Long typeId, AddonPopUpTypeEnum popUpType, List<Long> addonIdsNot,
        Long paymentCyclePricing, String circleType, Long periodPlanId, Long companyId, CustomerTypeEnum customerType, Pageable pageable) {

        Integer circleTypePricing = TimeTypeEnum.valueOf(circleType).value;
        Set<String> roles = AuthUtil.getCurrentUser().getAuthorities()
            .stream().map(GrantedAuthority::getAuthority)
            .collect(Collectors.toSet());
        Page<SubscriptionAddonResDTO> addons = null;
        Page<SubscriptionAddonResITFDTO> addonsITF;
        List<SubscriptionAddonResDTO> addonResDTOList = new ArrayList<>();
        Long provinceId = STATUS_DEV_PROVINCE_ALL;
        Integer statusSearchAddonIdsNot =
            CollectionUtils.isEmpty(addonIdsNot) ? ComboConst.SqlVar.ARRAY_EMPTY_STATUS
                : ComboConst.SqlVar.ARRAY_NOT_EMPTY_STATUS;
        //neu khong phai la admin, super admin, dev, dev operator
        if (!roles.contains(RoleType.ADMIN.getValue())
            && !roles.contains(RoleType.FULL_ADMIN.getValue())
            && !roles.contains(RoleType.DEVELOPER.getValue())
            && !roles.contains(RoleType.DEVELOPER_OPERATOR.getValue())) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        Sort sort = pageable.getSort();
        if (!CollectionUtils.isEmpty(sort.get().filter(e -> e.getProperty().equals("bonusType"))
            .collect(Collectors.toList()))) {
            List<Sort.Order> pageSort = sort.get().collect(Collectors.toList());
            Direction bonusTypeDirection = pageSort.get(0).getDirection();
            pageSort.add(new Order(bonusTypeDirection, "type"));
            pageSort.add(new Order(bonusTypeDirection, "bonusValue"));
            sort = Sort.by(pageSort);
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
        }
        if (roles.contains(RoleType.DEVELOPER.getValue()) || roles
            .contains(RoleType.DEVELOPER_OPERATOR.getValue())) {
            //neu la dev nhung portal khong phai dev
            if (PortalType.DEV.getType() != portalType.getType()) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
            addonsITF =
                AddonPopUpTypeEnum.PRICING.equals(popUpType) ?
                    subscriptionRepository.getSubscriptionAddonsPricing(
                        developerId, serviceId, addonId, paymentCyclePricing, circleTypePricing,
                        AuthUtil.getCurrentParentId(), typeId, statusSearchAddonIdsNot, addonIdsNot,
                        provinceId, periodId, periodPlanId, pageable)
                    : subscriptionRepository
                        .getSubscriptionAddonsCombo(developerId, serviceId, addonId, paymentCyclePricing,
                            circleTypePricing,
                            AuthUtil.getCurrentParentId(), typeId, statusSearchAddonIdsNot,
                            addonIdsNot, provinceId, periodId, customerType.getValue(), pageable);
        } else {
            if (AuthUtil.checkUserRoles(Arrays
                .asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(),
                    RoleType.CUSTOMER_SUPPORT.getValue()))) {
                UserDepartmentDTO actorDepartment = AuthUtil.getDepartment();
                provinceId = Objects.nonNull(actorDepartment.getProvinceId()) ? actorDepartment
                    .getProvinceId() : STATUS_ADMIN_PROVINCE_ALL;
            }
            addonsITF =
                AddonPopUpTypeEnum.PRICING.equals(popUpType) ?
                    subscriptionRepository
                        .getSubscriptionAddonsPricing(developerId, serviceId, addonId, paymentCyclePricing,
                            circleTypePricing,
                            SubscriptionConstant.ADMIN, typeId, statusSearchAddonIdsNot,
                            addonIdsNot, provinceId, periodId, periodPlanId, pageable)
                    : subscriptionRepository
                        .getSubscriptionAddonsCombo(developerId, serviceId, addonId, paymentCyclePricing,
                            circleTypePricing,
                            SubscriptionConstant.ADMIN, typeId, statusSearchAddonIdsNot,
                            addonIdsNot, provinceId, periodId, customerType.getValue(), pageable);
        }
        addons = convertItfToDTO(addonsITF, addonResDTOList);
        addons.forEach(this::getUnitLimitedList);

        // lay thue cua addon
        if (!CollectionUtils.isEmpty(addons.getContent())) {
            List<PricingTaxRes> addonTax = addonsTaxRepository
                .getAddonTaxSubscription(
                    addons.getContent().stream().map(SubscriptionAddonResDTO::getId)
                        .collect(Collectors.toSet()));
            addons.getContent().forEach(a -> {
                List<PricingTaxRes> taxes = addonTax.stream()
                    .filter(ad -> ad.getAddonId().equals(a.getId())).collect(Collectors.toList());
                a.setTaxList(taxes);
                a.setHasTax(getHasTaxAddon(taxes));
                a.setPrice(subscriptionFormula.priceBeforeTax(a.getPrice(), taxes));
                if (!CollectionUtils.isEmpty(a.getUnitLimitedList())) {
                    a.getUnitLimitedList().forEach(unit -> unit.setPrice(subscriptionFormula.priceBeforeTax(unit.getPrice(), taxes)));
                }
            });
        }
        // lay thue cua phi thiet lap
        if (!CollectionUtils.isEmpty(addons.getContent())) {
            List<PricingTaxRes> taxSetupFee = addonsTaxRepository
                .getAddonTaxSubscriptionSetupFee(addons.getContent().stream().map(SubscriptionAddonResDTO::getId).collect(
                    Collectors.toSet()));
            addons.getContent().forEach(a -> {
                List<PricingTaxRes> pricingTaxRes = taxSetupFee.stream()
                    .filter(ad -> Objects.equals(ad.getAddonId(), a.getId())).collect(Collectors.toList());
                a.setTaxListSetupFee(pricingTaxRes);
                a.setSetupFee(subscriptionFormula.priceBeforeTax(a.getSetupFee(), pricingTaxRes));

                //check addon có khuyến mại không
                String classify = CouponConst.COUPON_OF_ADDON;
                Page<CouponPopupDTO> couponsPopup = couponService.getCouponsPopup(PricingConst.DEFAULT_ID,
                    Objects.nonNull(a.getPeriodId()) ? a.getPeriodId() : -1L, Objects.nonNull(companyId) ? companyId : -1L, "ALL", a.getId(), 0,
                    10, "promotionType,ASC", classify, Collections.singletonList(0L), portalType, 0L, 0L);
                a.setHasCouponList(!CollectionUtils.isEmpty(couponsPopup.getContent()) ? YesNoEnum.YES : YesNoEnum.NO);
            });
        }

        return addons;
    }

    /**
     * convert ITF to DTO
     */
    private Page<SubscriptionAddonResDTO> convertItfToDTO(Page<SubscriptionAddonResITFDTO> addonsITF,
        List<SubscriptionAddonResDTO> addonResDTOList) {
        for (SubscriptionAddonResITFDTO a : addonsITF) {
            SubscriptionAddonResDTO addon = new SubscriptionAddonResDTO();
            addon.setName(a.getName());
            addon.setPrice(a.getPrice());
            addon.setIsRequire(a.getIsRequire());
            addon.setId(a.getId());
            addon.setBonusType(a.getBonusType());
            addon.setBonusValue(a.getBonusValue());
            addon.setCompanyName(a.getCompanyName());
            addon.setCurrencyType(a.getCurrencyType());
            addon.setFreeQuantity(a.getFreeQuantity());
            addon.setHasChangePrice(Objects.nonNull(a.getHasChangePrice()) ? YesNoEnum.valueOf(a.getHasChangePrice()) : null);
            addon.setHasChangeQuantity(
                Objects.nonNull(a.getHasChangeQuantity()) ? AddonChangeQuantityEnum.getValueOf(a.getHasChangeQuantity()) : null);
            addon.setHasTax(a.getHasTax());
            addon.setObjectName(a.getObjectName());
            addon.setPeriodId(Objects.equals(a.getPeriodId(), DEFAULT_ID) ? null : a.getPeriodId());
            addon.setPricingPlan(a.getPricingPlan());
            addon.setType(a.getType());
            addon.setSetupFee(a.getSetupFee());
            addon.setPaymentCycle(a.getPaymentCycle());
            addon.setPrice(a.getPrice());
            addon.setMinimumQuantity(Objects.nonNull(a.getMinimumQuantity()) ? a.getMinimumQuantity() : null);
            addon.setMaximumQuantity(Objects.nonNull(a.getMaximumQuantity()) ? a.getMaximumQuantity() : null);
            addonResDTOList.add(addon);
        }
        return new PageImpl<>(addonResDTOList, addonsITF.getPageable(), addonsITF.getTotalElements());
    }

    /**
     * unitLimitedList
     */
    private SubscriptionAddonResDTO getUnitLimitedList(SubscriptionAddonResDTO addon) {
        addon.setUnitLimitedList(Objects.isNull(addon.getPeriodId()) ? subscriptionRepository.getUnitLimitedList(addon.getId())
            : subscriptionRepository.getPricingPlanDetail(addon.getPeriodId()));
        return addon;
    }

    /**
     * Kiem tra addon co thue hay khong
     */
    private String getHasTaxAddon(List<PricingTaxRes> taxs) {
        for (PricingTaxRes tax : taxs) {
            if (tax.getHasTax().equals(AddonsConstant.NO_TAX)) {
                return AddonsConstant.NO;
            }
        }
        return AddonsConstant.YES;
    }

    @Override
    public Page<SubscriptionPricingPopupResDTO> getSubscriptionPricing(Long serviceId, Integer numberOfCycle, TimeTypeEnum type,
        String serviceName,
        String pricingName, Long pricingIdRemove, PortalType portalType, Long periodId, Long periodIdChange, YesNoEnum isOsService,
        Long userId, CustomerTypeEnum customerType, Integer registerEcontract, String apiType, Integer page, Integer size, String sort) {
        List<String> sortList = new ArrayList<>(Arrays.asList(sort.split(",")));
        ListRequest listRequest = new ListRequest(size, page, sort);
        Pageable pageable = listRequest.getPageable();
        if (Objects.equals(sortList.get(0), "sortType")) {
            List<Sort.Order> orders = new ArrayList<>();
            Sort.Order orderByType = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
                "sortType");//HiepNT order by theo number type
            orders.add(orderByType);
            Sort.Order orderByPaymentCycle = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
                "numberOfCycles");
            orders.add(orderByPaymentCycle);
            Sort sortObj = Sort.by(orders);
            size = size < 1 ? 1 : size;
            page = page < 0 ? 0 : page;
            pageable = PageRequest.of(page, size, sortObj);
        }
        if (periodId != -1) {
            pricingIdRemove = -1L;
        }
        List<Integer> owners =
            YesNoEnum.NO.equals(isOsService) || YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(0, 1) : Arrays.asList(3, 2, 0, 1);
        List<Long> periodIds = new ArrayList<>();
        periodIds.add(periodId);
        periodIds.add(periodIdChange);
        Long ownerId = Objects.equals(portalType, PortalType.DEV) ? AuthUtil.getCurrentParentId() : userId;

        Page<SubscriptionPricingPopupResDTO> subscriptionPricingPopupResDTOS = subscriptionRepository.getSubscriptionPricing(serviceId,
            serviceName, pricingName, pricingIdRemove, ownerId,
            periodIds, owners, numberOfCycle, type.value, customerType.getValue(), registerEcontract, apiType, pageable);

        // hiển thị pmp được sử dụng khi đổi gói subs
        if (periodIdChange != -1) {
            PricingSetupInfo pricingSetupInfo = getPricingSetupInfo(serviceId);
            PricingMultiPlan pricingMultiPlanSub = pricingMultiPlanRepository.findByIdAndDeletedFlag(periodIdChange, flagNotDeleted)
                .orElse(null);
            Boolean checkPricingConfigList = pricingRepository.checkPricingConfigList(serviceId);

            // lọc pricing
            List<Long> pricingIdsSelected = getPricingIdsSelected(pricingSetupInfo, pricingMultiPlanSub, serviceId, checkPricingConfigList);

            // lọc PricingMultiPlan
            List<Long> pricingMultiPlanIdsSelected = getPricingMultiPlanIdsSelected(pricingSetupInfo, pricingMultiPlanSub, pricingIdsSelected,
                checkPricingConfigList);

            // result TH đã xử lý
            List<SubscriptionPricingPopupResDTO> subscriptionPricingPopupResDTOList = subscriptionPricingPopupResDTOS.getContent();
            List<SubscriptionPricingPopupResDTO> res = new ArrayList<>();
            if (!CollectionUtils.isEmpty(subscriptionPricingPopupResDTOList)) {
                subscriptionPricingPopupResDTOList.forEach(s -> {
                    if (pricingMultiPlanIdsSelected.contains(s.getPeriodId()) && pricingIdsSelected.contains(s.getId())) {
                        res.add(s);
                    }
                });
            }
            Page<SubscriptionPricingPopupResDTO> result = new PageImpl<>(res, pageable, res.size());
            return result;
        }
        return subscriptionPricingPopupResDTOS;
    }

    private PricingSetupInfo getPricingSetupInfo(Long serviceId) {
        PricingSetupInfo pricingSetupInfo = pricingSetupInfoRepository.findFirstByServiceIdAndType(serviceId, 1);
        if (pricingSetupInfo == null) {
            pricingSetupInfo = new PricingSetupInfo();
            pricingSetupInfo.setTypeConfig(0);
            pricingSetupInfo.setAllowPackUpgrade(1);
            pricingSetupInfo.setAllowPackDowngrade(0);
            pricingSetupInfo.setAllowDiffPlans(1);
            pricingSetupInfo.setAllowDiffPlansInOther(1);
        }
        return pricingSetupInfo;
    }

    private List<Long> getPricingIdsSelected(PricingSetupInfo pricingSetupInfo, PricingMultiPlan pricingMultiPlanSub, Long serviceId,
        Boolean checkPricingConfigList) {
        List<Pricing> pricingList = pricingRepository.getPricingConfigList(serviceId);
        Pricing pricingSub = pricingRepository.findByIdAndDeletedFlag(pricingMultiPlanSub.getPricingId(),
            DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        List<Long> pricingIdsSelected = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pricingList) && pricingSub != null) {
            List<Long> pricingIds = new ArrayList<>();
            pricingList.forEach(e -> pricingIds.add(e.getId()));
            if (!pricingIds.contains(pricingMultiPlanSub.getPricingId())) {
                pricingIdsSelected.addAll(pricingIds);
            } else {
                pricingList.forEach(pr -> {
                    if (pricingSub.getId().equals(pr.getId()) && pricingSetupInfo.getAllowDiffPlans() == 1) {
                        pricingIdsSelected.add(pr.getId());
                    } else if (// 1: thủ công => lấy tất cả gói
                        pricingSetupInfo.getTypeConfig() == 1 ||
                            // 2: mặc định + cho phép nâng cấp gói
                            (pricingSetupInfo.getTypeConfig() == 0 && pricingSetupInfo.getAllowPackUpgrade() == 1
                                && (checkPricingConfigList ? pr.getPriority() > pricingSub.getPriority()
                                : pr.getCreatedAt().compareTo(pricingSub.getCreatedAt()) < 0)) ||
                            // 3: mặc định + cho phép hạ cấp gói
                            (pricingSetupInfo.getTypeConfig() == 0 && pricingSetupInfo.getAllowPackDowngrade() == 1 && (
                                checkPricingConfigList ? pr.getPriority() < pricingSub.getPriority()
                                    : pr.getCreatedAt().compareTo(pricingSub.getCreatedAt()) > 0))
                    ) {
                        pricingIdsSelected.add(pr.getId());
                    }
                });
            }
        }
        return pricingIdsSelected;
    }

    private List<Long> getPricingMultiPlanIdsSelected(PricingSetupInfo pricingSetupInfo, PricingMultiPlan pricingMultiPlanSub,
        List<Long> pricingIdsSelected, Boolean checkPricingConfigList) {
        List<PricingMultiPlan> muls = pricingMultiPlanRepository.findAllByPricingIdIn(pricingIdsSelected);
        List<Long> pricingMultiPlanIdsSelected = new ArrayList<>();
        boolean isPricingMultiPlanSubPrice = Objects.nonNull(pricingMultiPlanSub.getPrice());
        List<PricingPlanDetail> pricingPlanDetails = CollectionUtils.isEmpty(pricingMultiPlanSub.getPricingPlanDetails()) ?
            new ArrayList<>() :
            pricingMultiPlanSub.getPricingPlanDetails().stream().sorted(Comparator.comparing(PricingPlanDetail::getUnitFrom).reversed())
                .map(o -> {
                    PricingPlanDetail pricingPlanDetail = new PricingPlanDetail();
                    BeanUtils.copyProperties(o, pricingPlanDetail);
                    pricingPlanDetail.setPricingMultiPlanId(null);
                    pricingPlanDetail.setId(null);
                    pricingPlanDetail.setPricingMultiPlan(null);
                    return pricingPlanDetail;
                }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(muls)) {
            muls.forEach(mul -> {
                boolean isMulPrice = Objects.nonNull(mul.getPrice());
                mul.getPricingPlanDetails().stream().forEach(m -> m.setUnitFrom(Objects.isNull(m.getUnitFrom()) ? -1 : m.getUnitFrom()));
                List<PricingPlanDetail> mulPricingPlanDetails = CollectionUtils.isEmpty(mul.getPricingPlanDetails()) ?
                    new ArrayList<>() :
                    mul.getPricingPlanDetails().stream().sorted(Comparator.comparing(PricingPlanDetail::getUnitFrom).reversed()).map(o -> {
                        PricingPlanDetail pricingPlanDetail = new PricingPlanDetail();
                        BeanUtils.copyProperties(o, pricingPlanDetail);
                        pricingPlanDetail.setPricingMultiPlanId(null);
                        pricingPlanDetail.setId(null);
                        pricingPlanDetail.setPricingMultiPlan(null);
                        return pricingPlanDetail;
                    }).collect(Collectors.toList());
                // TH khi đổi gói => gói đăng ký trên sme là chu kỳ cũ => 1. khác gói => add tất cả mul, 2.cùng gói => add mul khác pricingPlan hoặc price
                if (!pricingIdsSelected.contains(pricingMultiPlanSub.getPricingId()) &&
                    // check sub ko phải gói dang bán
                    // khác pricing
                    (!mul.getPricingDraftId().equals(pricingMultiPlanSub.getPricingDraftId()) ||
//                               // cùng pricing
                        (mul.getPricingDraftId().equals(pricingMultiPlanSub.getPricingDraftId()) &&
                            // cùng pricing + khác pricingPlan
                            (!mul.getPricingPlan().equals(pricingMultiPlanSub.getPricingPlan())
                                // cùng pricing + cùng pricingPlan
                                || (mul.getPricingPlan().equals(pricingMultiPlanSub.getPricingPlan()) &&
                                // cùng pricing + cùng pricingPlan + khác price (case này chủ yếu check null price)
                                ((isMulPrice && isPricingMultiPlanSubPrice && mul.getPrice().compareTo(pricingMultiPlanSub.getPrice()) != 0)
                                    || (!isMulPrice && isPricingMultiPlanSubPrice)
                                    || (isMulPrice && !isPricingMultiPlanSubPrice)
                                    // cùng pricing + cùng pricingPlan + khác getPricingPlanDetails (ko ss price vì pricingPlan khác không có price)
                                    || (!isMulPrice && !isPricingMultiPlanSubPrice &&
                                    !Objects.equals(StringUtils.join(mulPricingPlanDetails, "|"), StringUtils.join(pricingPlanDetails, "|")))
                                )))))) {
                    pricingMultiPlanIdsSelected.add(mul.getId());
                }
                // TH khi đổi gói => gói đăng ký trên sme không phải là chu kỳ cũ
                if (pricingIdsSelected.contains(pricingMultiPlanSub.getPricingId()) && !mul.getId().equals(pricingMultiPlanSub.getId()) && (
                    // 1: mặc định + khác gói cước
                    (pricingSetupInfo.getTypeConfig() == 0 && !mul.getPricingId().equals(pricingMultiPlanSub.getPricingId()) && (
                        // 1.1: + cho phép Khác chu kỳ
                        pricingSetupInfo.getAllowDiffPlansInOther() == 1 ||
                            // 1.2: + không cho phép Khác chu kỳ
                            (pricingSetupInfo.getAllowDiffPlansInOther() == 0 &&
                                Objects.equals(mul.getPaymentCycle(), pricingMultiPlanSub.getPaymentCycle())
                                && Objects.equals(mul.getCircleType(), pricingMultiPlanSub.getCircleType())))) ||
                        // 2: mặc định + trong cùng một gói cước + cho phép cung chu kỳ
                        (pricingSetupInfo.getTypeConfig() == 0 && pricingSetupInfo.getAllowDiffPlans() == 1 &&
                            mul.getPricingId().equals(pricingMultiPlanSub.getPricingId()) && (
                            // 2.1: + nâng cấp + hạ cấp
                            (pricingSetupInfo.getAllowPackUpgrade() == 1 && pricingSetupInfo.getAllowPackDowngrade() == 1) ||
                                // 2.2: + nâng cấp + không hạ cấp
                                (pricingSetupInfo.getAllowPackUpgrade() == 1 && pricingSetupInfo.getAllowPackDowngrade() == 0
                                    && (checkPricingConfigList ? mul.getPriority() > pricingMultiPlanSub.getPriority()
                                    : mul.getCreatedAt().compareTo(pricingMultiPlanSub.getCreatedAt()) < 0)) ||
                                // 2.3: + không tăng cấp + hạ cấp
                                (pricingSetupInfo.getAllowPackUpgrade() == 0 && pricingSetupInfo.getAllowPackDowngrade() == 1
                                    && (checkPricingConfigList ? mul.getPriority() < pricingMultiPlanSub.getPriority()
                                    : mul.getCreatedAt().compareTo(pricingMultiPlanSub.getCreatedAt()) > 0)))) ||
                        // 3: thủ công => lấy những pmp được chọn
                        (pricingSetupInfo.getTypeConfig() == 1 && !CollectionUtils.isEmpty(pricingMultiPlanSub.getConvertedIds()) &&
                            pricingMultiPlanSub.getConvertedIds().stream().filter(Objects::nonNull).map(Long::parseLong)
                                .collect(Collectors.toSet()).contains(mul.getId()))
                )) {
                    pricingMultiPlanIdsSelected.add(mul.getId());
                }
            });
        }
        return pricingMultiPlanIdsSelected;
    }

    @Override
    public Page<SubscriptionComboPlanPopupResDTO> getSubscriptionComboPlan(
            String comboName, String comboPlaname, String developerName, PortalType portalType, ComboTypeEnum comboType,
            Long comboPlanId, YesNoEnum isOsService, CycleTypeEnum type, Integer paymentCycle, Long removeId,
            CustomerTypeEnum customerType, Integer size, Integer page, String sortBy) {
        Set<String> roles = AuthUtil.getCurrentUser().getAuthorities()
            .stream().map(GrantedAuthority::getAuthority)
            .collect(Collectors.toSet());
        Long comboId = -1L;
        if (!comboPlanId.equals(-1L)) {
            comboId = validateComboPlan(comboPlanId).getComboId();
        }
        //neu khong phai la admin nhung portal la admin -> Khong duoc phep
        //HiepNT sort cho TH sort theo chu kỳ thanh toán
        List<String> sortList = new ArrayList<>(Arrays.asList(sortBy.split(",")));
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        Pageable pageable = listRequest.getPageable();
        List<Sort.Order> orders = new ArrayList<>();
        Sort.Order orderByPaymentCycle = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
            sortList.get(0));
        if (Objects.equals(sortList.get(0), "paymentCycle")) {
            Sort.Order orderByType = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
                "cp.cycle_type");
            orders.add(orderByType);
            orderByPaymentCycle = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
                "paymentCycle");
        } else if (Objects.equals(sortList.get(0), "comboName")) {
            Sort.Order orderByType = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
                "c.combo_name");
            orders.add(orderByType);
            orderByPaymentCycle = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
                "cp.combo_name");
        }
        orders.add(orderByPaymentCycle);
        Sort sortObj = Sort.by(orders);
        size = size < 1 ? 1 : size;
        page = page < 0 ? 0 : page;
        pageable = PageRequest.of(page, size, sortObj);
        long isDev = -1L;
        int osService = YesNoEnum.UNSET.equals(isOsService) || YesNoEnum.YES.equals(isOsService) ? -1 : 0;
        Page<SubscriptionComboPlanPopupResDTO> result;

        if (PortalType.ADMIN.equals(portalType)) {
            isDev = 1L;
            if (!roles.contains(RoleType.ADMIN.getValue())
                && !roles.contains(RoleType.FULL_ADMIN.getValue())
                && !roles.contains(RoleType.CUSTOMER_SUPPORT.getValue())) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
            //lay provinceId
            Long provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
            //neu provinceId not null -> la admin tinh
            result = subscriptionRepository.getSubscriptionComboPlan(comboName, developerName, comboPlaname,
                SubscriptionConstant.ADMIN, Objects.nonNull(provinceId) ? provinceId : -1L, comboType.value, isDev,
                comboPlanId, comboId, osService, type.value, paymentCycle, removeId, customerType.getValue(), pageable);
        } else if (PortalType.DEV.equals(portalType)) {
            result = subscriptionRepository.getSubscriptionComboPlan(comboName, SubscriptionConstant.COMPANY_NAME_NULL,
                comboPlaname, AuthUtil.getCurrentParentId(), -1L, comboType.value, isDev,
                comboPlanId, comboId, osService, type.value, paymentCycle, removeId, customerType.getValue(), pageable);
        } else {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }

        if (!comboPlanId.equals(-1L)) {
            ComboPlan comboPlanSub = comboPlanRepository.findByIdAndDeletedFlag(comboPlanId, DeletedFlag.NOT_YET_DELETED.getValue())
                .orElse(null);
            PricingSetupInfo pricingSetupInfo = pricingSetupInfoRepository.findFirstByServiceIdAndType(comboPlanSub.getComboDraftId(), 0);
            if (pricingSetupInfo == null) {
                pricingSetupInfo = new PricingSetupInfo();
                pricingSetupInfo.setTypeConfig(0);
                pricingSetupInfo.setAllowPackUpgrade(1);
                pricingSetupInfo.setAllowPackDowngrade(0);
            }
            List<ComboPlan> lstCp = comboPlanRepository.getAllByComboDraftId(comboPlanSub.getComboDraftId());
            Set<Long> exchangedIds = pricingService.getPlanSelected(pricingSetupInfo, lstCp, comboPlanSub);

            List<SubscriptionComboPlanPopupResDTO> subscriptionPricingPopupResDTOList = result.getContent();
            List<SubscriptionComboPlanPopupResDTO> resExchanged = new ArrayList<>();
            if (!CollectionUtils.isEmpty(subscriptionPricingPopupResDTOList)) {
                subscriptionPricingPopupResDTOList.forEach(s -> {
                    if (exchangedIds.contains(s.getId())) {
                        resExchanged.add(s);
                    }
                });
            }
            Page<SubscriptionComboPlanPopupResDTO> res = new PageImpl<>(resExchanged, pageable, resExchanged.size());
            return res;
        }
        return result;
    }

    /**
     * Kiem tra combo hop le
     */
    private ComboPlan validateComboPlan(Long id) {
        return comboPlanService.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
    }

    @Override
    public SubscriptionPricingChosenResDTO choosePricingSub(Long id, Long subscriptionId, Long variantId, PortalType portalType,
        Long pricingPeriodId, Long companyId, CustomerTypeEnum customerType) {
        Pricing currentPricing = getCurrentPricing(id);
        // Check thông tin period của pricing có tồn tại
        boolean isOldPricing = true;
        PricingMultiPlan pricingMultiPlan = subMultiplePeriod
            .validatePricingMultiPlanByPricingId(id, pricingPeriodId);
        List<UnitLimited> unitLimiteds = new ArrayList<>();
        if (Objects.nonNull(pricingMultiPlan)) {
            PricingMultiplePeriodTransDTO transDTO = subMultiplePeriod
                .getPricingAndUnitLimitedByMultiPlanId(currentPricing, pricingMultiPlan.getId());
            currentPricing = transDTO.getPricing();
            unitLimiteds = transDTO.getUnitLimitedList();
            isOldPricing = false;
        }
        validateCustomerTypeCode(currentPricing, pricingMultiPlan,
            Objects.nonNull(customerType) ? customerType.getValue() : CustomerTypeEnum.ENTERPRISE.getValue());

        SubscriptionPricingChosenResDTO subscriptionPricingChosenResDTO = pricingSubscriptionDevMapper.toDto(currentPricing);
        if (Objects.nonNull(pricingMultiPlan)) {
            subscriptionPricingChosenResDTO.setMinimumQuantity(
                Objects.nonNull(pricingMultiPlan.getMinimumQuantity()) ? pricingMultiPlan.getMinimumQuantity() : null);
            subscriptionPricingChosenResDTO.setMaximumQuantity(
                Objects.nonNull(pricingMultiPlan.getMaximumQuantity()) ? pricingMultiPlan.getMaximumQuantity() : null);
        }
        //lay cau hinh ctkm tu system param
        String systemParamCoupon = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
        subscriptionPricingChosenResDTO.setSystemParam((Objects.isNull(systemParamCoupon) || Strings.EMPTY.equals(systemParamCoupon))
            ? null : SystemParamEnum.valueOf(Integer.valueOf(systemParamCoupon)));

        //Lấy thông tin tiền tệ
        if (Objects.nonNull(subscriptionPricingChosenResDTO.getCurrencyId())) {
            currencyRepository.findById(subscriptionPricingChosenResDTO.getCurrencyId())
                .ifPresent(currency -> subscriptionPricingChosenResDTO.setCurrencyName(currency.getCurrencyType()));
        }

        //Lấy thông tin đơn vị
        if (Objects.nonNull(subscriptionPricingChosenResDTO.getUnitId())) {
            unitRepository.findById(subscriptionPricingChosenResDTO.getUnitId())
                .ifPresent(unit -> subscriptionPricingChosenResDTO.setUnitName(unit.getName()));
        }

        if (Objects.nonNull(currentPricing.getServiceId())) {
            serviceRepository.findByIdAndDeletedFlag(currentPricing.getServiceId(), flagNotDeleted)
                .ifPresent(serviceEntity -> {
                    subscriptionPricingChosenResDTO.setServiceName(serviceEntity.getServiceName());
                    subscriptionPricingChosenResDTO.setServiceOwner(serviceEntity.getOnOsTypeEnum() == OnOsTypeEnum.ON ? ON : OS);
                    String categoryName = serviceRepository.findCategoryNameByServiceId(serviceEntity.getId());
                    subscriptionPricingChosenResDTO.setCategoryName(categoryName);
                    subscriptionPricingChosenResDTO.setPaymentMethod(PaymentMethodEnum.valueOf(serviceEntity.getPaymentMethod()));
                });
        }
        //Lưu thông tin thuế cho subscription
        List<SubscriptionPricingChosenResDTO.Tax> taxList = new ArrayList<>();
        int hasTax = 0;
        List<PricingTaxRes> pricingTax = pricingTaxRepository.getPricingTax(id);
        for (PricingTaxRes pricing : pricingTax) {
            hasTax = Objects.isNull(pricing.getHasTax()) ? 0 : pricing.getHasTax();
            taxList.add(new SubscriptionPricingChosenResDTO.Tax(pricing.getTaxId(), pricing.getTaxName(), pricing.getPercent()));
        }

        // Lấy thông tin thuế của phí thiết lập
        SubscriptionPricingChosenResDTO.SetupFeeTax setupFeeTax = null;
        List<PricingTaxRes> feeTaxes = pricingSetupFeeTaxRepository.getPricingSetupFeeTax(id);
        if (!CollectionUtils.isEmpty(feeTaxes)) {
            List<SubscriptionPricingChosenResDTO.Tax> feeTaxList = new ArrayList<>();
            int hasTaxFee = 0;
            for (PricingTaxRes feeTax : feeTaxes) {
                hasTaxFee = Objects.isNull(feeTax.getHasTax()) ? 0 : feeTax.getHasTax();
                feeTaxList.add(new SubscriptionPricingChosenResDTO.Tax(feeTax.getTaxId(), feeTax.getTaxName(), feeTax.getPercent()));
            }
            setupFeeTax = new SetupFeeTax(YesNoEnum.valueOf(hasTaxFee), feeTaxList);
        }

        if (!CollectionUtils.isEmpty(unitLimiteds)) {
            List<CalUnitLimitedCustomDTO> limiteds = new ArrayList<>();
            unitLimiteds.forEach(limit -> limiteds.add(new CalUnitLimitedCustomDTO(
                limit.getUnitFrom(),
                limit.getUnitTo(),
                subscriptionFormula.priceBeforeTax(limit.getPrice(), pricingTax)
            )));
            subscriptionPricingChosenResDTO.setUnitLimitedList(limiteds);
        } else if (BooleanUtils.isTrue(isOldPricing) && Objects.nonNull(currentPricing.getPricingPlan())) {
            // Thực hiện lấy thông tin của level cho gói
            List<UnitLimited> unitLimitedList = unitLimitedRepository.findByPricingIdAndQuantity(id);

            if (Objects.nonNull(unitLimitedList)) {
                List<CalUnitLimitedCustomDTO> limiteds =
                    unitLimitedList.stream().map(x -> new CalUnitLimitedCustomDTO(x.getUnitFrom(), x.getUnitTo(),
                            subscriptionFormula.priceBeforeTax(x.getPrice(), pricingTax)))
                        .collect(Collectors.toList());
                subscriptionPricingChosenResDTO.setUnitLimitedList(limiteds);
            }
        }

        subscriptionPricingChosenResDTO.setPrice(subscriptionFormula.priceBeforeTax(currentPricing.getPrice(), pricingTax));
        subscriptionPricingChosenResDTO.setTaxList(taxList);
        subscriptionPricingChosenResDTO.setHasTax(YesNoEnum.valueOf(hasTax));
        subscriptionPricingChosenResDTO.setSetupFee(subscriptionFormula.priceBeforeTax(currentPricing.getSetupFee(), feeTaxes));
        subscriptionPricingChosenResDTO.setSetupFeeTax(setupFeeTax);

        //Check tổng hóa đơn có coupon không
        String classify = CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING;
        Page<CouponPopupDTO> couponsPopup = couponService
            .getCouponsPopupForTotalBill(companyId, 0, 10, "promotionType,ASC", classify, Collections.singletonList(0L),
                portalType, 0L, 0L, customerType);
        subscriptionPricingChosenResDTO.setHasCouponList(!CollectionUtils.isEmpty(couponsPopup.getContent()) ? YesNoEnum.YES : YesNoEnum.NO);
        //Check gói có coupon không
        String classifyPricing = CouponConst.COUPON_OF_PRICING;
        Page<CouponPopupDTO> couponsPopupPricing = couponService
            .getCouponsPopup(Objects.nonNull(variantId) ? variantId : -1L, Objects.nonNull(pricingPeriodId) ? pricingPeriodId : -1L, companyId,
                "ALL", id, 0, 10, "promotionType,ASC", classifyPricing, Collections.singletonList(0L), portalType, 0L, 0L);
        subscriptionPricingChosenResDTO
            .setHasCouponListPricing(!CollectionUtils.isEmpty(couponsPopupPricing.getContent()) ? YesNoEnum.YES : YesNoEnum.NO);

        //neu co subscriptionId va subscriptionId khong phai -1 -> doi goi
        if (Objects.nonNull(subscriptionId) && !SubscriptionConstant.HAS_OLD_SUBSCRIPTION.equals(subscriptionId)) {
            Subscription subscription = validateSubscription(subscriptionId, portalType);
            Optional<User> user = userRepository.findByIdAndDeletedFlag(subscription.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
            if (user.isPresent()) {
                subscriptionPricingChosenResDTO.setSmeId(subscription.getUserId());
                subscriptionPricingChosenResDTO.setSmeName(user.get().getName());
            }
        }

        List<SubscriptionPricingChosenResDTO.Addon> addonList = getAddonPricing(id, pricingPeriodId, subscriptionId, portalType);

        subscriptionPricingChosenResDTO.setAddonsList(addonList.stream().distinct().collect(Collectors.toList()));

        // Lấy thông tin urlPreOrder
        Set<ComboDetailDTO> urlPreOrder = subscriptionRepository.getPricingAndUrlPreOrder(id)
            .stream().filter(x -> Objects.nonNull(x.getUrl())).collect(Collectors.toSet());
        subscriptionPricingChosenResDTO.setUrlPreOrder(urlPreOrder);
        subscriptionPricingChosenResDTO.setIsOneTime(currentPricing.getIsOneTime());
        return subscriptionPricingChosenResDTO;
    }

    /**
     * Kiểm tra pricing có tồn tại
     */
    private Pricing getCurrentPricing(Long id) {
        Pricing pricingDB = pricingRepository.findByIdAndDeletedFlagAndApproveAndStatus(id, DeletedFlag.NOT_YET_DELETED.getValue(),
                ApproveStatusEnum.APPROVED.value, StatusEnum.ACTIVE.value)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID, String.valueOf(id)));
        Pricing pricing = new Pricing();
        BeanUtils.copyProperties(pricingDB, pricing);
        return pricing;
    }

    /**
     * Lay danh sach CTKM theo product cua subscription
     */
    private List<SubscriptionCouponTotalBillDTO> getSubscriptionCouponProduct(Long id) {
        List<SubscriptionCouponTotalBillDTO> result = new ArrayList<>();
        subscriptionRepository.getCouponBySubscription(id, PromotionTypeEnum.PRODUCT.value).forEach(c -> {
            SubscriptionCouponTotalBillDTO coupon = new SubscriptionCouponTotalBillDTO();
            coupon.setId(c.getId());
            coupon.setName(c.getName());
            coupon.setMinimum(c.getMinimum());
            coupon.setMinimumAmount(c.getMinimumAmount());
            coupon.setMaximum(c.getMaximumPromotion());
            coupon.setTimesUsedType(c.getTimesUsedType() == null ? null : TimeUsedTypeEnum.valueOf(c.getTimesUsedType()));
            coupon.setLimitedQuantity(c.getLimitedQuantity());
            coupon.setType(c.getType() == null ? null : TimeTypeEnum.valueOf(c.getType()));
            coupon.setCode(c.getCode());
            coupon.setConditions(couponService.getCouponConditions(c, CouponConst.COUPON_OF_PRICING));
            result.add(coupon);
        });
        return result;
    }

    /**
     * Set param hàm caculate pricingPlantier
     */
    private SubscriptionFirstStepReqDTO setCaculateSubscriptionReq(Pricing pricing, Long pricingQuantity, Long subscriptionId,
        List<SubscriptionPricingCouponDTO> couponPricing, Set<SubscriptionPricingAddonsDTO> addonPricing) {
        SubscriptionFirstStepReqDTO result = new SubscriptionFirstStepReqDTO();

        // add thong tin goi dich vu
        SubscriptionFirstStepReqDTO.Pricing calPricing = new SubscriptionFirstStepReqDTO.Pricing();
        calPricing.setQuantity(pricingQuantity);
        calPricing.setId(pricing.getId());
        List<Long> couponPricingIds = new ArrayList<>();
        couponPricing.forEach(c -> couponPricingIds.add(c.getCouponId()));
        calPricing.setCouponIds(couponPricingIds);

        //add thong tin dich vu bo sung
        List<SubscriptionFirstStepReqDTO.Addon> calAddons = new ArrayList<>();
        addonPricing.forEach(addon -> {
            SubscriptionFirstStepReqDTO.Addon calAddon = new SubscriptionFirstStepReqDTO.Addon();
            calAddon.setId(addon.getId());
            List<Long> couponAddonIds = new ArrayList<>();
            addon.getCouponList().forEach(a -> couponAddonIds.add(a.getCouponId()));
            calAddon.setCouponIds(couponAddonIds);
            Long quantity = subscriptionAddonsRepository.getQuantityByAddonIdAndSubcriptionId(
                addon.getId(), subscriptionId);
            calAddon.setQuantity(quantity == null ? 1 : quantity);
            calAddon.setAddonMultiPlanId(addon.getAddonMultiPlanId());
            calAddons.add(calAddon);
        });
        result.setPricing(calPricing);
        result.setAddons(calAddons);
        //lay danh sach ctkm tong hoa don
        result.setCoupons(subscriptionCouponsRepository.getAllCouponIdBySubscriptionId(subscriptionId));
        return result;
    }

    /**
     * Lấy danh sách addon của pricing
     *
     * @param id id pricing
     */
    private List<SubscriptionPricingChosenResDTO.Addon> getAddonPricing(Long id, Long pricingPeriodId, Long subscriptionId,
        PortalType portalType) {
        List<SubscriptionPricingChosenResDTO.Addon> addonList = new ArrayList<>();
        // Lấy addon 1 lần của DVBS chung
        List<Addon> addons = addonRepository.getAddonRequiredFromPricing(id);
        List<Addon> subAddons = new ArrayList<>();

        //neu co subscriptionId va subscriptionId khong phai -1 -> doi goi
        if (Objects.nonNull(subscriptionId) && !SubscriptionConstant.HAS_OLD_SUBSCRIPTION.equals(subscriptionId)) {
            validateSubscription(subscriptionId, portalType);
            subAddons = addonRepository.getAddonSubscription(subscriptionId);
            addonList.addAll(getListAddonPricing(subAddons, id));
        }

        addonList.addAll(getListAddonPricing(addons, id));

        // Lấy addon 1 lần require của DVBS riêng
        List<Addon> addonOncePrivate = pricingMultiPlanAddonRepository
            .getAllAddonOnceTimeByPricingPlanIdAndInIsRequire(pricingPeriodId,
                Collections.singletonList(DeletedFlag.NOT_YET_DELETED.getValue()));
        List<SubscriptionPricingChosenResDTO.Addon> list = getListAddonPricing(addonOncePrivate, id).stream()
            .peek(a -> a.setIsRequired(YesNoEnum.YES)).collect(Collectors.toList());
        addonList.addAll(list);

        // Lấy addon multi plan require của DVBS riêng
        List<SubscriptionPricingChosenResDTO.Addon> addonPeriodicPrivate = subMultiplePeriod
            .transformAddonMultiPlanToAddonPeriodic(pricingPeriodId);
        addonList.addAll(addonPeriodicPrivate);
        List<PricingTaxRes> listTaxes = addonsTaxRepository.getAddonTax(
            addonList.stream().map(SubscriptionPricingChosenResDTO.Addon::getId).collect(Collectors.toList()));
        addonList.forEach(addon -> {
            List<PricingTaxRes> taxRes = pricingSetupFeeTaxRepository.getAddonSetupFeeTax(addon.getId());
            if (!CollectionUtils.isEmpty(taxRes)) {
                List<SubscriptionPricingChosenResDTO.Tax> feeTaxList = new ArrayList<>();
                int hasTaxFee = 0;
                for (PricingTaxRes feeTax : taxRes) {
                    hasTaxFee = Objects.isNull(feeTax.getHasTax()) ? 0 : feeTax.getHasTax();
                    feeTaxList.add(new SubscriptionPricingChosenResDTO.Tax(feeTax.getTaxId(), feeTax.getTaxName(), feeTax.getPercent()));
                }
                addon.setHasTax(YesNoEnum.valueOf(hasTaxFee));
                addon.setTaxSetupFeeList(feeTaxList);
            }
            addon.setSetupFee(subscriptionFormula.priceBeforeTax(addon.getSetupFee(), taxRes));
            addon.setPrice(subscriptionFormula.priceBeforeTax(addon.getPrice(), listTaxes.stream().filter(tax ->
                tax.getAddonId().equals(addon.getId())).collect(Collectors.toList())));
        });

        return addonList;
    }


    /**
     * set list addon pricing
     *
     * @param addons list addon
     */
    private List<SubscriptionPricingChosenResDTO.Addon> getListAddonPricing(List<Addon> addons, Long pricingId) {
        List<SubscriptionPricingChosenResDTO.Addon> addonList = new ArrayList<>();
        Set<Long> addonIds = addons.stream().map(Addon::getId).collect(Collectors.toSet());
        List<PricingAddon> pricingAddons = pricingAddonRepository.findAllByPricingIdAndAddonsIdIn(pricingId, addonIds);

        addons.forEach(addon -> {
            SubscriptionPricingChosenResDTO.Addon add = new SubscriptionPricingChosenResDTO.Addon();
            add.setName(addon.getName());
            add.setId(addon.getId());
            add.setCode(addon.getCode());
            add.setDescription(addon.getDescription());
            add.setSetupFee(addon.getSetupFee());
            //neu co addon dinh kem theo goi
            if (!CollectionUtils.isEmpty(pricingAddons)) {
                pricingAddons.stream().filter(a -> addon.getId().equals(a.getAddonsId())).findFirst()
                    .ifPresent(pa -> add.setIsRequired(Objects.nonNull(pa.getIsRequired()) ? YesNoEnum.valueOf(pa.getIsRequired()) : null));
            }

            if (Objects.nonNull(addon.getPricingPlan())) {
                // Thực hiện lấy thông tin của level cho dịch vụ bổ xung
                List<UnitLimited> unitLimitedList = unitLimitedRepository.findByAddonsIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromAsc(
                    addon.getId());

                if (Objects.nonNull(unitLimitedList)) {
                    List<CalUnitLimitedCustomDTO> limiteds =
                        unitLimitedList.stream().map(x -> new CalUnitLimitedCustomDTO(x.getUnitFrom(), x.getUnitTo(), x.getPrice()))
                            .collect(Collectors.toList());
                    add.setUnitLimitedList(limiteds);
                }
            }

            //Lấy thông tin dịch vụ
            serviceRepository.findByIdAndDeletedFlag(addon.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue())
                .ifPresent(serviceEntity -> add.setServiceName(serviceEntity.getServiceName()));

            //Lưu thông tin thuế cho subscription
            List<SubscriptionPricingChosenResDTO.Tax> taxes = new ArrayList<>();
            List<PricingTaxRes> addonTax = addonsTaxRepository.getAddonsTax(addon.getId());
            int hasTaxAdd = 0;
            for (PricingTaxRes pricing : addonTax) {
                hasTaxAdd = Objects.isNull(pricing.getHasTax()) ? 0 : pricing.getHasTax();
                taxes.add(new SubscriptionPricingChosenResDTO.Tax(pricing.getTaxId(), pricing.getTaxName(), pricing.getPercent()));
            }
            add.setTaxList(taxes);
            add.setHasTax(YesNoEnum.valueOf(hasTaxAdd));

            if (Objects.nonNull(addon.getBonusType())) {
                add.setBonusType(BonusTypeEnum.valueOf(addon.getBonusType()));
            }
            add.setBonusValue(addon.getBonusValue());

            if (Objects.nonNull(addon.getType())) {
                add.setType(TimeTypeEnum.valueOf(addon.getType()));
            }

            if (Objects.nonNull(addon.getPricingPlan())) {
                add.setPricingPlan(PricingPlanEnum.valueOf(addon.getPricingPlan()));
            }

            add.setPrice(addon.getPrice());
            add.setFreeQuantity(addon.getFreeQuantity());
            add.setHasChangePrice(addon.getAllowPriceChange() == null ? YesNoEnum.NO : YesNoEnum.valueOf(addon.getAllowPriceChange()));
            ChangeQuantityEnum changeQuantity = ChangeQuantityEnum.NONE;
            if (Objects.equals(String.valueOf(ChangeQuantityEnum.INCREASE.value), addon.getAllowChangeQuantity())) {
                changeQuantity = ChangeQuantityEnum.INCREASE;
            } else if (Objects.equals(String.valueOf(ChangeQuantityEnum.DECREASE.value), addon.getAllowChangeQuantity())) {
                changeQuantity = ChangeQuantityEnum.DECREASE;
            } else if (!StringUtils.isBlank(addon.getAllowChangeQuantity())) {
                changeQuantity = ChangeQuantityEnum.ALL;
            }
            add.setHasChangeQuantity(changeQuantity);
            addonList.add(add);
        });
        return addonList;
    }

    @Override
    public boolean checkEnableSmartCA(Long subscriptionId) {
        if (subscriptionId == null) {
            return false;
        }
        return subscriptionRepository.checkEnableSmartCA(subscriptionId).orElse(false);
    }

    @Override
    public boolean checkOrder(Long subscriptionId) {
        if (subscriptionId == null) {
            return false;
        }
        return subscriptionRepository.checkOrder(subscriptionId);
    }

    @Override
    public ClueDTO getMerchantInfo(Subscription subscription) {
        IServiceOwnerInfoDTO ownerInfo = subscriptionRepository.findServiceOwnerInfo(subscription.getId());
        // Với dịch vụ/combo của nhà cung cấp bên thứ 3, sử dụng chung một ví duy nhất
        List<Integer> set3rdServiceOwner = Arrays.asList(ServiceOwnerEnum.SAAS.getValue(), ServiceOwnerEnum.NONE.getValue());
        if (ownerInfo != null &&
            ownerInfo.getServiceOwnerPartner() == null && // Không phải dịch vụ của partner
            (ownerInfo.getServiceOwner() != null && set3rdServiceOwner.contains(ownerInfo.getServiceOwner()))) { // Dịch vụ từ 3rd party
            Wallet3rdPartyDTO systemConfig = (Wallet3rdPartyDTO) systemParamService.getParam(SystemParamTypeEnum.WALLET_3RD_PARTY);
            if (systemConfig == null ||
                systemConfig.getMerchantServiceId() == null ||
                systemConfig.getApiKey() == null ||
                systemConfig.getPrivateKey() == null) {
                throw exceptionFactory.dataInvalid(MessageKeyConstant.INVALID_CONFIGURATION, Resources.SYSTEM_PARAM, ErrorKey.PARAM_TYPE,
                    SystemParamTypeEnum.WALLET_3RD_PARTY.getValue());
            }
            ClueDTO clueDTO = new ClueDTO();
            clueDTO.setMerchantServiceId(systemConfig.getMerchantServiceId());
            clueDTO.setApiKey(systemConfig.getApiKey());
            clueDTO.setPrivateKey(systemConfig.getPrivateKey());
            clueDTO.setQrApiKey(systemConfig.getQrApiKey());
            clueDTO.setQrTerminalId(systemConfig.getQrTerminalId());
            clueDTO.setQrSecretKey(systemConfig.getQrSecretKey());
            clueDTO.setProvinceCodePay(MerchantData.PROVINCE_CODE_ENTERPRISE);
            clueDTO.setBaseUrl(baseUrl);
            return clueDTO;
        } else {
            return subscriptionRepository.getMerchantByUserId(subscription.getUserId());
        }
    }

    /**
     * lay du lieu tap tien trinh thue bao ON
     */
    @Override
    public OnlineServiceProgressDTO getOrderOnlineServiceProgress(Long subId) {
        ISubscriptionProgressDTO subDetail = subscriptionRepository.findSubProgressDetailBySubId(subId);
        if (Objects.isNull(subDetail)) {
            throw exceptionFactory.resourceNotFound(Resources.SUBSCRIPTION, ErrorKey.ID, String.valueOf(subId));
        }
        List<Long> lstTransactionLogId = transactionLogRepository.getLstTransactionLogIdBySubId(subId);
        List<String> lstActivityCode = getOnlineServiceActivityCode();

        // timeline chi tiết đơn hàng
        List<IOnlineServiceTimeLineDTO> lstLog = activityLogRepository.geLstOnlineServiceLog(lstTransactionLogId, lstActivityCode);

        List<OnlineServiceTimeLineDTO> lstActivityLog = new ArrayList<>();

        // thông tin trạng thái ĐANG CÀI ĐĂT không được lưu trong activityLog, phải lấy ra từ bảng subscriptions
        // luôn là trạng thái đầu, default khi sub tạo ra --> luôn có trạng thái ĐANG CÀI ĐĂT
        String actorRole = Objects.nonNull(subDetail.getPortalType()) ? (Objects.equals(subDetail.getPortalType(), PortalType.SME.getValue())
            ? "" : PortalType.getPortalTypeStr(subDetail.getPortalType())) : null;
        OnlineServiceTimeLineDTO installingSubLog = OnlineServiceTimeLineDTO.builder()
            .updateTime(subDetail.getCreatedAt()) // thời gian log là ngày tạo sub
            .activityCode(ActivityCodeEnum.CREATE_SUB_SPDV.value) // code tạo mới
            .status(TransactionLogStatusEnum.INIT.value)
            .actorId(subDetail.getRegistedBy())
            .actorRole(actorRole)
            .actorEmail(subDetail.getEmail())
            .build();
        lstActivityLog.add(installingSubLog);

        // lấy thông tin các trạng thái khác
        if (!CollectionUtils.isEmpty(lstLog)) {
            lstActivityLog.addAll(lstLog.stream().map(OnlineServiceTimeLineDTO::new).collect(Collectors.toList()));
        }
        OnlineServiceProgressDTO response = new OnlineServiceProgressDTO();
        response.setTimeLine(lstActivityLog);
        response.setSubscriptionCode(subDetail.getSubscriptionCode()); // ID đơn hàng
        response.setStatusOrderId(subDetail.getStatusOrderId()); // status hiện tại của sub (đang cài đặt, đã cài đặt, gắp sự cố)
        response.setPricingName(subDetail.getPricingName()); // Tên gói cước
        response.setServiceName(subDetail.getServiceName()); // Tên dịch vụ
        response.setIcon(subDetail.getIcon()); // url icon service

        Integer newestBillStatus = billsRepository.findLatestBillStatusBySubscriptionId(subId);
        if (Objects.nonNull(newestBillStatus)) {
            response.setPaymentStatus(BillStatusEnum.valueOf(newestBillStatus)); // trạng thái thanh toán
        }
        return response;
    }

    private List<String> getOnlineServiceActivityCode() {
        List<String> lstActivityCode = new ArrayList<>();
        lstActivityCode.add(ActivityCodeEnum.CREATE_SUB_SPDV.value);
        lstActivityCode.add(ActivityCodeEnum.RENEW_PRICING_SPDV.value);
        lstActivityCode.add(ActivityCodeEnum.CHANGE_PRICING_SPDV.value);
        lstActivityCode.add(ActivityCodeEnum.UPDATE_PRICING_SPDV.value);
        lstActivityCode.add(ActivityCodeEnum.CANCEL_SPDV.value);
        lstActivityCode.add(ActivityCodeEnum.CANCEL_SPDV_END_OF_PERIOD.value);
        return lstActivityCode;
    }

    @Override
    public SubscriptionProgressDTO getSubscriptionProgress(Long subscriptionId) {
        Subscription subscription = findByIdAndDeletedFlag(subscriptionId, DeletedFlag.NOT_YET_DELETED.getValue());
        if (subscription.getService() != null) {
            if (Objects.equals(subscription.getService().getOnOsTypeEnum(), OnOsTypeEnum.ON)) {
                return SubscriptionProgressDTO.builder().onlineProgress(getOrderOnlineServiceProgress(subscriptionId)).build();
            } else {
                return SubscriptionProgressDTO.builder().orderProgress(orderServiceReceiveService.getProgress((subscriptionId))).build();
            }
        } else {
            // Combo không có thông tin tiến trình đơn hàng
            throw exceptionFactory.resourceNotFound(Resources.SERVICES, ErrorKey.ID, String.valueOf(subscription));
        }
    }

    @Override
    public Page<GetSubscriptionServiceDTO> findAll(
        String inputValue, Boolean isServiceName, Boolean isPricingName, Long categoryId, Long devId, String search,
        List<Integer> lstServiceOwner, Pageable pageable) {
        Long parenId = AuthUtil.getCurrentParentId();
        search = StringUtils.isEmpty(search) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(search);
        inputValue = StringUtils.isEmpty(inputValue) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(inputValue);
        Long paymentDateFailOn = systemParamService.getPaymentDateFailOn(SystemParamConstant.PARAM_COUPON).orElse(0L);

        Page<MyServiceSubscriptionResDTO> result = subscriptionRepository.getMyServiceSubscriptionOSON(inputValue, isServiceName, isPricingName,
            parenId, search, categoryId, devId, paymentDateFailOn, lstServiceOwner, pageable);

        /* Fill discount/gift info */
        List<ProductTagDTO> lstBannerInfo = result.getContent().stream()
            .map(serviceDTO -> new ProductTagDTO(serviceDTO.getServiceId(), ProductTypeEnum.SERVICE))
            .collect(Collectors.toList());
        campaignSmeService.getListProductTag(lstBannerInfo);
        return result.map(serviceDTO -> {
            Optional<ProductTagDTO> oBanner = lstBannerInfo.stream()
                .filter(banner -> Objects.equals(banner.getObjectId(), serviceDTO.getServiceId()) &&
                    banner.getObjectType() == ProductTypeEnum.SERVICE)
                .findFirst();
            Optional<ServiceEntity> serviceEntity = serviceRepository.findById(serviceDTO.getServiceId());
            Long count = subscriptionRepository.countUnexpiredSub(parenId, serviceDTO.getServiceId());
            boolean checkRenew = false;
            if (serviceDTO.getIsRenew() == 1 && count == 0) {
                checkRenew = true;
            } else if (serviceDTO.getIsRenew() == 1 && serviceEntity.get().getAllowMultiSub() == 1 && count > 0) {
                checkRenew = true;
            }
            return new GetSubscriptionServiceDTO(serviceDTO, oBanner.orElse(null), checkRenew);
        });
    }

    /**
     * Xem danh sach tat ca subscription da duoc mua
     */
    @Override
    public Page<SubscriptionDTO> findSubscriptionByRole(Pageable pageable) {
        return subscriptionMapper.mapEntitiesPage(subscriptionRepository.findByUserId(AuthUtil.getCurrentUserId(), pageable));
    }

    @Override
    public String getLinkSubDetailSME(Long userId) {
        SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.PARAM_THEME_CONFIG);
        if (Objects.nonNull(systemParam) && Objects.nonNull(systemParam.getParamTextValue())) {
            ThemeEnum themeEnum = ThemeEnum.getByValue(systemParam.getParamTextValue());
            if (Objects.nonNull(themeEnum) && Objects.equals(themeEnum, ThemeEnum.NEW_THEME)) {
                User user = userRepository.findById(userId).orElse(null);
                String customerTypeString = "enterprise";
                if (Objects.nonNull(user) && Objects.nonNull(user.getCustomerType())) {
                    customerTypeString = CustomerTypeEnum.getCustomerLink(user.getCustomerType());
                }
                return "%s/" + customerTypeString + "/account/subscription?id=%d";
            }
        }
        return SubscriptionConstant.LINK_SME_SUBS_SERVICE_DETAIL;
    }


    @Override
    public Page<SubComboFilterResDTO> findSubsForCombo(SubComboFilterReqDTO request, Pageable pageable) {
        Page<SubComboFilterResInterfaceDTO> response = subscriptionRepository.findSubsForComboInterface(
            Objects.isNull(request.getNameSearch()) ? ""
                : SqlUtils.optimizeSearchLike(request.getNameSearch()),
            Objects.isNull(request.getCompanyName()) ? ""
                : SqlUtils.optimizeSearchLike(request.getCompanyName()),
            Objects.isNull(request.getStatus()) ? -1 : request.getStatus().value,
            Objects.isNull(request.getType()) ? "" : request.getType(),
            AuthUtil.getCurrentUserId(),
            pageable);
        Page<SubComboFilterResDTO> dto = response.map(subDTO -> {
            SubComboFilterResDTO subComboFilterResDTO = new SubComboFilterResDTO();
            BeanUtils.copyProperties(subDTO, subComboFilterResDTO);
            subComboFilterResDTO.setNextPaymentTime(DateUtil.convertLocalDateToString(
                DateUtil.toLocalDate(subDTO.getNextPaymentTime()),
                DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
            subComboFilterResDTO.setStartDate(DateUtil.convertLocalDateToString(
                DateUtil.toLocalDate(subDTO.getStartDate()),
                DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
            subComboFilterResDTO.setPricingList(subscriptionRepository.findPricingOfCombo(subDTO.getComboPlanId(),
                subDTO.getSubscriptionId()));
            return subComboFilterResDTO;
        });
        return dto;
    }

    @Override
    public SubscriptionComboBasicDTO getAdminOrDevSubscriptionServiceBasic(Long comboId, Long comboPlanId) {

        Combo combo = comboRepository.findByIdAndDeletedFlag(comboId, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> {
                String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, null,
                        LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.COMBO,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });

        comboPlanRepository.findByIdAndDeletedFlagAndComboId(comboPlanId, DeletedFlag.NOT_YET_DELETED.getValue(), comboId)
            .orElseThrow(() -> {
                String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, new Object[]{comboPlanId},
                        LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.COMBO_PLAN,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });
        return comboPlanRepository.getComboBasic(comboPlanId, comboId);
    }

    @Override
    public Page<SubComboFilterDevResDTO> findSubsComboDev(SubComboFilterReqDTO request, Pageable pageable) {
        FilterReqDefault.setComboRequestDefault(request);
        request.setNameSearch(
            StringUtils.isEmpty(request.getNameSearch()) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(request.getNameSearch()));
        Page<SubComboFilterDevRes> response = subscriptionRepository.findSubsForComboDev(request,
            AuthUtil.getCurrentParentId(),
            -1L, -1L,
            pageable);
        List<SubComboFilterDevResDTO> content = response.getContent().stream().map(subComboFilterDevRes -> {
            SubComboFilterDevResDTO subComboFilterDevResDTO = new SubComboFilterDevResDTO();
            BeanUtils.copyProperties(subComboFilterDevRes, subComboFilterDevResDTO);
            SubscriptionStatusEnum subStatusEnum = SubscriptionStatusEnum.valueOf(subComboFilterDevRes.getStatus());
            subComboFilterDevResDTO.setCycleType(
                Objects.isNull(subComboFilterDevRes.getCycleType()) ? null : CycleTypeEnum.valueOf(subComboFilterDevRes.getCycleType()));
            subComboFilterDevResDTO.setStatus(subStatusEnum);
            subComboFilterDevResDTO.setCustomerType(CustomerTypeEnum.getValueOf(subComboFilterDevRes.getCustomerType()));
            if (subStatusEnum == SubscriptionStatusEnum.IN_TRIAL) {
                subComboFilterDevResDTO.setCycleType(null);
                subComboFilterDevResDTO.setPaymentCycle(null);
            }
            subComboFilterDevResDTO.setPricingList(subscriptionRepository.findPricingOfCombo(subComboFilterDevRes.getComboPlanId(),
                subComboFilterDevRes.getId()));
            return subComboFilterDevResDTO;
        }).collect(Collectors.toList());

        return new PageImpl<>(content, response.getPageable(), response.getTotalElements());
    }

    @Override
    public Page<SubComboFilterDevResDTO> findSubsComboAdmin(SubComboFilterReqDTO request, Pageable pageable) {
        //     validate Input param
        request.setCompanyName(
            StringUtils.isEmpty(request.getCompanyName()) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(request.getCompanyName()));
        request.setDevName(StringUtils.isEmpty(request.getDevName()) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(request.getDevName()));
        request.setNameSearch(
            StringUtils.isEmpty(request.getNameSearch()) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(request.getNameSearch()));

        FilterReqDefault.setComboRequestDefault(request);
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long provinceId = departmentsRepository.getProvinceIdByUserId(currentUserId);
        List<String> roles = userRepository.getRole(currentUserId);
        Long createBy = !CollectionUtils.isEmpty(roles) && roles.contains(RoleType.AM.name()) ? currentUserId : -1L;
        Page<SubComboFilterDevRes> response = subscriptionRepository.findSubsForComboDev(request,
            -1L,
            Objects.isNull(provinceId) ? -1L : provinceId,
            createBy,
            pageable);
        List<SubComboFilterDevResDTO> content = response.stream().map(subComboFilterDevRes -> {
            SubComboFilterDevResDTO subComboFilterDevResDTO = new SubComboFilterDevResDTO();
            BeanUtils.copyProperties(subComboFilterDevRes, subComboFilterDevResDTO);
            SubscriptionStatusEnum subStatusEnum = SubscriptionStatusEnum.valueOf(subComboFilterDevRes.getStatus());
            subComboFilterDevResDTO.setStatus(subStatusEnum);
            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getValueOf(subComboFilterDevRes.getCustomerType());
            subComboFilterDevResDTO.setCustomerType(customerTypeEnum);
            subComboFilterDevResDTO.setCycleType(
                Objects.isNull(subComboFilterDevRes.getCycleType()) ? null : CycleTypeEnum.valueOf(subComboFilterDevRes.getCycleType()));
            if (subStatusEnum == SubscriptionStatusEnum.IN_TRIAL) {
                subComboFilterDevResDTO.setCycleType(null);
                subComboFilterDevResDTO.setPaymentCycle(null);
            }
            subComboFilterDevResDTO.setPricingList(subscriptionRepository.findPricingOfCombo(subComboFilterDevRes.getComboPlanId(),
                subComboFilterDevRes.getId()));
            return subComboFilterDevResDTO;
        }).collect(Collectors.toList());

        return new PageImpl<>(content, response.getPageable(), response.getTotalElements());
    }

    @Override
    public Set<String> getDeveloperName(String developerName, CustomerTypeEnum customerType) {
        Set<String> roles = AuthUtil.getCurrentUser().getAuthorities().stream()
            .map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
//        Chỉ trên màn admin mới có dropdown list nhà phát triển (Hiển thị tất cả)
        long provinceId = Objects.nonNull(AuthUtil.getDepartment().getProvinceId()) ? AuthUtil.getDepartment().getProvinceId() : -1L;
        if (!roles.contains(RoleType.ADMIN.getValue())
            && !roles.contains(RoleType.FULL_ADMIN.getValue())
            && !roles.contains(RoleType.CUSTOMER_SUPPORT.getValue())) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        if (provinceId == -1L) {
            return subscriptionRepository.getDevelopeNameBySuperAdmin(developerName, customerType.getValue());
        }
        return subscriptionRepository.getDevelopeNameByProvinceAdmin(developerName, provinceId, customerType.getValue());
    }


    @Override
    public List<PricingSaaSResDTO> findAllPricingByService1(Long serviceId) {
        Set<Long> maxVerIds = subscriptionRepository.findMaxVerIdsByServiceId1(serviceId);
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerType = Objects.isNull(userLogin) ? null : userLogin.getCustomerType();
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getValueOf(customerType);
        List<PricingSaaSResDTO> response = subscriptionRepository.findAllPricingByService(maxVerIds);
        List<PricingSaaSResDTO> responseCustomerType = new ArrayList<>();
        for (PricingSaaSResDTO pr : response) {
            if (pr.getCustomerType().contains(customerTypeEnum) || userLogin == null) {
                responseCustomerType.add(pr);
            }
        }
        Optional<ServiceEntity> service = serviceRepository.findByIdAndDeletedFlag(serviceId, 1);
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            boolean hasSub = subscriptionRepository.existsSubIsActiveByUserIdAndServiceId(AuthUtil.getCurrentUserId(), serviceId);
            if (service.isPresent() && service.get().getAllowMultiSub() == 0 && hasSub) {
                yesNoEnum = YesNoEnum.NO;
            }
        }
        YesNoEnum finalYesNoEnum = yesNoEnum;
        val result = responseCustomerType.stream().peek(p -> {
            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(p.getId());
            // Số tiền trước thuế
            BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(p.getPriceValue(), taxes);
            p.setPrice(pricingPreTax);
            List<PricingMultiplePeriodResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodForPricing(p.getId());
            List<PricingMultiplePeriodResDTO> multiplePeriodResponse = new ArrayList<>();
            for (PricingMultiplePeriodResDTO mul : multiplePeriods) {
                if (mul.getCustomerTypeCode().contains(customerTypeEnum) || userLogin == null) {
                    multiplePeriodResponse.add(mul);
                }
            }
            p.setPricingMultiplePeriods(multiplePeriodResponse);
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                p.setCouponList(couponService.getCouponListByPricingIdOrPricingMultiPlanId(p.getId(), null));
                p.setUnitLimitedList(pricingService.getUnitLimitedByPricingId(p.getId()));
            }
            p.setFeatureList(getListFeatureByFeatureIds(p.getListFeatureId()));
            p.setAllowSubscript(finalYesNoEnum);
            ServiceReaction reaction = serviceReactionService.getByServiceIdAndType(p.getPricingDraftId(), 3);
            if (reaction != null) {
                p.setReaction(true);
            }
        }).collect(Collectors.toList());
        return result;
    }


    /**
     * Lấy danh sách chức năng theo danh sách mã tính năng
     *
     * @param featureIdList the feature id list
     * @return the list feature by pricing id
     */
    private List<PricingDetailResDTO.Feature> getListFeatureByFeatureIds(String featureIdList) {
        String[] split = StringUtils.split(
            StringUtils.defaultString(
                featureIdList, CharacterConstant.BLANK),
            CharacterConstant.COMMA);
        String[] featureArr = StringUtils.stripAll(split);
        Set<Long> featureIds = Arrays.stream(featureArr).map(Long::valueOf).collect(Collectors.toSet());

        List<PricingDetailResDTO.Feature> outFeatures = new ArrayList<>();
        comboPlanRepository.getFeaturesCombo(featureIds).forEach(feature -> {
            PricingDetailResDTO.Feature feat = new PricingDetailResDTO.Feature();
            BeanUtils.copyProperties(feature, feat);
            feat.setIcon(feature.getIcon());
            feat.setFilePath(feature.getFilePath());
            feat.setType(feature.getType());
            feat.setDescription(feature.getDescription());
            feat.setId(feature.getId());
            feat.setName(feature.getName());
            outFeatures.add(feat);
        });
        outFeatures.sort(Comparator.comparing(item -> new ArrayList<>(featureIds).indexOf(item.getId())));
        return outFeatures;
    }


    @Override
    public List<PricingSaaSResDTO> findAllExchangedPricing(Long serviceId, Long subId) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerType = Objects.isNull(userLogin) ? null : userLogin.getCustomerType();
        PricingSetupInfo pricingSetupInfo = getPricingSetupInfo(serviceId);
        Subscription subscription = findByIdAndDeletedFlag(subId, DeletedFlag.NOT_YET_DELETED.getValue());
        PricingMultiPlan pricingMultiPlanSub = pricingMultiPlanRepository.findByIdAndDeletedFlag(subscription.getPricingMultiPlanId(),
                DELETED_FLAG)
            .orElse(null);
        if (subscription.getPricingMultiPlanId() == null) {
            return pricingService.findAllPricingByService(serviceId,
                Objects.nonNull(subscription.getPricingId()) ? subscription.getPricingId() : null,
                customerType != null ? CustomerTypeEnum.getValueOf(customerType) : null, -1L);
        }

        Boolean checkPricingConfigList = pricingRepository.checkPricingConfigList(serviceId);
        validateChangePlanFinalCycle(subscription, pricingMultiPlanSub);

        // lọc điều kiện của gói Pricing
        List<PricingSaaSResDTO> pricingListSelected = new ArrayList<>();
        List<Long> pricingIdsSelected = pricingService.getPricingIdsSelected(pricingSetupInfo, pricingMultiPlanSub, serviceId,
            checkPricingConfigList);

        // pricingListSelected
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getValueOf(customerType);
        Set<Long> maxVerIds = subscriptionRepository.findMaxVerIdsByServiceId(serviceId, -1L);
        List<PricingSaaSResDTO> pricingSaaSResDTOList = subscriptionRepository.findAllPricingByService(maxVerIds);
        for (PricingSaaSResDTO pr : pricingSaaSResDTOList) {
            if (pr.getCustomerType() == null || pr.getCustomerType().contains(customerTypeEnum) || userLogin == null) {
                if (pricingIdsSelected.contains(pr.getId())) {
                    pricingListSelected.add(pr);
                }
            }
        }

        Optional<ServiceEntity> service = serviceRepository.findByIdAndDeletedFlag(serviceId, 1);
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (service.isPresent() && Objects.nonNull(AuthUtil.getCurrentUser())) {
            boolean hasSub = subscriptionRepository.existsSubIsActiveByUserIdAndServiceId(AuthUtil.getCurrentUserId(), serviceId);
            if (service.get().getAllowMultiSub() == 0 && hasSub) {
                yesNoEnum = YesNoEnum.NO;
            }
        }
        YesNoEnum finalYesNoEnum = yesNoEnum;

        // lọc điều kiện của gói Pricing
        List<Long> pricingMultiPlanIdsSelected = pricingService.getPricingMultiPlanIdsSelected(pricingSetupInfo, pricingMultiPlanSub,
            pricingIdsSelected, checkPricingConfigList);

        val result = pricingListSelected.stream().peek(p -> {
            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(p.getId());
            // Số tiền trước thuế
            BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(p.getPriceValue(), taxes);
            p.setPrice(pricingPreTax);
            List<PricingMultiplePeriodResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodForPricing(p.getId());
            List<PricingMultiplePeriodResDTO> pricingMultiPlanSelected = new ArrayList<>();
            if (!CollectionUtils.isEmpty(multiplePeriods)) {
                multiplePeriods.forEach(mul -> {
                    if ((mul.getCustomerTypeCode() == null || mul.getCustomerTypeCode().contains(customerTypeEnum) || userLogin == null) &&
                        pricingMultiPlanIdsSelected.contains(mul.getIdPricingPeriod())) {
                        pricingMultiPlanSelected.add(mul);
                    }
                });
            }
            p.setPricingMultiplePeriods(pricingMultiPlanSelected);
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(pricingMultiPlanSelected)) {
                p.setCouponList(couponService.getCouponListByPricingIdOrPricingMultiPlanId(p.getId(), null));
                p.setUnitLimitedList(pricingService.getUnitLimitedByPricingId(p.getId()));
            }
            p.setFeatureList(getListFeatureByFeatureIds(p.getListFeatureId()));
            p.setSeoDTO(seoService.getSeoDetailSme(p.getPricingDraftId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU));
            p.setAllowSubscript(finalYesNoEnum);
            ServiceReaction reaction = serviceReactionService.getByServiceIdAndType(p.getPricingDraftId(), 3);
            if (reaction != null) {
                p.setReaction(true);
            }
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(result)) {
            return result.stream().filter(p -> p.getPricingMultiplePeriods().size() > 0).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private void validateChangePlanFinalCycle(Subscription subscription, PricingMultiPlan pricingMultiPlanSub) {
        Long pricingId = pricingMultiPlanSub != null ? pricingMultiPlanSub.getPricing().getId() : subscription.getPricing().getId();
        Pricing pricing = getCurrentPricing(pricingId);
        Integer currentCycle = subscription.getCurrentCycle();
        Integer numberOfCycles = Objects.nonNull(subscription.getNumberOfCycles()) ?
            subscription.getNumberOfCycles() : pricing.getNumberOfCycles();
        if (Objects.nonNull(currentCycle) && Objects.nonNull(numberOfCycles) &&
            (checkSubsInFinalCycle(numberOfCycles, currentCycle) || !isSwapPricingOrUpdateSub(subscription))) { //sme doi pricing
            throw exceptionFactory.badRequest(MessageKeyConstant.CAN_NOT_SWAP_PRICING, Resources.SUBSCRIPTION, ErrorKey.ID, subscriptionMessage);
        }
    }

    /**
     * kiểm tra subscription có đang trong chu ký cuối cùng không bất kể là gói có cấu hình: đổi gói ngay lập tức hoặc đổi gói cuối chu kỳ
     */
    private Boolean checkSubsInFinalCycle(Integer numberOfCycle, Integer currentCycle) {
        return currentCycle != null && currentCycle.equals(numberOfCycle);
    }

    @Override
    public Subscription findById(Long subscriptionId) {
        return subscriptionRepository.findById(subscriptionId).orElse(null);
    }

    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    @Override
    public Page<CustomerDTO> getListCustomer(String companyName, String adminName, String tin,
        String provinceName, Long removeID, CustomerTypeEnum customerType, String repPersonalCertNumber, Long userId,
        String searchName, String searchEmail, String searchPhone, String searchTin, String values, Pageable pageable) {
        Integer searchProvinceStatus = STATUS_UN_SEARCHING;
        Long provinceId = IS_EMPTY;
        // Neu nguoi dang nhap he thong la admin thi lay du lieu theo admin
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(),
            RoleType.CUSTOMER_SUPPORT.getValue()))) {
            UserDepartmentDTO actorDepartment = AuthUtil.getDepartment();

            // Neu province cua admin dang nhap != null thi la admin tinh
            if (Objects.nonNull(actorDepartment.getProvinceId())) {
                // lay du lieu theo tinh thanh cua admin tong
                searchProvinceStatus = STATUS_SEARCHING;
                provinceId = actorDepartment.getProvinceId();
            }

        }
        Page<BigInteger> pageUserId = subscriptionRepository.getListCustomerId(searchProvinceStatus, provinceId, companyName, adminName, tin,
            provinceName, removeID, customerType.getValue(), repPersonalCertNumber, userId,
            searchName, searchEmail, searchPhone, searchTin, values, pageable);

        List<Long> lstUserId = pageUserId.stream().map(BigInteger::longValue).collect(Collectors.toList());
        Map<Long, CustomerDTO> customerIdToDetail = subscriptionRepository.getListCustomerDetail(lstUserId)
            .stream().collect(Collectors.toMap(CustomerDTO::getId, Function.identity()));
        return pageUserId.map(id -> customerIdToDetail.get(id.longValue()));
    }

    @Override
    public Integer getPricingType(Long subscriptionId) {
        return subscriptionRepository.getPricingType(subscriptionId);
    }


    @Transactional
    public PricingSaaSResDTO getPricingSubscriptionByPricingId(Long pricingId) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerType = Objects.isNull(userLogin) ? null : userLogin.getCustomerType();
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getValueOf(customerType);
        //
        PricingSaaSResDTO pr = pricingRepository.getPricingSaaSResDTOByPricingId(pricingId);
        ServiceResponseDTO service = serviceRepository.getServiceCommonResponseById(pr.getServiceId());
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            boolean hasSub = subscriptionRepository.existsSubIsActiveByUserIdAndServiceId(AuthUtil.getCurrentUserId(), pr.getServiceId());
            if (Objects.nonNull(service) && service.getAllowMultiSub() == 0 && hasSub) {
                yesNoEnum = YesNoEnum.NO;
            }
        }
        YesNoEnum finalYesNoEnum = yesNoEnum;
        if (pr.getCustomerType() == null || pr.getCustomerType().contains(customerTypeEnum) || userLogin == null) {
            List<PricingMultiplePeriodResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodForPricing(pr.getId());
            List<PricingMultiplePeriodResDTO> multiplePeriodResponse = new ArrayList<>();
            for (PricingMultiplePeriodResDTO mul : multiplePeriods) {
                if (mul.getCustomerTypeCode() == null || mul.getCustomerTypeCode().contains(customerTypeEnum) || userLogin == null) {
                    multiplePeriodResponse.add(mul);
                }
            }
            // Nếu có period nhưng không thuộc kiểu người dùng thì add vào listErr để remove
            if (!CollectionUtils.isEmpty(multiplePeriods) && CollectionUtils.isEmpty(multiplePeriodResponse)) {
                return null;
            }
            pr.setPricingMultiplePeriods(multiplePeriodResponse);
            // Số tiền trước thuế
            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(pr.getId());
            BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(pr.getPriceValue(), taxes);
            pr.setPrice(pricingPreTax);
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                pr.setCouponList(couponService.getCouponListByPricingIdOrPricingMultiPlanId(pr.getId(), null));
                pr.setUnitLimitedList(pricingService.getUnitLimitedByPricingId(pr.getId()));
            }
            pr.setFeatureList(getListFeatureByFeatureIds(pr.getListFeatureId()));
            pr.setSeoDTO(seoService.getSeoDetailSme(pr.getPricingDraftId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU));
            pr.setAllowSubscript(finalYesNoEnum);
            ServiceReaction reaction = serviceReactionService.getByServiceIdAndType(pr.getPricingDraftId(), 3);
            if (reaction != null) {
                pr.setReaction(true);
            }
            // set thông tin dịch vụ
            pr.setServiceName(service.getName());
            pr.setServiceIcon(service.getIcon());
            pr.setServiceOnOsType(service.getOnOsType());

            //set creationLayoutId, Customfiled
            Optional<PricingDraft> pd = pricingDraftRepository.findById(pr.getPricingDraftId());
            pd.ifPresent(pricingDraft -> {
                Long layoutId = pricingDraft.getCreationLayoutId();
                layoutId = layoutId != null ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.PRICING.getValue());
                List<CustomFieldValueDTO> lstFieldValueDTO = customFieldManager.getListFieldDraftValue(layoutId,
                    EntityTypeEnum.PRICING.getValue(), pricingDraft.getId());
                pr.setCreationLayoutId(layoutId);
                pr.setLstCustomFields(lstFieldValueDTO);
            });
            return pr;
        }
        return null;
    }


    /**
     * Hiển thị thông tin khách hàng khi subscription
     */
    @Override
    public SubscriptionCustomerDTO getCustomerSME() {
        Long parentId = AuthUtil.getCurrentParentId();
        return userRepository.getCustomerSMESubscription(parentId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.USER, ErrorKey.ID, userMessage));
    }

    @Override
    public Page<SubscriptionUserResponseDTO> getSubscriptionUsersBySubscriptionId(Long subscriptionId, PortalType portalType,
        UserSubscriptionEnum status, String name, Long serviceId, Pageable pageable) {
        name = StringUtils.isEmpty(name) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(name);
        Subscription subscription = checkExistsSubscriptionPortal(subscriptionId, portalType);
        // Nếu trạng thái đang chờ thì không trả ra dữ liệu
        if (Objects.equals(subscription.getStatus(), SubscriptionStatusEnum.FUTURE.value)) {
            return null;
        }
        // Check status or name is null
        Integer userStatus = Objects.isNull(status) ? UserSubscriptionEnum.NOT_SET.value : status.value;
        name = Objects.nonNull(name) ? SqlUtils.optimizeSearchLike(name) : "";
        serviceId = (serviceId == null) ? -1L : serviceId;
        return subscriptionRepository.getSubscriptionUsersBySubscriptionId(subscriptionId, userStatus, name, serviceId, subscription.getUserId(),
            pageable);
    }

    /**
     * Tìm thuê bao theo ID và portal
     */
    private Subscription checkExistsSubscriptionPortal(Long id, PortalType portalType) {
        Long currentParentId = AuthUtil.getCurrentParentId();
        switch (portalType) {
            case SME:
                // Trả về thuê bao của SME đang đăng nhập
                return subscriptionRepository.findByIdAndDeletedFlagAndUserId(id, DeletedFlag.NOT_YET_DELETED.getValue(), currentParentId)
                    .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SUBSCRIPTION, ErrorKey.ID, String.valueOf(id)));
            case DEV:
                Long providerId = subscriptionRepository.findProviderId(id);
                if (!Objects.equals(currentParentId, providerId)) {
                    throw exceptionFactory.permissionDenied("DEV is not the owner of the service");
                }
            default:
                return findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        }
    }

    @Override
    public Page<SubscriptionActiveResponseDTO> searchActiveSubscription(String apiKey, String name, Long categoryId, Long devId, String devName,
        String searchText, Pageable pageable) {
        //check if any param is null
        if (Objects.isNull(apiKey)) {
            apiKey = CharacterConstant.BLANK;
        }
        if (Objects.isNull(name)) {
            name = CharacterConstant.BLANK;
        }
        if (Objects.isNull(categoryId)) {
            categoryId = -1L;
        }
        if (Objects.isNull(devId)) {
            devId = -1L;
        }
        if (Objects.isNull(devName)) {
            devName = CharacterConstant.BLANK;
        }
        if (Objects.isNull(searchText)) {
            searchText = CharacterConstant.BLANK;
        }
        return subscriptionRepository.searchActiveSubscription(AuthUtil.getCurrentUserId(), apiKey, name, categoryId, devId, devName,
            searchText, pageable);
    }

    @Override
    public Page<SubscriptionDevCompanyResponseDTO> getDevCompanyList(SubscriptionRequestDTO requestDTO, Pageable pageable) {
        String name = StringUtils.isEmpty(requestDTO.getName()) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(requestDTO.getName());
        return subscriptionRepository.getCompanyList(AuthUtil.getCurrentUserId(), name, requestDTO.getStatus().value,
            requestDTO.getUserStatus().value, pageable);
    }

    @Override
    public Page<SubscriptionSubPlanResponseDTO> getSubPlanList(SubscriptionRequestDTO requestDTO, Pageable pageable) {
        return subscriptionRepository.getSubPlanList(AuthUtil.getCurrentUserId(), requestDTO.getName(),
            requestDTO.getStatus().value, requestDTO.getUserStatus().value, pageable);
    }

    @Override
    public Page<SubscriptionServiceResponseDTO> getServiceList(SubscriptionRequestDTO requestDTO, Pageable pageable) {
        String name = StringUtils.isEmpty(requestDTO.getName()) ? StringUtils.EMPTY : SqlUtils.optimizeSearchLike(requestDTO.getName());
        Long userId = AuthUtil.getCurrentParentId();
        return subscriptionRepository.getServiceList(userId, name, requestDTO.getStatus().value, requestDTO.getUserStatus().value, pageable);
    }

    @Override
    public Page<SubscriptionSmeCompaniesResponseDTO> getListSmeSubscription(SubscriptionSmeCompaniesRequestDTO requestDTO, Pageable pageable) {
        Long userId = AuthUtil.getCurrentParentId();
        return subscriptionRepository.getListSmeSubscription(userId, requestDTO.getName(), requestDTO.getStatus().value, pageable);
    }

    @Transactional(readOnly = true)
    @Override
    public Page<CustomerDTO> getCustomer(String companyName, String adminName, String tin,
        String provinceName, Long removeID, CustomerTypeEnum customerType, String repPersonalCertNumber, Long userId, Pageable pageable) {
        Integer searchProvinceStatus = STATUS_UN_SEARCHING;
        Long provinceId = IS_EMPTY;
        // Neu nguoi dang nhap he thong la admin thi lay du lieu theo admin
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(),
            RoleType.CUSTOMER_SUPPORT.getValue()))) {
            UserDepartmentDTO actorDepartment = AuthUtil.getDepartment();

            // Neu province cua admin dang nhap != null thi la admin tinh
            if (Objects.nonNull(actorDepartment.getProvinceId())) {
                // lay du lieu theo tinh thanh cua admin tong
                searchProvinceStatus = STATUS_SEARCHING;
                provinceId = actorDepartment.getProvinceId();
            }

        }
        return subscriptionRepository.getCustomer(searchProvinceStatus, provinceId, companyName, adminName, tin, provinceName,
            removeID, customerType.getValue(), repPersonalCertNumber, userId, pageable);
    }

    @Override
    public SubscriptionIntegrationResDTO getSubscriptionIntegration(Long subscriptionId) {
        Subscription subscription = findByIdAndDeletedFlag(subscriptionId, DeletedFlag.NOT_YET_DELETED.getValue());
        if (!AuthUtil.getCurrentUserId().equals(subscription.getCreatedBy()) && !AuthUtil.getCurrentUserId()
            .equals(subscription.getUserId())) {
            String message = messageSource
                .getMessage(MessageKeyConstant.CURRENT_USER_NOT_BE_OWNER, subscriptionMessage,
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SUBSCRIPTION, ErrorKey.ID,
                MessageKeyConstant.CURRENT_USER_NOT_BE_OWNER);
        }
        return subscriptionRepository.getSubscriptionIntegration(subscriptionId);
    }

    @Override
    public Page<SubscriptionSaaSResDTO> getSaaSUser(String search, String developer, SubscriptionStatusEnum status, Integer createdSource,
        Pageable pageable) {
        search = StringUtils.isEmpty(search) ? StringUtils.EMPTY
            : SqlUtils.optimizeSearchLike(search);
        developer = StringUtils.isEmpty(developer) ? StringUtils.EMPTY
            : SqlUtils.optimizeSearchLike(developer);
        return subscriptionRepository.getSaaSList(AuthUtil.getCurrentParentId(), search, developer, status.value, createdSource, pageable);
    }

    @Override
    public Map<String, List<SubscriptionServiceDTO>> getServiceBySubId(Long id) {
        //kiem tra subscription co ton tai hoac chua bi xoa va co phai do chinh SME do tao hay khong
        Long idSme = AuthUtil.getCurrentParentId();
        Subscription subscriptionBySme = subscriptionRepository
            .findByIdAndDeletedFlagAndUserId(id, DeletedFlag.NOT_YET_DELETED.getValue(), idSme).orElseThrow(() -> {
                String msg = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                    new Long[]{id}, LocaleContextHolder.getLocale());
                return new BadRequestException(msg, Resources.SUBSCRIPTION,
                    ErrorKey.Subscription.ID, MessageKeyConstant.NOT_FOUND);
            });
        List<SubscriptionServiceDTO> subscriptionServiceDTOS = subscriptionRepository
            .getServiceBySubId(id);
        Map<String, List<SubscriptionServiceDTO>> map = new HashMap<>();
        map.put("data", subscriptionServiceDTOS);
        return map;
    }

    @Override
    public Map<String, String> getPaymentMethod(Long id) {
        checkSubscriptionByAdminDevSme(id);
        SubscriptionPaymentDTO subscriptionPaymentDTO = subscriptionRepository.getPaymentMethod(id);
        Map<String, String> payment = new HashMap<>();
        payment.put("paymentMethod",
            Objects.isNull(subscriptionPaymentDTO) ? "" : PaymentMethodEnum.valueOf(subscriptionPaymentDTO.getPaymentMethod()).name());
        return payment;
    }

    /**
     * check sub by admin dev sme
     */
    private void checkSubscriptionByAdminDevSme(Long id) {
        Subscription subscription = findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        ServiceEntity service = serviceRepository.getServiceBySubscription(id);
        Combo combo = comboRepository.getComboBySubsId(id);
        Long userOwnerId = Objects.nonNull(service) ? service.getUserId() : combo.getUserId();
        // Báo lỗi nếu người thao tác không phải admin, SME (hoặc nhân viên SME), nhà phát triển (hoặc nhân viên thuộc nhà phát triển)
        CustomUserDetails userDetails = AuthUtil.getLoggedInUser();
        Long userId = userDetails.getId();
        Long parentId = userDetails.getParentId();
        if (!AuthUtil.isAdmin() && // Không phải admin
            !Objects.equals(userId, subscription.getUserId()) && // Không phải SME
            !Objects.equals(parentId, subscription.getUserId()) && // Không phải nhân viên của SME
            (!Objects.equals(userId, userOwnerId)) && // Không phải nhà phát triển sản phẩm
            !Objects.equals(parentId, userOwnerId)) { // Không phải nhân viên thuộc nhà phát triển sản phẩm
            throw exceptionFactory.permissionDenied(ExceptionConstants.NEED_PERMISSION);
        }
    }

    /**
     * tìm kiếm các ghi nhớ của subscription
     */
    @Override
    public Page<SubscriptionCreditNoteResDTO> getAllCreditNotesOfSubscription(Long subscriptionId,
        String code, CreditNoteDetailStatusEnum status, CreditNoteType creditNoteType, String billingCode,
        Date startTime, Date endDate, Pageable pageable, Long isSME,
        Integer subject) {
        checkPermissionUserLoginBySubscriptionId(subscriptionId);
        // set search field default if null
        code = code == null ? CreditNoteConst.SqlVar.STRING_NULL : code;
        Integer statusInt = Objects.isNull(status) ? -1 : status.code;
        Page<SubscriptionSmeCreditNoteDTO> creditNotesPage = creditNoteRepository
            .findAllBySubscriptionId(subscriptionId, code, statusInt, pageable);
        return creditNotesPage.map(this::convertToSubCreditNoteResDTO);
    }

    /**
     * Kiểm tra user đăng nhập có quyền xem subscription
     */
    private Subscription checkPermissionUserLoginBySubscriptionId (Long subscriptionId) {
        Subscription subscription = findByIdAndDeletedFlag(subscriptionId, DeletedFlag.NOT_YET_DELETED.getValue());
        boolean checkUserRole = true;
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT
            .getValue()))) {
            //Nếu là admin thì phải là admin tổng, hoặc admin tỉnh chỉ xem đc credit_note của tỉnh đó
            if (Objects.nonNull(subscription.getUser()) && Objects.nonNull(subscription.getUser().getDepartment()) && Objects.nonNull(subscription.getUser().getDepartment()
                .getProvinceId()) && !Objects.equals(AuthUtil.getDepartment().getProvinceId(), subscription.getUser().getDepartment()
                .getProvinceId())) {
                checkUserRole = false;
            }
            //Nếu là dev thì chỉ xem đc credit_note từ dịch vụ dev đó cung cấp
        } else if (AuthUtil.checkUserRoles(
            Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_BUSINESS.getValue(), RoleType.DEVELOPER_OPERATOR.getValue()))) {
            if (Objects.nonNull(subscription.getPricingId()) && !Objects
                .equals(AuthUtil.getCurrentParentId(), subscription.getPricing().getServiceEntity().getUserId())) {
                checkUserRole = false;
            } else if (Objects.nonNull(subscription.getComboPlanId()) && !Objects
                .equals(AuthUtil.getCurrentParentId(), subscription.getComboPlan().getCombo().getUserId())) {
                checkUserRole = false;
            }
        } else {
            //Nếu là sme thì chỉ xem đc credit_note của DN đó
            if (!Objects.equals(AuthUtil.getCurrentParentId(), subscription.getUserId())) {
                checkUserRole = false;
            }
        }
        if (!checkUserRole) {
            String message = messageSource.getMessage(MessageKeyConstant.USER_NOT_HAVE_PERMISSION, creditNotes, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.CREDIT_NOTE, ErrorKey.CREDIT_NOTE_ID, MessageKeyConstant.USER_NOT_HAVE_PERMISSION);
        }
        return subscription;
    }

    /**
     * Chuyen doi tu object SubscriptionSmeCreditNoteDTO sang object SubscriptionCreditNoteResDTO
     *
     * @return instance of SubscriptionCreditNoteResDTO
     */
    private SubscriptionCreditNoteResDTO convertToSubCreditNoteResDTO(SubscriptionSmeCreditNoteDTO subSmeCreNoteDTO) {

        SubscriptionCreditNoteResDTO creditNoteResDTO = new SubscriptionCreditNoteResDTO();
        creditNoteResDTO.setId(subSmeCreNoteDTO.getId());
        creditNoteResDTO.setCode(subSmeCreNoteDTO.getCode());
        creditNoteResDTO.setBillingCode(subSmeCreNoteDTO.getBillingCode());
        creditNoteResDTO.setMoneyRefund(subSmeCreNoteDTO.getMoneyRefund());
        creditNoteResDTO.setCreatedAt(subSmeCreNoteDTO.getCreatedAt());
        creditNoteResDTO
            .setStatus(Objects.nonNull(subSmeCreNoteDTO.getStatus()) ? CreditNoteDetailStatusEnum.valueOf(subSmeCreNoteDTO.getStatus()) : null);
        creditNoteResDTO
            .setCreditNoteType(Objects.nonNull(subSmeCreNoteDTO.getCreditNoteType()) ? CreditNoteType.valueOf(subSmeCreNoteDTO.getCreditNoteType()) : null);
        creditNoteResDTO.setReason(subSmeCreNoteDTO.getReason());
        creditNoteResDTO.setContent(subSmeCreNoteDTO.getContent());
        return creditNoteResDTO;
    }

    @Override
    public SubscriptionInfoDTO getSubscriptionInfo(Long id) {
        checkSubscriptionByAdminDevSme(id);
        SubscriptionGeneralInformationDTO informationDTO = subscriptionRepository.getSubscriptionInfo(id);
        SubscriptionInfoDTO subscriptionInfoDTO = new SubscriptionInfoDTO();
        BeanUtils.copyProperties(informationDTO, subscriptionInfoDTO);
        int newTrialType = Integer.parseInt(Objects.isNull(informationDTO.getCycleType()) ? "" : informationDTO.getCycleType());
        subscriptionInfoDTO.setCycleType(TimeTypeEnum.valueOf(newTrialType).name());
        return subscriptionInfoDTO;
    }

    @Override
    public SubscriptionOfficialJointScreenDTO getSubscriptionOfficial(Long id) {
        checkSubscriptionByAdminDevSme(id);
        Subscription subscription = findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        SubscriptionOfficialJointScreenDTO jointScreenDTO = subscriptionOfficialMapper.toDto(subscription);
        jointScreenDTO.setRegType(RegTypeEnum.valueOf(subscription.getRegType()).name());
        LocalDate expiredTimeLocal = DateUtil.convertDateToLocalDate(subscription.getExpiredTime()).minusDays(10L);
        Date expiredTimeDate = DateUtil.toDate(expiredTimeLocal);
        if (expiredTimeDate.compareTo(new Date()) < 0) {
            jointScreenDTO.setExtension(true);
        }
        jointScreenDTO.setExtension(false);
        return jointScreenDTO;
    }

    @Override
    public Page<SubscriptionsOrderDTO> getLstOrder(String value, Integer isName, Integer isPhone, Integer isEmail,
        Integer isServiceName, Integer isSubCode, List<Long> lstProvider, Date startDate, Date endDate, Integer progress,
        Integer createSource, List<Long> lstProvince, List<Long> lstMst, String invoiceAddress,
        String address, String assigneeName, PortalType portal, Pageable pageable) {
        Long devId = -1L;
        if (portal.equals(PortalType.DEV)) {
            if (checkPermissionWithSubsOfCurrentUser(AuthUtil.getCurrentUserId())) {
                devId = AuthUtil.getCurrentParentId();
            } else {
                devId = AuthUtil.getCurrentUserId();
            }
        }
        Page<ISubCodePageDTO> subCodePage = subscriptionRepository.getSubCodeOrderService(SqlUtils.optimizeSearchLike(value), isName, isPhone,
            isEmail, isServiceName,
            isSubCode, lstProvider, startDate, endDate, progress, createSource, lstProvince, lstMst, SqlUtils.optimizeSearchLike(invoiceAddress),
            SqlUtils.optimizeSearchLike(address),
            SqlUtils.optimizeSearchLike(assigneeName), devId, portal.name(), pageable);

        List<SubscriptionsOrderITF> dataOrder = subscriptionRepository.getOrderData(
            subCodePage.getContent().stream().map(ISubCodePageDTO::getSubCode).collect(Collectors.toList()));

        Map<String, List<SubscriptionsOrderITF>> map = dataOrder.stream().collect(Collectors.groupingBy(SubscriptionsOrderITF::getSubCode));

        return subCodePage.map(item -> convertDataLstSubOrder(item.getSubCode(), map.get(item.getSubCode())));
    }

    private Boolean checkPermissionWithSubsOfCurrentUser(Long currentUserId) {
        List<String> roles = userRepository.getRole(currentUserId);
        if (!roles.isEmpty()) {
            List<String> permissionName = userRepository.getPermissionByRoleName(roles);
            return !permissionName.isEmpty() &&
                (permissionName.contains(PermissionNameEnum.MANAGE_SUBSCRIPTION.getValue()) ||
                    permissionName.contains(PermissionNameEnum.VIEW_LIST_SUBSCRIPTION.getValue())
                );
        }
        return false;
    }

    private SubscriptionsOrderDTO convertDataLstSubOrder(String subCode, List<SubscriptionsOrderITF> subscriptionsOrderITFS) {
        SubscriptionsOrderDTO responseDTO = new SubscriptionsOrderDTO();
        PortalType portal = AuthUtil.getPortalOfUserRoles();
        if (PortalType.DEV.equals(portal)) { // dev chỉ xem đc sản phẩm thuộc NCC của n
            Long userId = AuthUtil.getCurrentParentId();
            subscriptionsOrderITFS = subscriptionsOrderITFS.stream().filter(item -> item.getProviderId().equals(userId))
                .collect(Collectors.toList());
        }

        if (subscriptionsOrderITFS.isEmpty()) {
            return responseDTO;
        }
        SubscriptionsOrderITF subscriptionsOrderITF = subscriptionsOrderITFS.get(0);
        String provider = subscriptionsOrderITFS.stream().map(SubscriptionsOrderITF::getProviderName).distinct()
            .collect(Collectors.joining(", "));
        List<String> listAssignee = subscriptionsOrderITFS.stream().map(i -> {
            if (Objects.nonNull(i.getAssigneeName()) && !i.getAssigneeName().isEmpty()) {
                return Objects.nonNull(i.getAssigneePhone()) ? i.getAssigneeName().concat(CharacterConstant.HYPHEN_EXTRA)
                    .concat(i.getAssigneePhone()) : i.getAssigneeName();
            }
            return null;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        BeanUtils.copyProperties(subscriptionsOrderITF, responseDTO);
        if (Objects.nonNull(subscriptionsOrderITF.getProgressStatus())) {
            responseDTO.setAllProcessStatusForSubs(StringUtil.convertStringArrToListString(subscriptionsOrderITF.getProgressStatus()));
        }
        SubscriptionsOrderITF progress = subscriptionsOrderITFS.stream().filter(e -> Objects.nonNull(e.getProcessStatus()))
            .min(Comparator.comparing(SubscriptionsOrderITF::getProcessStatus)).orElse(null);
        if (Objects.nonNull(progress)) {
            responseDTO.setProcessStatus(progress.getProcessStatus());
        }
        Integer sizeBillPaid = (int) subscriptionsOrderITFS.stream().filter(i -> Objects.equals(BillStatusEnum.PAID.value, i.getBillingStatus()))
            .count();
        responseDTO.setConfirmPayment(sizeBillPaid.equals(subscriptionsOrderITFS.size()));
        boolean isExportInvoice = subscriptionsOrderITF.getExportInvoice() &&
            subscriptionsOrderITFS.stream().filter(i -> !Objects.equals(BillStatusEnum.PAID.value, i.getStatusBill())).collect(
                Collectors.toList()).isEmpty();
        responseDTO.setExportInvoice(isExportInvoice);
        responseDTO.setPaymentMethod(PaymentMethodEnum.valueOf(subscriptionsOrderITF.getPaymentMethod()));
        responseDTO.setAssigneeName(listAssignee.stream().collect(Collectors.joining(", ")));
        // Lấy thông tin thuê bao
        responseDTO.setSubCode(subCode);
        responseDTO.setProviderName(provider);
        responseDTO.setPricingType(PricingTypeEnum.valueOf(subscriptionsOrderITF.getPricingType()));

        // tính tiền
        BigDecimal totalAmount = subscriptionsOrderITFS.stream().map(SubscriptionsOrderITF::getBillTotal)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        responseDTO.setBillTotal(totalAmount);
        return responseDTO;
    }

    @Override
    public BigDecimal getInputDevicePrice(Long subscriptionId, Long serviceDraftId, Long variantDraftId) {
        var lstInfo = subscriptionSetupFeeRepository.findBySubscriptionId(subscriptionId);
        if (variantDraftId != null) {
            return lstInfo.stream()
                .filter(item -> Objects.equals(item.getObjectType(), SubscriptionSetupFeeTypeEnum.VARIANT.getValue()) &&
                    Objects.equals(item.getObjectId(), variantDraftId))
                .findFirst().orElse(new SubscriptionSetupFee()).getPrice();
        } else {
            return lstInfo.stream()
                .filter(item -> Objects.equals(item.getObjectType(), SubscriptionSetupFeeTypeEnum.DEVICE.getValue()) &&
                    Objects.equals(item.getObjectId(), serviceDraftId))
                .findFirst().orElse(new SubscriptionSetupFee()).getPrice();
        }
    }

    /**
     * Lấy danh sách feature
     */
    @Override
    public List<FeatureReqDTO> getFeatureDetaiolByIds (String listFeatureId) {
        String[] split = StringUtils.split(StringUtils.defaultString(listFeatureId, CharacterConstant.BLANK), CharacterConstant.COMMA);
        String[] featureArr = StringUtils.stripAll(split);
        List<Long> featureIds = Arrays.stream(featureArr)
                .map(Long::valueOf).collect(Collectors.toList());
        List<FeatureReqDTO> outFeatures = new ArrayList<>();
        featureRepository.findAllById(featureIds).forEach(feature -> {
            FeatureReqDTO feat = FeatureReqDTO.builder()
                    .id(feature.getId())
                    .code(feature.getCode())
                    .name(feature.getName())
                    .serviceId(feature.getServiceId())
                    .description(feature.getDescription())
                    .type(feature.getType())
                    .fileId(feature.getFileId())
                    .baseId(feature.getBaseId())
                    .priorityOrder(feature.getPriorityOrder())
                    .icon(feature.getIcon())
                    .build();
            if(feat.getFileId() !=null){
                Optional<FileAttach> fileAttach = fileAttachRepository.findById(feat.getFileId());
                if(fileAttach.isPresent()){
                    FileAttach f = fileAttach.get();
                    feat.setFilePath(f.getFilePath());
                    feat.setExternalLink(f.getExternalLink());
                    feat.setFileName(f.getFileName());
                }
            }
            outFeatures.add(feat);
        });
        outFeatures.sort(Comparator.comparing(item -> featureIds.indexOf(item.getId())));
        return outFeatures;
    }
}
