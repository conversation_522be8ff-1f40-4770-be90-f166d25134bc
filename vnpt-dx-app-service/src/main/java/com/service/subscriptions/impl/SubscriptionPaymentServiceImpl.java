package com.service.subscriptions.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.constant.OrderConstant;
import com.constant.PaymentConstant.MerchantData;
import com.constant.SubscriptionConstant;
import com.constant.SubscriptionConstant.MailParams;
import com.constant.SystemParamConstant;
import com.constant.enums.common.KeyGenType;
import com.constant.enums.subscription.OrderStatusEnum;
import com.constant.enums.subscription.StatusOrderEnum;
import com.constant.enums.transactionLog.ActivityCodeEnum;
import com.constant.enums.transactionLog.TransactionLogStatusEnum;
import com.dto.contracts.ResponseEcontractDTO;
import com.dto.contracts.ResponseEcontractSSODTO;
import com.dto.payment.ClueDTO;
import com.dto.payment.PaymentInitReqDTO;
import com.dto.payment.PaymentInitResDTO;
import com.dto.payment.QRConfig;
import com.dto.payment.QRCreateReqDTO;
import com.dto.payment.QRCreateResDTO;
import com.dto.quotation.ChangeStatusMetadataDTO;
import com.dto.subscriptions.RegisterInfoDTO;
import com.dto.subscriptions.SmartCAPopupDTO;
import com.dto.subscriptions.SubscriptionRegisterDTO;
import com.dto.subscriptions.SubscriptionRegisterResDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.subscriptions.register.IGetSubRegisterInfo;
import com.dto.transaction_log.CommonActivityLogInfoDTO;
import com.entity.bills.Bills;
import com.entity.combo.ComboPlan;
import com.entity.contracts.Econtract;
import com.entity.payment.VNPTPayResponse;
import com.entity.pricing.Pricing;
import com.entity.quotation.QuotationStatusEnum;
import com.entity.subscriptions.BatchKafka;
import com.entity.subscriptions.Subscription;
import com.entity.transaction_log.ActivityLog;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.billings.BillStatusEnum;
import com.onedx.common.constants.enums.integration.PartnerCallAPI;
import com.onedx.common.constants.enums.integration.backend.AccessTradeStatusEnum;
import com.onedx.common.constants.enums.integration.backend.IntegrationActionTypeEnum;
import com.onedx.common.constants.enums.integration.backend.MasOfferStatusEnum;
import com.onedx.common.constants.enums.migration.RepeatTypeEnum;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionConfirmStatusEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.constants.values.SubscriptionHistoryConstant.ContentType;
import com.onedx.common.entity.subscriptions.SubscriptionHistory;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.VNPTPayResponseException;
import com.onedx.common.repository.subscriptions.SubscriptionHistoryRepository;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.HttpRestUtil;
import com.onedx.common.utils.ObjectMapperUtil;
import com.onedx.common.utils.ObjectUtil;
import com.repository.bills.BillsRepository;
import com.repository.contracts.EcontractRepository;
import com.repository.currency.CurrencyRepository;
import com.repository.payment.VNPTPayResponseRepository;
import com.repository.subscriptions.BatchKafkaRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.service.bills.BillsService;
import com.service.common.CommonService;
import com.service.contracts.EcontractService;
import com.service.creditNote.CreditNoteService;
import com.service.integrate.IntegrationService;
import com.service.integrate.InventoryService;
import com.service.integrated.ExecutiveProducerService;
import com.service.orderServiceReceive.OrderServiceReceiveService;
import com.service.payment.impl.PaymentService;
import com.service.preorder.PreOrderService;
import com.service.quotation.QuotationService;
import com.service.rating.ServiceReactionService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.subscriptions.SubscriptionDetailService;
import com.service.subscriptions.SubscriptionHistoryService;
import com.service.subscriptions.SubscriptionNotificationService;
import com.service.subscriptions.SubscriptionPaymentService;
import com.service.subscriptions.SubscriptionRegisterService;
import com.service.system.param.SystemParamService;
import com.service.transactionLog.ActivityLogService;
import com.util.AuthUtil;
import com.util.CheckUtil;
import static com.constant.SystemParamConstant.CONFIG_TIME_QR;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionPaymentServiceImpl implements SubscriptionPaymentService {

    private final SubscriptionRepository subscriptionRepository;
    private final CurrencyRepository currencyRepository;

    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private BillsRepository billsRepository;
    @Autowired
    private VNPTPayResponseRepository vnptPayResponseRepository;
    @Autowired
    private HttpRestUtil httpUtil;
    @Autowired
    private SubscriptionHistoryRepository subscriptionHistoryRepository;
    @Autowired
    private PreOrderService preOrderService;
    @Autowired
    private ExecutiveProducerService executiveProducerService;
    @Autowired
    private QRConfig qrConfig;
    @Autowired
    private SubscriptionRegisterService subscriptionRegisterService;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private SubscriptionDetailService subscriptionDetailService;
    @Autowired
    private OrderServiceReceiveService orderServiceReceiveService;
    @Autowired
    private BillsService billsService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ServiceReactionService serviceReactionService;
    @Autowired
    private CreditNoteService creditNoteService;
    @Autowired
    private EcontractRepository eContractRepository;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private BatchKafkaRepository batchKafkaRepository;
    @Autowired
    private ActivityLogService activityLogService;
    @Autowired
    private SubscriptionHistoryService subscriptionHistoryService;
    @Autowired
    private SubscriptionNotificationService subscriptionNotificationService;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private QuotationService quotationService;

    @Value("${dhsxkd.header.token.key}")
    private String tokenKey;
    @Value("${dhsxkd.header.token.value}")
    private String tokenValue;
    @Value("${dhsxkd.header.acc.key}")
    private String accKey;
    @Value("${dhsxkd.header.acc.value}")
    private String accValue;
    @Value("${dhsxkd.header.pwd.key}")
    private String pwdKey;
    @Value("${dhsxkd.header.pwd.value}")
    private String pwdValue;
    @Value(("${dhsxkd.timeout}"))
    private int timeout;
    @Value(value = "${web.host}")
    private String webHost;
    @Value("${transaction.merchant_service_id}")
    private String merchantServiceId;
    @Value("${transaction.init_url}")
    private String initUrl;
    @Value("${transaction.token}")
    private String token;

    /**
     * Thực hiện các tiến trình thanh toán/ kích hoạt tiến trình cài đặt/ lưu lịch sử .. cho thuê bao
     */
    @Override
    public SubscriptionRegisterResDTO paymentExec(RegisterInfoDTO infoDTO, SubscriptionFormulaResDTO formulaResDTO, Subscription subscription,
        Bills bills, CommonActivityLogInfoDTO activityLogInfoDTO) {
        log.info("START paymentExec for sub id = {}" , subscription.getId());
        // Lấy thông tin từ DTO
        SubscriptionRegisterDTO registerDTO = infoDTO.getRegisterDTO();
        Pricing pricing = infoDTO.getPricing();
        ComboPlan comboPlan = infoDTO.getComboPlan();
        String ipAddress = infoDTO.getIpAddress();
        PortalType portalType = infoDTO.getPortalType();
        Integer serviceOwner = infoDTO.getServiceOwner();
        Boolean allowCallApiOrderService = infoDTO.getAllowCallApiOrderService();
        CalculateTypeEnum calculateTypeEnum = infoDTO.getCalculateTypeEnum();

        SubscriptionRegisterResDTO result = null;
        boolean isPrepay = false;
        // Với dịch vụ ON trả sau đky với thanh toán VNPT thì bỏ qua bước tạo link thanh toán, cho phép đăng ký thành công luôn (update 05/09)
        Integer pricingType = Objects.nonNull(pricing) ? pricing.getPricingType() : comboPlan.getComboPlanType();
        boolean paymentWithVnptPay;
        if (infoDTO.hasPricing()) {
            // Nếu gói cước thanh toán một lần HOẶC
            // Nếu dịch vụ thanh toán bằng VNPTPay, gói thanh toán trả trước
            paymentWithVnptPay = ((Objects.equals(pricing.getIsOneTime(), RepeatTypeEnum.ONCE.getValue())) && CheckUtil.checkIsONService(serviceOwner)) ||
                (Arrays.asList(PaymentMethodEnum.VNPTPAY, PaymentMethodEnum.VNPTPAY_QR).contains(registerDTO.getPaymentMethod()) &&
                    Objects.equals(pricing.getPricingType(), PricingTypeEnum.PREPAY.value));
        } else {
            // Nếu combo thanh toán bằng VNPTPay, gói thanh toán trả trước
            paymentWithVnptPay = Arrays.asList(PaymentMethodEnum.VNPTPAY, PaymentMethodEnum.VNPTPAY_QR).contains(registerDTO.getPaymentMethod()) &&
                Objects.equals(comboPlan.getComboPlanType(), PricingTypeEnum.PREPAY.value);
        }
        if (paymentWithVnptPay) {
            subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
            subscriptionRepository.save(subscription);
            bills.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
            bills.setStatus(BillStatusEnum.INVALID.value);
            creditNoteService.saveToChangeCreditNote(bills.getId(), formulaResDTO.getCreditNoteListNew());
            billsRepository.save(bills);
            subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);

            // Lưu lịch sử đăng ký thuê bao
            String histContent = SubscriptionHistoryConstant.ORDER_SUCCESS;
            Integer histType = ContentType.ORDER_DEVICE;
            addSubscriptionHistory(subscription.getId(), histContent, bills.getId(), histType);

            result = paymentWithVNPTPay(ipAddress, subscription, bills, token, false, pricing, comboPlan,
                    formulaResDTO.getTotalAmountAfterRefund(), portalType, activityLogInfoDTO.getActionType(),
                    activityLogInfoDTO, registerDTO.getPaymentMethod());
            isPrepay = true;
        }
        subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);

        if (registerDTO.getPaymentMethod().value != PaymentMethodEnum.VNPTPAY.value && Objects.equals(PricingTypeEnum.PREPAY.value,
            pricingType)) {
            subscription.setOs3rdStatus((long) StatusOrderEnum.WAITING_CONFIRM.status);
        } else if (!Objects.equals(PricingTypeEnum.PREPAY.value, pricingType)) {
            subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);
        }
        // Goi api confirm của marketing campaign
        subscriptionRegisterService.confirmAppliedMc(infoDTO.getCurrentParentId(), subscription.getId(), bills.getId(), isPrepay, formulaResDTO,
            calculateTypeEnum);
        subscription = subscriptionRepository.save(subscription);

        // Tính toán credit note
        if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew()))
            subscriptionFormula.calculateCreditNote(subscription, bills, formulaResDTO.getCreditNoteListNew(), null);
        if (Objects.nonNull(registerDTO.getPreOrderId())) {
            preOrderService.deleteById(registerDTO.getPreOrderId());
        }
        // Tạo order service trên DHSXKD, đồng thời tạo item order_service_receive
        if (allowCallApiOrderService) {
            orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, pricing, comboPlan, activityLogInfoDTO);
        }
        // call postback sang massoffer với trạng thái pending khi mới tạo sub
        if (StringUtils.isNotBlank(subscription.getTrafficId())) {
            if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) {
                integrationService.sendPostbackAccesstrade(subscription, subscription.getId().toString());
                integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.PENDING.value, null);
                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
            } else if (Objects.equals(SubscriptionConstant.APINFO, subscription.getTrafficSource())) {
                integrationService.sendPostbackApinfo(subscription, subscription.getTrafficId(), AccessTradeStatusEnum.PENDING.value, null);
                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
            } else {
                integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(), MasOfferStatusEnum.PENDING.value, null);
                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
            }

        }
        if (result != null) {
            String histContent = SubscriptionHistoryConstant.CREATE_ACTUAL_SUBSCRIPTION;
            Integer histType = ContentType.CREATE_ACTUAL_SUBSCRIPTION;
            addSubscriptionHistory(subscription.getId(), formatSubHistoryContent(subscription, histContent, histType), bills.getId(), histType);
            serviceReactionService.successRegister(pricing, comboPlan);
            log.info("==== SubscriptionServiceImpl: END paymentExec 1 for sub id = {} - paymentWithVnptPay is true ====", subscription.getId());

            return result;
        }

        // Kích hoạt quá trình cài đặt thuê bao
        Subscription finalSubscription = subscription;
        Long finalSubscriptionId = finalSubscription.getId();

        // Gọi sang DHSXKD
        if (Objects.isNull(bills.getActionType()) || Objects.equals(-1, bills.getActionType())) {
            executiveProducerService.callDHSXWhenSub(subscription, subscription.getUserId(), pricing, comboPlan, activityLogInfoDTO);
        }
        // Lưu lịch sử đăng ký thuê bao
        String histContent = SubscriptionHistoryConstant.CREATE_ACTUAL_SUBSCRIPTION;
        Integer histType = ContentType.CREATE_ACTUAL_SUBSCRIPTION;
        addSubscriptionHistory(finalSubscriptionId, formatSubHistoryContent(finalSubscription, histContent, histType), bills.getId(), histType);
        // Gửi thông báo khi đăng ký thuê bao
        subscriptionNotificationService.sendNotifySubForSmeDevOrAdmin(subscription, pricing, comboPlan, MailParams.SME);
        // Gửi thông báo khuyến mãi sản phẩm
        paymentService.sendMailPromotion(subscription.getUserId(), subscription.getId());
        // Lưu thông tin dịch vụ đã mua
        serviceReactionService.successRegister(pricing, comboPlan);
        // Tích hợp e-contract
        if(!CheckUtil.checkIsONService(serviceOwner)){
            SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.AUTO_ECONTRACT);
            Econtract econtract = eContractRepository.findFirstByIdSubscriptionOrderByIdDesc(subscription.getId());
            if(econtract == null && systemParam != null && systemParam.getEcontractOn() == 1){
                try {
                    econtractService.createElectronicContractAuto(subscription.getId());
                } catch (Exception e) {
                    log.error("paymentExec: {}", e.getMessage(), e);
                }
            }
        }

        // lưu lịch sử đặt hàng thành công
        String histContentFinal = SubscriptionHistoryConstant.ORDER_SUCCESS;
        Integer histTypeFinal = ContentType.ORDER_DEVICE;
        addSubscriptionHistory(subscription.getId(), histContentFinal, bills.getId(), histTypeFinal);

        // Mua off bên oneSMe chỉ log đơn hàng đang đóng gói, chờ xác nhận thanh toán mới log : Thanh toán thành công
        if (registerDTO.getPaymentMethod().value != PaymentMethodEnum.VNPTPAY.value) {
            // log đơn hàng đang đóng gói
            if (ObjectUtil.getOrDefault(subscription.getOs3rdStatus(), -1L).intValue() == StatusOrderEnum.PREPARING.status) {
                addSubscriptionHistory(subscription.getId(), SubscriptionHistoryConstant.PACKING_SUBSCRIPTION, ContentType.PACKING_SUBSCRIPTION);
            }
        }

        // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
        inventoryService.onSubscriptionRegistered(Collections.singletonList(subscription));
        // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
        if (subscription.getQuotationId() != null) {
            quotationService.changeStatus(Collections.singleton(subscription.getQuotationId()), QuotationStatusEnum.ORDER, PortalType.UNSET,
                new ChangeStatusMetadataDTO(subscription.getCode()));
        }
        log.info("END paymentExec 2 for sub id = {}", subscription.getId());
        return SubscriptionRegisterResDTO.builder()
            .id(subscription.getId())
            .subCode(subscription.getSubCode())
            .isON(infoDTO.getIsON())
            .build();
    }

    /**
     * Payment with vnpt pay subscription register res dto.
     *
     * @param ipAddress      the request
     * @param subscription the subscription
     *
     * @return the subscription register res dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SubscriptionRegisterResDTO paymentWithVNPTPay(String ipAddress, Subscription subscription, Bills bills,
        String token, boolean isBillPayment, Pricing pricing, ComboPlan comboPlan, BigDecimal totalAmount, PortalType portalType,
        IntegrationActionTypeEnum integrationActionType, CommonActivityLogInfoDTO activityLogInfoDTO, PaymentMethodEnum paymentMethod) {
        log.info("paymentWithVNPTPay: start, subscriptionId {}, totalAmount {}", subscription.getId(), totalAmount);
        SubscriptionRegisterResDTO responseDTO = new SubscriptionRegisterResDTO();
        responseDTO.setId(subscription.getId());
        responseDTO.setBillId(bills.getId());
        // Trường hợp số tiền cần thanh toán > 0 (cần tiến hành thanh toán)
        if (Objects.nonNull(totalAmount) && BigDecimal.ZERO.compareTo(totalAmount) < 0 ) {
            // tao order moi
            ClueDTO clueDTO = subscriptionDetailService.getMerchantInfo(subscription);
            VNPTPayResponse vnptPayResponse = createNewOrder(totalAmount, subscription, bills.getId(), portalType, paymentMethod, clueDTO);

            // neu tao thanh cong thi khoi tao thong tin thanh toán vnpt pay
            boolean isInitSuccess;
            Object VNPTPayResponseException;
            if (paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR)) {
                QRCreateResDTO initResponse = initPaymentQR(vnptPayResponse, subscription, activityLogInfoDTO, clueDTO);
                VNPTPayResponseException = initResponse;
                isInitSuccess = Objects.nonNull(initResponse) &&
                        initResponse.getResponseCode().equals(SubscriptionConstant.VNPTPayQRResponseCode.SUCCESS);
                if (isInitSuccess) {
                    vnptPayResponse.setDescription(initResponse.getDescription());
                    //
                    responseDTO.setQrcodeImage(initResponse.getQrcodeImage());
                    SystemParam systemParam = systemParamService.findByParamType(CONFIG_TIME_QR);
                    Long expTimeQR = systemParam.getParamValueLong();
                    responseDTO.setExpirationTime(expTimeQR);
                }
            } else {
                PaymentInitResDTO initResponse = initPayment(vnptPayResponse, ipAddress, subscription, activityLogInfoDTO);
                VNPTPayResponseException = initResponse;
                isInitSuccess = Objects.nonNull(initResponse) && initResponse.getResponse_code().equals(SubscriptionConstant.INIT_SUCCESS);
                if (isInitSuccess) {
                    vnptPayResponse.setDescription(initResponse.getDescription());
                    vnptPayResponse.setSecureCode(initResponse.getSecure_code());
                    //
                    responseDTO.setRedirectURL(initResponse.getRedirect_url());
                }
            }
            //
            if (isInitSuccess) {
                //Nếu không phải là bill payment thì update confirm status
                if (!isBillPayment) {
                    //update confirm status subscription thanh confirm
                    if (!Objects.equals(subscription.getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value)){
                        subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                        subscriptionRepository.save(subscription);
                    }
                    if (!Objects.equals(bills.getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value)) {
                        bills.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                        billsRepository.save(bills);
                    }
                }

                //update order vnpt pay
                vnptPayResponse.setResponseCode(SubscriptionConstant.INIT_SUCCESS);
                vnptPayResponse.setOrderStatus(OrderStatusEnum.PROCESSING.value);
                //bổ sung token lưu lại trong bảng này, hỗ trợ tích hợp IDC Portal
                if (AuthUtil.getCurrentUser() != null && AuthUtil.getCurrentUser().getBearerToken() != null) {
                    vnptPayResponse.setAccessToken(AuthUtil.getCurrentUser().getBearerToken());
                }
                vnptPayResponseRepository.save(vnptPayResponse);
                // update pay response id cho transaction logs
                if(Objects.nonNull(activityLogInfoDTO) && Objects.nonNull(activityLogInfoDTO.getTransactionLogDB())) {
                    activityLogInfoDTO.getTransactionLogDB().setVnptPayResponseId(vnptPayResponse.getId());
                }
                commonService.saveVNPTPayToken(token, KeyGenType.TOKEN_PAYMENT);
                log.info("paymentWithVNPTPay: end, subscriptionId {}, redirectUrl {}", subscription.getId(), responseDTO.getRedirectURL());
                return responseDTO;
            } else {
                // Init với VNPT Pay không thành công
                throw new VNPTPayResponseException(VNPTPayResponseException);
            }
        }
        /*
         * Không phát sinh tiền
         */
        if (Objects.nonNull(totalAmount) && BigDecimal.ZERO.compareTo(totalAmount) == 0) {
            subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
            subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
            // Ngày bắt đầu sử dụng > Ngày hiện tại => status = FUTURE
            if (Period.between(LocalDate.now(), DateUtil.toLocalDate(subscription.getStartedAt())).getDays() > 0) {
                subscription.setStatus(SubscriptionStatusEnum.FUTURE.value);
            }
            Subscription subscriptionFinal = subscriptionRepository.save(subscription);
            // Lưu lịch sử thay đổi trạng thái thuê bao
            subscriptionHistoryService.saveStatusHistory(subscriptionFinal);

            // Lưu thông tin hóa đơn
            bills.setStatus(BillStatusEnum.PAID.value);
            if (Objects.isNull(bills.getPaymentDate())) {
                bills.setPaymentDate(new Date());
            }
            bills.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
            billsRepository.save(bills);

            // call dhsxkd
            if (Objects.isNull(bills.getActionType()) || Objects.equals(-1, bills.getActionType())) {
                executiveProducerService.callDHSXWhenSub(subscription, subscription.getUserId(), pricing, comboPlan, activityLogInfoDTO);
            }

            // Tích hợp SmartCA với các dịch vụ được cấu hình
            boolean enableSmartCA = subscriptionDetailService.checkEnableSmartCA(subscription.getId());
            if (enableSmartCA && Objects.equals(IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, integrationActionType)) {
                ResponseEcontractDTO contractDTO = econtractService.createElectronicContractDraft(subscription.getId(), activityLogInfoDTO);
                //lay access token
                SmartCAPopupDTO smartCAPopupDTO = new SmartCAPopupDTO(contractDTO);
                if(contractDTO !=null && contractDTO.getObject().getContractId() !=null){
                    ResponseEcontractSSODTO sso = econtractService.getTokenSSO(null, null);
                    if(sso !=null && sso.getObject() !=null){
                        smartCAPopupDTO.setAccessToken(sso.getObject().getAccessToken());
                    }
                }
                log.info("paymentWithVNPTPay: end, subscriptionId {}, smartCAPopupDTO {}",
                    subscription.getId(), ObjectMapperUtil.toJsonString(smartCAPopupDTO));
                return SubscriptionRegisterResDTO.builder().id(subscription.getId()).smartCAPopupDTO(smartCAPopupDTO).build();
            } else { // Với các dịch vụ không tích hợp SmartCA
                // Lưu thông tin dùng để gọi Kafka sang backend SPDV bằng batch -> tránh TH chưa save sub đã gọi SPDV
                Integer actionType = Objects.isNull(bills.getActionType()) || bills.getActionType() == -1 ? 0 : bills.getActionType();
                BatchKafka batchKafka = new BatchKafka(null, subscription.getId(),actionType , AuthUtil.getCurrentUser().getBearerToken(),
                    DeletedFlag.NOT_YET_DELETED.getValue(), activityLogInfoDTO.getTransactionLogDB().getId());
                batchKafkaRepository.save(batchKafka);
//                if (Objects.nonNull(pricing) && !Objects.equals(IntegrationActionTypeEnum.CHANGE_PLAN, integrationActionType)) {
//                    log.info("paymentWithVnptPay: transactionOneSME, subscriptionId {}", subscriptionFinal.getId());
//                    integrationService.transactionOneSME(token, subscriptionFinal, pricing,
//                        integrationActionType, null, null, null, false, activityLogInfoDTO);
//                } else if (Objects.nonNull(comboPlan) && !Objects.equals(IntegrationActionTypeEnum.CHANGE_PLAN, integrationActionType)) {
//                    log.info("paymentWithVnptPay: transactionOneSMECombo, subscriptionId {}", subscriptionFinal.getId());
//                    integrationService.transactionOneSMECombo(token, subscriptionFinal, comboPlan,
//                        integrationActionType, null, null, null, null, activityLogInfoDTO);
//                }
            }
            // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
            inventoryService.onSubscriptionRegistered(Collections.singletonList(subscription));
            // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
            if (subscription.getQuotationId() != null) {
                quotationService.changeStatus(Collections.singleton(subscription.getQuotationId()), QuotationStatusEnum.ORDER, PortalType.UNSET,
                    new ChangeStatusMetadataDTO(subscription.getCode()));
            }
        }
        // Gửi thông báo khi đăng ký thuê bao 0 đồng
        subscriptionNotificationService.sendNotifySubForSmeDevOrAdmin(subscription, pricing, comboPlan, MailParams.SME);

        log.info("paymentWithVNPTPay: end, subscriptionId {}", subscription.getId());
        return SubscriptionRegisterResDTO.builder().id(subscription.getId()).build();
    }

    /**
     * Thực hiện các tiến trình thanh toán/ kích hoạt tiến trình cài đặt/ lưu lịch sử .. cho thuê bao được tặng
     */
    @Override
    public void paymentGiveAwayExec(RegisterInfoDTO infoDTO, SubscriptionFormulaResDTO formulaResDTO, Subscription subscription,
        Bills bills, CommonActivityLogInfoDTO activityLogInfoDTO) {
        Subscription mainSub = infoDTO.getGiveAwayMainSubscription();
        if (mainSub == null) { // thuê bao chính
            paymentExec(infoDTO, formulaResDTO, subscription, bills, activityLogInfoDTO);
        } else { // thuê bao được tặng
            // Lấy thông tin từ DTO
            SubscriptionRegisterDTO registerDTO = infoDTO.getRegisterDTO();
            Pricing pricing = infoDTO.getPricing();
            ComboPlan comboPlan = infoDTO.getComboPlan();
            Integer serviceOwner = infoDTO.getServiceOwner();
            Boolean allowCallApiOrderService = infoDTO.getAllowCallApiOrderService();

            // Cập nhật thông tin thuê bao theo thông tin thuê bao của sub chính
            subscription.setConfirmStatus(mainSub.getConfirmStatus());
            subscription = subscriptionRepository.save(subscription);
            if (!Objects.equals(bills.getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value)) {
                bills.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                bills.setStatus(BillStatusEnum.INVALID.value);
                billsRepository.save(bills);
            }

            //Tính toán credit note
            if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew()))
                subscriptionFormula.calculateCreditNote(subscription, bills, formulaResDTO.getCreditNoteListNew(), null);
            if (Objects.nonNull(registerDTO.getPreOrderId())) {
                preOrderService.deleteById(registerDTO.getPreOrderId());
            }
            //=====================HiepNT Call API tiepnhan_yc_shop===================//
            if (allowCallApiOrderService) {
                orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, pricing, comboPlan, activityLogInfoDTO);
            }
            // Nếu sub chính chưa được thanh toán, kết thúc xử lý payment
            if (mainSub.getConfirmStatus() == SubscriptionConfirmStatusEnum.WAITING.value) {
                String histContent = SubscriptionHistoryConstant.CREATE_ACTUAL_SUBSCRIPTION;
                Integer histType = ContentType.CREATE_ACTUAL_SUBSCRIPTION;
                addSubscriptionHistory(subscription.getId(),
                    formatSubHistoryContent(subscription, histContent, histType), bills.getId(), histType);
                serviceReactionService.successRegister(pricing, comboPlan);
                return;
            }

            // Gửi yêu cầu cài đặt
            Subscription finalSubscription = subscription;
            if (subscription.getConfirmStatus().equals(SubscriptionConfirmStatusEnum.CONFIRM.value)) {
                if (Objects.nonNull(subscription.getPricingId())) {
                    log.info("paymentGiveAwayExec: transactionOneSME, subscriptionId {}", finalSubscription.getId());
                    integrationService.transactionOneSME(token, finalSubscription, pricing,
                        IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, null, null, null, false,
                        activityLogInfoDTO);
                } else {
                    log.info("paymentGiveAwayExec: transactionOneSMECombo, subscriptionId {}", finalSubscription.getId());
                    integrationService.transactionOneSMECombo(token, finalSubscription, comboPlan,
                        IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, null, null, null, null,
                        activityLogInfoDTO);
                }
                // gui mail hoa don
                billsService.sendBillMail(bills.getId());
            }
            // call dhsxkd
            if (Objects.isNull(bills.getActionType()) || Objects.equals(-1, bills.getActionType())) {
                executiveProducerService.callDHSXWhenSub(subscription, subscription.getUserId(), pricing, comboPlan, activityLogInfoDTO);
            }

            // call postback sang massoffer với trạng thái pending khi mới tạo sub
            if (StringUtils.isNotBlank(subscription.getTrafficId())) {
                if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) {
                    integrationService.sendPostbackAccesstrade(subscription, subscription.getId().toString());
                    integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.PENDING.value, null);
                    subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                } else if (Objects.equals(SubscriptionConstant.APINFO, subscription.getTrafficSource())) {
                    integrationService.sendPostbackApinfo(subscription, subscription.getTrafficId(), AccessTradeStatusEnum.PENDING.value, null);
                    subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                } else {
                    integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(),
                        MasOfferStatusEnum.PENDING.value, null);
                    subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                }

            }

            String histContent = SubscriptionHistoryConstant.CREATE_ACTUAL_SUBSCRIPTION;
            Integer histType = ContentType.CREATE_ACTUAL_SUBSCRIPTION;
            addSubscriptionHistory(finalSubscription.getId(),
                formatSubHistoryContent(finalSubscription, histContent, histType), bills.getId(), histType);


            // Gửi thông báo khi đky mới
            subscriptionNotificationService.sendNotifySubForSmeDevOrAdmin(subscription, pricing, comboPlan, MailParams.SME);
            serviceReactionService.successRegister(pricing, comboPlan);
            if(!CheckUtil.checkIsONService(serviceOwner)){
                SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.AUTO_ECONTRACT);
                Econtract econtract = eContractRepository.findFirstByIdSubscriptionOrderByIdDesc(subscription.getId());
                if(econtract == null && systemParam != null && systemParam.getEcontractOn() == 1){
                    try {
                        econtractService.createElectronicContractAuto(subscription.getId());
                    } catch (Exception e) {
                        log.error("paymentGiveAwayExec: {}", e.getMessage(), e);
                    }
                }
            }
        }
    }


    @Override
    public SubscriptionRegisterResDTO paymentExecOnlyService(RegisterInfoDTO infoDTO, SubscriptionFormulaResDTO formulaResDTO, Subscription subscription,
        Bills bills, CommonActivityLogInfoDTO activityLogInfoDTO) {
        // Lấy thông tin từ DTO
        SubscriptionRegisterDTO registerDTO = infoDTO.getRegisterDTO();
        PortalType portalType = infoDTO.getPortalType();
        Boolean allowCallApiOrderService = infoDTO.getAllowCallApiOrderService();

        SubscriptionRegisterResDTO result = null;
        // Với dịch vụ ON trả sau đky với thanh toán VNPT thì bỏ qua bước tạo link thanh toán, cho phép đăng ký thành công luôn (update 05/09)
        boolean paymentWithVnptPay = Arrays.asList(PaymentMethodEnum.VNPTPAY, PaymentMethodEnum.VNPTPAY_QR).contains(registerDTO.getPaymentMethod());

        if (paymentWithVnptPay) {
            subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
            subscriptionRepository.save(subscription);
            bills.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
            bills.setStatus(BillStatusEnum.INVALID.value);
            creditNoteService.saveToChangeCreditNote(bills.getId(), formulaResDTO.getCreditNoteListNew());
            billsRepository.save(bills);

            result = paymentWithVNPTPay(infoDTO.getIpAddress(), subscription, bills, token, false, null,
                null, formulaResDTO.getTotalAmountAfterRefund(), portalType, activityLogInfoDTO.getActionType(),
                    activityLogInfoDTO, registerDTO.getPaymentMethod());
            subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);

            // Lưu lịch sử đăng ký thuê bao
            String histContent = SubscriptionHistoryConstant.ORDER_SUCCESS;
            Integer histType = ContentType.ORDER_DEVICE;
            addSubscriptionHistory(subscription.getId(), histContent, bills.getId(), histType);

        }
        subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);
        // Goi api confirm của marketing campaign
        subscription = subscriptionRepository.save(subscription);

        // Tính toán credit note
        if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew()))
            subscriptionFormula.calculateCreditNote(subscription, bills, formulaResDTO.getCreditNoteListNew(), null);
        if (Objects.nonNull(registerDTO.getPreOrderId())) {
            preOrderService.deleteById(registerDTO.getPreOrderId());
        }
        // Tạo order service trên DHSXKD, đồng thời tạo item order_service_receive
        if (allowCallApiOrderService) {
            orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, null, null, activityLogInfoDTO);
        }
        // call postback sang massoffer với trạng thái pending khi mới tạo sub
        if (StringUtils.isNotBlank(subscription.getTrafficId())) {
            if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) {
                integrationService.sendPostbackAccesstrade(subscription, subscription.getId().toString());
                integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.PENDING.value, null);
                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
            } else if (Objects.equals(SubscriptionConstant.APINFO, subscription.getTrafficSource())) {
                integrationService.sendPostbackApinfo(subscription, subscription.getTrafficId(), AccessTradeStatusEnum.PENDING.value, null);
                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
            } else {
                integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(), MasOfferStatusEnum.PENDING.value, null);
                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
            }

        }
        if (result != null) {
            String histContent = SubscriptionHistoryConstant.CREATE_ACTUAL_SUBSCRIPTION;
            Integer histType = ContentType.CREATE_ACTUAL_SUBSCRIPTION;
            addSubscriptionHistory(subscription.getId(), formatSubHistoryContent(subscription, histContent, histType), bills.getId(), histType);
//            serviceReactionService.successRegister(pricing, comboPlan);
            return result;
        }

        // Kích hoạt quá trình cài đặt thuê bao
        Subscription finalSubscription = subscription;
        Long finalSubscriptionId = finalSubscription.getId();
        if (subscription.getConfirmStatus().equals(SubscriptionConfirmStatusEnum.CONFIRM.value)) {
            if (Objects.nonNull(subscription.getPricingId())) {
                log.info("paymentExec: transactionOneSME, subscriptionId {}", finalSubscriptionId);
                integrationService.transactionOneSME(token, finalSubscription, new Pricing(),
                    IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, null, null, null, false,
                    activityLogInfoDTO);
            } else {
                log.info("paymentExec: transactionOneSMECombo, subscriptionId {}", finalSubscriptionId);
                integrationService.transactionOneSMECombo(token, finalSubscription, new ComboPlan(),
                    IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, null, null, null, null,
                    activityLogInfoDTO);
            }
            // Gửi mail hóa đơn
            billsService.sendBillMail(bills.getId());
        }
        // Gọi sang DHSXKD
        if (Objects.isNull(bills.getActionType()) || Objects.equals(-1, bills.getActionType())) {
            executiveProducerService.callDHSXWhenSub(subscription, subscription.getUserId(), null, null, activityLogInfoDTO);
        }
        // Gửi thông báo + mail khuyến mãi sản phẩm
        paymentService.sendMailPromotion(subscription.getUserId(), subscription.getId());
        // Lưu lịch sử đăng ký thuê bao
        String histContent = SubscriptionHistoryConstant.CREATE_ACTUAL_SUBSCRIPTION;
        Integer histType = ContentType.CREATE_ACTUAL_SUBSCRIPTION;
        addSubscriptionHistory(finalSubscriptionId, formatSubHistoryContent(finalSubscription, histContent, histType), bills.getId(), histType);

        // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
        inventoryService.onSubscriptionRegistered(Collections.singletonList(subscription));
        // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
        if (subscription.getQuotationId() != null) {
            quotationService.changeStatus(Collections.singleton(subscription.getQuotationId()), QuotationStatusEnum.ORDER, PortalType.UNSET,
                new ChangeStatusMetadataDTO(subscription.getCode()));
        }
        // Lưu lịch sử đăng ký thuê bao
        String hisContent = SubscriptionHistoryConstant.ORDER_SUCCESS;
        Integer hisType = ContentType.ORDER_DEVICE;
        addSubscriptionHistory(finalSubscriptionId, formatSubHistoryContent(finalSubscription, hisContent, hisType), bills.getId(), hisType);

        // lưu lịch sử thanh toán thành công
        String historyContent = SubscriptionHistoryConstant.PAYMENT_SUCCESS;
        Integer historyType = ContentType.PAYMENT_SUCCESS;
        addSubscriptionHistory(subscription.getId(), historyContent, historyType);

        // log đơn hàng đang đóng gói
        if (ObjectUtil.getOrDefault(subscription.getOs3rdStatus(), -1L).intValue() == StatusOrderEnum.PREPARING.status) {
            addSubscriptionHistory(subscription.getId(), SubscriptionHistoryConstant.PACKING_SUBSCRIPTION, ContentType.PACKING_SUBSCRIPTION);
        }
        return SubscriptionRegisterResDTO.builder()
            .id(subscription.getId())
            .subCode(subscription.getSubCode())
            .isON(infoDTO.getIsON())
            .build();
    }

    /**
     * Khởi tạo payment
     *
     * @param vnptPayResponse the vnpt pay response
     * @param ipAddress         the request
     *
     * @return the payment init res dto
     */
    @Override
    public PaymentInitResDTO initPayment(VNPTPayResponse vnptPayResponse, String ipAddress, Subscription subscription,
        CommonActivityLogInfoDTO activityLogInfoDTO) {
        log.info("====== START initPayment for sub id = {}", subscription.getId());
        //validate IPv4 address
        if (!validateIpv4(ipAddress)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_IP_ADDRESS, Resources.PAYMENT, ErrorKey.IP_ADDRESS, ipAddress);
        }
        String merchantServiceId = null;
        String token = null;
        String secretKey = null;
        String initUrl = null;
        ClueDTO clueDTO = subscriptionDetailService.getMerchantInfo(subscription);
        if (Objects.nonNull(clueDTO)) {
            merchantServiceId = String.valueOf(clueDTO.getMerchantServiceId());
            token = clueDTO.getApiKey();
            secretKey = clueDTO.getPrivateKey();
            initUrl = clueDTO.getBaseUrl().concat("init");
        }
        log.info("initPayment: merchantServiceId {}, token {}, secretKey {}, initUrl {}", merchantServiceId, token, secretKey, initUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(token);
        PaymentInitReqDTO paymentInitReqDTO = new PaymentInitReqDTO();
        paymentInitReqDTO.setMerchant_service_id(merchantServiceId);
        paymentInitReqDTO.setMerchant_order_id(vnptPayResponse.getMerchantOrderId());
        paymentInitReqDTO.setAmount(vnptPayResponse.getAmount());
        paymentInitReqDTO.setAmount_detail(vnptPayResponse.getAmount().toString());
        paymentInitReqDTO.setCurrency_code(vnptPayResponse.getCurrencyCode());

        //get currentTime
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS);
        String createdDate = LocalDateTime.now().format(formatter);
        paymentInitReqDTO.setCreated_date(createdDate);

        paymentInitReqDTO.setClient_ip(ipAddress);

        List<String> stringArr = Arrays.asList(
            paymentInitReqDTO.getAction(),
            paymentInitReqDTO.getVersion(),
            merchantServiceId,
            vnptPayResponse.getMerchantOrderId(),
            vnptPayResponse.getAmount().toString(),
            vnptPayResponse.getAmount().toString(),
            paymentInitReqDTO.getService_id().toString(),
            paymentInitReqDTO.getService_category().toString(),
            paymentInitReqDTO.getDevice().toString(),
            paymentInitReqDTO.getLocale(),
            paymentInitReqDTO.getCurrency_code(),
            paymentInitReqDTO.getPayment_method(),
            paymentInitReqDTO.getDescription(),
            createdDate,
            ipAddress,
            secretKey
        );
        //generate secure code
        StringJoiner joiner = new StringJoiner("=");
        joiner.add(MerchantData.PROVINCE_CODE).add(clueDTO.getProvinceCodePay());
        String merchantData = joiner.toString();

        paymentInitReqDTO.setMerchantData(merchantData);

        String secureCode = DigestUtils.sha256Hex(Strings.join(stringArr, '|'));
        paymentInitReqDTO.setSecure_code(secureCode);
        HttpEntity<PaymentInitReqDTO> requestToInit = new HttpEntity<>(paymentInitReqDTO, headers);
        PaymentInitResDTO paymentInitResDTO = null;
        try {
            // khởi tạo activityLog trước khi send request -> createdAt chính xác hơn
            paymentInitResDTO = httpUtil.exchange(initUrl, HttpMethod.POST, headers, paymentInitReqDTO, PaymentInitResDTO.class);
            log.info("initPayment: request [" + paymentInitReqDTO + "], response [" + paymentInitResDTO + "]");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new VNPTPayResponseException(e);
        } finally {
            // Lưu lịch sử call api sang VNPT Pay
            // apiStatus 0 là thành công
            Integer apiStatus = Objects.nonNull(paymentInitResDTO) && paymentInitResDTO.getResponse_code().equals(SubscriptionConstant.INIT_SUCCESS) ?
                YesNoEnum.NO.value : YesNoEnum.YES.value;

            // Lưu log activity
            ActivityLog activityLog = activityLogService.commonActivityLogData(ActivityCodeEnum.PAY_INIT, activityLogInfoDTO);
            // set tgian phản hồi
            activityLog.setResponseAt(LocalDateTime.now());
            if (Objects.nonNull(activityLogInfoDTO) && Objects.nonNull(activityLogInfoDTO.getActivityLog())) {
                activityLogInfoDTO.getActivityLog().setResponseAt(LocalDateTime.now());
            }
            activityLog.updateResponseReq(
                requestToInit, paymentInitResDTO, headers,
                Objects.nonNull(paymentInitResDTO) && paymentInitResDTO.getResponse_code().equals(SubscriptionConstant.INIT_SUCCESS) ?
                    TransactionLogStatusEnum.SUCCESS : TransactionLogStatusEnum.FAIL,
                Objects.nonNull(paymentInitResDTO) ? paymentInitResDTO.getResponse_code() : null,
                Objects.nonNull(paymentInitResDTO) ? paymentInitResDTO.getDescription() : null,
                activityLogInfoDTO);

            executiveProducerService.saveHistoryCallApiDHSX(initUrl, paymentInitReqDTO, paymentInitResDTO, apiStatus, PartnerCallAPI.PAY, null,
                activityLogInfoDTO);
        }
        log.info("====== END initPayment for sub id = {}, return paymentInitResDTO = {}", subscription.getId(), paymentInitResDTO);
        return paymentInitResDTO;
    }

    @Override
    public QRCreateResDTO initPaymentQR(
            VNPTPayResponse vnptPayResponse, Subscription subscription, CommonActivityLogInfoDTO activityLogInfoDTO, ClueDTO clueDTO) {
        log.info("====== START initPaymentQR for sub id = {}", subscription.getId());
        // header
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(clueDTO.getQrApiKey());
        // request
        QRCreateReqDTO rep = new QRCreateReqDTO();
        rep.setMerchantClientId(qrConfig.getMerchantClientId());
        rep.setMerchantCode(qrConfig.getMerchantCode());
        rep.setMerchantName(qrConfig.getMerchantName());
        rep.setTerminalId(clueDTO.getQrTerminalId());
        rep.setBillNumber(vnptPayResponse.getMerchantOrderId());
        rep.setAmount(vnptPayResponse.getAmount().toString());
        rep.setProvinceCode(clueDTO.getProvinceCodePay());
        SystemParam systemParam = systemParamService.findByParamType(CONFIG_TIME_QR);
        Long expTimeQR = systemParam.getParamValueLong();
        Date referenceDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(referenceDate);
        c.add(Calendar.MINUTE, Math.toIntExact(expTimeQR));
        String expDate = new SimpleDateFormat(DateUtil.FORMAT_DATE_YY_MMDD_HHMM).format(c.getTime());
        rep.setExpDate(expDate);
        List<String> checksums = Arrays.asList(
                rep.getMerchantClientId(),
                rep.getMerchantName(),
                rep.getCountryCode(),
                rep.getMerchantCode(),
                rep.getTerminalId(),
                rep.getQrCodeType(),
                rep.getTxnId(),
                rep.getBillNumber(),
                rep.getAmount(),
                rep.getCcy(),
                rep.getExpDate(),
                rep.getMcc(),
                rep.getTipAndFee(),
                rep.getConsumerId(),
                rep.getPurpose(),
                clueDTO.getQrSecretKey()
        );
        rep.setChecksum(DigestUtils.sha256Hex(Strings.join(checksums, '|')));
        // call
        QRCreateResDTO res = null;
        String url = qrConfig.getInit_url() + "/create_vietqr";
        try {
            // khởi tạo activityLog trước khi send request -> createdAt chính xác hơn
            res = httpUtil.callRest(url, HttpMethod.POST, headers, rep, QRCreateResDTO.class);
            log.info("initPaymentQR: request [" + rep + "], response [" + res + "]");
            // Lưu lịch sử call api sang VNPT Pay
            int apiStatus = Objects.nonNull(res) && res.getResponseCode().equals(SubscriptionConstant.VNPTPayQRResponseCode.SUCCESS) ?
                    YesNoEnum.NO.value : YesNoEnum.YES.value;

            // Lưu log activity
            ActivityLog activityLog = activityLogService.commonActivityLogData(ActivityCodeEnum.PAY_INIT, activityLogInfoDTO);
            // set tgian phản hồi
            activityLog.setResponseAt(LocalDateTime.now());
            if (Objects.nonNull(activityLogInfoDTO) && Objects.nonNull(activityLogInfoDTO.getActivityLog())) {
                activityLogInfoDTO.getActivityLog().setResponseAt(LocalDateTime.now());
            }
            activityLog.setApiUrl(url);
            activityLog.updateResponseReq(
                    new HttpEntity<>(rep, headers), res, headers,
                    apiStatus == YesNoEnum.NO.value ? TransactionLogStatusEnum.SUCCESS : TransactionLogStatusEnum.FAIL,
                    Objects.nonNull(res) ? res.getResponseCode() : null,
                    Objects.nonNull(res) ? res.getDescription() : null,
                    activityLogInfoDTO);

            executiveProducerService.saveHistoryCallApiDHSX(url, rep, res, apiStatus, PartnerCallAPI.PAY, null,
                    activityLogInfoDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("====== END initPaymentQR for sub id = {}, return QRCreateResDTO = {}", subscription.getId(), res);
        return res;
    }

    /**
     * Thêm subscription history
     */
    public void addSubscriptionHistory(Long subscriptionId, String hisContent, Long billingId, Integer contentType) {
        subscriptionHistoryRepository.save(SubscriptionHistory.builder()
            .subscriptionId(subscriptionId)
            .createdAt(LocalDateTime.now())
            .content(hisContent)
            .contentType(contentType)
            .billingId(billingId)
            .createdBy(AuthUtil.getCurrentUserId())
            .build());
    }

    public void addSubscriptionHistory(Long subscriptionId, String hisContent, Integer contentType) {
        subscriptionHistoryRepository.save(SubscriptionHistory.builder()
            .subscriptionId(subscriptionId)
            .createdAt(LocalDateTime.now())
            .content(hisContent)
            .contentType(contentType)
            .createdBy(AuthUtil.getCurrentUserId())
            .build());
    }

    public String formatSubHistoryContent(Subscription subscription, String baseContent, Integer contentType){
        IGetSubRegisterInfo subRegister = subscriptionRepository.getSubRegisterInfo(subscription.getId());
        String formatContent = baseContent;
        // Các loại content type có cung kiểu và dữ liệu format
        // Format các content tạo sub
        List<Integer> lstCreatedSubFormatType = Arrays.asList(
            ContentType.CREATE_TRIAL_SUBSCRIPTION,
            ContentType.CREATE_ACTUAL_SUBSCRIPTION
        );
        if(lstCreatedSubFormatType.contains(contentType)){
            formatContent = String.format(baseContent, subRegister.getServiceName(), subRegister.getPricingName(), subRegister.getEnterpriseName());
        }
        return formatContent;
    }

    /**
     * Tạo một order mới
     *
     */
    private VNPTPayResponse createNewOrder(
            BigDecimal totalAmount, Subscription subscription, Long billId, PortalType portalType, PaymentMethodEnum paymentMethod, ClueDTO clueDTO)
    {
        VNPTPayResponse vnptPayResponse = new VNPTPayResponse();
        vnptPayResponse.setAmount(totalAmount.setScale(0, RoundingMode.HALF_UP));
        vnptPayResponse.setPaymentMethod(paymentMethod.name());
        String currency = null;
        if (Objects.nonNull(subscription.getComboPlanId()) || Objects.nonNull(subscription.getPricing())) {
            currency = Objects.nonNull(subscription.getComboPlanId()) ? currencyRepository.getCurrencyCodeByComboPlanId(subscription.getComboPlanId())
                : Objects.nonNull(subscription.getPricingMultiPlanId()) ? currencyRepository
                    .getCurrencyCodeByPricingPlanId(subscription.getPricingMultiPlanId())
                    : currencyRepository.getCurrencyCodeByPricingId(subscription.getPricingId());
        }

        vnptPayResponse.setCurrencyCode(Objects.nonNull(currency) ? currency : OrderConstant.CURRENCY_CODE);
        vnptPayResponse.setLocale(OrderConstant.LOCALE);
        vnptPayResponse.setMerchantOrderId(commonService.getMaximumCode(KeyGenType.VNPT_PAY));
        vnptPayResponse.setMerchantServiceId(Objects.nonNull(clueDTO) && Objects.nonNull(clueDTO.getMerchantServiceId()) ?
                clueDTO.getMerchantServiceId().toString() : null);
        vnptPayResponse.setSubscriptionId(subscription.getId());
        vnptPayResponse.setBillingId(billId);
        vnptPayResponse.setOrderStatus(OrderStatusEnum.INIT.value);
        vnptPayResponse.setPortalType(Objects.nonNull(portalType) ? portalType.getType() : PortalType.SME.getType());
        //bổ sung token lưu lại trong bảng này, hỗ trợ tích hợp IDC Portal
        if (AuthUtil.getCurrentUser() != null && AuthUtil.getCurrentUser().getBearerToken() != null) {
            vnptPayResponse.setAccessToken(AuthUtil.getCurrentUser().getBearerToken());
        }
        return vnptPayResponseRepository.save(vnptPayResponse);
    }

    /**
     * validate IPv4
     *
     * @param ip address
     */
    private boolean validateIpv4(String ip) {
        String PATTERN = "^((0|1\\d?\\d?|2[0-4]?\\d?|25[0-5]?|[3-9]\\d?)\\.){3}(0|1\\d?\\d?|2[0-4]?\\d?|25[0-5]?|[3-9]\\d?)$";
        return ip.matches(PATTERN);
    }
}
