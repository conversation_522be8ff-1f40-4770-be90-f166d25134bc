package com.service.transactionLog;


import java.io.IOException;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.util.Pair;
import com.dto.transaction_log.banking.AddTransactionReqDTO;
import com.dto.transaction_log.banking.ThirdPartyTransactionDTO;

public interface ThirdPartyTransactionDevService {

    String addTransaction(AddTransactionReqDTO requestDTO);

    Page<ThirdPartyTransactionDTO> getTransaction(List<String> lstTransactionType, String transactionId, String taxCode, String email,
        String corpName, String phone, Date startDate, Date endDate, Long subscriptionId, Pageable pageable);

    List<Pair<String, String>> getTransactionType();

    InputStreamResource exportTransaction(List<String> lstTransactionType, String transactionId, String taxCode, String email,
        String corpName, String phone, Date startDate, Date endDate, Long subscriptionId) throws SQLException, IOException;
}
