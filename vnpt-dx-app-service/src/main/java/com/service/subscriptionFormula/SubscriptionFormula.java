package com.service.subscriptionFormula;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import javax.validation.Valid;
import com.dto.addons.AddonPriceBeforeTaxDTO;
import com.dto.bills.CouponSubscriptionDTO;
import com.dto.bills.caculate.BillPopUpCostIncurred;
import com.dto.bills.caculate.BillPopUpCostIncurredOnce;
import com.dto.creditNote.CreditNoteCalculateDTO;
import com.dto.integrated.request.UpdateQuantitySubscriptionDTO;
import com.dto.marketingCampaign.smePortal.McAppliedEffectDTO;
import com.dto.marketingCampaign.smePortal.PromotionDTO;
import com.dto.pricing.PricingTaxRes;
import com.dto.services.SetupFeeInfoConvertDTO;
import com.dto.services.TaxInfoConvertDTO;
import com.dto.shoppingCart.fomula.ShoppingCartFormulaReqDTO;
import com.dto.subscriptions.CalCostSubOnceDTO;
import com.dto.subscriptions.PricingPlanResDTO;
import com.dto.subscriptions.SubCalBaseDevReqDTO;
import com.dto.subscriptions.SubEditorReqDTO;
import com.dto.subscriptions.SubscriptionCalculateDTO;
import com.dto.subscriptions.SubscriptionCalculateDevComboReqDTO;
import com.dto.subscriptions.SubscriptionCalculateDevReqDTO;
import com.dto.subscriptions.SubscriptionEditorDTO;
import com.dto.subscriptions.SubscriptionRegisterDevResDTO;
import com.dto.subscriptions.calculate.CalOtherFeeReqDTO;
import com.dto.subscriptions.calculate.CalSetupFeeDeviceReqDTO;
import com.dto.subscriptions.calculate.UnitLimitedNewDTO;
import com.dto.subscriptions.calculate.UnitLimitedNewDTO.UnitLimitedNew;
import com.dto.subscriptions.detail.SubscriptionDetailDTO;
import com.dto.subscriptions.formula.ServiceCalculateDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaReqDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaReqDTO.FormulaAddon;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.CustomFee;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaObject;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaService;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaVariant;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.SetupFee;
import com.dto.subscriptions.formula.VariantCalculateDTO;
import com.dto.transaction_log.CommonActivityLogInfoDTO;
import com.entity.addons.Addon;
import com.entity.bills.BillItem;
import com.entity.bills.Bills;
import com.entity.coupons.Coupon;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.subscriptions.Subscription;
import com.entity.unitLimited.UnitLimited;
import com.enums.BillActionTypeEnum;
import com.model.dto.SetupSuccessDTO;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.integration.backend.subscription.AddonSubReqDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO;
import com.payload.response.sub.combo.addon.SubAddonTaxDTO;
import com.service.calculator.SubscriptionCalculationInput;
import com.service.calculator.SubscriptionCalculator;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0 07/16/2021
 */
public interface SubscriptionFormula {

    /**
     * Tính tiền đăng kí thuê bao cho pricing/combo update 24/9:  đã có biến multiPlanId trong FormulaObject object
     * SME đăng ký sub
     *
     */
    SubscriptionFormulaResDTO calculate(SubscriptionFormulaReqDTO subscriptionFormulaReqDTO);

    /**
     * Tính tiền khi Dev, Admin đăng ký cho SME
     *
     */
    SubscriptionFormulaResDTO calculateDevAdminSubscription(SubscriptionCalculateDevReqDTO request);

    /**
     * Tính tiền khi Dev, Admin đăng ký combo cho SME
     *
     */
    SubscriptionFormulaResDTO calculateDevAdminSubscriptionCombo(SubscriptionCalculateDevComboReqDTO request);

    /**
     * Tính tiền khi Dev/Admin đăng kí pricing/combo-plan cho SME
     */
    SubscriptionFormulaResDTO calculateSubscription(SubCalBaseDevReqDTO request);

    /**
     * Thu thập thông tin khuyến mại theo độ ưu tiên
     */
    List<PromotionDTO> collectPromotionInfo(SubCalBaseDevReqDTO request);

    /**
     * Thu thập thông tin calculation input (có bao gồm thông tin effect CDQC)
     *
     * @param userId            ID khách hàng
     * @param subscriptionId    ID thuê bao đang được tính toán. Với đăng kí mới thì subscriptionId = null
     * @param lstPromotion      Danh sách các khuyến mại được áp dụng
     * @param input             CalculationInput tính toán trước
     * @param lstOtherFee       Danh sách các phí khác
     * @return  Thông tin calculation input (có bao gồm thông tin effect CDQC)
     */
    SubscriptionCalculationInput collectCalculationInput(Long userId, Long subscriptionId, List<PromotionDTO> lstPromotion,
        SubscriptionCalculationInput input, List<CalOtherFeeReqDTO> lstOtherFee);

    /**
     * Tính đơn giá trước thuế
     *
     * @return đơn giá trước thuế
     */
    BigDecimal priceBeforeTax(BigDecimal price, List<PricingTaxRes> taxes);

    /**
     * Ke hoach dinh gia Khoi luong, Bac thang
     *
     */
    PricingPlanResDTO calculateVolumeAndStair(Pricing pricing, Addon addon, Long quantity,
        boolean isStairStep, List<SubscriptionCalculateDTO> datas, List<UnitLimited> unitLimitedNewList, PricingMultiPlan pricingMultiPlan);

    /**
     * Lấy đơn giá của pricing theo kế hoạnh định giá
     * @return list đơn giá trước thuế
     */
    List<UnitLimitedNew> getPricingPriceBeforeTax(Pricing pricing, Long subscriptionId, PricingMultiPlan pricingMultiPlan);

    /**
     * Lấy đơn giá trước thuế của addon
     * @return list các đơn giá trước thuế của addon
     */
    List<UnitLimitedNewDTO> getListAddonPriceBeforeTax(List<AddonPriceBeforeTaxDTO> priceBeforeTax, Long subscriptionId);

    /**
     * Tính lại đơn giá ban đầu (không phân biệt thuế)
     *
     */
    BigDecimal getInitialPrice(BigDecimal price, List<PricingTaxRes> taxRes);

    /**
     * Tạo hóa đơn khi có chi phí phát sinh
     * @param subscriptionEditorDTO request dto
     * @param ipAddress ip request
     * @param token token
     */
    BaseResponseDTO createNewBillIncurredPricing(BillPopUpCostIncurred costIncurred,
            SubEditorReqDTO subscriptionEditorDTO, Subscription subscription, String ipAddress,
            String token, BillActionTypeEnum action, Integer planType, PortalType portalType,
            SubEditorReqDTO subEditorWithoutCoupon, CommonActivityLogInfoDTO activityLogInfoDTO);

    /*
     * Tính toán tỉ lệ số ngày sử dụng và update vào db
     */
    /* List<BillCostIncurred> getOldBill(List<BillItem> billItem); */

    /*
     * Tính toán tổng tiền hóa đơn dự kiến cả chu kì
     */
    /* void calculateTotalAmount(BillCostIncurred billCostIncurred); */

    /*
     *
     * Convert bill to dto để tính toán
     * @param billItems bill item
     * @return bill dto
     */
    /* List<BillCostIncurred> convertBillEntityToDto(List<BillItem> billItems); */


    /**
     * Tính đơn giá trước thuế combo
     *
     */
    BigDecimal priceBeforeTaxCombo(BigDecimal price, Set<SubAddonTaxDTO> taxes);


    /**
     * tính tiền trước thuế với kế hoạch định giá lũy kế - TIER
     *
     * @param pricing  the pricing
     * @param quantity the quantity
     * @return the pricing plan tier
     */
    PricingPlanResDTO getPricingPlanTier(Pricing pricing, Long quantity, List<UnitLimited> unitLimitedNewList, Long pricingMultiPlanId);

    /**
     * tinh tien Addon voi thue
     *
     */
    BigDecimal getPreAmountAddonWithTax(Addon addon, Long quantity, List<UnitLimited> unitLimitedAddonNew, PricingMultiPlan pricingMultiPlan);

    /**
     * validate subscription bằng api key và secret key
     *
     * @return subscription optional
     */
    Optional<Subscription> validateAndGetSubscriptionFromParam(Long subscriptionId, String apiKey, String secretKey, SetupSuccessDTO setupSuccessDTO);

    /**
     * Cập nhật số lượng sử dụng của subscription
     *
     * @return id subscription được cập nhật
     */
    Long updateSubscriptionQuantity(UpdateQuantitySubscriptionDTO request);

    /**
     * Tính lại tiền khi update Sub, kích hoạt lại gói dịch vụ
     *
     */
    SubscriptionFormulaResDTO reCalculateUpdateSub(Subscription subscription, List<AddonSubReqDTO> addonList, Integer type, CouponSubscriptionDTO couponAll);

    /**
     * Tính lại tiền khi update Sub, kích hoạt lại gói dịch vụ
     *
     */
    SubscriptionFormulaResDTO reCalculateUpdateSubNew(Subscription subscription, List<AddonSubReqDTO> addonList, Integer type, CouponSubscriptionDTO couponAll, Integer cycleQuantity);

    /**
     * Clone billItem
     *
     * @param input billItem
     * @return billitem has been cloned
     */
    List<BillItem> cloneListBillItem(List<BillItem> input);


    /**
     * Tính đơn giá trước thuế khi xem chi tiết gói lần đầu đăng ký
     * @return đơn giá trước thuế
     */
    BigDecimal getPriceBeforeTax(BigDecimal price, List<SubscriptionPricingAddonDTO.Tax> taxes);

    /*
     * Tính lại subscription cũ
     */
    /* Long updateOldBillItem(Long subscriptionId); */

    /**
     * Tính toán chi phí phát sinh khi biến động thuê bao
     */
	BillPopUpCostIncurred updateSubscription(@Valid SubCalBaseDevReqDTO dto, Integer cycleNo);

    BillPopUpCostIncurredOnce updateSubscriptionOnce(@Valid CalCostSubOnceDTO dto);

    /**
     * save bill_item when cancel subscription
     * @param subscription subscription information
     */
    void calculateCreditNote(Subscription subscription, Bills bills, Map<BigDecimal, List<CreditNoteCalculateDTO>> creditNoteList, String actor);

    /**
     * Convert to calculate request
     */
    SubscriptionCalculateDevReqDTO convertSubscriptionRegReqToCalculateDevReq(SubscriptionRegisterDevResDTO req, Subscription subscription);

    /**
     * Convert to update request
     */
    SubscriptionEditorDTO convertDevAdminRequestToCommonReq(SubscriptionRegisterDevResDTO request);

    /**
     * convert to calculate dev request
     */
    SubscriptionCalculateDevReqDTO convertBaseRqToDevReq(SubscriptionEditorDTO dto, Subscription subscription);

    /**
     * Tính toán số tiền sub đã sử dụng
     * @return chi tiết số tiền đã sử dụng
     */
    BillPopUpCostIncurred getBillUsed(Subscription subscription);

    /*
     * Convert combo update request to combo calculate request
     * @return combo calculate request
     */
    /* SubscriptionCalculateDevComboReqDTO convertComboEditReqToBaseReq(SubComboPlanReqDTO input, Long subscriptionId); */

    /**
     * Tính toán chi phí cả chu kỳ trong màn xem chi tiết billing
     * @return chi tiết số tiền đã sử dụng
     */
    BillPopUpCostIncurred calculateBySubscriptionId(Bills bills);

    /**
     * Tính toán credit note khi đổi gói
     */
    void calculateCreditNoteWhenSwap(Subscription oldSubscription, SubscriptionFormulaResDTO res);

    /**
     * Tính refund lại đối với bill của hóa đơn trả sau
     */
    void calculateRefundByBill(Bills bills, Map<BigDecimal, List<CreditNoteCalculateDTO>> creditNoteMap);

    /**
     * Thu thập thông tin SubscriptionCalculator từ thông tin chi tiết của thuê bao
     */
    SubscriptionCalculator createCalculation(Subscription subscription, SubscriptionDetailDTO detailDTO);

    /**
     * Convert request to input cho hàm tính tiền
     */
    SubscriptionCalculationInput collectCalculatorInput(SubCalBaseDevReqDTO request, int index);

    /**
     * Cập nhật thông tin khuyến mại ở lần mua sau, hoặc khuyến mại tặng sản phẩm vào DTO
     */
    void updateFormulaObject(List<McAppliedEffectDTO> lstTotalEffect, FormulaObject formulaObject, boolean isCouponInvoice);

    /**
     * Tinh creditNote of setupFee
     */
    void calSetupFeeRefund(SetupFee fee, AtomicReference<BigDecimal> totalRefund, CreditNoteCalculateDTO credit,
        Integer type);

    /**
     * Tinh creditNote of pricing, combo, addon
     */
    void calculateAmountRefund(AtomicReference<BigDecimal> totalRefund, CreditNoteCalculateDTO credit, BigDecimal finalAmountAfterTax,
        FormulaObject object, CustomFee fee, SetupFee setupFee, Integer type);

    /**
     * Lấy thông tin coupon tổng hóa đơn
     */
    List<Coupon> collectInvoiceCouponList(List<Long> invoiceCouponReq);

    List<SubscriptionFormulaResDTO.FormulaTax> calculateTaxVariant(List<SetupFeeInfoConvertDTO.Tax> tax, BigDecimal amountAfter, BigDecimal quantity);

    void convertTaxFeePaymentOne(SubscriptionFormulaResDTO formulaResDTO, List<SubscriptionFormulaReqDTO.FormulaCustomFee> onceTimeFee);

    List<SubscriptionFormulaResDTO.FormulaTax> calculateTaxDeviceByAmountUpdate(TaxInfoConvertDTO tax, BigDecimal devicePrice, BigDecimal quantity);

    void saveBillItemVariant(FormulaVariant formulaVariant, Bills bills);

    void saveBillItemService(FormulaService formulaService, Bills bills);

    void calculateNextPaymentAmount(Long subscriptionId, SubscriptionFormulaResDTO formulaResDTO);


    // Sim không có gói
    SubscriptionFormulaResDTO calculateOnlySim(Subscription sub, SubscriptionDetailDTO detailDTO);

    // Sim có gói
    void calculateSimWithPricing(Subscription sub, SubscriptionDetailDTO res, SubscriptionFormulaResDTO formulaResDTO);

    void calculatePricePrePayment(SubscriptionFormulaResDTO formulaResDTO);


    SubscriptionFormulaResDTO calculateServiceGroup(SubscriptionFormulaReqDTO preSub, ShoppingCartFormulaReqDTO request,
                                                    List<CreditNoteCalculateDTO> lstCreditNoteTemp);

    List<FormulaObject> calculateAddon(List<FormulaAddon> addons);
}
