package com.service.customerContact;

import com.dto.customerContact.*;
import com.dto.enterprise.update.UpdateContactRequestDTO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.web.multipart.MultipartFile;
import com.dto.file.attach.IFileAttachResponse;
import com.entity.enterprise.Enterprise;
import com.onedx.common.dto.customerContact.ContactMessageDTO;
import com.onedx.common.dto.enterprise.CommonResponseDTO;
import com.dto.enterprise.update.UpdateBusinessDetailRequestDTO;
import com.dto.enterprise.update.UpdateContactDetailRequestDTO;
import com.dto.enterprise.update.UpdateRepresentativeDetailRequestDTO;
import com.entity.customerContact.CustomerContact;
import com.onedx.common.dto.interestProduct.InterestProductDTO;
import com.service.utils.jsonObject.McConditionItemGroupDTO;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

public interface CustomerContactService {

    /**
     * Tạo mới liên hệ
     */
    CustomerContact create(CustomerContactCreateDTO createDTO);

    void syncFromSme(CustomerContact customerContact);

    Slice<String> getListContactName(String name, List<String> customerTypes, Pageable pageable);

    Slice<String> getListContactEmail(String name, List<String> customerTypes, Pageable pageable);

    Slice<String> getListContactPhone(String name, List<String> customerTypes, Pageable pageable);

    Page<GetListContactResponseDTO> getListContact(ContactListReqDTO contactListReqDTO);

    InputStreamResource getExportListContact(List<GetListContactResponseDTO> result) throws IOException;

    List<Long> getListContactId(List<Long> contactIds, String contactName, String phone, String email, Long status, List<Integer> source, List<String> customerTypes);

    void archiveContact(ArchiveContactRequest archiveContactRequest);

    void deleteContact(Long contactId);

    /**
     * Lấy chi tiết liên hệ
     *
     * @param contactId ID của liên hệ cần lấy thông tin
     * @return GetContactDetailResponseDTO
     */
    GetContactDetailResponseDTO getContactDetail(Long contactId);

    /**
     * Lấy chi tiết doanh nghiệp
     *
     * @param contactId ID của liên hệ cần lấy thông tin
     * @return GetBusinessDetailResponseDTO
     */
    GetBusinessDetailResponseDTO getBusinessDetail(Long contactId);

    /**
     * Lấy chi tiết người đại diện
     *
     * @param contactId ID của liên hệ cần lấy thông tin
     * @return GetRepresentativeDetailResponseDTO
     */
    GetRepresentativeDetailResponseDTO getRepresentativeDetail(Long contactId);

    /**
     * Lấy chi tiết thông tin khách hàng cá nhân
     *
     * @param contactId ID của liên hệ cần lấy thông tin
     * @return GetPersonalDetailResponseDTO
     */
    GetPersonalDetailResponseDTO getPersonalDetail(Long contactId);

    /**
     * Sửa thông tin liên hệ
     */
    void updateContactDetail(UpdateContactDetailRequestDTO request);

    /**
     * Sửa thông tin doanh nghiệp
     */
    void updateBusinessDetail(UpdateBusinessDetailRequestDTO request);

    /**
     * Sửa thông tin người đại diện
     */
    void updateRepresentativeDetail(UpdateRepresentativeDetailRequestDTO request);

    /**
     * Sửa thông tin cá nhân
     */
    void updatePersonalDetail(UpdatePersonalDetailRequestDTO request);

    /**
     * Chuyển liên hệ sang khách hàng tiềm năng
     */
    CommonResponseDTO convertToPotentialEnterprise(CustomerContact customerContact);

    /**
     * Import customer group từ file excel
     *
     * @param file Binary file excel cần import
     * @return ImportEnterpriseResponseDTO
     */
    InputStreamResource importCustomerContact(MultipartFile file, CommonResponseDTO response, String customerType);

    void updateContact(UpdateContactRequestDTO request);

    void updateContactStatus(Long id, Integer status);

    void addContactToDataPartition (Set<Long> lstContactId, Set<Long> lstAddedPartitionId, Set<Long> lstRemovedPartitionId);

    void assignAssignee (Long assigneeId, Set<Long> lstCustomerContactId);

    /********************************************************************************************************************************************
     * Bổ sung thông tin SPDV quan tâm và lời nhắn của liên hệ
     *******************************************************************************************************************************************/
    List<InterestProductDTO> getInterestProductInfo(Long contactId);

    void addInterestProductInfo(Long contactId, List<InterestProductDTO> request);

    void updateInterestProductInfo(Long contactId, List<InterestProductDTO> lstInterestProduct);

    List<ContactMessageDTO> getContactMessageInfo(Long contactId);

    void addContactMessageInfo(Long contactId, List<ContactMessageDTO> request);

    void updateContactMessageInfo(Long contactId, List<ContactMessageDTO> lstContactMessage);

    /**
     * Tìm kiếm danh sách các liên hệ trùng lặp
     *
     * @param conditions Bộ điều kiện tìm kiếm trùng lặp
     */
    Page<IDupplicateContactDTO> getDupplicateContacts(String customerType, List<McConditionItemGroupDTO> conditions, Pageable pageable);

    /**
     * Lấy danh sách thông tin của tất cả các liên hệ trùng lặp
     *
     * @param lstContactId Danh sách ID các liên hệ trùng lặp
     */
    List<DupplicateContactInfoDTO> getDupplicateContactInfo(List<Long> lstContactId);


    List<IFileAttachResponse> getContactFileAttach(Long contactId);

    void updateContactFileAttach(Long contactId, List<Long> attachmentIds);

    /*
     * Callback gọi khi liên hệ được chuyển đổi thành khách hàng
     */
    void onConvertedToEnterprise(CustomerContact contact, Enterprise enterprise);

    /**
     * Thêm contact vào các chiến dịch quảng cáo
     */
    void addContactToMc(Set<Long> lstContactId, Set<Long> lstMcId);

    /**
     * Xoá các liên hệ
     */
    void deleteContact(Set<Long> lstContactId);

    InputStreamResource exportContactList(List<Long> contactIds, String optimizeSearchLike, String optimizeSearchLike1, String optimizeSearchLike2, Long status, List<Integer> source, List<String> customerTypes, Set<Long> provinceIds, Set<Long> contactStatus, Long assigneeId, String startTime, String endTime, Long partitionId) throws SQLException, IOException;
}
