package com.service.calculator;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.dto.subscriptions.SubscriptionCalculateDTO;
import com.entity.pricing.PricingTax;

/**
 * Tính đơn giá gói khối lượng khi người dùng sửa giá gói
 * 
 * <AUTHOR>
 *
 */
public class InputVolumePriceCalculator extends VolumePriceCalculator implements InputPlanPriceCalculator {

	private final List<SubscriptionCalculateDTO> planUnitPriceList;

	public InputVolumePriceCalculator(List<SubscriptionCalculateDTO> planPriceList, BigDecimal registerQuantity,
			List<PricingTax> taxList, List<SubscriptionCalculateDTO> planUnitPriceList) {
		super(planPriceList, registerQuantity, taxList);
		this.planUnitPriceList = planUnitPriceList;
	}

	@Override
	protected SubscriptionCalculateDTO calcPricingPrice(BigDecimal quantity, SubscriptionCalculateDTO planPrice) {
		BigDecimal price = planPrice.getUnitPrice();
		boolean correctPlanPrice = isCorrectPlanPrice(quantity.longValue(), planPrice);
		BigDecimal actualQuantity = correctPlanPrice ? quantity : BigDecimal.ZERO;

		SubscriptionCalculateDTO rst = new SubscriptionCalculateDTO();
		rst.setQuantity(actualQuantity.longValue());
		rst.setUnitFrom(planPrice.getUnitFrom());
		rst.setUnitPrice(price);
		rst.setUnitTo(planPrice.getUnitTo());
		setPrePosTaxAmount(price, actualQuantity, rst);
		return rst;
	}

	@Override
	protected void setPrePosTaxAmount(BigDecimal price, BigDecimal quantity, SubscriptionCalculateDTO pricingPrice) {
		SubscriptionCalculateDTO input = planUnitPriceList.stream()
				.filter(i -> Objects.equals(pricingPrice.getUnitFrom(), i.getUnitFrom())).findFirst().get();
		if (input.getUnitPrice().compareTo(price) != 0) {
			BigDecimal preTaxAmount = calc(price, quantity);
			pricingPrice.setAmountAfterTax(preTaxAmount);
			pricingPrice.setAmountBeforeTax(preTaxAmount);
		} else {
			pricingPrice.setAmountAfterTax(input.getAmountBeforeTax());
			pricingPrice.setAmountBeforeTax(input.getAmountBeforeTax());
		}
	}

}
