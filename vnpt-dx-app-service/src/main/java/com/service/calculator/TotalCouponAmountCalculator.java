package com.service.calculator;

import java.math.BigDecimal;
import java.util.List;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaCoupon;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;

/**
 * 
 * <AUTHOR>
 *
 */
public interface TotalCouponAmountCalculator extends AmountCalculator {

	@Override
	default BigDecimal calc() {
		List<FormulaCoupon> couponList = getCouponList();
		return couponList.stream().filter(this::isCorrectCoupon).map(FormulaCoupon::getPrice)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	List<FormulaCoupon> getCouponList();

	default boolean isCorrectCoupon(FormulaCoupon coupon) {
		return getDiscountTypeEnum() == coupon.getCouponDiscountType();
	}

	DiscountTypeEnum getDiscountTypeEnum();

}
