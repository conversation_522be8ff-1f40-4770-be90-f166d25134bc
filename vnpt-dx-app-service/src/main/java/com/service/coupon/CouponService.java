package com.service.coupon;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.constant.enums.coupon.CouponEnterprisePopupEnum;
import com.constant.enums.coupon.CouponRestoreStatus;
import com.dto.coupons.CouponApproveDTO;
import com.dto.coupons.CouponCategoryResDTO;
import com.dto.coupons.CouponDeleteReqDTO;
import com.dto.coupons.CouponDeleteResDTO;
import com.dto.coupons.CouponDetailDTO;
import com.dto.coupons.CouponDevReqDTO;
import com.dto.coupons.CouponEnterpriseResponse;
import com.dto.coupons.CouponIdAndNameDTO;
import com.dto.coupons.CouponPopupTreeResDTO;
import com.dto.coupons.CouponPricingPlanResDTO;
import com.dto.coupons.CouponSendMailDTO;
import com.dto.coupons.CouponServiceResDTO;
import com.dto.coupons.CouponUpdateApproveResDTO;
import com.dto.coupons.ICouponListResDTO;
import com.dto.coupons.PricingCouponResDTO;
import com.dto.marketingCampaign.adminPortal.McCouponDTO;
import com.dto.pricing.PricingApplyDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.product_solustions.IGetCouponMcDTO;
import com.dto.product_solustions.IGetCouponPackageDTO;
import com.dto.subscriptions.CouponMcDevAdminReqDTO;
import com.dto.subscriptions.CouponPopupDTO;
import com.entity.coupons.Coupon;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO;

/**
 * <AUTHOR> DangNDH
 * @version    : 1.0
 * 11/5/2021
 */
public interface CouponService {

    Coupon findByIdAndDeletedFlag(Long couponId, Integer deletedFlag);

    /**
     * Developer/Admin xem chi tiết chương trình khuyến mại
     *
     */
    CouponDetailDTO getCouponDetail(Long id, boolean isAdmin);


    /**
     * Phê duyệt chương trình khuyến mại - Admin
     *
     */
    BaseResponseDTO approveStatus(Long id, CouponApproveDTO approveStatusDTO);

    /**
     * Tắt/bật trạng thái chương trình khuyến mại
     *
     */
    void updateStatus(Long id, StatusEnum status, PortalType portalType);

    /**
     * Tạo khuyến mại
     *
     * @param couponDTO the coupon DTO
     * @param portal the portal
     * @return the map
     */
    BaseResponseDTO createCoupon(CouponDevReqDTO couponDTO, PortalType portal);

    /**
     * Lấy danh sách doanh nghiệp
     *
     * @param name     the name
     * @param type     enterprise type
     * @param notIds   the not ids
     * @param pageable the pageable
     *
     * @return the coupon enterprises
     */
    Page<CouponEnterpriseResponse> getCouponEnterprises(String name, CouponEnterprisePopupEnum type, Set<Long> notIds,
        Set<CustomerTypeEnum> customerTypes, Boolean isValidate, Pageable pageable);

    /**
     * Developer xem danh sách Chương trình khuyến mại
     *
     */
    Page<ICouponListResDTO> getListCouponDEV(String searchText, Integer status, String duration, Integer approveStatus, Set<CustomerTypeEnum>
        customerTypes, String code, Pageable pageable);

    /**
     * ADMIN - Xem danh sách chương trình khuyến mại
     *
     */
    Page<ICouponListResDTO> getListCouponADMIN(String value, Integer status, String duration,
            Integer approveStatus, Pageable pageable, String permission, Set<CustomerTypeEnum> customerTypes, Integer isCode, Integer isName);

    /**
     * Cập nhật chương trình khuyến mại
     *
     * @param id         Mã chương trình khuyến mại
     * @param couponDto  the coupon dto
     * @param portalType the portal type
     *
     * @return the base response dto
     */
    BaseResponseDTO updateCoupon(Long id, CouponDevReqDTO couponDto, PortalType portalType);

    /**
     * Yêu cầu phê duyệt chương trình khuyến mại
     *
     */
    BaseResponseDTO requestApprove(Long id);

    /**
     * Xoa chuong trinh khuyen mai
     *
     */
    CouponDeleteResDTO deleteCoupons(CouponDeleteReqDTO ids);

    /**
     * Developer khôi phục thông tin, xác nhận thay đổi thông tin coupon đã được phê duyệt
     *
     * @param id     the id
     * @param status the status
     *
     * @return the base response dto
     */
    BaseResponseDTO restoreUpdateInfo(Long id, CouponRestoreStatus status);


    /**
     * Developer cập nhật coupon đã được phê duyệt
     *
     * @param id         the id
     * @param updateInfo the update info
     *
     * @return the base response dto
     */
    BaseResponseDTO updateApprove(Long id, CouponUpdateApproveResDTO updateInfo, PortalType portalType);

    /**
     * Get list coupon popup of pricing/addon/combo-plan when subscription
     */
    Page<CouponPopupDTO> getCouponsPopup(Long variantId, Long multiPlanId, Long companyId, String customerType, Long mainId, Integer page, Integer size, String sort,
        String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity);

    /**
     * Get list coupon popup of pricing/addon/combo-plan when subscription
     */
    List<CouponPopupDTO> getAllCouponsPopup(Long variantId,
                                            Long multiPlanId,
                                            Long companyId,
                                            String customerType,
                                            Long mainId,
                                            String classify,
                                            List<Long> couponIds,
                                            PortalType portalType);

    /**
     * Get list coupon popup of total bill when subscription
     */
    Page<CouponPopupDTO> getCouponsPopupForTotalBill(Long companyId, Integer page,
        Integer size, String sort, String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity, CustomerTypeEnum customerType);

    List<CouponPopupDTO> getAllCouponsPopupForTotalBill(Long companyId, String classify, List<Long> couponIds, PortalType portalType,
        CouponMcDevAdminReqDTO couponMcDevAdminReqDTO);

    /**
     *
     */
    void saveHistoryCouponOutOfUsed(Long subsId, List<Long> couponIds, String objName);

    /**
     * Validate coupon theo đối tượng áp dụng
     */
    boolean validCouponApplyFor (Coupon coupon, String subjectApplyCoupon);

    /**
     * Tìm kiếm service cho popup chọn dịch vụ của gói dịch vụ hoặc dịch vụ bỏ sung
     */
    List<CouponServiceResDTO> searchServiceMultiPlan(String name, String type);

    /**
     * Tìm kiếm gói dịch vụ hoặc dịch vụ bổ
     */
    List<PricingCouponResDTO> searchPricingMultiPlan(String name, String type);

    /**
     * Tìm kiếm danh danh mục
     */
    List<CouponCategoryResDTO> searchCategory(String name);

    /**
     * Tìm kiếm chu kì thanh toán của dịch vụ hoặc của dịch vụ bổ sung
     */
    List<CouponPricingPlanResDTO> searchPricingPlan(String name, String type);

    /**
     * list addon cho popup addon
     */
    CouponPopupTreeResDTO getListAddons(String serviceName, String addonName, Integer bonusValue, CycleTypeEnum type,
        Long categoryId, Long addonId, BonusTypeEnum bonusType, Set<CustomerTypeEnum> customerTypes, Pageable pageable);

    /**
     * Get list coupon popup of pricing/addon/combo-plan when subscription
     */
    Page<CouponPopupDTO> getOnlyOneCouponPopup(Long multiPlanId,Long companyId, Long mainId, Integer page, Integer size, String sort,
                                         String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity,Long couponId);

    /**
     * Get list coupon popup of total bill when subscription
     */
    Page<CouponPopupDTO> getOnlyOneCouponsPopupForTotalBill(Long companyId, Long pricingId, List<Long> addonIds, Integer page,
                                                     Integer size, String sort, String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity,Long couponId);

    /**
     * gửi mail khi active coupon
     */
    void sendMailCouponSmeWhenActiveStatus(CouponSendMailDTO coupon);

    /**
     * Get conditions of coupon
     */
    List<String> getCouponConditions(Coupon c, String classify);

    List<String> getCouponConditions(HashMap<Long, List<CouponIdAndNameDTO>> couponIdToCouponName, Coupon c, String classify);

    /**
     * Get conditions of coupon
     */
    List<String> getCouponConditions(Long couponId, String classify);

    /**
     * Đổi định dạng tiền
     */
    String convertFormatAmount(BigDecimal amount);

    /*
    * List Coupon Available cho MKC configs
    * */
    List<McCouponDTO> getListMcCouponDTO(String couponName, Pageable pageable);

    Page<ICouponListResDTO> getListCouponsFormCouponSet(String searchText, Pageable pageable);

    /**
     * Lấy thông tin pricing bởi coupon_id
     */
    List<SubscriptionPricingAddonDTO.PricingByCouponId> getPricingByCoupon(Long couponId);

    /**
     * Lấy danh sách chương trình khuyến mại theo mã gói và theo chu kỳ multi period
     *
     * @param pricingId the pricing id
     * @return the coupon list by pricing id
     */
    List<PricingSaaSResDTO.Coupon> getCouponListByPricingIdOrPricingMultiPlanId(Long pricingId, Long pricingMultiPlanId);

    /**
     * Lấy danh sách chương trình khuyến mại mới nhất theo mã gói và theo chu kỳ multi period
     *
     * @param pricingId the pricing id
     * @return the coupon list by pricing id
     */
    Optional<PricingSaaSResDTO.Coupon> getLatestCouponByPricingIdOrPricingMultiPlanId(Long pricingId, Long pricingMultiPlanId,
        String customerType, Long userId, Long provinceId, Long pricingOwnerId);

    /**
     * Lấy danh sách chương trình khuyến mại theo mã gói và theo chu kỳ multi period
     *
     * @param pricingId        the pricing id
     * @param customerTypeEnum Loại khách hàng sử dụng để lọc danh sách gói dịch vụ trả về (null nếu không cần lọc theo loại khách hàng)
     * @return the coupon list by pricing id
     */
    List<PricingSaaSResDTO.Coupon> getCouponListByPricingIdOrPricingMultiPlanId(Long pricingId, Long pricingMultiPlanId,
        CustomerTypeEnum customerTypeEnum);

    PricingSaaSResDTO.Coupon convertCouponPricing(PricingApplyDTO coupon);

    /**
     * Validate coupons.
     *
     * @param coupon the coupon
     * @return the boolean
     */
    boolean validateCoupons(Coupon coupon, Long userId, String classify);

    CouponDetailDTO getSMECouponDetail(Coupon coupon);

    List<CouponPopupDTO> getAllCouponsPricingAddonPopup(Long pricingId, Long pricingMultiPlanId, List<Long> addonIds,
        List<Long> lstAddonMultiPlanId, Long companyId, String customerType, List<Long> couponIds, PortalType portalType);

    List<IGetCouponPackageDTO> getCouponPricingPackages(Set<Long> couponPricingIds);

    List<IGetCouponPackageDTO> getCouponAddonPackages(Set<Long> couponAddonIds);

    List<IGetCouponMcDTO> getCouponPricingAddonMcInPackage(Set<String> couponPricingMcIds);

    /**
     * Publish event khi component thay đổi để cập nhật lại giá các packages liên quan
     * @param componentId ID của component bị thay đổi (coupon, addon, pricing, variant)
     * @param type Loại thay đổi: COUPON, ADDON, PRICING, VARIANT
     */
    void publishComponentChangedEvent(Long componentId, String type);

}
