package com.service.utils;

import com.service.utils.constants.OperatorConstant;
import com.service.utils.values.ValueManager;

import static com.service.utils.constants.OperatorConstant.OperatorEnum.EQUAL;
import static com.service.utils.constants.OperatorConstant.OperatorEnum.NOT_EQUAL;

public class CondDisplayText {
    public static String dateTimeDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase valueBase) {
        try {
            switch (operator) {
                case NO_LIMIT:
                    return "(không giới hạn thời gian)";
                case EQUAL: {
                    return "trong ngày " + valueBase.toString();
                }
                case BETWEEN: {
                    return valueBase.toString();
                }
                case NOT_BETWEEN: {
                    return "không nằm trong thời gian " + valueBase.toString();
                }
                case IN: {
                    return "trong các ngày " + valueBase.toString();
                }
                case NOT_IN: {
                    return "không nằm trong các ngày " + valueBase.toString();
                }
                case DURING:
                    return "trong vòng " + valueBase.toString();
                default:
                    return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static String stringDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase valueBase) {
        try {
            switch (operator) {
                case EQUAL: {
                    return valueBase.toString();
                }
                case NOT_EQUAL: {
                    return "khác " + valueBase.toString();
                }
                case CONTAIN: {
                    return "chứa chữ " + valueBase.toString();
                }
                case NOT_CONTAIN: {
                    return "không chứa chữ " + valueBase.toString();
                }
                case START_WITH: {
                    return "bắt đầu bằng chữ " + valueBase.toString();
                }
                case END_WITH: {
                    return "kết thúc bằng chữ " + valueBase.toString();
                }
                case NOT_START_WITH: {
                    return "không bắt đầu bằng chữ " + valueBase.toString();
                }
                case NOT_END_WITH: {
                    return "không kết thúc bằng chữ " + valueBase.toString();
                }
                default:
                    // NOT_AVAILABLE and ANY are special cases; they are not handled here
                    return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static String longDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase valueBase) {
        try {
            switch (operator) {
                case EQUAL:
                case BETWEEN:
                case IN: {
                    return valueBase.toString();
                }
                case NOT_EQUAL: {
                    return "khác " + valueBase.toString();
                }
                case NOT_BETWEEN: {
                    return "không nằm giữa " + valueBase.toString();
                }
                case NOT_IN: {
                    return "không thuộc " + valueBase.toString();
                }
                case LESS_THAN: {
                    return "ít hơn " + valueBase.toString();
                }
                case BIGGER_THAN: {
                    return "lớn hơn " + valueBase.toString();
                }
                case LESS_OR_EQUAL: {
                    return "tối đa " + valueBase.toString();
                }
                case BIGGER_OR_EQUAL: {
                    return "tối thiểu " + valueBase.toString();
                }
                default:
                    // NOT_AVAILABLE and ANY are special cases; they are not handled here
                    return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String doubleDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase valueBase) {
        try {
            switch (operator) {
                case EQUAL:
                case BETWEEN:
                case IN: {
                    return valueBase.toString();
                }
                case NOT_EQUAL: {
                    return "khác " + valueBase.toString();
                }
                case NOT_BETWEEN: {
                    return "không nằm giữa " + valueBase.toString();
                }
                case NOT_IN: {
                    return "không thuộc " + valueBase.toString();
                }
                case LESS_THAN: {
                    return "ít hơn " + valueBase.toString();
                }
                case BIGGER_THAN: {
                    return "lớn hơn " + valueBase.toString();
                }
                case LESS_OR_EQUAL: {
                    return "tối đa " + valueBase.toString();
                }
                case BIGGER_OR_EQUAL: {
                    return "tối thiểu " + valueBase.toString();
                }
                default:
                    // NOT_AVAILABLE and ANY are special cases; they are not handled here
                    return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String paymentMethodDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase mcValue) {
        try {
            if (operator == EQUAL) {
                return mcValue.toString();
            } else if (operator == NOT_EQUAL) {
                return "khác " + mcValue.toString();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String addonTypeDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase cValue) {
        try {
            switch (operator) {
                case EQUAL:
                    return cValue.toString();
                case NOT_EQUAL:
                    return "khác "  + cValue.toString();
                default:
                    return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static String timeUnitDisplayText(OperatorConstant.OperatorEnum operator, ValueManager.CValueBase mcValue) {
        try {
            switch (operator) {
                case EQUAL: {
                    return mcValue.toString();
                }
                case NOT_EQUAL: {
                    return "khác " + mcValue.toString();
                }
                case LESS_THAN: {
                    return "ít hơn " + mcValue.toString();
                }
                case BIGGER_THAN: {
                    return "lớn hơn " + mcValue.toString();
                }
                case LESS_OR_EQUAL: {
                    return "tối đa " + mcValue.toString();
                }
                case BIGGER_OR_EQUAL: {
                    return "tối thiểu " + mcValue.toString();
                }
                case BETWEEN: {
                    return mcValue.toString();
                }
                case NOT_BETWEEN: {
                    return "không nằm giữa " + mcValue.toString();
                }
                case IN: {
                    return "trong thời gian" + mcValue.toString();
                }
                case NOT_IN: {
                    return "không trong thời gian" + mcValue.toString();
                }
                default:
                    return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
