package com.service.utils.condition;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import com.entity.customerContact.CustomerContact;
import com.entity.enterprise.Enterprise;
import com.onedx.common.utils.GsonMapperUtil;
import com.service.utils.jsonObject.McConditionItemGroupDTO;
import com.service.utils.jsonObject.McIfConditionDTO;
import com.util.SpringContextUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConditionItemSetExecute {

    private final McConditionItemGroupDTO mcCondSetJson;
    private final List<ConditionItemExecute> listMcCondItem;
    private final ConditionItemFactory condItemFactory;
    @Getter
    private int id;
    private final Long mcId;

    public ConditionItemSetExecute(Long mcId, McConditionItemGroupDTO mcCondSetJson) {
        this.mcId = mcId;
        this.mcCondSetJson = mcCondSetJson;
        listMcCondItem = new ArrayList<>();
        condItemFactory = SpringContextUtils.getBean(ConditionItemFactory.class);
        parseJson();
    }

    private void parseJson() {
        try {
            id = mcCondSetJson.id;
            // Parse Condition Item
            for (McIfConditionDTO condItemJson : mcCondSetJson.getIfconds()) {
                ConditionItemExecute condItemExe = condItemFactory.makeConditionItem(condItemJson);
                if (condItemExe != null) {
                    listMcCondItem.add(condItemExe);
                } else {
                    log.error("Cannot make condition item from campaignId '{}', condItemJson '{}'",
                        mcId, GsonMapperUtil.toJson(condItemJson));
                }
            }
            // Sort list by id
            listMcCondItem.sort(Comparator.comparingInt(ConditionItemExecute::getId));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean verify(ConditionParam conditionParam) {
        if (conditionParam == null || conditionParam.customerId == null) {
            return false;
        }
        for (ConditionItemExecute item : listMcCondItem) {
            boolean check = item.verify(conditionParam);
            if (check) {
                return true;
            }
        }
        return false;
    }

    public List<String> getDisplayText() {
        List<String> displayTexts = new ArrayList<>();
        for (ConditionItemExecute item : listMcCondItem) {
            String text = item.getDisplayText();
            if (text != null) {
                displayTexts.add(text);
            }
        }
        return displayTexts;
    }

    public List<String> getDisplayApplyText() {
        List<String> displayTexts = new ArrayList<>();
        for (ConditionItemExecute item : listMcCondItem) {
            String text = item.getDisplayApplyText();
            if (text != null) {
                displayTexts.add(text);
            }
        }
        return displayTexts;
    }

    public Set<Long> filterEnterprise() {
        Set<Long> result = new HashSet<>();
        for (ConditionItemExecute item : listMcCondItem) {
            result.addAll(item.filterEnterprise());
        }
        return result;
    }

    public Set<Long> filterEnterprise(Long adminProvinceId) {
        Set<Long> result = new HashSet<>();
        for (ConditionItemExecute item : listMcCondItem) {
            result.addAll(item.filterEnterprise(adminProvinceId));
        }
        return result;
    }

    public Set<Long> filterEnterprise(Integer groupType) {
        Set<Long> result = new HashSet<>();
        for (ConditionItemExecute item : listMcCondItem) {
            result.addAll(item.filterEnterprise(groupType));
        }
        return result;
    }

    public Set<Long> filterContact() {
        Set<Long> result = new HashSet<>();
        for (ConditionItemExecute item : listMcCondItem) {
            result.addAll(item.filterContact());
        }
        return result;
    }

    public Set<Long> filterContact(Long adminProvinceId) {
        Set<Long> result = new HashSet<>();
        for (ConditionItemExecute item : listMcCondItem) {
            result.addAll(item.filterContact(adminProvinceId));
        }
        return result;
    }

    /**
     * Kiểm tra một enterprise có match nhóm điều kiện hay không (gộp bằng OR)
     */
    public boolean isMatched(Enterprise enterprise) {
        boolean result = false;
        for (ConditionItemExecute item : listMcCondItem) {
            result |= item.isMatched(enterprise);
        }
        return result;
    }

    /**
     * Kiểm tra một customer contact có match nhóm điều kiện hay không (gộp bằng OR)
     */
    public boolean isMatched(CustomerContact customerContact) {
        boolean result = false;
        for (ConditionItemExecute item : listMcCondItem) {
            result |= item.isMatched(customerContact);
        }
        return result;
    }

    /**
     * Gộp condition các điều kiện thuộc cùng group (gộp bằng OR)
     */
    public String getCondition(Integer objectType) {
        List<String> lstCondition = new ArrayList<>();
        if (listMcCondItem == null || listMcCondItem.isEmpty()) {
            return "        false \n";
        }
        for (ConditionItemExecute item : listMcCondItem) {
            String condition = item.getCondition(objectType);
            lstCondition.add(condition);
        }
        return String.join(" or \n", lstCondition) + "\n";
    }
}
