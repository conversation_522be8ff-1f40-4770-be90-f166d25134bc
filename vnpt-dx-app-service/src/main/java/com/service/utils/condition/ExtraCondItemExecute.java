package com.service.utils.condition;

import static com.service.utils.constants.OperatorConstant.OperatorEnum.NO_LIMIT;

import java.util.List;
import com.onedx.common.constants.values.ruleEngine.DataTypeConstant;
import com.onedx.common.constants.values.ruleEngine.DataTypeConstant.CondPurchaseTimeEnum;
import com.service.utils.constants.OperatorConstant;
import com.service.utils.jsonObject.McExtendConditionDTO;
import com.service.utils.values.ValueManager;
import com.service.utils.values.ValueManager.CPaymentCycle;
import lombok.Getter;

@Getter
public class ExtraCondItemExecute {

    private McExtendConditionDTO extraCondItemJson;
    private OperatorConstant.OperatorEnum timeSelectionOperator;
    private ValueManager.CValueBase mcTimeSelectionValue;
    private DataTypeConstant.CondPurchaseTimeEnum purchaseTime;
    private List<Long> serviceComboList;
    private List<Long> pricingComboPlanList;
    private List<CPaymentCycle> paymentCycleList;

    public ExtraCondItemExecute(McExtendConditionDTO extraCondItemJson) throws Exception {
        this.extraCondItemJson = extraCondItemJson;
        parseJson();
    }

    public ExtraCondItemExecute() {
        // Set default value
        this.timeSelectionOperator = NO_LIMIT;
        this.mcTimeSelectionValue = null;
        this.purchaseTime = CondPurchaseTimeEnum.ALL;
        this.serviceComboList = null;
        this.pricingComboPlanList = null;
        this.paymentCycleList = null;
    }

    private void parseJson() throws Exception {

        try {
            serviceComboList = extraCondItemJson.getServiceList();
            pricingComboPlanList = extraCondItemJson.getPricingList();
            paymentCycleList = extraCondItemJson.getPaymentCycleList();
            if (paymentCycleList != null && !paymentCycleList.isEmpty()) {
                if (serviceComboList == null || serviceComboList.isEmpty() || pricingComboPlanList == null || pricingComboPlanList.isEmpty()) {
                    throw new Exception("Invalid data");
                }
            }
            if (pricingComboPlanList != null && !pricingComboPlanList.isEmpty()) {
                if (serviceComboList == null || serviceComboList.isEmpty()) {
                    throw new Exception("Invalid data");
                }
            }

            purchaseTime = extraCondItemJson.getPurchaseTime() != null ?
                DataTypeConstant.CondPurchaseTimeEnum.values()[extraCondItemJson.getPurchaseTime()] : null;
            timeSelectionOperator = extraCondItemJson.getApplyTimeType() != null ?
                OperatorConstant.OperatorEnum.fromValue(extraCondItemJson.getApplyTimeType()) : null;

            if (timeSelectionOperator == null) {
                // Time Selection is not available
                return;
            }

            switch (timeSelectionOperator) {
                case NO_LIMIT:
                    mcTimeSelectionValue = null;
                    break;
                case EQUAL:
                    mcTimeSelectionValue = new ValueManager.CDateTime(extraCondItemJson.getTime());
                    break;
                case BETWEEN:
                case NOT_BETWEEN:
                    mcTimeSelectionValue = new ValueManager.CDateTimeRange(extraCondItemJson.getTimeRange().start,
                        extraCondItemJson.getTimeRange().end);
                    break;
                case IN:
                case NOT_IN:
                    mcTimeSelectionValue = new ValueManager.CListDateTime(extraCondItemJson.getTimeList());
                    break;
                case DURING:
                    mcTimeSelectionValue = new ValueManager.CTimeDuration(extraCondItemJson.getDuration().unit,
                        extraCondItemJson.getDuration().value,
                        extraCondItemJson.getDuration().since);
                    break;
                default:
                    throw new Exception("Invalid data");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public ValueManager.CValueBase getTimeSelectionValue() {
        return mcTimeSelectionValue;
    }

    public DataTypeConstant.CondPurchaseTimeEnum getPurchaseTime() {
        return purchaseTime == null ? CondPurchaseTimeEnum.ALL : purchaseTime;
    }

}
