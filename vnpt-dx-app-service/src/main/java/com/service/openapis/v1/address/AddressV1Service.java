package com.service.openapis.v1.address;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import com.dto.enterprise.ComboboxDistrictResponseDTO;
import com.dto.enterprise.ComboboxResponseDTO;
import com.dto.enterprise.ComboboxStreetDTO;
import com.dto.enterprise.ComboboxWardResponseDTO;

public interface AddressV1Service {

    List<ComboboxResponseDTO> getRegions(String regionName);

    List<ComboboxResponseDTO> getProvinceByRegion(List<Long> regionIds, String provinceName);

    Slice<ComboboxDistrictResponseDTO> getDistrictByProvince(Pageable pageable, List<Long> provinceIds, String districtName);

    Slice<ComboboxWardResponseDTO> getSliceWardByProvinceIdDistrictId(Pageable pageable, List<Long> districtIds, List<Long> provinceIds,
        String wardName);

    Slice<ComboboxResponseDTO> getBusinessArea(Pageable pageable, String name, String code);

    Page<ComboboxStreetDTO> getStreetByProvince(Pageable pageable, List<Long> provinceIds, List<Long> wardIds, String name);
}
