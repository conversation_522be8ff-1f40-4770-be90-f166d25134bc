package com.service.openapis.v1.address.impl;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import com.dto.enterprise.ComboboxDistrictResponseDTO;
import com.dto.enterprise.ComboboxResponseDTO;
import com.dto.enterprise.ComboboxStreetDTO;
import com.dto.enterprise.ComboboxWardResponseDTO;
import com.repository.enterprise.RegionsRepository;
import com.service.openapis.v1.address.AddressV1Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AddressV1ServiceImpl implements AddressV1Service {

    private final RegionsRepository regionsRepository;

    @Override
    public List<ComboboxResponseDTO> getRegions(String regionName) {
        return regionsRepository.getAllRegions(regionName);
    }

    @Override
    public List<ComboboxResponseDTO> getProvinceByRegion(List<Long> regionIds, String provinceName) {
        return regionsRepository.getProvinceByRegion(regionIds, provinceName);
    }

    @Override
    public Slice<ComboboxDistrictResponseDTO> getDistrictByProvince(Pageable pageable, List<Long> provinceIds, String districtName) {
        return regionsRepository.getDistinctByProvince(pageable, provinceIds, districtName);
    }

    @Override
    public Slice<ComboboxWardResponseDTO> getSliceWardByProvinceIdDistrictId(Pageable pageable, List<Long> districtIds, List<Long> provinceIds,
        String wardName) {
        return regionsRepository.getSliceWardByProvinceIdDistrictId(pageable, districtIds, provinceIds, wardName);
    }

    @Override
    public Slice<ComboboxResponseDTO> getBusinessArea(Pageable pageable, String name, String code) {
        return regionsRepository.getBusinessArea(pageable, name, code);
    }

    @Override
    public Page<ComboboxStreetDTO> getStreetByProvince(Pageable pageable, List<Long> provinceIds, List<Long> wardIds, String name) {
        return regionsRepository.getStreetByProvinceIdAndWardId(pageable, provinceIds, wardIds, name);
    }
}
