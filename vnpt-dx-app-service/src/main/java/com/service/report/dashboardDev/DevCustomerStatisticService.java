package com.service.report.dashboardDev;

import java.io.IOException;
import java.util.List;
import org.springframework.context.annotation.Description;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import com.dto.report.dashboardDev.customerStatistic.IDataNewSubscriptionDTO;
import com.dto.report.dashboardDev.customerStatistic.IOverviewNewSubscriptionDTO;
import com.dto.report.dashboardDev.customerStatistic.NewSubscriptionReqDTO;

public interface DevCustomerStatisticService {

    @Description("Service overview dữ liệu top khu vực có thuê bao mới nhất")
    List<IOverviewNewSubscriptionDTO> overviewNewSubscription(NewSubscriptionReqDTO request);

    @Description("Service preview dữ liệu top khu vực có thuê bao mới nhất")
    Page<IDataNewSubscriptionDTO> previewNewSubscription(NewSubscriptionReqDTO request);

    @Description("Service export dữ liệu top khu vực có thuê bao mới nhất")
    InputStreamResource exportNewSubscription(NewSubscriptionReqDTO request) throws IOException;
}
