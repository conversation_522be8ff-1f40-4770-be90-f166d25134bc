package com.service.serviceSuggestion;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.dto.pricing.PricingCommonDetailDTO;
import com.dto.product_solustions.PackageListResDTO;
import com.dto.product_solustions.PackageListResDTO.ProductItemDTO;
import com.dto.services.sugesstion.ServiceCommonDetailDTO;
import com.dto.services.sugesstion.ServiceSuggestionDetailDTO;
import com.enums.product_solutions.SuggestionTypeEnum;
import com.onedx.common.utils.ObjectMapperUtil;
import com.repository.product_solutions.PackageRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class PackageServiceMapper extends ServiceMapper {

    private final PackageRepository packageRepository;

    @Override
    public SuggestionTypeEnum getServiceType() {
        return SuggestionTypeEnum.PACKAGE;
    }

    @Override
    public ServiceCommonDetailDTO mapDetail(ServiceMapperContext serviceMapperContext) {
        switch (serviceMapperContext.getOperationType()) {
            case ALL:
                return mappingPackageDetail(serviceMapperContext);
            case SUGGESTION:
                return mapPackageSuggestDetail(serviceMapperContext);
            default:
                return new ServiceCommonDetailDTO(serviceMapperContext.getServiceSuggestionDTO());
        }
    }

    private ServiceCommonDetailDTO mappingPackageDetail(ServiceMapperContext serviceMapperContext) {
        ServiceCommonDetailDTO detailDTO = new ServiceCommonDetailDTO();
        mapCommonDetail(detailDTO, serviceMapperContext);
        List<ProductItemDTO> productItemList = packageRepository.getListPackageProduct(detailDTO.getId()).stream().map(item -> {
            PackageListResDTO.ProductItemDTO resItem = new PackageListResDTO.ProductItemDTO();
            BeanUtils.copyProperties(item, resItem);
            resItem.setAddons(ObjectMapperUtil.listMapper(item.getLstAddonJson(), PackageListResDTO.ProductAddonDTO.class));
            return resItem;
        }).collect(Collectors.toList());
        detailDTO.setLstPricing(productItemList.stream().map(PricingCommonDetailDTO::new).collect(Collectors.toList()));
        detailDTO.setNumPricing(detailDTO.getLstPricing().size());
        detailDTO.setLstFeature(packageRepository.getListFeatureByPackageId(SuggestionTypeEnum.PACKAGE.name(), detailDTO.getId()));
        // thông tin về số lượng sp loại thiết bị và dịch vụ (nếu có variantId -> sản phẩm thiết bị kèm plan, không -> saas)
        long numOfDeviceItem = productItemList.stream().filter(item -> Objects.nonNull(item.getVariantId())).count();
        detailDTO.setNumOfDevice(numOfDeviceItem);
        detailDTO.setNumOfService(productItemList.size() - numOfDeviceItem);
        return detailDTO;
    }

    private ServiceSuggestionDetailDTO mapPackageSuggestDetail(ServiceMapperContext serviceMapperContext) {
        return null;
    }
}
