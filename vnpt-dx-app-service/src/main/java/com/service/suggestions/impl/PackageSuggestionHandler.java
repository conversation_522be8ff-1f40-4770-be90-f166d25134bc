package com.service.suggestions.impl;

import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import com.dto.pricing.PricingCommonDetailDTO;
import com.dto.product_solustions.PackageListResDTO;
import com.dto.services.sugesstion.ServiceCommonDetailDTO;
import com.dto.suggestions.SuggestionGroupDetailDTO;
import com.dto.suggestions.SuggestionGroupDetailDTO.SuggestionItemDetailDTO;
import com.entity.product_solutions.SuggestionMapping;
import com.enums.product_solutions.ObjectTypeEnum;
import com.enums.product_solutions.SuggestionTypeEnum;
import com.onedx.common.utils.ObjectMapperUtil;
import com.repository.ServiceSuggestion.ServiceSuggestionRepository;
import com.repository.product_solutions.PackageRepository;
import com.service.suggestions.SuggestionHandler;
import lombok.RequiredArgsConstructor;
import lombok.var;

@Component
@RequiredArgsConstructor
public class PackageSuggestionHandler implements SuggestionHandler {

    private final ServiceSuggestionRepository serviceSuggestionRepository;
    private final PackageRepository packageRepository;

    @Override
    public ServiceCommonDetailDTO detailSme(SuggestionMapping mapping) {
        var packageDetail = serviceSuggestionRepository.getPackageSuggestionDetail(mapping.getSuggestObjectDraftId());
        if (packageDetail != null) {
            var productItemList = packageRepository.getListPackageProduct(packageDetail.getId()).stream().map(item -> {
                PackageListResDTO.ProductItemDTO resItem = new PackageListResDTO.ProductItemDTO();
                BeanUtils.copyProperties(item, resItem);
                resItem.setAddons(ObjectMapperUtil.listMapper(item.getLstAddonJson(), PackageListResDTO.ProductAddonDTO.class));
                return resItem;
            }).collect(Collectors.toList());
            var result = new ServiceCommonDetailDTO(packageDetail,
                productItemList.stream().map(PricingCommonDetailDTO::new).collect(Collectors.toList()));
            result.setLstFeature(packageRepository.getListFeatureByPackageId(SuggestionTypeEnum.PACKAGE.name(), packageDetail.getId()));
            // thông tin về số lượng sp loại thiết bị và dịch vụ (nếu có variantId -> sản phẩm thiết bị kèm plan, không -> saas)
            long numOfDeviceItem = productItemList.stream().filter(item -> Objects.nonNull(item.getVariantId())).count();
            result.setNumOfDevice(numOfDeviceItem);
            result.setNumOfService(productItemList.size() - numOfDeviceItem);
            return result;
        }
        return null;
    }

    @Override
    public SuggestionGroupDetailDTO parseGroupInfo(SuggestionMapping mapping) {
        var packageDraftId = mapping.getSuggestObjectDraftId();
        var detail = packageRepository.getSuggestionGroupDetail(packageDraftId);
        if (detail != null) {
            return new SuggestionGroupDetailDTO(ObjectTypeEnum.PACKAGE, detail);
        }
        return null;
    }

    @Override
    public SuggestionItemDetailDTO parseItemInfo(SuggestionMapping mapping) {
        return null;
    }
}
