package com.service.notification.template;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import com.dto.actionNotification.EmailParamDTO;
import com.dto.crm.assignmentRule.notification.AssignmentRuleMailDetailDTO;
import com.enums.ActionNotificationEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.dto.base.ICommonIdNameEmail;
import com.onedx.common.dto.notification.NotificationDTO;
import com.service.notification.impl.ActionNotificationTemplateBase;
import com.util.NotifyUtil;

public class PGD09 extends ActionNotificationTemplateBase {

    public PGD09(Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapAssigneeToObjectContent, CrmObjectTypeEnum objectTypeEnum) {

        // Type
        this.actionNotificationEnum = ActionNotificationEnum.PGD_09;

        String objectType = CrmObjectTypeEnum.getNameFromValue(objectTypeEnum.getValue());
        for (Map.Entry<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> entry : mapAssigneeToObjectContent.entrySet()) {
            ICommonIdNameEmail receiver = entry.getKey();
            AssignmentRuleMailDetailDTO content = entry.getValue();
            // Email
            EmailParamDTO emailParamDTO = new EmailParamDTO();
            emailParamDTO.setEmail(receiver.getEmail());
            HashMap<String, String> lstParam = emailParamDTO.getLstParam();
            lstParam.put("$STAFF_NAME", receiver.getName());
            lstParam.put("$OBJECT_TYPE", objectType);
            lstParam.put("$TABLE", content.getTableHTML());

            this.lstEmailParam.add(emailParamDTO);

            // Notification
            NotificationDTO notificationDTO = NotificationDTO.builder()
                .title(NotifyUtil.getContent(actionNotificationEnum.getTitle()))
                .body(NotifyUtil.getContent(actionNotificationEnum.getContent(), objectType))
                .screenId(actionNotificationEnum.getScreentId())
                .portalType(Objects.equals(receiver.getPortalName(), PortalType.ADMIN) ? PortalType.ADMIN.getType() : PortalType.DEV.getType())
                .status(StatusEnum.INACTIVE.value)
                .createdAt(LocalDateTime.now())
                .userId(receiver.getId())
                .payload(objectTypeEnum.toString())
                .build();
            this.lstNotificationParam.add(notificationDTO);
        }
    }
}
