package com.service.enterprise;

import java.util.List;
import java.util.Set;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.dto.customerContact.ContactMessageDTO;
import com.onedx.common.dto.enterprise.CommonResponseDTO;
import com.dto.enterprise.detail.ChangePassRequest;
import com.dto.enterprise.detail.ChangeUsernameRequest;
import com.dto.enterprise.detail.EnterpriseBusinessDetailRequestDTO;
import com.dto.enterprise.detail.EnterpriseContactPersonReqDTO;
import com.dto.enterprise.detail.EnterpriseRepresentativeDetailRequestDTO;
import com.dto.enterprise.detail.EnterpriseSMEDetailRequestDTO;
import com.dto.enterprise.detail.UpdateEnterpriseContactDetailRequestDTO;
import com.dto.enterprise.detail.UpdateEnterprisePersonalDetailRequestDTO;
import com.dto.enterprise.detail.UpdateEnterpriseRequestDTO;
import com.dto.enterprise.update.EnterpriseAssigneePartitionUpdate;
import com.dto.enterprise.update.EnterpriseUpdateStatusRequestDTO;
import com.onedx.common.constants.enums.enterprise.EnterpriseTypeEnum;
import com.onedx.common.dto.interestProduct.InterestProductDTO;

/**
 * <AUTHOR> NghiaPN
 * @version : 1.0 11/5/2021
 */
public interface EnterpriseUpdateService {

    /**
     * Cập nhật trạng thái lưu trữ của enterprise và trạng thái hoạt động của user
     */
    Boolean updateEnterpriseStatus(EnterpriseUpdateStatusRequestDTO requestDTO);

    /**
     * Chỉnh sửa thông tin doanh nghiệp
     */
    void updateBusinessDetail(EnterpriseBusinessDetailRequestDTO request, CustomerTypeEnum customerType);

    /**
     * Chỉnh sửa thông tin người đại diện
     */
    void updateRepresentativeDetail(EnterpriseRepresentativeDetailRequestDTO request, CustomerTypeEnum customerType);

    /**
     * Chỉnh sửa thông tin liên hệ
     *
     * @param request Thông tin liên hệ cần chỉnh sửa
     * @return CommonResponseDTO
     */
    CommonResponseDTO updateContactDetail(UpdateEnterpriseContactDetailRequestDTO request);

    /**
     * Chỉnh sửa thông tin cá nhân
     */
    void updateSMEDetail(EnterpriseSMEDetailRequestDTO request, CustomerTypeEnum customerType);

    /**
     * Chỉnh sửa thông tin liên hệ của khách hàng cá nhân tiềm năng
     *
     */
    void updateContactPersonalDetail(EnterpriseContactPersonReqDTO request);

    /**
     * Chỉnh sửa thông tin doanh nghiệp
     */
    void updateBusiness(UpdateEnterpriseRequestDTO request, CustomerTypeEnum customerType);

    void activeAcount(Long id, CustomerTypeEnum customerType) throws Exception;

    void sendEmailactiveAcount(Long id, CustomerTypeEnum customerType) throws Exception;

    void changeUsername(Long id, ChangeUsernameRequest changePassRequest) throws Exception;

    void changePass(Long id, ChangePassRequest changePassRequest) throws Exception;

    void updatePersonal(UpdateEnterprisePersonalDetailRequestDTO request, EnterpriseTypeEnum enterpriseType);

    void updateAssigneeAndPartition(EnterpriseAssigneePartitionUpdate updateDTO);

    void addEnterpriseToMc(Set<Long> lstEnterpriseId, Set<Long> lstMcId);

    /**
     * Xoá list khách hàng
     */
    void deleteEnterprise(Set<Long> lstEnterpriseId);

    void updateEnterpriseFileAttach(Long enterpriseId, List<Long> attachmentIds);

    void addEnterpriseMessageInfo(Long enterpriseId, List<ContactMessageDTO> request);

    void updateEnterpriseMessageInfo(Long enterpriseId, List<ContactMessageDTO> request);

    void addInterestProductInfo(Long enterpriseId, List<InterestProductDTO> request);

    void updateInterestProductInfo(Long enterpriseId, List<InterestProductDTO> request);

}
