/**
 * <AUTHOR> VinhNT
 * @version    : 1.0
 */
package com.service.pricing.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.InputMismatchException;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.Query;

import com.dto.pricing.PricingConfigDTO;
import com.dto.pricing.PricingConfigResDTO;
import com.dto.pricing.PricingPromotionResDTO;
import com.dto.services.*;
import com.entity.pricing.*;
import com.entity.product_variant.Variant;
import com.enums.ServiceDetailTabEnum;
import com.onedx.common.constants.enums.services.VariantApplyEnum;
import com.repository.enterprise.RegionsRepository;
import com.repository.pricing.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.constant.CustomerTicketConstant;
import com.constant.EntitiesConstant.DeleteFlag;
import com.constant.PricingConst;
import com.constant.PricingConst.MailParam;
import com.constant.SeoTypeCodeConstant;
import com.constant.SubscriptionConstant;
import com.constant.enums.action.log.ActionLogTypeEnum;
import com.constant.enums.coupon.ApproveTypeEnum;
import com.constant.enums.coupon.CouponPricingPlanTypeEnum;
import com.constant.enums.coupon.CouponPricingTypeEnum;
import com.constant.enums.coupon.CouponServiceTypeEnum;
import com.constant.enums.file.attach.AccessTypeEnum;
import com.constant.enums.pricing.IntegrationEnum;
import com.constant.enums.pricing.PeriodFilterTypeEnum;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.pricing.ValidateCodeAction;
import com.constant.enums.pricing.ValidateCodeType;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.constant.enums.subscription.SubTypeEnum;
import com.constant.enums.subscription_plans.SubscriptionPlanRecommendedStatusEnum;
import com.dto.action.log.ActionLogTemplateDTO;
import com.dto.addons.AddonRelationPricingDTO;
import com.dto.addons.AddonsResDTO;
import com.dto.coupons.CouponPopupTreeResDTO;
import com.dto.coupons.CouponPricingResDTO;
import com.dto.coupons.CouponPricingResDTO.PricingResDTO;
import com.dto.coupons.CouponPricingResDTO.PricingStrategyResDTO;
import com.dto.coupons.CouponServiceDTO;
import com.dto.coupons.CouponServiceResDTO;
import com.dto.coupons.PopupTreeResDTO;
import com.dto.file.attach.FileAttachDTO;
import com.dto.mobile.PricingDetailRespDTO;
import com.dto.mobile.PricingPlanDetailRespDTO;
import com.dto.pricing.ApproveReqDTO;
import com.dto.pricing.IDetailPricingMultiPlanDTO;
import com.dto.pricing.IGetTopViewPricingDTO;
import com.dto.pricing.IPaymentCycleDTO;
import com.dto.pricing.IPmpIdOldAndNewDTO;
import com.dto.pricing.IPricingIdAndMultiPlanIdDTO;
import com.dto.pricing.IPricingMultiPlanQuantityDTO;
import com.dto.pricing.IPricingSaaSResDTO;
import com.dto.pricing.PaymentCycleDTO;
import com.dto.pricing.PlanPeriodResDTO;
import com.dto.pricing.PricingAddonRes;
import com.dto.pricing.PricingAddonResDTO;
import com.dto.pricing.PricingApplyDTO;
import com.dto.pricing.PricingApproveReqDTO;
import com.dto.pricing.PricingDetailHisResDTO;
import com.dto.pricing.PricingDetailResDTO;
import com.dto.pricing.PricingDetailResDTO.AddOn;
import com.dto.pricing.PricingHistoryResDTO;
import com.dto.pricing.PricingIntegrationReqDTO;
import com.dto.pricing.PricingIntegrationUpdateReqDTO;
import com.dto.pricing.PricingLevelReqDTO;
import com.dto.pricing.PricingListDevRes;
import com.dto.pricing.PricingListDevResDTO;
import com.dto.pricing.PricingNameDTO;
import com.dto.pricing.PricingPriceInfoDTO;
import com.dto.pricing.PricingReqDTO;
import com.dto.pricing.PricingReqDTO.PricingStrategy;
import com.dto.pricing.PricingReqDTO.RowAddon;
import com.dto.pricing.PricingReqDTO.RowLimited;
import com.dto.pricing.PricingReqDTO.RowTax;
import com.dto.pricing.PricingReqDTO.SetupFee;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.pricing.PricingSettingReqDTO;
import com.dto.pricing.PricingSettingReqDTO.Plan;
import com.dto.pricing.PricingTaxRes;
import com.dto.pricing.PricingTransactionDTO;
import com.dto.pricing.multiplePeriod.PricingMultiplePeriodResDTO;
import com.dto.report.dashboardSme.PricingComboPlanReportResDTO;
import com.dto.report.dashboardSme.ServiceComboReportResDTO;
import com.dto.report.dashboardSme.UserNameFilterResDTO;
import com.dto.seo.SeoDTO;
import com.dto.subscription_plans.OrderListResponseDTO;
import com.dto.subscriptions.SubscriptionCalculateDTO;
import com.dto.subscriptions.SubscriptionServiceBasicDTO;
import com.dto.users.UserAdminDTO;
import com.entity.action.log.ActionLogServiceGroup;
import com.entity.addons.Addon;
import com.entity.addons.AddonDraft;
import com.entity.bills.Bills;
import com.entity.combo.ComboPlan;
import com.entity.combo.ComboPlanDraft;
import com.entity.combo.ComboPricing;
import com.entity.coupons.CouponPricingPlan;
import com.entity.currency.Currency;
import com.entity.feature.Feature;
import com.entity.file.attach.FileAttach;
import com.entity.rating.ServiceReaction;
import com.entity.seo.Seo;
import com.entity.serviceGroup.ServiceGroupDraft;
import com.entity.serviceGroup.ServiceGroupPricingItem;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.Subscription;
import com.entity.systemParam.ApprovedRule;
import com.entity.tax.Tax;
import com.entity.unitLimited.UnitLimited;
import com.entity.units.Unit;
import com.enums.ActionNotificationEnum;
import com.enums.ActionNotificationStatusEnum;
import com.enums.ApproveStatusEnum;
import com.enums.DisplayStatus;
import com.exception.ErrorKey;
import com.exception.ErrorKey.Services;
import com.exception.Resources;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mapper.pricing.PricingDetailMapper;
import com.mapper.pricing.PricingDraftDetailMapper;
import com.mapper.pricing.PricingReqMapper;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.customFields.CustomFieldCategoryEnum;
import com.onedx.common.constants.enums.customFields.EntityTypeEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.enums.history.interactionHistory.InteractiveObjectTypeEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.pricings.PeriodTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.onedx.common.constants.enums.systemParams.ApprovedChangePricingEnum;
import com.onedx.common.constants.enums.systemParams.ApprovedTypeEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.converter.SetConverter;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingPeriodAddonDTO;
import com.onedx.common.dto.mail.MailParamResDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.entity.customField.CustomField;
import com.onedx.common.entity.customField.CustomFieldDraftValue;
import com.onedx.common.entity.customField.CustomLayout;
import com.onedx.common.entity.notification.ActionNotification;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.MessageKeyConstant.Validation;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.repository.emails.mailTemplate.ParamEmailRepository;
import com.onedx.common.repository.notifications.ActionNotificationRepository;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.action.log.ActionLogServiceGroupRepository;
import com.repository.addons.AddonDraftRepository;
import com.repository.addons.AddonRepository;
import com.repository.bills.BillsRepository;
import com.repository.combo.ComboPlanDraftRepository;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboPricingRepository;
import com.repository.coupons.CouponAddonRepository;
import com.repository.coupons.CouponPricingApplyRepository;
import com.repository.coupons.CouponPricingPlanRepository;
import com.repository.coupons.CouponPricingRepository;
import com.repository.coupons.CouponRepository;
import com.repository.currency.CurrencyRepository;
import com.repository.customField.CustomFieldDraftValueRepository;
import com.repository.customField.CustomFieldRepository;
import com.repository.customField.CustomLayoutRepository;
import com.repository.feature.FeatureRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.product_variant.VariantRepository;
import com.repository.rating.ServiceReationRepository;
import com.repository.seo.SeoRepository;
import com.repository.serviceGroup.ServiceGroupDraftRepository;
import com.repository.serviceGroup.ServiceGroupPricingItemRepository;
import com.repository.services.ServiceRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.systemParam.ApprovedRuleRepository;
import com.repository.tax.TaxRepository;
import com.repository.unitLimited.UnitLimitedRepository;
import com.repository.units.UnitRepository;
import com.repository.users.UserRepository;
import com.service.action.log.ActionLogService;
import com.service.action.log.ActionLogServiceGroupService;
import com.service.calculator.ContainedTaxListCalculator;
import com.service.calculator.PriceCalculator;
import com.service.coupon.CouponService;
import com.service.customField.impl.CustomFieldManager;
import com.service.email.EmailService;
import com.service.history.ObjectInteractiveHistoryService;
import com.service.multiplePeriod.SubMultiplePeriod;
import com.service.pricing.PricingService;
import com.service.rating.ServiceReactionService;
import com.service.seo.SeoService;
import com.service.serviceGroup.ServiceGroupService;
import com.service.services.ServicesService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.subscriptions.SubscriptionDetailService;
import com.service.system.param.SystemParamService;
import com.util.AuthUtil;
import com.util.NotifyUtil;
import com.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;

@Service
@Slf4j
public class PricingServiceImpl implements PricingService {

    private static final int ADDON_PRICING_MAX_ACCEPTED = 30;
    private static final Integer DEFAULT_QUANTITY = 0;
    private final String[] pricingM = {"pricing"};
    private final String[] serviceM = {"services"};
    private final String[] userM = {"user"};
    private final String SPACE = " ";
    private final String STRING_EMPTY = "";
    private static final String SORT_LIST_PRICING_SERVICE_CREATED_AT_PROPERTY = "serviceCreatedAt";
    private static final String POPUP_ADDON_SORT_FIELD_BONUS_TYPE = "bonusTypeSort";
    private static final String POPUP_ADDON_SORT_FIELD_TYPE = "typeSort";
    private static final String POPUP_ADDON_SORT_FIELD_PAYMENT_CYCLE = "paymentCycle";
    private static final String TITLE_CYCLE_TYPE_DAILY = "ngày";
    private static final String TITLE_CYCLE_TYPE_WEEKLY = "tuần";
    private static final String TITLE_CYCLE_TYPE_MONTHLY = "tháng";
    private static final String TITLE_CYCLE_TYPE_YEARLY = "năm";
    private static final String TITLE_CYCLE_TYPE_UNLIMITED = "Không giới hạn";
    private static final String PRICING_SERVICE_TYPE = "SERVICE";
    private static final Long DEFAULT_PARENT_ID = -1L;
    private static final String CAU_HINH_GOI_DICH_VU = "CAU_HINH_GOI_DICH_VU";

    @Autowired
    private CustomFieldDraftValueRepository customFieldDraftValueRepository;
    @Autowired
    private CustomFieldRepository customFieldRepository;
    @Autowired
    CustomLayoutRepository customLayoutRepository;
    @Autowired
    private CustomFieldManager customFieldManager;
    @Autowired
    private ComboPricingRepository comboPricingRepository;
    @Autowired
    private ComboPlanDraftRepository comboPlanDraftRepository;
    @Autowired
    private ApprovedRuleRepository approvedRuleRepository;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private SeoService seoService;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private PricingDraftRepository pricingDraftRepository;
    @Autowired
    private PricingDetailMapper pricingDetailMapper;
    @Autowired
    private PricingTaxRepository pricingTaxRepository;
    @Autowired
    private PricingDraftDetailMapper pricingDraftDetailMapper;
    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    @Lazy
    private ServicesService servicesService;
    @Autowired
    private UnitLimitedRepository unitLimitedRepository;
    @Autowired
    private PricingAddonRepository pricingAddonRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private CurrencyRepository currencyRepository;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private TaxRepository taxRepository;
    @Autowired
    private AddonRepository addonRepository;
    @Autowired
    private AddonDraftRepository addonDraftRepository;
    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private PricingReqMapper pricingReqMapper;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private ServiceGroupPricingItemRepository serviceGroupPricingItemRepository;
    @Autowired
    private ServiceGroupDraftRepository serviceGroupDraftRepository;
    @Autowired
    @Lazy
    private ServiceGroupService serviceGroupService;
    @Autowired
    private ActionLogServiceGroupService actionLogServiceGroupService;
    @Autowired
    private ActionLogServiceGroupRepository actionLogServiceGroupRepository;
    @Autowired
    private ActionLogService actionLogService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CouponPricingRepository couponPricingRepository;
    @Autowired
    private CouponPricingApplyRepository couponPricingApplyRepository;
    @Autowired
    private CouponAddonRepository couponAddonRepository;
    @Autowired
    private ParamEmailRepository paramEmailRepository;
    @Autowired
    private EmailService emailService;
    @Autowired
    private ActionNotificationRepository actionNotificationRepository;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private PricingMultiPlanAddonRepository pricingMultiPlanAddonRepository;
    @Autowired
    private PricingPlanDetailRepository pricingPlanDetailRepository;
    @Autowired
    private PricingSetupFeeTaxRepository pricingSetupFeeTaxRepository;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private CouponPricingPlanRepository couponPricingPlanRepository;
    @Autowired
    private SeoRepository seoRepository;
    @Autowired
    private FileAttachRepository fileAttachRepository;
    @Autowired
    private BillsRepository billsRepository;
    @Autowired
    private ObjectInteractiveHistoryService interactiveHistoryService;
    @Autowired
    private PricingSetupInfoRepository pricingSetupInfoRepository;
    @Autowired
    private ServiceReationRepository serviceReactionRepository;
    @Autowired
    private VariantRepository variantRepository;
    @Autowired
    private SubscriptionDetailService subscriptionDetailService;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    @Qualifier("asyncExecutorSendRequestApproveNotif")
    private Executor executor;
    @Autowired
    private ServiceReactionService serviceReactionService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private SubMultiplePeriod subMultiplePeriod;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private PricingVariantRepository pricingVariantRepository;
    @Autowired
    private RegionsRepository regionsRepository;

    @Override
    public Pricing findByIdAndDeletedFlag(Long pricingId, Integer deletedFlag) {
        return pricingRepository.findByIdAndDeletedFlag(pricingId, deletedFlag)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID, String.valueOf(pricingId)));
    }

    @Override
    public boolean isLatestPricing(Pricing pricing) {
        return pricingRepository.isLatestPricing(pricing.getId(), pricing.getPricingDraftId());
    }

    @Override
    public PricingDetailResDTO getPricingDetail(Long id, PortalType portalType, PricingDetailInputEnum type) {
        PricingDraft currentPricingDraft = getCurrentPricingDraft(id);
        // Kiem tra quyen truy cap khi dang nhap bang tai khoan dev
        checkRolePortal(portalType, currentPricingDraft.getServiceId());
        PricingDetailResDTO response = new PricingDetailResDTO();

        //Kiem tra dieu kien lay du lieu
        //Neu la PROCESSING: Se lay du lieu trong bang nhap
        //Neu la APPROVED: Se lay du lieu tu bang pricing da duoc APPROVED gan day nhat
        if (type.equals(PricingDetailInputEnum.PROCESSING)) {
            response = pricingDraftDetailMapper.toDto(currentPricingDraft);
            // fill thông tin pricing config
            response.setPricingConfig(fillPricingConfigResponse(currentPricingDraft.getPricingConfig()));
            response.setPricingCommitmentTime(currentPricingDraft.getPricingCommitmentTime());
            response.setPricingPromotion(new PricingPromotionResDTO(currentPricingDraft.getPricingPromotion()));
            response.setHasRenew(
                Objects.nonNull(currentPricingDraft.getHasRenew()) ? YesNoEnum.valueOf(currentPricingDraft.getHasRenew()) : YesNoEnum.YES);
            response.setIsOneTime(currentPricingDraft.getIsOneTime());
        } else if (type.equals(PricingDetailInputEnum.APPROVED)) {
            Pricing pricing = pricingRepository.findByDraftIdApproved(id).orElseThrow(() -> {
                String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(messageNotFound, Resources.PRICING,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });
            response = pricingDetailMapper.toDto(pricing);
            response.setPricingConfig(fillPricingConfigResponse(pricing.getPricingConfig()));
            response.setPricingCommitmentTime(pricing.getPricingCommitmentTime());
            response.setPricingPromotion(new PricingPromotionResDTO(pricing.getPricingPromotion()));
            response.setIsOneTime(pricing.getIsOneTime());
            response.setHasRenew(Objects.nonNull(pricing.getHasRenew()) ? YesNoEnum.valueOf(pricing.getHasRenew()) : YesNoEnum.YES);
            // Lưu lịch sử xem gói
            if (portalType == PortalType.SME) {
                interactiveHistoryService.view(InteractiveObjectTypeEnum.PRICING, id, pricing.getId(), null, null);
            }
        }
        getPricingDetail(response, type, portalType, id);
        return response;
    }

    @Override
    public Page<PricingHistoryResDTO> getPricingHistory(Long id, PortalType portalType, Pageable pageable) {
        PricingDraft currentPricingDraft = getCurrentPricingDraft(id);
        // Kiem tra quyen truy cap khi dang nhap bang tai khoan dev
        checkRolePortal(portalType, currentPricingDraft.getServiceId());
        return pricingRepository.findHistoryByDraftId(id, pageable);
    }

    @Override
    public PricingDetailHisResDTO getPricingFromHistory(Long id, PortalType portalType, ApproveStatusEnum approve) {
        PricingDetailResDTO response;
        if (!Objects.equals(approve, ApproveStatusEnum.APPROVED)) {
            PricingDraft currentPricingDraft = getCurrentPricingDraft(id);
            checkRolePortal(portalType, currentPricingDraft.getServiceId());
            response = getPricingDetail(id, portalType, PricingDetailInputEnum.PROCESSING);
            return getServiceInfo(response, currentPricingDraft.getId(), currentPricingDraft.getServiceId(), currentPricingDraft.getId());
        } else {
            Pricing pricing = pricingRepository.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(messageNotFound, Resources.PRICING,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });
            response = pricingDetailMapper.toDto(pricing);
            getPricingDetail(response, PricingDetailInputEnum.APPROVED, portalType, id);
            return getServiceInfo(response, pricing.getPricingDraftId(), pricing.getServiceId(), pricing.getId());
        }
    }

    @Override
    public String getTitleCycleType(Integer type) {
        type = Objects.nonNull(type) ? type : -1;
        String title = TITLE_CYCLE_TYPE_UNLIMITED;
        switch (type) {
            case 0:
                title = TITLE_CYCLE_TYPE_DAILY;
                break;
            case 1:
                title = TITLE_CYCLE_TYPE_WEEKLY;
                break;
            case 2:
                title = TITLE_CYCLE_TYPE_MONTHLY;
                break;
            case 3:
                title = TITLE_CYCLE_TYPE_YEARLY;
                break;
        }
        return title;
    }

    /**
     * Lấy thông tin chi tiết dịch vụ
     */
    private PricingDetailHisResDTO getServiceInfo(PricingDetailResDTO response, Long draftId, Long serviceId, Long pricingId) {

        PricingDetailHisResDTO res = new PricingDetailHisResDTO();
        BeanUtils.copyProperties(response, res);
        // Set thong tin ban nhap
        pricingDraftRepository.findByIdAndDeletedFlag(draftId, DeletedFlag.NOT_YET_DELETED.getValue())
            .ifPresent(pricingDraft -> {
                res.setPricingNameDraft(pricingDraft.getPricingName());
                res.setPricingIdDraft(draftId);
            });

        serviceRepository.findByIdAndDeletedFlag(serviceId, DeletedFlag.NOT_YET_DELETED.getValue())
            .ifPresent(serviceEntity -> {
                res.setServiceId(serviceEntity.getId());
                res.setServiceName(serviceEntity.getServiceName());
                res.setServiceStatus(ApproveTypeEnum.valueOf(serviceEntity.getStatus()));
            });

        pricingDraftRepository.getVersion(draftId, pricingId)
            .ifPresent(res::setVersion);
        return res;
    }

    /**
     * Kiem tra quyen
     *
     * @param portalType the portal type
     * @param serviceId  the service id
     */
    private void checkRolePortal(PortalType portalType, Long serviceId) {
        Set<String> roles = Objects.requireNonNull(AuthUtil.getCurrentUser()).getAuthorities()
            .stream().map(GrantedAuthority::getAuthority)
            .collect(Collectors.toSet());
        //neu khong phai la admin nhung portal la admin -> Khong duoc phep
        if (!roles.contains(RoleType.ADMIN.getValue())
            && !roles.contains(RoleType.FULL_ADMIN.getValue())
            && PortalType.ADMIN.equals(portalType)) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        if (PortalType.DEV.equals(portalType)) {
            ServiceEntity currentService = servicesService.getCurrentService(serviceId);
            if (!Objects.equals(AuthUtil.getCurrentParentId(), currentService.getUserId())) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
        }
    }

    /**
     * Lấy thông tin chi tiết gói dịch vụ
     */
    private void getPricingDetail(PricingDetailResDTO response, PricingDetailInputEnum type, PortalType portalType, Long pricingDraftId) {

        Long id = response.getId();

        boolean hasApproved = pricingRepository.existsByPricingDraftId(id);
        boolean isOldPricingValue;
        List<PricingSetupFeeTax> pricingSetupFeeTaxes;
        response.setHasApproved(hasApproved ? "YES" : "NO");

        // set list chiến lược định giá
        List<PricingMultiPlan> pricingMultiPlans;
        if (PricingDetailInputEnum.PROCESSING.equals(type)) {
            // kiểm tra xem pricing draft có phải dữ liệu cũ hay không
//          fixbug ko hien chu ky thanh toan pricing multi plan voi voi TH Dev sua goi ban KHDN (co pricing migrate Id not null ...)
            isOldPricingValue = pricingDraftRepository.existsByIdAndPaymentCycleIsNotNullAndCycleTypeIsNotNullAndMigrationIdIsNull(id);
            // nếu pricing draft mà đang ở trạng thái approve -> lấy theo bản pricing mới nhất
            if (pricingDraftRepository.existsByIdAndApprove(id, ApproveStatusEnum.APPROVED.value)) {
                pricingMultiPlans =
                    !isOldPricingValue ? pricingMultiPlanRepository.getAllPricingMultiPlanLatestByPricingDraftId(id) : new ArrayList<>();
                pricingSetupFeeTaxes = pricingSetupFeeTaxRepository.getAllLatestPricingByPricingDraftId(id);
            } else {
                // nếu không lấy theo bảng pricing draft hiện tại (những pricing_multi_plan có pricing_id = mull)
                pricingMultiPlans = !isOldPricingValue ? pricingMultiPlanRepository
                    .findAllByPricingDraftIdAndDeletedFlagAndPricingIdIsNull(id, DeletedFlag.NOT_YET_DELETED.getValue())
                    : new ArrayList<>();
                pricingSetupFeeTaxes = pricingSetupFeeTaxRepository.findAllByPricingDraftIdAndPricingIdIsNull(id);
            }
        } else {
            // kiểm tra xem pricing có phải dữ liệu cũ hay không
//          fixbug ko hien chu ky thanh toan pricing multi plan voi voi TH Dev sua goi ban KHDN (co pricing migrate Id not null ...)
            isOldPricingValue = false;

            pricingMultiPlans = pricingMultiPlanRepository.findAllByPricingIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
            pricingSetupFeeTaxes = pricingSetupFeeTaxRepository.findAllByPricingId(id);
        }

        List<PricingDetailResDTO.PricingStrategy> pricingPlansDTO = new ArrayList<>();
        if (!isOldPricingValue && !CollectionUtils.isEmpty(pricingMultiPlans)) {
            pricingMultiPlans.forEach(pricingPlan -> pricingPlansDTO.add(getPricingPlanDetailResDTO(pricingPlan)));
            response.setPricingStrategies(pricingPlansDTO);
        }

        // set setup fee tax
        if (!CollectionUtils.isEmpty(pricingSetupFeeTaxes)) {
            // Set thông tin phí thiết lập gói cước
            List<SetupFee> setupFees = new ArrayList<>();
            pricingSetupFeeTaxes.forEach(fee -> {
                SetupFee setupFee = new SetupFee();
                BeanUtils.copyProperties(fee, setupFee);
                setupFee.setHasTax(fee.getHasTax() != null ? YesNoEnum.valueOf(fee.getHasTax()) : YesNoEnum.NO);
                setupFee.setPercent(ObjectUtil.getOrDefault(fee.getPercent(), 0.0));
                setupFees.add(setupFee);
            });
            response.setSetupFees(setupFees);

            // thông tin thuế của gói cước
            response.setHasTaxSetupFee(
                Objects.nonNull(pricingSetupFeeTaxes.get(0).getHasTax()) ? YesNoEnum.valueOf(pricingSetupFeeTaxes.get(0).getHasTax())
                    : null);
            List<PricingDetailResDTO.Tax> setupFeeTaxes = new ArrayList<>();
            pricingSetupFeeTaxes.forEach(tax -> {
                Optional<Tax> taxDBOptional = taxRepository.findByIdAndDeletedFlag(tax.getTaxId(), DeletedFlag.NOT_YET_DELETED.getValue());
                taxDBOptional
                    .ifPresent(value -> setupFeeTaxes.add(new PricingDetailResDTO.Tax(tax.getTaxId(), value.getName(), tax.getPercent(),
                        Objects.nonNull(tax.getHasTax()) ? YesNoEnum.valueOf(tax.getHasTax())
                            : null)));
            });
            response.setSetupFeeTaxList(setupFeeTaxes);
        }

        // tạo 1 array pricingMultiPlans 1 phần tử cho dữ liệu cũ
        if (isOldPricingValue) {
            pricingPlansDTO.add(createFakeDataPricingPlanForOldPricing(id, type));
            response.setPricingStrategies(pricingPlansDTO);
        }

        //Set don vi tinh
        if (Objects.nonNull(response.getCurrencyId())) {
            currencyRepository.findById(response.getCurrencyId()).ifPresent(currency -> {
                response.setCurrencyName(currency.getCurrencyType());
            });
        }

        // Set gia tri cho taxList
        List<PricingDetailResDTO.Tax> outTaxes = new ArrayList<>();
        List<PricingTaxRes> pricingTaxes;
        if (Objects.equals(type, PricingDetailInputEnum.PROCESSING)) {
            pricingTaxes = pricingTaxRepository.getPricingTaxByDraft(id);
            pricingTaxes.forEach(t -> {
                PricingDetailResDTO.Tax tax = new PricingDetailResDTO.Tax();
                tax.setTaxId(t.getTaxId());
                tax.setTaxName(t.getTaxName());
                tax.setPercent(t.getPercent());
                tax.setHasTax(Objects.nonNull(t.getHasTax()) ? YesNoEnum.valueOf(t.getHasTax()) : YesNoEnum.UNSET);
                outTaxes.add(tax);
            });
        } else {
            pricingTaxes = pricingTaxRepository.getPricingTax(id);
            pricingTaxes.forEach(t -> {
                PricingDetailResDTO.Tax tax = new PricingDetailResDTO.Tax();
                tax.setTaxId(t.getTaxId());
                tax.setTaxName(t.getTaxName());
                tax.setPercent(t.getPercent());
                tax.setHasTax(Objects.nonNull(t.getHasTax()) ? YesNoEnum.valueOf(t.getHasTax()) : YesNoEnum.UNSET);
                outTaxes.add(tax);
            });
        }

        response.setTaxList(outTaxes);

        // thay đổi ngày 09/06/2021 tất cả loại thuế có cùng giá trị hasTax.
        if (!CollectionUtils.isEmpty(outTaxes)) {
            response.setHasTax(YesNoEnum.valueOf(pricingTaxes.get(0).getHasTax()));
        } else {
            response.setHasTax(YesNoEnum.NO);
        }

        // Set gia tri cho bonusServiceList
        List<AddOn> outAddOns = new ArrayList<>();
        if (!isOldPricingValue) {
            if (Objects.equals(type, PricingDetailInputEnum.PROCESSING)) {
                Set<PricingAddonRes> pricingAddons = pricingAddonRepository
                    .findByPricingDraftIds(id, BonusTypeEnum.ONCE.value);
                outAddOns = getListPricingAddonsRes(pricingAddons);
            } else {
                Set<PricingAddonRes> pricingAddons = pricingAddonRepository
                    .findByPricingId(id, BonusTypeEnum.ONCE.value);
                outAddOns = getListPricingAddonsRes(pricingAddons);
            }
        }

        response.setAddonList(outAddOns);

        // Set gia tri cho featureList
        String listFeatureId = response.getListFeatureId();
        String[] split = StringUtils.split(StringUtils.defaultString(listFeatureId, CharacterConstant.BLANK), CharacterConstant.COMMA);
        String[] featureArr = StringUtils.stripAll(split);
        List<Long> featureIds = Arrays.stream(featureArr)
            .map(Long::valueOf).collect(Collectors.toList());
        List<PricingDetailResDTO.Feature> outFeatures = new ArrayList<>();
        featureRepository.findAllById(featureIds).forEach(feature -> {
            PricingDetailResDTO.Feature feat = new PricingDetailResDTO.Feature();
            feat.setId(feature.getId());
            feat.setName(feature.getName());
            outFeatures.add(feat);
        });
        outFeatures.sort(Comparator.comparing(item -> featureIds.indexOf(item.getId())));
        response.setFeatureList(outFeatures);

        // Lấy thông tin cấu hình SEO của gói dịch vụ
        if (Objects.equals(type, PricingDetailInputEnum.PROCESSING)) {
            List<SeoDTO> seoPricingDraft = new ArrayList<>();
            seoRepository.getAllSeoByPricingDraftId(id).forEach(d -> {
                d.setPricingDraftId(id);
                d.setPricingId(pricingRepository.findMaxVersionByDraftId(id));
                d.setKeyWord(seoRepository.getAllKeywordBySeoId(d.getSeoId()));
                if (Objects.nonNull(d.getFileAttachId())) {
                    if (fileAttachRepository.getFiledAttachBySeoId(d.getSeoId()).contains(d.getFileAttachId())) {
                        seoPricingDraft.add(d);
                    }
                } else {
                    seoPricingDraft.add(d);
                }
            });
            response.setSeoList(seoPricingDraft);
        } else {
            List<SeoDTO> seoPricing = new ArrayList<>();
            seoRepository.getAllSeoByPricingId(id).forEach(p -> {
                p.setPricingId(id);
                p.setPricingDraftId(pricingDraftId);
                p.setKeyWord(seoRepository.getAllKeywordBySeoId(p.getSeoId()));
                if (Objects.nonNull(p.getFileAttachId())) {
                    if (fileAttachRepository.getFiledAttachBySeoId(p.getSeoId()).contains(p.getFileAttachId())) {
                        seoPricing.add(p);
                    }
                } else {
                    seoPricing.add(p);
                }
            });
            response.setSeoList(seoPricing);
        }

        // Set creationLayoutId và danh sách các custom field value
        pricingDraftRepository.findById(pricingDraftId).ifPresent(pricingDraft -> {
            Long layoutId = pricingDraft.getCreationLayoutId();
            layoutId = layoutId != null ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.PRICING.getValue());
            List<com.onedx.common.dto.customFields.CustomFieldValueDTO> lstFieldValueDTO = customFieldManager.getListFieldDraftValue(layoutId, EntityTypeEnum.PRICING.getValue(),
                pricingDraft.getId());
            response.setCreationLayoutId(layoutId);
            response.setLstCustomFields(lstFieldValueDTO);
        });

        // Lấy thông tin ảnh gói cước
        response.setPricingImage(getPricingImage(type, pricingDraftId));
    }

    private List<AddOn> getListPricingAddonsRes(Set<PricingAddonRes> pricingAddons) {
        return pricingAddons.stream().map(pricingAddonRes -> {
            AddOn addOn = new AddOn();
            addOn.setId(pricingAddonRes.getId());
            addOn.setName(pricingAddonRes.getName());
            addOn.setServiceName(pricingAddonRes.getServiceName());
            addOn.setCode(pricingAddonRes.getCode());
            addOn.setDisplayed(DisplayStatus.valueOf(pricingAddonRes.getDisplayed()));
            addOn.setIsRequired(YesNoEnum.valueOf(pricingAddonRes.getIsRequired()));
            addOn.setBonusValue(pricingAddonRes.getBonusValue());
            addOn.setType(Objects.nonNull(pricingAddonRes.getType()) ? CycleTypeEnum.valueOf(pricingAddonRes.getType()) : null);
            addOn.setBonusType(
                Objects.nonNull(pricingAddonRes.getBonusType()) ? BonusTypeEnum.valueOf(pricingAddonRes.getBonusType()) : null);
            return addOn;
        }).collect(Collectors.toList());
    }

    /**
     * Hàm fake data cho list PricingStrategies 1 phần tử cho pricing detail
     */
    private PricingDetailResDTO.PricingStrategy createFakeDataPricingPlanForOldPricing(Long pricingId, PricingDetailInputEnum type) {
        PricingDetailResDTO.PricingStrategy response = new PricingDetailResDTO.PricingStrategy();

        CycleTypeEnum cycleType;
        Integer paymentCycle;
        Integer numberOfCycles;
        Integer numberOfTrial;
        Long freeQuantity;
        TimeTypeEnum trialType;
        PricingPlanEnum pricingPlan;
        Long unitId;
        BigDecimal price;
        List<UnitLimited> unitLimitedList;
        List<AddOn> addonsList;
        Set<CustomerTypeEnum> customerTypeEnums = new HashSet<>();

        if (PricingDetailInputEnum.PROCESSING.equals(type)) {
            PricingDraft pricingDraft = getCurrentPricingDraft(pricingId);
            cycleType = Objects.nonNull(pricingDraft.getCycleType()) ? CycleTypeEnum.valueOf(pricingDraft.getCycleType()) : null;
            paymentCycle = pricingDraft.getPaymentCycle();
            numberOfCycles = pricingDraft.getNumberOfCycles();
            numberOfTrial = pricingDraft.getNumberOfTrial();
            unitId = pricingDraft.getUnitId();
            freeQuantity = pricingDraft.getFreeQuantity();
            price = pricingDraft.getPrice();
            pricingPlan = Objects.nonNull(pricingDraft.getPricingPlan()) ? PricingPlanEnum.valueOf(pricingDraft.getPricingPlan()) : null;
            trialType = Objects.nonNull(pricingDraft.getTrialType()) ? TimeTypeEnum.valueOf(pricingDraft.getTrialType()) : null;
            Set<PricingAddonRes> pricingAddons = pricingAddonRepository
                .findByPricingDraftIds(pricingId, BonusTypeEnum.UNSET.value);
            addonsList = getListPricingAddonsRes(pricingAddons);
            unitLimitedList = unitLimitedRepository.findAllByPricingDraftIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromAsc(pricingId);
            if (Objects.nonNull(pricingDraft.getCustomerTypeCode())) {
                customerTypeEnums = pricingDraft.getCustomerTypeCode().stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet());
            }

        } else {
            Pricing pricing = pricingRepository.findByIdAndDeletedFlag(pricingId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(messageNotFound, Resources.PRICING,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });
            cycleType = Objects.nonNull(pricing.getCycleType()) ? CycleTypeEnum.valueOf(pricing.getCycleType()) : null;
            paymentCycle = pricing.getPaymentCycle();
            numberOfCycles = pricing.getNumberOfCycles();
            numberOfTrial = pricing.getNumberOfTrial();
            unitId = pricing.getUnitId();
            freeQuantity = pricing.getFreeQuantity();
            price = pricing.getPrice();
            pricingPlan = Objects.nonNull(pricing.getPricingPlan()) ? PricingPlanEnum.valueOf(pricing.getPricingPlan()) : null;
            trialType = Objects.nonNull(pricing.getTrialType()) ? TimeTypeEnum.valueOf(pricing.getTrialType()) : null;
            Set<PricingAddonRes> pricingAddons = pricingAddonRepository
                .findByPricingId(pricingId, BonusTypeEnum.UNSET.value);
            addonsList = getListPricingAddonsRes(pricingAddons);
            unitLimitedList = unitLimitedRepository.findAllByPricingIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromAsc(pricingId);
            if (Objects.nonNull(pricing.getCustomerTypeCode())) {
                customerTypeEnums = pricing.getCustomerTypeCode().stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet());
            }
        }

        response.setPaymentCycle(Objects.nonNull(paymentCycle) ? paymentCycle.longValue() : null);
        response.setCycleType(cycleType);
        response.setNumberOfCycles(numberOfCycles);
        response.setPricingPlan(pricingPlan);
        response.setNumberOfTrial(numberOfTrial);
        response.setTrialType(trialType);
        response.setFreeQuantity(freeQuantity);
        response.setPrice(price);
        response.setDefaultCircle(YesNoEnum.YES);
        response.setAddonList(addonsList);
        response.setDisplayStatus(StatusEnum.ACTIVE.value);
        response.setCustomerTypeCode(customerTypeEnums);

        //Set don vi
        if (Objects.nonNull(unitId)) {
            unitRepository.findById(unitId).ifPresent(unit -> {
                response.setUnitName(unit.getName());
                response.setUnitId(unitId);
            });
        }

        // lưu thông tin kế hoạch định giá chi tiết
        List<PricingDetailResDTO.UnitLimited> outUnitLimited = new ArrayList<>();
        unitLimitedList.forEach(pricingPlanDetail -> {
            PricingDetailResDTO.UnitLimited limited = new PricingDetailResDTO.UnitLimited();
            limited.setPrice(pricingPlanDetail.getPrice());
            limited.setUnitFrom(Objects.nonNull(pricingPlanDetail.getUnitFrom()) ? pricingPlanDetail.getUnitFrom() : null);
            limited.setUnitTo((Objects.isNull(pricingPlanDetail.getUnitTo()) || pricingPlanDetail.getUnitTo() == -1) ? null
                : pricingPlanDetail.getUnitTo());
            outUnitLimited.add(limited);
        });
        response.setUnitLimitedList(outUnitLimited);

        return response;
    }

    /**
     * convert response chiến lược định giá
     */
    private PricingDetailResDTO.PricingStrategy getPricingPlanDetailResDTO(PricingMultiPlan pricingPlan) {
        PricingDetailResDTO.PricingStrategy response = new PricingDetailResDTO.PricingStrategy();
        response.setId(pricingPlan.getId());
        response.setPlanName(pricingPlan.getPlanName());
        response.setPaymentCycle(pricingPlan.getPaymentCycle());
        response.setCycleType(Objects.nonNull(pricingPlan.getCircleType()) ? CycleTypeEnum.valueOf(pricingPlan.getCircleType()) : null);
        response.setNumberOfCycles(pricingPlan.getNumberOfCycle());
        response.setPricingPlan(Objects.nonNull(pricingPlan.getPricingPlan()) ? PricingPlanEnum.valueOf(pricingPlan.getPricingPlan()) : null);
        response.setNumberOfTrial(pricingPlan.getNumberOfTrial());
        response.setCycleCode(pricingPlan.getCycleCode());
        response.setPriority(pricingPlan.getPriority());
        response.setExchangedPricingStrategy(pricingPlan.getConvertedIds().stream().map(Long::parseLong).collect(Collectors.toSet()));
        response.setCreatedAt(pricingPlan.getCreatedAt());

        Integer timeTrialTypeInteger = Objects.isNull(pricingPlan.getTrialType()) ? null : Math.toIntExact(pricingPlan.getTrialType());
        response.setTrialType(Objects.nonNull(timeTrialTypeInteger) ? TimeTypeEnum.valueOf(timeTrialTypeInteger) : null);
        Set<CustomerTypeEnum> cusTypeCodes = new HashSet<>();
        if (!CollectionUtils.isEmpty(pricingPlan.getCustomerTypeCode())) {
            pricingPlan.getCustomerTypeCode().forEach(p -> cusTypeCodes.add(CustomerTypeEnum.getValueOf(p)));
        }
        response.setCustomerTypeCode(CollectionUtils.isEmpty(cusTypeCodes) ? new HashSet<>() : cusTypeCodes);
        //Set don vi
        if (Objects.nonNull(pricingPlan.getUnitId())) {
            unitRepository.findById(pricingPlan.getUnitId()).ifPresent(unit -> {
                response.setUnitName(unit.getName());
                response.setUnitId(pricingPlan.getUnitId());
            });
        }

        response.setFreeQuantity(Objects.nonNull(pricingPlan.getFreeQuantity()) ? Long.valueOf(pricingPlan.getFreeQuantity()) : null);
        response.setDefaultCircle(Objects.nonNull(pricingPlan.getDefaultCircle()) ? YesNoEnum.valueOf(pricingPlan.getDefaultCircle()) : null);
        response.setPrice(pricingPlan.getPrice());

        // Set gia tri cho unitLimitedList
        List<PricingDetailResDTO.UnitLimited> outUnitLimited = new ArrayList<>();
//        List<PricingTaxRes> taxes;
//        if (Objects.nonNull(pricingPlan.getPricingId())) {
//            taxes = pricingTaxRepository.getPricingTax(pricingPlan.getPricingId());
//        } else {
//            taxes = pricingTaxRepository.getPricingTaxByPricingDraftId(pricingPlan.getPricingDraftId());
//        }

        // lưu thông tin kế hoạch định giá chi tiết
        pricingPlanDetailRepository.findAllByPricingMultiPlanIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromAsc(pricingPlan.getId())
            .forEach(pricingPlanDetail -> {
                PricingDetailResDTO.UnitLimited limited = new PricingDetailResDTO.UnitLimited();
//            if (CollectionUtils.isEmpty(taxes) || taxes.size() <= 0 || Objects.isNull(pricingPlanDetail.getPrice())) {
//                limited.setPrice(pricingPlanDetail.getPrice());
//            } else {
//                double totalPercentTax = 0;
//                for (PricingTaxRes tax : taxes) {
//                    //nếu có thuế chưa bao gồm -> addon chưa bao gồm thuế, trả về đơn giá ban đầu
//                    if (Objects.equals(YesNoEnum.NO.value, tax.getHasTax()))
//                        limited.setPrice(pricingPlanDetail.getPrice());
//                    totalPercentTax += tax.getPercent();
//                }
//                if (totalPercentTax != 0) {
//                    //tính đơn giá trước thuế
//                    limited.setPrice(pricingPlanDetail.getPrice().divide(BigDecimal.valueOf(1)
//                            .add(BigDecimal.valueOf(totalPercentTax).divide(BigDecimal.valueOf(100))), 0 , RoundingMode.HALF_UP));
//                }
//            }
                limited.setPrice(pricingPlanDetail.getPrice());
                limited.setUnitFrom(Objects.nonNull(pricingPlanDetail.getUnitFrom()) ? Long.valueOf(pricingPlanDetail.getUnitFrom()) : null);
                limited.setUnitTo((Objects.isNull(pricingPlanDetail.getUnitTo()) || pricingPlanDetail.getUnitTo() == -1) ? null
                    : Long.valueOf(pricingPlanDetail.getUnitTo()));
                outUnitLimited.add(limited);
            });
        response.setUnitLimitedList(outUnitLimited);

        // lưu thông tin addon riêng của kế hoạch định giá
        response.setAddonList(pricingMultiPlanAddonRepository.getAllPricingPlanDetailAddon(pricingPlan.getId()).stream()
            .map(e -> new AddOn(e.getId(), e.getName(), e.getServiceName(), e.getCode(), e.getStatus(),
                e.getBonusValue(), e.getType(), e.getBonusType(), e.getIsRequire(), e.getPricingMultiPlanId())).collect(
                Collectors.toList()));

        response.setReferenceId(pricingPlan.getReferenceId());
        response.setPricingId(pricingPlan.getPricingId());
        response.setDisplayStatus(pricingPlan.getDisplayStatus());
        if (pricingPlan.getPricingId() != null) {
            if (pricingRepository.findById(pricingPlan.getPricingId()).isPresent()) {
                int approveCode = pricingRepository.findById(pricingPlan.getPricingId()).get().getApprove();
                ApproveTypeEnum approveStatusEnum = ApproveTypeEnum.valueOf(approveCode);
                response.setApproveStatus(approveStatusEnum);
            }
        } else {
            if (pricingDraftRepository.findById(pricingPlan.getPricingDraftId()).isPresent()) {
                int approveCode = pricingDraftRepository.findById(pricingPlan.getPricingDraftId()).get().getApprove();
                ApproveTypeEnum approveStatusEnum = ApproveTypeEnum.valueOf(approveCode);
                response.setApproveStatus(approveStatusEnum);
            }
        }
        response.setMinimumQuantity(Objects.nonNull(pricingPlan.getMinimumQuantity()) ? pricingPlan.getMinimumQuantity() : null);
        response.setMaximumQuantity(Objects.nonNull(pricingPlan.getMaximumQuantity()) ? pricingPlan.getMaximumQuantity() : null);
        return response;
    }

    /**
     * Lấy goi nhap hiện tại
     *
     * @param draftId the draft id
     *
     * @return the current pricing
     */
    private PricingDraft getCurrentPricingDraft(Long draftId) {
        return pricingDraftRepository.findByIdAndDeletedFlag(draftId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.DRAFT,
                ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        });
    }

    @Override
    @Transactional(readOnly = true)
    public CouponPopupTreeResDTO getListPricing(Pageable pageable, String serviceName, CouponServiceTypeEnum serviceType,
        String pricingName,
        CouponPricingTypeEnum pricingType, Long categoryId, Integer paymentCycle, CycleTypeEnum cycleType, Set<Long> pricingIds,
        Set<Long> multiPlanId, Set<Long> combosId, Set<CustomerTypeEnum> customerTypes, PortalType portalType) {
        Sort pageSort = pageable.getSort();
        List<Order> pageSortOrders = pageSort.get().collect(Collectors.toList());

        // khoi tao sort theo createdAt
        if (pageSortOrders.stream().map(Order::getProperty).collect(Collectors.toList()).contains(Resources.CREATED_AT)) {
            Direction sortDirection = pageSortOrders.get(0).getDirection();
            pageSortOrders.add(0, new Order(sortDirection, SORT_LIST_PRICING_SERVICE_CREATED_AT_PROPERTY));
            pageSort = Sort.by(pageSortOrders);
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), pageSort);
        }

        String roleType = RoleType.ADMIN.toString();
        Long userId = DEFAULT_PARENT_ID;
        //user dang nhap co quyen dev-admin hoac dev-operator
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_OPERATOR.getValue()))) {
            roleType = RoleType.DEVELOPER.toString();
            userId = AuthUtil.getCurrentParentId();
        }

        String customerTypeStr = CollectionUtils.isEmpty(customerTypes) ? CharacterConstant.BLANK :
            String.join(CharacterConstant.VERTICAL_BAR, customerTypes.stream().map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));

        List<CouponServiceDTO> allElements = serviceRepository
            .getAllServiceHasMultiPlan(serviceName, pricingName, categoryId, paymentCycle, cycleType.value,
                userId, roleType, pricingIds, multiPlanId, combosId, customerTypeStr);

        Collection<CouponServiceDTO> pageService = allElements.stream().collect(
            Collectors.toMap(item -> String.join(":::", String.valueOf(item.getId()), item.getType()), Function.identity(), (o1, o2) -> o1,
                LinkedHashMap::new)).values();

        // lấy ra các pricing id có multi plan
        Set<Long> pricingMultiPlanIds = allElements.stream()
            .filter(pricing -> pricing.getIsMultiPlan().equals(YesNoEnum.YES.value))
            .map(CouponServiceDTO::getObjectId).collect(
                Collectors.toSet());

        Set<Long> comboIds = allElements.stream()
            .filter(service -> !service.getType().equals(PRICING_SERVICE_TYPE))
            .map(CouponServiceDTO::getId).collect(
                Collectors.toSet());

        // lấy ra các multi plan của pricing theo id
        List<PricingMultiPlan> allPricingMultiPlans = CollectionUtils.isEmpty(pricingMultiPlanIds) ? new ArrayList<>()
            : pricingMultiPlanRepository.getPricingMultiPlanByPricingIdAndPaymentCycleAndCycleTypeAndDisplayStatus(pricingMultiPlanIds,
                paymentCycle,
                cycleType.value, multiPlanId, customerTypeStr);

        Map<Long, List<PricingMultiPlan>> multiPlansOfPricing = mappingMultiPlanForPricing(pricingMultiPlanIds, allPricingMultiPlans);

        List<ComboPlan> comboPlans = comboPlanRepository
            .getAllComboPlansByComboIdAndComboName(comboIds, pricingName, paymentCycle, cycleType.value, combosId, customerTypeStr);
        Map<Long, List<ComboPlan>> comboPlansOfCombo = mappingComboPlanForCombo(comboIds, comboPlans);

        List<PopupTreeResDTO> pageData = pageService.stream().map(
                e -> convertToCouponPricingResDTO(e, allElements, multiPlansOfPricing, comboPlansOfCombo))
            .collect(
                Collectors.toList());

        //lấy index trong list dựa theo page size và vị trí page hiện tại
        final int start = Math.min((int) pageable.getOffset(), pageData.size());
        final int end = Math.min((start + pageable.getPageSize()), pageData.size());
        Page<PopupTreeResDTO> response = new PageImpl<>(pageData.subList(start, end), pageable, pageData.size());
        // đếm số lượng phần tử kế hoạch định giá của page
        return new CouponPopupTreeResDTO(response, (long) allElements.size());
    }

    /**
     * Xóa đi các phần tử trùng lặp theo key
     */
    @Override
    public <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * convert kết quả response trả về popup lấy ra dịch vụ
     */
    private CouponPricingResDTO convertToCouponPricingResDTO(CouponServiceDTO serviceDTO, List<CouponServiceDTO> services,
        Map<Long, List<PricingMultiPlan>> multiPlansOfPricing, Map<Long, List<ComboPlan>> comboPlansOfCombo) {

        // set response trả ra
        CouponPricingResDTO response = new CouponPricingResDTO();
        // set thông tin service response
        response.setServiceId(serviceDTO.getId());
        response.setTitle(serviceDTO.getServiceName());
        response.setType(serviceDTO.getType());
        String serviceKey = serviceDTO.getServiceName() + CharacterConstant.UNDERLINED + serviceDTO.getId();
        response.setKey(serviceKey);

        // Serivce có 2 loại: serivice (SERVICE) hoặc combo (COMBO)
        // nếu là loại service
        if (PRICING_SERVICE_TYPE.equals(serviceDTO.getType())) {
            // lấy các pricing của service
            List<PricingResDTO> pricingResDTOList = new ArrayList<>();
            services.stream().filter(e -> PRICING_SERVICE_TYPE.equals(e.getType()) && e.getId().equals(serviceDTO.getId()))
                .filter(distinctByKey(CouponServiceDTO::getObjectId))
                .forEach(servicePricing -> {
                    PricingResDTO pricingResDTO = new PricingResDTO();
                    pricingResDTO.setTitle(servicePricing.getObjectName());
                    pricingResDTO.setPricingId(servicePricing.getObjectId());
                    String pricingKey =
                        serviceKey + CharacterConstant.SLASH + servicePricing.getObjectCode() + CharacterConstant.UNDERLINED + servicePricing
                            .getObjectId();
                    pricingResDTO.setKey(pricingKey);
                    pricingResDTO.setServiceName(serviceDTO.getServiceName());

                    // Nếu là pricing và có multi plan (data mới) -> lấy multi plan ở bảng pricing_multi_plan
                    if (SubTypeEnum.PERIODIC.value.equals(servicePricing.getIsOneTime())) {
                        if (Objects.equals(servicePricing.getIsMultiPlan(), YesNoEnum.YES.value)) {
                            pricingResDTO.setChildren(
                                getPricingMultiPlanOfPricingResDTO(serviceDTO, multiPlansOfPricing.get(servicePricing.getObjectId()),
                                    pricingKey));
                        } else {
                            // nếu là data cũ -> lấy thông tin ở chính bảng pricing
                            pricingResDTO.setChildren(Collections.singletonList(getOldPricingPlanOfPricingResDTO(serviceDTO, pricingKey)));
                        }
                    } else {
                        List<PricingMultiPlan> pricingMultiPlans = multiPlansOfPricing.get(servicePricing.getObjectId());
                        pricingResDTO.setMultiPlanId(pricingMultiPlans.get(0).getId());
                    }
                    pricingResDTO.setIsOneTime(servicePricing.getIsOneTime());
                    pricingResDTOList.add(pricingResDTO);
                });
            response.setChildren(pricingResDTOList);
        } else {
            // nếu là combo plan -> không có multi period -> lấy thông tin chính bảng combo_plan
            response.setChildren(getPricingPlanOfComboPricingResDTO(serviceDTO, comboPlansOfCombo.get(serviceDTO.getId()), serviceKey));
        }
        return response;
    }

    /**
     * mapping multi plan cho pricing theo id
     */
    private Map<Long, List<PricingMultiPlan>> mappingMultiPlanForPricing(Set<Long> pricingMultiPlanIds,
        List<PricingMultiPlan> allPricingMultiPlans) {
        Map<Long, List<PricingMultiPlan>> multiPlansOfPricing = new HashMap<>();
        pricingMultiPlanIds.forEach(pricingId -> {
            multiPlansOfPricing
                .put(pricingId, allPricingMultiPlans.stream().filter(e -> e.getPricingId().equals(pricingId)).collect(Collectors.toList()));
        });
        return multiPlansOfPricing;
    }

    /**
     * mapping comboPlan cho combo theo id
     */
    private Map<Long, List<ComboPlan>> mappingComboPlanForCombo(Set<Long> comboIds,
        List<ComboPlan> allComboPlans) {
        Map<Long, List<ComboPlan>> comboPlanOfCombo = new HashMap<>();
        comboIds.forEach(comboId -> {
            comboPlanOfCombo
                .put(comboId, allComboPlans.stream().filter(e -> e.getComboId().equals(comboId)).collect(Collectors.toList()));
        });
        return comboPlanOfCombo;
    }

    /**
     * Lấy ra chu kỳ thanh toán của gói combo dịch vụ trong bảng combo plan cho PricingResDTO
     */
    private List<PricingResDTO> getPricingPlanOfComboPricingResDTO(CouponServiceResDTO serviceDTO,
        List<ComboPlan> comboPlans, String serviceKey) {
        List<PricingResDTO> pricingResDTOList = new ArrayList<>();
        comboPlans.forEach(comboPlan -> {
            PricingResDTO pricingResDTO = new PricingResDTO();
            pricingResDTO.setTitle(comboPlan.getComboName());
            pricingResDTO.setPricingId(comboPlan.getId());
            String comboKey =
                serviceKey + CharacterConstant.SLASH + comboPlan.getComboCode() + CharacterConstant.UNDERLINED + comboPlan.getId();
            pricingResDTO.setKey(comboKey);
            String comboChildKey =
                comboKey + CharacterConstant.SLASH + comboPlan.getPaymentCycle() + CharacterConstant.UNDERLINED + comboPlan
                    .getCycleType();
            String title = TITLE_CYCLE_TYPE_UNLIMITED;
            if (Objects.nonNull(comboPlan.getPaymentCycle()) && Objects
                .nonNull(comboPlan.getCycleType())) {
                String currentCycleType =
                    comboPlan.getCycleType().equals(CycleTypeEnum.DAILY.value) ? TITLE_CYCLE_TYPE_DAILY
                        : comboPlan.getCycleType().equals(CycleTypeEnum.WEEKLY.value) ? TITLE_CYCLE_TYPE_WEEKLY
                            : comboPlan.getCycleType().equals(CycleTypeEnum.MONTHLY.value)
                                ? TITLE_CYCLE_TYPE_MONTHLY
                                : TITLE_CYCLE_TYPE_YEARLY;
                title = comboPlan.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType;
            }
            pricingResDTO.setChildren(
                Collections
                    .singletonList(
                        new PricingStrategyResDTO(comboPlan.getId(), comboChildKey, title, serviceDTO.getType(), YesNoEnum.NO)));
            pricingResDTOList.add(pricingResDTO);
        });
        return pricingResDTOList;
    }

    /**
     * Lấy ra ké hoạnh định giá cũ của gói dịch vụ trong bảng pricing cho PricingResDTO
     */
    private PricingStrategyResDTO getOldPricingPlanOfPricingResDTO(CouponServiceDTO serviceDTO, String pricingKey) {
        String key = pricingKey + CharacterConstant.SLASH + serviceDTO.getPaymentCycle() + CharacterConstant.UNDERLINED + serviceDTO
            .getPaymentCycle();
        String title = TITLE_CYCLE_TYPE_UNLIMITED;
        if (Objects.nonNull(serviceDTO.getPaymentCycle()) && Objects
            .nonNull(serviceDTO.getCycleType())) {
            String currentCycleType =
                serviceDTO.getCycleType().equals(CycleTypeEnum.DAILY.value) ? TITLE_CYCLE_TYPE_DAILY
                    : serviceDTO.getCycleType().equals(CycleTypeEnum.WEEKLY.value) ? TITLE_CYCLE_TYPE_WEEKLY
                        : serviceDTO.getCycleType().equals(CycleTypeEnum.MONTHLY.value)
                            ? TITLE_CYCLE_TYPE_MONTHLY
                            : TITLE_CYCLE_TYPE_YEARLY;
            title = serviceDTO.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType;
        }
        return new PricingStrategyResDTO(serviceDTO.getObjectId(), key, title, serviceDTO.getType(), YesNoEnum.NO);
    }

    /**
     * lấy ra các chu kỳ thanh toán của gói dịch vụ trong bảng pricing_multi_plan cho PricingResDTO
     */
    private List<PricingStrategyResDTO> getPricingMultiPlanOfPricingResDTO(CouponServiceResDTO serviceDTO,
        List<PricingMultiPlan> pricingMultiPlans, String pricingKey) {
        return pricingMultiPlans
            .stream()
            .map(
                pricingMultiPlan -> {
                    String key =
                        pricingKey + CharacterConstant.SLASH + pricingMultiPlan.getPricingId() + CharacterConstant.UNDERLINED
                            + pricingMultiPlan.getId();
                    String title = TITLE_CYCLE_TYPE_UNLIMITED;
                    if (Objects.nonNull(pricingMultiPlan.getPaymentCycle()) && Objects
                        .nonNull(pricingMultiPlan.getCircleType())) {
                        String currentCycleType =
                            pricingMultiPlan.getCircleType().equals(CycleTypeEnum.DAILY.value) ? TITLE_CYCLE_TYPE_DAILY
                                : pricingMultiPlan.getCircleType().equals(CycleTypeEnum.WEEKLY.value) ? TITLE_CYCLE_TYPE_WEEKLY
                                    : pricingMultiPlan.getCircleType().equals(CycleTypeEnum.MONTHLY.value)
                                        ? TITLE_CYCLE_TYPE_MONTHLY
                                        : TITLE_CYCLE_TYPE_YEARLY;
                        title = pricingMultiPlan.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType;
                    }
                    return new PricingStrategyResDTO(pricingMultiPlan.getId(), key, title,
                        serviceDTO.getType(),
                        YesNoEnum.YES);
                }
            ).collect(Collectors.toList());
    }

    @Override
    public void requestApproveManyPricing(ApproveReqDTO reqDTO) {
        // kiem tra service co ton tai khong
        ServiceEntity serviceEntity = serviceRepository.findByIdAndDeletedFlag(reqDTO.getServiceId(), CustomerTicketConstant.DELETED_FLAG)
            .orElseThrow(() -> {
                String messageNotFound = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, serviceM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(messageNotFound, Resources.SERVICES, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });
        if (!Objects.equals(serviceEntity.getUserId(), AuthUtil.getCurrentParentId())) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        // kiem tra customer cua pricing co thuoc DV ko
        List<PricingDraft> pricingDrafts = reqDTO.getIds().stream().map(e -> {
            PricingDraft pricingDraft = pricingDraftRepository
                .findByIdAndDeletedFlag(e, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                    String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, Locale.US);
                    return new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.ID,
                        MessageKeyConstant.NOT_FOUND);
                });
            if (!serviceEntity.getCustomerTypeCode().containsAll(pricingDraft.getCustomerTypeCode())) {
                throw new BadRequestException("Đối tượng khách hàng không thỏa mãn", Resources.PRICING, ErrorKey.Pricing.APPROVE_STATUS,
                    MessageKeyConstant.PRICING_CUSTOMER_TYPE_CODE_INVALID);
            }
            addPricingHistory(pricingDraft, ApproveStatusEnum.AWAITING_APPROVAL, PortalType.DEV);
            pricingDraft.setApprove(ApproveStatusEnum.AWAITING_APPROVAL.value);
            // gửi notification
            executor.execute(() -> sendEmailAndNotifyRequestApprovePricing(pricingDraft));
            return pricingDraft;
        }).collect(Collectors.toList());
        pricingDraftRepository.saveAll(pricingDrafts);
    }

    /**
     * Gửi mai va thong bao khi gui approve pricing
     */
    private void sendEmailAndNotifyRequestApprovePricing(PricingDraft pricingDraft) {
        log.info("================== start sendEmailAndNotifyRequestApprovePricing =================");
        // lay ra service cua pricing
        ServiceEntity servicePricing = serviceRepository
            .findByIdAndDeletedFlag(pricingDraft.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, serviceM, Locale.US);
                return new BadRequestException(message, Resources.SERVICES, Services.ID,
                    MessageKeyConstant.NOT_FOUND);
            });

        // Lấy thông tin cấu hình thông báo và gửi email
        Optional<ActionNotification> actionDevOpt =
            actionNotificationRepository.findByActionCode(ActionNotificationEnum.PC01.getCode());
        Optional<ActionNotification> actionAdminOpt =
            actionNotificationRepository.findByActionCode(ActionNotificationEnum.PC02.getCode());
        if (!actionDevOpt.isPresent() && !actionAdminOpt.isPresent()) {
            return;
        }

        // lay ra list user cos quyen FULL_ADMIN
        List<UserAdminDTO> userAdmins = userRepository.getLstFullAdminDetail();

        // get dev tao ra combo dich vu
        User createdUser = userRepository.findByIdAndDeletedFlag(pricingDraft.getCreatedBy(), DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, userM, Locale.US);
                return new BadRequestException(message, Resources.PRICING, ErrorKey.Pricing.CREATED_BY,
                    MessageKeyConstant.NOT_FOUND);
            });
        String userFullName = createdUser.getFirstName() + SPACE + createdUser.getLastName();

        // send mail va thong bao to dev
        String[] param = new String[]{pricingDraft.getPricingName(), servicePricing.getServiceName()};
        String paramServiceId = CharacterConstant.SLASH + pricingDraft.getServiceId().toString();

        // hotline nền tảng
        String hotlineParam = systemParamService.getHotlineParam();

        // Gửi cho admin hệ thống
        log.info("================== start sending mail and notif to admin =================");
        if (actionAdminOpt.isPresent() && (Objects.equals(ActionNotificationStatusEnum.ON.action, actionAdminOpt.get().getIsSendEmail()) ||
            Objects.equals(ActionNotificationStatusEnum.ON.action, actionAdminOpt.get().getIsNotification()))) {
            List<NotificationDTO> notificationDTOs = new ArrayList<>();
            ActionNotification actionAdmin = actionAdminOpt.get();
            String userCreatedName = createdUser.getName();

            userAdmins.forEach(user -> {
                if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionAdmin.getIsSendEmail())) {
                    String userDevelopName = user.getFirstName() + SPACE + user.getLastName();
                    emailService.sendMail(user.getEmail(), EmailCodeEnum.PC02,
                        getListPramRequestApprovePC02(userDevelopName, hotlineParam, pricingDraft.getPricingName(),
                            servicePricing.getServiceName(), userDevelopName, userCreatedName));
                }
                if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionAdmin.getIsNotification())) {
                    String content = NotifyUtil.getContent(ActionNotificationEnum.PC02.getContent(), param);
                    NotificationDTO notificationDTO = new NotificationDTO(ActionNotificationEnum.PC02.getTitle(),
                        content,
                        ActionNotificationEnum.PC02.getCode().concat(paramServiceId),
                        user.getId(),
                        PortalType.ADMIN.getType(), pricingDraft.getId());
                    notificationDTOs.add(notificationDTO);
                }
            });

            if (!CollectionUtils.isEmpty(notificationDTOs)) {
                NotifyUtil.sendNotify(notificationDTOs, ActionNotificationEnum.PC02.getCode());
            }
            log.info("================== end sending mail and notif to admin =================");
        }

        // Gửi cho dev tạo gói
        log.info("================== start sending mail and notif to pricing dev =================");
        if (actionDevOpt.isPresent()) {
            ActionNotification actionDev = actionDevOpt.get();
            if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionDev.getIsSendEmail())) {
                emailService.sendMail(createdUser.getEmail(), EmailCodeEnum.PC01,
                    getListPramRequestApprovePC01(userFullName, hotlineParam, pricingDraft.getPricingName(),
                        servicePricing.getServiceName()));
            }
            if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionDev.getIsNotification())) {
                String content = NotifyUtil.getContent(ActionNotificationEnum.PC01.getContent(), param);
                NotificationDTO notificationDTO = new NotificationDTO(ActionNotificationEnum.PC01.getTitle(),
                    content,
                    ActionNotificationEnum.PC01.getCode().concat(paramServiceId),
                    createdUser.getId(),
                    PortalType.DEV.getType(), pricingDraft.getId());
                NotifyUtil.sendNotify(notificationDTO, ActionNotificationEnum.PC01.getCode());
            }
        }
        log.info("================== end sending mail and notif to pricing dev =================");
        log.info("================== end sendEmailAndNotifyRequestApprovePricing =================");
    }

    /**
     * Lấy ra list param khi gửi mail cho DEV
     */
    private List<MailParamResDTO> getListPramRequestApprovePC01(String userName, String hotline, String pricingName, String serviceName) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(EmailCodeEnum.PC01.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.USER)) {
                mailParamResDTO.setValue(userName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_PRICING)) {
                mailParamResDTO.setValue(pricingName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_SERVICE)) {
                mailParamResDTO.setValue(serviceName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.HOTLINE)) {
                mailParamResDTO.setValue(hotline);
            }
        });
        return paramNameByCode;
    }

    /**
     * Lấy ra list param khi gửi mail cho Admin
     */
    private List<MailParamResDTO> getListPramRequestApprovePC02(String userName, String hotline, String pricingName, String serviceName,
        String developerName, String companyName) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(EmailCodeEnum.PC02.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.USER)) {
                mailParamResDTO.setValue(userName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_PRICING)) {
                mailParamResDTO.setValue(pricingName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_SERVICE)) {
                mailParamResDTO.setValue(serviceName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_DEVELOPER)) {
                mailParamResDTO.setValue(companyName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.HOTLINE)) {
                mailParamResDTO.setValue(hotline);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_COMPANY)) {
                mailParamResDTO.setValue(serviceName);
            } else {
                mailParamResDTO.setValue(STRING_EMPTY);
            }
        });
        return paramNameByCode;
    }

    public void updateStatusBills(Long id) {
        Set<Long> pricingIds = new HashSet<>();
        Set<Long> SubIds = new HashSet<>();
        Date now = new Date();

        Pricing pricingNew = pricingRepository.getPricingNew(id);
        List<Pricing> pricings = pricingRepository.getAllByPricingDraftId(id);
        for (Pricing pricing : pricings) {
            pricingIds.add(pricing.getId());
        }
        List<Bills> bills = billsRepository.findAllByPricingIdIn(pricingIds);
        for (Bills b : bills) {
            User user = Objects.nonNull(b.getSubscription()) ? b.getSubscription().getUser() : null;
            if (user != null) {
                String customerType = user.getCustomerType() != null ? user.getCustomerType() : CustomerTypeEnum.ENTERPRISE.getValue();
                if ((!pricingNew.getCustomerTypeCode().contains(customerType) || CollectionUtils.isEmpty(pricingNew.getCustomerTypeCode()))) {
                    b.setNextPaymentTime(null);
                    if (Objects.nonNull(b.getEndDate()) && now.after(b.getEndDate())) {
                        SubIds.add(b.getSubscriptionsId());
                    }
                }
            }
        }
        if (bills.size() > 0) {
            billsRepository.saveAll(bills);
        }
        if (SubIds.size() > 0) {
            List<Subscription> subscriptions = subscriptionRepository.findAllByIdIn(SubIds);
            for (Subscription s : subscriptions) {
                s.setStatus(4);
            }
            subscriptionRepository.saveAll(subscriptions);
        }
    }

    @Override
    @Transactional
    public void approvePricing(Long id, PricingApproveReqDTO pricingApproveReqDTO, Boolean isApproveWithService) {

        Long provinceId = Objects.requireNonNull(AuthUtil.getDepartment()).getProvinceId();
        if (Objects.nonNull(provinceId)) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }

        PricingDraft pricingDraft = pricingDraftRepository.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, Locale.US);
                return new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });
//        //kiem tra pricing co o trang thai cho duyet khong
//        if (!pricingDraft.getApprove().equals(ApproveStatusEnum.AWAITING_APPROVAL.value)) {
//            String message = messageSource.getMessage(MessageKeyConstant.PRICING_NOT_BE_AWAITING_APPROVAL, pricingM, Locale.US);
//            throw new BadRequestException(message, Resources.PRICING, ErrorKey.Pricing.APPROVE_STATUS,
//                    MessageKeyConstant.PRICING_NOT_BE_AWAITING_APPROVAL);
//        }
        Long pricingServiceId = pricingDraft.getServiceId();
        Long createdUserPricing = pricingDraft.getCreatedBy();
        String pricingName = pricingDraft.getPricingName();
        // neu admin approve thi tao ra mot ban ghi pricing moi voi du lieu giong nhu ban ghi draft
        if (ApproveStatusEnum.APPROVED.equals(pricingApproveReqDTO.getStatus())) {
            Pricing pricingOld = pricingRepository.getPricingNew(id);
            List<Pricing> pricingOlds = pricingRepository
                    .findAllByPricingDraftIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
            Date now = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
            pricingDraft.setApprove(ApproveStatusEnum.APPROVED.value);
            pricingDraft.setApproveTime(now);
            pricingDraft = pricingDraftRepository.save(pricingDraft);
            Pricing pricing = new Pricing();
            BeanUtils.copyProperties(pricingDraft, pricing);
            pricing.setRecommendedStatus(pricingDraft.getRecommendedStatus());
            pricing.setPricingDraftId(pricingDraft.getId());
            pricing.setHasChangeQuantity(pricingDraft.getHasChangeQuantity());
            pricing.setHasRenew(pricingDraft.getHasRenew());
            pricing.setSmePricingId(pricingDraft.getSmePricingId());
            pricing.setCreatedAt(now);
            pricing.setId(null);
            if (Objects.nonNull(pricingOld)) {
                pricing.setPriority(pricingOld.getPriority());
            } else {
                List<Pricing> pricingList = pricingRepository.getPricingConfigList(pricing.getServiceId());
                Boolean checkPricingConfigList = pricingRepository.checkPricingConfigList(pricing.getServiceId());
                if (!CollectionUtils.isEmpty(pricingList) && checkPricingConfigList) {
                    pricingList.forEach(e -> e.setPriority(e.getPriority() + 1));
                    pricingRepository.saveAll(pricingList);
                    pricing.setPriority(1);
                }
            }
            if (SubTypeEnum.ONCE.value.equals(pricingDraft.getIsOneTime())) {
                pricing.setPaymentCycle(-1);
                pricing.setNumberOfCycles(-1);
            }
            pricing = pricingRepository.save(pricing);
            Long pricingId = pricing.getId();
            if (!fileAttachRepository.existsByObjectIdAndObjectType(pricingId, FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue()) &&
                Objects.nonNull(pricingOld)) {
                Optional<FileAttach> fileAttachOld = fileAttachRepository.findFirstByObjectTypeAndObjectIdOrderByIdDesc(
                    FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(), pricingOld.getId());
                fileAttachOld.ifPresent(oldAttach -> {
                    FileAttach fileAttach = FileAttach.builder()
                        .accessType(AccessTypeEnum.PUBLIC.value)
                        .modifiedBy(AuthUtil.getCurrentUserId())
                        .build();
                    fileAttach.setFileName(oldAttach.getFileName());
                    fileAttach.setFilePath(oldAttach.getFilePath());
                    fileAttach.setFileSize(oldAttach.getFileSize());
                    fileAttach.setFileType(oldAttach.getFileType());
                    fileAttach.setObjectId(pricingId);
                    fileAttach.setObjectType(oldAttach.getObjectType());
                    fileAttach.setObjectDraftId(oldAttach.getObjectDraftId());

                    fileAttachRepository.save(fileAttach);
                });
            }
            createdUserPricing = pricing.getCreatedBy();
            List<PricingMultiPlan> pricingMultiPlans = cloneMappingOfPricing(id, pricingId);
            upgradeCouponPricing(pricingOld, pricingMultiPlans, id);
            pricingServiceId = pricing.getServiceId();

            // Lưu thông tin custom field khi phê duyệt gói dịch vụ
            customFieldManager.saveFieldDraftToFieldValue(EntityTypeEnum.PRICING.getValue(), id, pricingId);

            // Lưu thông tin ảnh gói cước
            approveDraftPricingImage(pricingDraft.getId(), pricingId);

            // scan update nhóm sản phẩm:
            // 5/8/2024: nếu update + approve pricing -> update lại các pricing trong nhóm SP thành bản mới nhất
            updateServiceGroupItemAfterUpdatePricing(id);
            // dev => combo => chi tiết combo => update tổng giá khi update gói dv
            Set<Long> oldPricingIds = pricingOlds.stream().map(Pricing::getId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(oldPricingIds)) updateComboPlanPriceByPricingIds(oldPricingIds);
            // lưu lại thông tin gói được phê duyệt với biến thể
            if (Objects.nonNull(pricing.getVariantApply()) && pricing.getVariantApply().equals(VariantApplyEnum.SELECTED.getValue())) {
                List<PricingVariant> pricingVariants = pricingVariantRepository.findAllByPricingDraftId(pricingDraft.getId());
                if (!CollectionUtils.isEmpty(pricingVariants)) {
                    // set pricing_id vào bản ghi đã có thay vì tạo mới
                    for (PricingVariant pricingVariant : pricingVariants) {
                        pricingVariant.setPricingId(pricingId);
                    }
                    pricingVariantRepository.saveAll(pricingVariants);
                }
            }
        }
        // neu admin reject thi se doi trang thai ban ghi draft thanh reject va luu lai li do reject
        else if (ApproveStatusEnum.REJECTED.equals(pricingApproveReqDTO.getStatus())) {
            //neu admin tu choi thi truong noi dung khong duoc de trong
            if (StringUtils.isEmpty(pricingApproveReqDTO.getCause())) {
                String message = messageSource.getMessage(Validation.NOT_NULL, pricingM, Locale.US);
                throw new BadRequestException(message, Resources.PRICING, ErrorKey.Pricing.REJECT_CAUSE,
                    Validation.NOT_NULL);
            }
            pricingDraft.setApprove(ApproveStatusEnum.REJECTED.value);
            pricingDraft.setRejectReason(pricingApproveReqDTO.getCause());
            pricingDraftRepository.save(pricingDraft);
        }
        // lay ra service cua pricing
        ServiceEntity servicePricing = serviceRepository
            .findByIdAndDeletedFlag(pricingServiceId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, serviceM, Locale.US);
                return new BadRequestException(message, Resources.SERVICES, Services.ID,
                    MessageKeyConstant.NOT_FOUND);
            });

        // get dev tao ra combo dich vu
        User createdUser = userRepository.findByIdAndDeletedFlag(createdUserPricing, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, userM, Locale.US);
                return new BadRequestException(message, Resources.PRICING, ErrorKey.Pricing.CREATED_BY,
                    MessageKeyConstant.NOT_FOUND);
            });

        String userFullName = createdUser.getFirstName() + SPACE + createdUser.getLastName();
        String[] param = new String[]{pricingDraft.getPricingName(), servicePricing.getServiceName()};
        String paramServiceId = CharacterConstant.SLASH + pricingDraft.getServiceId().toString();

        // hotline nền tảng
        String hotlineParam = systemParamService.getHotlineParam();
        if (ApproveStatusEnum.APPROVED.equals(pricingApproveReqDTO.getStatus())) {
            // Lấy thông tin cấu hình thông báo và gửi email khi phê duyệt gói
            Optional<ActionNotification> actionDevOpt =
                actionNotificationRepository.findByActionCode(ActionNotificationEnum.PC03.getCode());
            if (!actionDevOpt.isPresent()) {
                return;
            }
            if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionDevOpt.get().getIsSendEmail())) {
                sendEmailApprovePricing(userFullName, hotlineParam, pricingName, servicePricing.getServiceName(),
                    createdUser.getEmail());
            }
            if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionDevOpt.get().getIsNotification())) {
                String content = NotifyUtil.getContent(ActionNotificationEnum.PC03.getContent(), param);
                NotificationDTO notificationDTO = new NotificationDTO(ActionNotificationEnum.PC03.getTitle(),
                    content,
                    ActionNotificationEnum.PC03.getCode().concat(paramServiceId),
                    createdUser.getId(),
                    PortalType.DEV.getType(), pricingDraft.getId());
                NotifyUtil.sendNotify(notificationDTO, ActionNotificationEnum.PC03.getCode());
            }
        } else if (ApproveStatusEnum.REJECTED.equals(pricingApproveReqDTO.getStatus())) {
            // Lấy thông tin cấu hình thông báo và gửi email khi từ chối gói
            Optional<ActionNotification> actionDevOpt =
                actionNotificationRepository.findByActionCode(ActionNotificationEnum.PC04.getCode());
            if (!actionDevOpt.isPresent()) {
                return;
            }
            if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionDevOpt.get().getIsSendEmail())) {
                sendEmailRejectPricing(userFullName, hotlineParam, pricingName, servicePricing.getServiceName(),
                    pricingDraft.getRejectReason(), createdUser.getEmail());
            }
            if (Objects.equals(ActionNotificationStatusEnum.ON.action, actionDevOpt.get().getIsNotification())) {
                String content = NotifyUtil.getContent(ActionNotificationEnum.PC04.getContent(), param);
                NotificationDTO notificationDTO = new NotificationDTO(ActionNotificationEnum.PC04.getTitle(),
                    content,
                    ActionNotificationEnum.PC04.getCode().concat(paramServiceId),
                    createdUser.getId(),
                    PortalType.DEV.getType(), pricingDraft.getId());
                NotifyUtil.sendNotify(notificationDTO, ActionNotificationEnum.PC04.getCode());
            }
        }
        addPricingHistory(pricingDraft, pricingApproveReqDTO.getStatus(), PortalType.ADMIN);

        updateStatusBills(id);
    }

    private Long getNumDayByCycleTypeAndPaymentCycle(Integer cycleType, Integer paymentCycle) {
        long ct = 0L;
        if (cycleType.equals(0)) {
            ct = 1L;
        } else if (cycleType.equals(1)) {
            ct = 7L;
        } else if (cycleType.equals(2)){
            ct = 30L;
        } else {
            ct = 365L;
        }
        return ct * paymentCycle;
    }

    private void updateComboPlanPriceByPricingIds(Set<Long> oldPricingIds){
        List<ComboPricing> comboPricings = comboPricingRepository.findAllByObjectIdInAndObjectType(oldPricingIds, "PRICING");
        if (!CollectionUtils.isEmpty(comboPricings)) {
            //
            List<IPmpIdOldAndNewDTO> idOldAndNewDTOS = pricingMultiPlanRepository.getPmpIdNewestByPMPOldIds(
                    comboPricings.stream().map(ComboPricing::getPricingMultiPlanId).collect(Collectors.toSet()));
            Set<PricingMultiPlan> newPmps = pricingMultiPlanRepository.findAllByIdIn(
                    idOldAndNewDTOS.stream().map(IPmpIdOldAndNewDTO::getPmpIdNewest).collect(Collectors.toSet()));
            Set<Long> comboPlanIds = comboPricings.stream().map(ComboPricing::getComboPlanId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<ComboPlan> comboPlans = comboPlanRepository.findByIdIn(comboPlanIds);
            Set<Long> comboPlanDraftIds = comboPricings.stream().map(ComboPricing::getComboPlanDraftId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<ComboPlanDraft> comboPlanDrafts = comboPlanDraftRepository.findByIdIn(comboPlanDraftIds);
            List<PricingPlanDetail> pricingPlanDetails = pricingPlanDetailRepository.findAllByPricingMultiPlanIdIn(
                    idOldAndNewDTOS.stream().map(IPmpIdOldAndNewDTO::getPmpIdNewest).collect(Collectors.toSet()));
            comboPricingRepository.saveAll(comboPricings.stream()
                    .peek(comboPricing -> {
                        IPmpIdOldAndNewDTO idOldAndNewDTO = idOldAndNewDTOS.stream()
                                .filter(e -> e.getPmpIdOld().equals(comboPricing.getPricingMultiPlanId()))
                                .findFirst().get();
                        // số tiền
                        PricingMultiPlan newPmp = newPmps.stream()
                                .filter(e -> e.getId().equals(idOldAndNewDTO.getPmpIdNewest()))
                                .findFirst().get();
                        Integer paymentCycleNewPmp = Objects.nonNull(newPmp.getPaymentCycle()) ?
                                newPmp.getPaymentCycle().intValue() : 1;
                        Integer cycleTypeNewPmp = Objects.nonNull(newPmp.getCircleType())
                                ? newPmp.getCircleType() : 2;
                        Long numDayNewPmp = getNumDayByCycleTypeAndPaymentCycle(cycleTypeNewPmp, paymentCycleNewPmp);
                        long price = Objects.nonNull(newPmp.getPrice()) ? newPmp.getPrice().longValue() : 0L;
                        if (!newPmp.getPricingPlan().equals(0)) {
                            Long quantity = Objects.nonNull(comboPricing.getQuantity()) ? comboPricing.getQuantity() : 1L;
                            Long freeQuantity = Objects.nonNull(comboPricing.getFreeQuantity()) && newPmp.getPricingPlan().equals(2)
                                    ? comboPricing.getFreeQuantity() : 0L;
                            long quantityActual = Math.max((quantity - freeQuantity), 0L);
                            if (newPmp.getPricingPlan().equals(1)) {
                                price = quantityActual * price;
                            } else {
                                List<PricingPlanDetail> ppds = pricingPlanDetails.stream()
                                        .filter(e -> e.getPricingMultiPlanId().equals(newPmp.getId()))
                                        .collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(ppds)) {
                                    ppds = ppds.stream()
                                            .filter(e -> (e.getUnitTo().equals(-1) && quantityActual >= e.getUnitFrom()) || (
                                                    quantityActual <= e.getUnitTo() && quantityActual >= e.getUnitFrom()
                                            )).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(ppds)) {
                                        price = ppds.stream().findFirst().get().getPrice().longValue();
                                    } else {
                                        price = 0L;
                                    }
                                }
                            }
                        }
                        //
                        Integer paymentCycle = 1;
                        Integer cycleType = 0 ;
                        if (Objects.nonNull(comboPricing.getComboPlanDraftId())) {
                            ComboPlanDraft comboPlanDraft = comboPlanDrafts.stream()
                                    .filter(e -> e.getId().equals(comboPricing.getComboPlanDraftId()))
                                    .findFirst().get();
                            paymentCycle =  comboPlanDraft.getPaymentCycle();
                            cycleType =  comboPlanDraft.getCycleType();
                        } else if (Objects.nonNull(comboPricing.getComboPlanId())) {
                            ComboPlan comboPlan = comboPlans.stream()
                                    .filter(e -> e.getId().equals(comboPricing.getComboPlanId()))
                                    .findFirst().get();
                            paymentCycle = comboPlan.getPaymentCycle();
                            cycleType = comboPlan.getCycleType();
                        }
                        Long numDay = getNumDayByCycleTypeAndPaymentCycle(cycleType, paymentCycle);
                        //
                        comboPricing.setAmount(price * numDay / numDayNewPmp);
                        comboPricing.setPricingMultiPlanId(idOldAndNewDTO.getPmpIdNewest());
                        comboPricing.setObjectId(idOldAndNewDTO.getPricingIdNewest());
                    })
                    .collect(Collectors.toList()));
            //
            if (!CollectionUtils.isEmpty(comboPlanIds)) {
                comboPricingRepository.updateAmountComboPlanByIds(comboPlanIds);
            }
            if (!CollectionUtils.isEmpty(comboPlanDraftIds)) {
                comboPricingRepository.updateAmountComboPlanDraftByIds(comboPlanDraftIds);
            }
        }
    }

    /**
     * Gửi mail khi approve pricing
     */
    private void sendEmailApprovePricing(String userName, String hotline, String pricingName, String serviceName, String userEmail) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(EmailCodeEnum.PC03.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.USER)) {
                mailParamResDTO.setValue(userName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_PRICING)) {
                mailParamResDTO.setValue(pricingName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_SERVICE)) {
                mailParamResDTO.setValue(serviceName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.HOTLINE)) {
                mailParamResDTO.setValue(hotline);
            }
        });

        emailService.sendMail(userEmail, EmailCodeEnum.PC03, paramNameByCode);
    }

    /**
     * Gửi mail khi reject pricing
     */
    private void sendEmailRejectPricing(String userName, String hotline, String pricingName, String serviceName, String reasonReject,
        String userEmail) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(EmailCodeEnum.PC04.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.USER)) {
                mailParamResDTO.setValue(userName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_PRICING)) {
                mailParamResDTO.setValue(pricingName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_SERVICE)) {
                mailParamResDTO.setValue(serviceName);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.HOTLINE)) {
                mailParamResDTO.setValue(hotline);
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.REJECT_REASON)) {
                mailParamResDTO.setValue(reasonReject);
            }
        });
        emailService.sendMail(userEmail, EmailCodeEnum.PC04, paramNameByCode);
    }

    /**
     * Cập nhật version mới nhất cho phần chương trình khuyến mại
     * @param pricing the pricing id
     * @param pricingMultiPlans the pricingMultiPlan id
     * @param draftId   the draft id
     */
    private void upgradeCouponPricing(Pricing pricing, List<PricingMultiPlan> pricingMultiPlans, Long draftId) {
        if (Objects.nonNull(pricing)) {
            //update coupon khi pricing là gói dữ liệu cũ (trước multi period)
            if (Objects.nonNull(pricing.getPaymentCycle())) {
                List<CouponPricingPlan> couponPricingPlanList = new ArrayList<>();
                List<Long> couponIds = couponRepository.findCouponPricingApplyUpgrade(draftId);
                pricingMultiPlans.forEach(p -> {
                    couponIds.forEach(c -> {
                        couponPricingPlanList.add(new CouponPricingPlan(null, c, p.getId(), p.getOriginalId(), CouponPricingPlanTypeEnum.PRICING.value));
                    });
                });
                if (!CollectionUtils.isEmpty(couponPricingPlanList)) {
                    couponPricingPlanRepository.saveAll(couponPricingPlanList);
                    couponRepository.deleteCouponPricingApplyUpgrade(draftId);
                }
                //update coupon khi pricing là gói dữ liệu mới (sau multi period)
            } else {
                List<Long> pricingMultiIds = pricingMultiPlans.stream().map(PricingMultiPlan::getReferenceId).collect(Collectors.toList());
                List<CouponPricingPlan> couponPricingPlanOld = couponPricingPlanRepository.findAllByPricingMultiPlanIds(pricingMultiIds);
                List<CouponPricingPlan> couponPricingPlanNew = new ArrayList<>();
                couponPricingPlanOld.forEach(cpp -> {
                    pricingMultiPlans.forEach(pmp -> {
                        if (Objects.equals(pmp.getReferenceId(), cpp.getPricingMultiPlanId())) {
                            couponPricingPlanNew
                                .add(new CouponPricingPlan(null, cpp.getCouponId(), pmp.getId(), pmp.getOriginalId(), CouponPricingPlanTypeEnum.PRICING.value));
                        }
                    });
                });
                if (!CollectionUtils.isEmpty(couponPricingPlanNew)) {
                    couponPricingPlanRepository.saveAll(couponPricingPlanNew);
                }
            }
        }
    }

    /**
     * clone các bản ghi mapping của pricing_draft được duyệt cho bản ghi pricing mới
     */
    private List<PricingMultiPlan> cloneMappingOfPricing(Long id, Long pricingId) {
        //clone cac ban ghi cua pricingDraft cho pricing moi trong bang pricing_tax
        List<PricingTax> pricingTaxes = pricingTaxRepository.findByPricingDraftId(id);
        List<PricingTax> listTaxToClone = new ArrayList<>();
        pricingTaxes.forEach(pricingTax -> {
            PricingTax pricingToClone = new PricingTax();
            BeanUtils.copyProperties(pricingTax, pricingToClone);
            pricingToClone.setId(null);
            pricingToClone.setPricingId(pricingId);
            pricingToClone.setPricingDraftId(null);
            listTaxToClone.add(pricingToClone);
        });
        pricingTaxRepository.saveAll(listTaxToClone);
        //clone cac ban ghi cua pricingDraft cho pricing moi trong bang pricing_addons
        List<PricingAddon> pricingAddons = pricingAddonRepository.findByPricingDraftId(id);
        List<PricingAddon> listAddonToClone = new ArrayList<>();
        pricingAddons.forEach(pricingAddon -> {
            PricingAddon addonToClone = new PricingAddon();
            BeanUtils.copyProperties(pricingAddon, addonToClone);
            addonToClone.setId(null);
            addonToClone.setPricingId(pricingId);
            addonToClone.setPricingDraftId(null);
            listAddonToClone.add(addonToClone);
        });
        pricingAddonRepository.saveAll(listAddonToClone);
        //clone cac ban ghi cua pricingDraft cho pricing moi trong bang unit_limited
        List<UnitLimited> unitLimiteds = unitLimitedRepository.findByPricingDraftId(id);
        List<UnitLimited> listUnitLimitedToClone = new ArrayList<>();
        unitLimiteds.forEach(unitLimited -> {
            UnitLimited unitlimitedToClone = new UnitLimited();
            BeanUtils.copyProperties(unitLimited, unitlimitedToClone);
            unitlimitedToClone.setId(null);
            unitlimitedToClone.setPricingId(pricingId);
            unitlimitedToClone.setPricingDraftId(null);
            listUnitLimitedToClone.add(unitlimitedToClone);
        });
        unitLimitedRepository.saveAll(listUnitLimitedToClone);

        // update lại id pricing cho pricing_multi_plan
        List<PricingMultiPlan> pricingMultiPlans = pricingMultiPlanRepository
            .findAllByPricingDraftIdAndDeletedFlagAndPricingIdIsNull(id, DeletedFlag.NOT_YET_DELETED
                .getValue());
        List<PricingMultiPlan> pricingMultiPlanDB = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pricingMultiPlans)) {
            pricingMultiPlans.forEach(e -> e.setPricingId(pricingId));
            pricingMultiPlanDB = pricingMultiPlanRepository.saveAll(pricingMultiPlans);
        }

        // update lại id pricing cho bảng pricing_setup_fee_tax
        List<PricingSetupFeeTax> pricingSetupFeeTaxes = pricingSetupFeeTaxRepository.findAllByPricingDraftIdAndPricingIdIsNull(id);
        if (!CollectionUtils.isEmpty(pricingSetupFeeTaxes)) {
            pricingSetupFeeTaxes.forEach(e -> e.setPricingId(pricingId));
            pricingSetupFeeTaxRepository.saveAll(pricingSetupFeeTaxes);
        }
        return pricingMultiPlanDB;
    }

    @Override
    public Long getPricingDefault(ServiceEntity service) {
        if (Objects.nonNull(service.getPricingDefault())) {
            return service.getPricingDefault();
        }
        Long pricingDraftId = pricingDraftRepository.getPricingDefaultAuto(service.getId());
        return Objects.nonNull(pricingDraftId) ? pricingDraftId : null;
    }

    @Override
    public PricingSaaSResDTO getPricingSubscriptionByPricingId(Long pricingId) {
        return subscriptionDetailService.getPricingSubscriptionByPricingId(pricingId);
    }

    @Override
    public PricingSaaSResDTO convertPricingSaaSResponse(IPricingSaaSResDTO source) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerType = Objects.isNull(userLogin) ? null : userLogin.getCustomerType();
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getValueOf(customerType);
        //
        PricingSaaSResDTO pr = new PricingSaaSResDTO(source);
        pr.setCalculateTypeEnum(CalculateTypeEnum.fromValue(source.getCalculateType()));
        ServiceResponseDTO service = serviceRepository.getServiceCommonResponseById(pr.getServiceId());
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (Objects.nonNull(userLogin)) {
            boolean hasSub = subscriptionRepository.existsSubIsActiveByUserIdAndServiceId(userLogin.getId(), pr.getServiceId());
            if (Objects.nonNull(service) && service.getAllowMultiSub() == 0 && hasSub) {
                yesNoEnum = YesNoEnum.NO;
            }
        }
        YesNoEnum finalYesNoEnum = yesNoEnum;
        if (pr.getCustomerType() == null || pr.getCustomerType().contains(customerTypeEnum) || userLogin == null) {
            List<PricingMultiplePeriodResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodForPricing(pr.getId());
            List<PricingMultiplePeriodResDTO> multiplePeriodResponse = new ArrayList<>();
            for (PricingMultiplePeriodResDTO mul : multiplePeriods) {
                if (mul.getCustomerTypeCode() == null || mul.getCustomerTypeCode().contains(customerTypeEnum) || userLogin == null) {
                    multiplePeriodResponse.add(mul);
                }
            }
            // Nếu có period nhưng không thuộc kiểu người dùng thì add vào listErr để remove
            if (!CollectionUtils.isEmpty(multiplePeriods) && CollectionUtils.isEmpty(multiplePeriodResponse)) {
                return null;
            }
            pr.setPricingMultiplePeriods(multiplePeriodResponse);
            // Số tiền trước thuế
            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(pr.getId());
            BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(pr.getPriceValue(), taxes);
            pr.setPrice(pricingPreTax);
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                // pr.setCouponList(couponService.getCouponListByPricingIdOrPricingMultiPlanId(pr.getId(), null));
                pr.setUnitLimitedList(getUnitLimitedByPricingId(pr.getId()));
            }
            pr.setFeatureList(getListFeatureByFeatureIds(pr.getListFeatureId()));
            pr.setSeoDTO(seoService.getSeoDetailSme(pr.getPricingDraftId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU));
            pr.setAllowSubscript(finalYesNoEnum);
            pr.setReaction(serviceReactionRepository.existsByUserIdAndServiceIdAndType(Objects.nonNull(userLogin) ? userLogin.getId() : -1L,
                pr.getPricingDraftId(), 3));

            // set thông tin dịch vụ
            // nếu là gói cước của thiết bị, trả thêm id biến thể mặc định
            if (Objects.equals(pr.getServiceProductType(), ServiceProductTypeEnum.DEVICE.value)) {
                pr.setDefaultVariantDraftId(variantRepository.getDefaultVariantDraftIdByServiceId(pr.getServiceId()));
            }

            //set creationLayoutId, CustomField
            pricingDraftRepository.findById(pr.getPricingDraftId()).ifPresent(pricingDraft -> {
                Long layoutId = pricingDraft.getCreationLayoutId();
                layoutId = layoutId != null ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.PRICING.getValue());
                List<com.onedx.common.dto.customFields.CustomFieldValueDTO> lstFieldValueDTO = customFieldManager.getListFieldDraftValue(layoutId, EntityTypeEnum.PRICING.getValue(), pricingDraft.getId());
                pr.setCreationLayoutId(layoutId);
                pr.setLstCustomFields(lstFieldValueDTO);
            });
            return pr;
        }
        return null;
    }

    @Override
    public List<SubscriptionPricingPeriodAddonDTO> getAddonByPricingId(Long pricingId, Long pricingMultiPlanId) {
        return pricingRepository.getAddonByPricingIdAndPricingMultiPlanId(pricingId, pricingMultiPlanId);
    }

    @Override
    public List<PricingListDevRes> getListPricingDevV2(Long serviceId, String name, CustomerTypeEnum customerTypeCode,
        DisplayStatus displayStatus, ApproveStatusEnum approveStatus, ServiceDetailTabEnum tab) {
        if (serviceId <= 0) {
            String message = messageSource
                .getMessage(MessageKeyConstant.MUST_BE_GREATER_THAN_0, null, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                MessageKeyConstant.MUST_BE_GREATER_THAN_0);
        }
        Optional<ServiceEntity> service = serviceRepository.findByIdAndDeletedFlag(serviceId, 1);
        if (!service.isPresent()) {
            String message = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }

        //kiem tra dich vu co phai cua dev dang dang nhap hay khong
        ServiceDetailResponseDTO serviceDetailResponseDTO = serviceRepository.getDetailService(serviceId, AuthUtil.getCurrentUserId());
        if (!Objects.isNull(serviceDetailResponseDTO) && !serviceDetailResponseDTO.getDeveloperId()
            .equals(AuthUtil.getCurrentParentId())) {
            String message = messageSource
                .getMessage(MessageKeyConstant.SERVICE_IS_NOT_OWNED, null, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                MessageKeyConstant.SERVICE_IS_NOT_OWNED);
        }

        List<PricingListDevResDTO> originalPricingList = fetchOriginalPricingList(serviceId, name, customerTypeCode, displayStatus,
            approveStatus, tab);

        List<PricingListDevRes> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(originalPricingList)) {
            Long pricingDefault = getPricingDefault(service.get());
            for (PricingListDevResDTO item : originalPricingList) {
                PricingListDevRes temp = new PricingListDevRes();
                BeanUtils.copyProperties(item, temp);
                List<PricingMultiPlan> listPrm = new LinkedList<>();
                if (item.getApprove() != null && !item.getApprove().equals("APPROVED")) {
                    listPrm = pricingMultiPlanRepository.getAllPricingMultiPlanLatestByPricingDraftIdDraft(item.getId());
                } else {
                    listPrm = pricingMultiPlanRepository.getAllPricingMultiPlanLatestByPricingDraftIdAll(item.getId());
                }

                List<PricingDetailResDTO.PricingStrategy> listPrmRes = new ArrayList<>();
                for (PricingMultiPlan itemPrm : listPrm) {
                    listPrmRes.add(getPricingPlanDetailResDTO(itemPrm));
                }
                //Check dvu cu
                boolean isOldPricingValue = pricingDraftRepository.existsByIdAndPaymentCycleIsNotNullAndCycleTypeIsNotNull(temp.getId());
                if (isOldPricingValue && listPrmRes.isEmpty()) {
                    PricingDetailResDTO.PricingStrategy pricingStrategy = createFakeDataPricingPlanForOldPricing(temp.getId(),
                        PricingDetailInputEnum.PROCESSING);
                    pricingStrategy.setPricingId(item.getId());
                    pricingStrategy.setId(-1L);
                    pricingStrategy.setApproveStatus(ApproveTypeEnum.valueOf(item.getApproveValue()));
                    listPrmRes.add(pricingStrategy);
                }
                temp.setPricingStrategies(listPrmRes);
                temp.setIsDefault(
                    Objects.nonNull(pricingDefault) && Objects.equals(pricingDefault, item.getId()) ? Boolean.TRUE : Boolean.FALSE);
                result.add(temp);
            }
        }
        return result;
    }

    @Override
    public List<PricingListDevRes> getListPricingAdmin(Long serviceId, String name, CustomerTypeEnum customerTypeCode,
        DisplayStatus displayStatus, ApproveStatusEnum approveStatus, ServiceDetailTabEnum tab) {
        if (serviceId <= 0) {
            String message = messageSource
                .getMessage(MessageKeyConstant.MUST_BE_GREATER_THAN_0, null, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                MessageKeyConstant.MUST_BE_GREATER_THAN_0);
        }
        Optional<ServiceEntity> service = serviceRepository.findByIdAndDeletedFlag(serviceId, 1);
        if (!service.isPresent()) {
            String message = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }

        List<PricingListDevResDTO> originalPricingList = fetchOriginalPricingList(serviceId, name, customerTypeCode, displayStatus,
            approveStatus, tab);

        List<PricingListDevRes> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(originalPricingList)) {
            Long pricingDefault = getPricingDefault(service.get());
            for (PricingListDevResDTO item : originalPricingList) {
                PricingListDevRes temp = new PricingListDevRes();
                BeanUtils.copyProperties(item, temp);
                List<PricingMultiPlan> listPrm = new LinkedList<>();
                if (item.getApprove() != null && !item.getApprove().equals("APPROVED")) {
                    listPrm = pricingMultiPlanRepository.getAllPricingMultiPlanLatestByPricingDraftIdDraft(item.getId());
                } else {
                    listPrm = pricingMultiPlanRepository.getAllPricingMultiPlanLatestByPricingDraftIdAll(item.getId());
                }
                List<PricingDetailResDTO.PricingStrategy> listPrmRes = new ArrayList<>();
                for (PricingMultiPlan itemPrm : listPrm) {
                    listPrmRes.add(getPricingPlanDetailResDTO(itemPrm));
                }
                temp.setPricingStrategies(listPrmRes);
                temp.setIsDefault(
                    Objects.nonNull(pricingDefault) && Objects.equals(pricingDefault, item.getId()) ? Boolean.TRUE : Boolean.FALSE);
                result.add(temp);
            }
        }
        return result;

    }

    private List<PricingListDevResDTO> fetchOriginalPricingList(Long serviceId, String name, CustomerTypeEnum customerTypeCode,
        DisplayStatus displayStatus, ApproveStatusEnum approveStatus, ServiceDetailTabEnum tab) {
        List<PricingListDevResDTO> originalPricingList;
        if (Objects.equals(tab, ServiceDetailTabEnum.APPROVED)) { //Danh sách pricing tab đã duyệt
            originalPricingList = pricingRepository.getListPricingDev(serviceId, name, customerTypeCode.getValue(),
                displayStatus.value, approveStatus.value);
        } else { // Danh sách pricing tab đang xử lý
            originalPricingList = pricingRepository.getListPricingDraftDev(serviceId, name, customerTypeCode.getValue(),
                displayStatus.value, approveStatus.value);
        }
        return originalPricingList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseDTO updateDisplay(Long id, DisplayStatus status, PortalType portal) {
        PricingDraft currentDraft = getCurrentPricing(id);
        // Kiểm tra quyền tắt/bật Addon
        if (!Objects.equals(portal, PortalType.ADMIN)) {
            checkRolePortal(portal, currentDraft.getServiceId());
        }
        // Kiểm tra pricingDraft đã có bản pricing chưa?
        if (!pricingDraftRepository.checkPricingApprove(id)) {
            String message = messageSource.getMessage(
                MessageKeyConstant.PRICING_NOT_APPROVED, pricingM,
                LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.PRICING,
                ErrorKey.Pricing.STATUS,
                MessageKeyConstant.PRICING_NOT_APPROVED);
        }
        // Kiểm tra loại đối tượng khách hàng có là tập con của DTKH dịch vụ
        ServiceEntity serviceEntity = servicesService.getCurrentService(currentDraft.getServiceId());
        if (!serviceEntity.getCustomerTypeCode().containsAll(currentDraft.getCustomerTypeCode()) || CollectionUtils.isEmpty(
            currentDraft.getCustomerTypeCode())) {
            throw new BadRequestException("CustomerType của gói không hợp lệ", Resources.PRICING,
                ErrorKey.Pricing.STATUS,
                MessageKeyConstant.PRICING_CUSTOMER_TYPE_NOT_AVAILABLE);
        }
        currentDraft.setStatus(status.value);
        // Lấy bản pricing mới nhất và update trạng thái theo pricingDraft
        Pricing pricing = pricingRepository.getPricingNew(id);

        pricing.setStatus(status.value);
        currentDraft.setModifiedAt(new Date());
        pricingDraftRepository.save(currentDraft);
        pricingRepository.save(pricing);

        //Đổi lại trạng thái áp dụng khi bật
        if (Objects.equals(status, DisplayStatus.VISIBLE)) {
            if (Objects.isNull(serviceEntity.getPricingDefault())) { // neu bat goi dich vu va chua gan pricing default cho dich vu
                serviceRepository.changePricingDefault(id, serviceEntity.getId());
            }
            couponAddonRepository.changeOnStatusCouponPricingType();
        }

        //Đổi lại trạng thái áp dụng khi tắt
        if (Objects.equals(status, DisplayStatus.INVISIBLE)) {
            couponAddonRepository.changeOffStatusCouponPricingType();
        }
        return new BaseResponseDTO(id);
    }

    /**
     * Kiểm tra pricing có tồn tại
     *
     */
    public PricingDraft getCurrentPricing(Long id) {
        Optional<PricingDraft> draftOpt = pricingDraftRepository
            .findByIdAndDeletedFlag(id, DeleteFlag.ACTIVE);
        if (!draftOpt.isPresent()) {
            String messageNotFound = messageSource.getMessage(
                MessageKeyConstant.NOT_FOUND, pricingM,
                LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(messageNotFound, Resources.TAX,
                ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        return draftOpt.get();
    }

    @Transactional
    @Override
    public void deletePricing(Long id) {
        PricingDraft pricingDraft = pricingDraftRepository
            .findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });
        //Check user đăng nhập có phải là nhân viên của doanh nghiệp tạo ra gói dịch vụ
        if (!AuthUtil.getCurrentParentId().equals(pricingRepository.findOwnerPricingDraft(id))) {
            String message = messageSource.getMessage(MessageKeyConstant.USER_CAN_NOT_DELETE_PRICING, pricingM,
                LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                MessageKeyConstant.USER_CAN_NOT_DELETE_PRICING);
        }
        List<Pricing> listPricing = pricingRepository
            .findAllByPricingDraftIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        if (!listPricing.isEmpty()) {
            List<Long> listPricingIds = new ArrayList<>();
            for (Pricing pricing : listPricing) {
                listPricingIds.add(pricing.getId());
            }
            //Check xem có pricing nào trong list đang đăng ký không
            if (pricingRepository.checkPricingIsUsing(listPricingIds)) {
                String message = messageSource
                    .getMessage(MessageKeyConstant.PRICING_STILL_USED, pricingM, LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                    MessageKeyConstant.PRICING_STILL_USED);
            }
            for (Pricing pricing : listPricing) {
                pricing.setDeletedFlag(DeletedFlag.DELETED.getValue());
            }

            //Kiem tra pricing này đã có trong coupon được approve hay chưa?
            Boolean checkInCouponApproved = couponPricingRepository.checkCouponPricing(listPricingIds);
            if (checkInCouponApproved) {
                throw throwPricingBadRequest(MessageKeyConstant.COUPON_APPROVE, ErrorKey.ID, null);
            }

            //Kiem tra pricing này đã có trong combo_plan chưa?
            boolean checkInComboPlan = pricingRepository.checkListPricingInComboPlan(listPricingIds);
            if (checkInComboPlan) {
                throw throwPricingBadRequest(MessageKeyConstant.COMBO_USING_PRICING, ErrorKey.ID, null);
            }

            // Xoa lien ket giua bang pricing va coupon
            couponPricingRepository.deleteByPricingIdIn(listPricingIds);
            couponPricingApplyRepository.deleteByPricingIdIn(listPricingIds);
            couponAddonRepository.changeOffStatusCouponPricingType();
            pricingRepository.saveAll(listPricing);
            // update nhóm sp khi xóa pricing
            // chuyển các nhóm có chứa gói bị xóa thành trạng thái Chưa duyệt
            List<ServiceGroupDraft> lstServiceGroupDraft = serviceGroupDraftRepository.getLstGroupDraftByPricingIdIn(listPricingIds);
            serviceGroupService.saveGroupDraftAsUnapproved(lstServiceGroupDraft, Boolean.TRUE);
        }
        pricingDraft.setDeletedFlag(DeletedFlag.DELETED.getValue());

        pricingDraftRepository.save(pricingDraft);
    }

    @Transactional
    @Override
    public void deletePricings(List<Long> ids) {
        List<Long> pricingIds = new ArrayList<>();
        List<Pricing> pricings = new ArrayList<>();
        List<PricingDraft> pricingDrafts = ids.stream().map(id -> {
            PricingDraft pricingDraft = pricingDraftRepository
                .findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                    String message = messageSource
                        .getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
                    return new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.ID,
                        MessageKeyConstant.NOT_FOUND);
                });
            //Check user đăng nhập có phải là nhân viên của doanh nghiệp tạo ra gói dịch vụ
            if (!(AuthUtil.getCurrentParentId().equals(pricingRepository.findOwnerPricingDraft(id)) || AuthUtil.isAdmin())) {
                String message = messageSource.getMessage(MessageKeyConstant.USER_CAN_NOT_DELETE_PRICING, pricingM,
                    LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                    MessageKeyConstant.USER_CAN_NOT_DELETE_PRICING);
            }
            List<Pricing> listPricing = pricingRepository
                .findAllByPricingDraftIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
            if (!listPricing.isEmpty()) {
                List<Long> listPricingIds = new ArrayList<>();
                for (Pricing pricing : listPricing) {
                    listPricingIds.add(pricing.getId());
                }
                //Check xem có pricing nào trong list đang đăng ký không
                if (pricingRepository.checkPricingIsUsing(listPricingIds)) {
                    String message = messageSource
                        .getMessage(MessageKeyConstant.PRICING_STILL_USED, pricingM, LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.PRICING, ErrorKey.ID,
                        MessageKeyConstant.PRICING_STILL_USED);
                }
                for (Pricing pricing : listPricing) {
                    pricing.setDeletedFlag(DeletedFlag.DELETED.getValue());
                }

                //Kiem tra pricing này đã có trong coupon được approve hay chưa?
                Boolean checkInCouponApproved = couponPricingRepository.checkCouponPricing(listPricingIds);
                if (checkInCouponApproved) {
                    throw throwPricingBadRequest(MessageKeyConstant.COUPON_APPROVE, ErrorKey.ID, null);
                }

                //Kiem tra pricing này đã có trong combo_plan chưa?
                boolean checkInComboPlan = pricingRepository.checkListPricingInComboPlan(listPricingIds);
                if (checkInComboPlan) {
                    throw throwPricingBadRequest(MessageKeyConstant.COMBO_USING_PRICING, ErrorKey.ID, null);
                }

                pricingIds.addAll(listPricingIds);
                pricings.addAll(listPricing);
            }
            pricingDraft.setDeletedFlag(DeletedFlag.DELETED.getValue());
            return pricingDraft;
        }).collect(Collectors.toList());
        // Xoa lien ket giua bang pricing va coupon
        if (!pricingIds.isEmpty()) {
            couponPricingRepository.deleteByPricingIdIn(pricingIds);
            couponPricingApplyRepository.deleteByPricingIdIn(pricingIds);
            couponAddonRepository.changeOffStatusCouponPricingType();
        }
        //
        if (!pricings.isEmpty()) {
            pricingRepository.saveAll(pricings);
        }
        if (!pricingDrafts.isEmpty()) {
            pricingDraftRepository.saveAll(pricingDrafts);
        }
        // update nhóm sp khi xóa pricing
        // chuyển các nhóm có chứa gói bị xóa thành trạng thái Chưa duyệt
        List<ServiceGroupDraft> lstServiceGroupDraft = serviceGroupDraftRepository.getLstGroupDraftByPricingIdIn(pricingIds);
        serviceGroupService.saveGroupDraftAsUnapproved(lstServiceGroupDraft, Boolean.TRUE);
    }

    @Transactional
    @Override
    public void changePricingDefault(Long serviceId, Long pricingDraftId) {
        serviceRepository.changePricingDefault(pricingDraftId, serviceId);
    }

    @Override
    @Transactional
    public synchronized BaseResponseDTO createPricing(PricingReqDTO pricingDto, Boolean isCreateWithService, Boolean isApproved) throws JsonProcessingException {
        // dich vu
        if (!isCreateWithService && !AuthUtil.isFullAdmin()) {
            validateService(pricingDto);
        }
        // thong tin chung
//        validateName(pricingDto.getServiceId(), pricingDto.getPricingName()); //Nghĩa fix ngày 5/8: bỏ check trùng tên SPDV
        if (pricingDto.getPricingCode() != null && pricingDto.getPricingCode() != "") {
            validateCode(pricingDto);
        }
        // loại tiền tệ
        validateCurrency(pricingDto);
        // chiến lược định giá
        validatePricingPlan(pricingDto, pricingDto.getServiceId());
        // thông tin thuế
        validateTax(pricingDto);
        // thông tin thuế setup fee
        validateSetupFeeTax(pricingDto);
        // dịch vụ bổ sung
        validateAddon(pricingDto);
        // danh sach tinh nang
        validateFeatures(pricingDto);

        PricingDraft pricing = pricingReqMapper.toEntity(pricingDto);
        pricing.setIsOneTime(pricingDto.getIsOneTime());
        addPricingHistory(pricing, ApproveStatusEnum.UNAPPROVED, pricingDto.getPortalType());

        // luu vao database
        return savePricingEntity(pricingDto, IntegrationEnum.NOT_INTEGRATION, pricingDto.getPortalType(), isCreateWithService, isApproved);
    }

    /**
     * Validate setup fee tax
     */
    private void validateSetupFeeTax(PricingReqDTO pricingDto) {
        List<RowTax> taxes = pricingDto.getSetupFees().stream().map(SetupFee::getTax).filter(tax -> Objects.nonNull(tax)).collect(Collectors.toList());;
        if (!CollectionUtils.isEmpty(taxes)) {
            Set<Long> taxIds = taxes.stream().map(RowTax::getTaxId).collect(Collectors.toSet());
            Set<Tax> taxDbs = taxRepository.findByIdIn(taxIds);
            Set<Long> taxDbIds = taxDbs.stream().map(Tax::getId).collect(Collectors.toSet());
            StringBuilder msgError = new StringBuilder();
            boolean hasError = false;
            Set<Long> taxNotFound = new LinkedHashSet<>();
            // tax không tồn tại hệ thống
            if (!Objects.equals(taxes.size(), taxDbs.size())) {
                for (Long id : taxIds) {
                    if (!taxDbIds.contains(id)) {
                        taxNotFound.add(id);
                    }
                }
            }
            // tax đã bị xóa
            Set<Long> taxDeleted = taxDbs.stream()
                .filter(t -> t.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                .map(Tax::getId).collect(Collectors.toSet());
            // tax đã bị tắt hoạt động
            Set<Long> taxDisable =
                taxDbs.stream().filter(t -> t.getStatus().equals(StatusEnum.INACTIVE.value))
                    .map(Tax::getId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(taxNotFound)) {
                hasError = true;
                String stringIds = String.join(",",
                    taxNotFound.stream().map(Object::toString).collect(Collectors.toSet()));
                msgError.append(messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                    new String[]{stringIds}, LocaleContextHolder.getLocale()));
            }
            if (!CollectionUtils.isEmpty(taxDeleted)) {
                hasError = true;
                if (!CollectionUtils.isEmpty(taxNotFound)) {
                    msgError.append("|");
                }
                String stringIds = String.join(",",
                    taxDeleted.stream().map(Object::toString).collect(Collectors.toSet()));
                msgError.append(messageSource.getMessage(MessageKeyConstant.DELETED,
                    new String[]{stringIds}, LocaleContextHolder.getLocale()));
            }
            if (!CollectionUtils.isEmpty(taxDisable)) {
                hasError = true;
                if (!CollectionUtils.isEmpty(taxNotFound) || !CollectionUtils.isEmpty(taxDeleted)) {
                    msgError.append("|");
                }
                String stringIds = String.join(",",
                    taxDisable.stream().map(Object::toString).collect(Collectors.toSet()));
                msgError.append(messageSource.getMessage(MessageKeyConstant.INACTIVE,
                    new String[]{stringIds}, LocaleContextHolder.getLocale()));
            }
            if (hasError) {
                throw new BadRequestException(msgError.toString(), Resources.PRICING,
                    ErrorKey.Pricing.TAX_LIST, MessageKeyConstant.TAX_INVALID);
            }
        }
    }

    /**
     * Tạo lịch sử gói
     *
     * @param pricingDraft the pricing draft
     * @param statusEnum   the status enum
     */
    private void addPricingHistory(PricingDraft pricingDraft, ApproveStatusEnum statusEnum, PortalType type) {

        String content = null;

        if (Objects.equals(statusEnum, ApproveStatusEnum.REJECTED)) {
            content = pricingDraft.getRejectReason();
        }

        if (Objects.equals(statusEnum, ApproveStatusEnum.AWAITING_APPROVAL)) {
            content = pricingDraft.getUpdateReason();
        }

        ActionLogTemplateDTO log = ActionLogTemplateDTO.builder()
            .serviceId(pricingDraft.getServiceId())
            .status(statusEnum)
            .type(ActionLogTypeEnum.PRICING)
            .content(content)
            .portal(type)
            .object(pricingDraft.getPricingName())
            .build();

        actionLogService.addLog(log);
    }

    @Override
    @Transactional
    public void updateOrderAndRecommendOfSaas(List<OrderListResponseDTO> orderListResponseDTOs,
        Long serviceId) {
        //kiem tra service co ton tai khong
        Optional<ServiceEntity> serviceEntity = serviceRepository
            .findByIdAndDeletedFlag(serviceId, CustomerTicketConstant.DELETED_FLAG);
        if (!serviceEntity.isPresent()) {
            String messageNotFound = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, serviceM, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(messageNotFound, Resources.SERVICES, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        }
        if (!Objects.equals(serviceEntity.get().getUserId(), AuthUtil.getCurrentParentId())) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        List<Long> pricingDraftIds = orderListResponseDTOs.stream().map(OrderListResponseDTO::getId).collect(Collectors.toList());
        List<PricingDraft> pricingDrafts = pricingDraftRepository
            .findByServiceIdAndIdInAndDeletedFlag(serviceId, pricingDraftIds, DeletedFlag.NOT_YET_DELETED.getValue());

        List<Pricing> pricings = pricingRepository.getLstNewestPricingByPricingDraftId(pricingDraftIds);

        //kiem tra goi dich vu co thuoc ve saas khong
        if (pricingDrafts.size() != orderListResponseDTOs.size()) {
            String messageNotFound = messageSource
                .getMessage(MessageKeyConstant.INVALID_SUBSCRIPTION_PLAN, pricingM,
                    LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(messageNotFound, Resources.SUBSCRIPTION_PLAN, ErrorKey.ID,
                MessageKeyConstant.INVALID_SUBSCRIPTION_PLAN);
        }

        Map<Long, OrderListResponseDTO> mapOrderListResponseDTOs = orderListResponseDTOs.stream()
            .collect(Collectors.toMap(OrderListResponseDTO::getId, i -> i));

        pricingDrafts.forEach(pricingDraft -> {
            pricingDraft.setPricingOrder(mapOrderListResponseDTOs.get(pricingDraft.getId()).getDisplayedOrder());
            pricingDraft.setRecommendedStatus(SubscriptionPlanRecommendedStatusEnum
                .valueOf(mapOrderListResponseDTOs.get(pricingDraft.getId()).getRecommendedStatus()).value);
        });

        pricings.forEach(pricing -> {
            pricing.setPricingOrder(mapOrderListResponseDTOs.get(pricing.getPricingDraftId()).getDisplayedOrder());
            pricing.setRecommendedStatus(SubscriptionPlanRecommendedStatusEnum
                .valueOf(mapOrderListResponseDTOs.get(pricing.getPricingDraftId()).getRecommendedStatus()).value);
        });
        pricingDraftRepository.saveAll(pricingDrafts);
        pricingRepository.saveAll(pricings);
    }

    /**
     * get thong bao khong tim thay
     *
     * @return the message not found
     */
    private String getMessageNotFound() {
        return messageSource.getMessage(MessageKeyConstant.NOT_FOUND, null,
            LocaleContextHolder.getLocale());
    }

    /**
     * Lấy message khi null
     *
     * @return the message must null
     */
    private String getMessageMustNull() {
        return messageSource.getMessage(Validation.NULL, null,
            LocaleContextHolder.getLocale());
    }

    /**
     * Lấy message khi không null
     *
     * @return the message not null
     */
    private String getMessageNotNull() {
        return messageSource.getMessage(Validation.NOT_NULL, null,
            LocaleContextHolder.getLocale());
    }

    /**
     * kiem tra service hop le
     *
     * @param pricingDto the pricing dto
     */
    public void validateService(PricingReqDTO pricingDto) {
        Long parentId = AuthUtil.getCurrentParentId();
        if (!serviceRepository.checkExistsByIdAndParentId(pricingDto.getServiceId(), parentId)) {
            throw new BadRequestException(getMessageNotFound(), Resources.SERVICES,
                Services.ID, MessageKeyConstant.NOT_FOUND);
        }
    }

    /**
     * kiem tra code không được trùng trong cùng 1 dịch vụ
     *
     * @param pricingDto the pricing dto
     */
    private void validateCode(PricingReqDTO pricingDto) {
        if (pricingRepository.checkCodeExists(pricingDto.getPricingCode())
            || pricingDraftRepository.checkCodeExists(pricingDto.getPricingCode())) {
            throw throwPricingBadRequest(MessageKeyConstant.DATA_EXISTS, ErrorKey.Pricing.PRICING_CODE, ErrorKey.Pricing.PRICING_CODE);
        }
    }

    /**
     * kiem tra loai tien te cua chiến lược định giá
     *
     * @param pricingDto the pricing dto
     */
    private void validateCurrency(PricingReqDTO pricingDto) {
        Currency currency = currencyRepository.findById(pricingDto.getCurrencyId()).orElseThrow(() -> {
            String msg = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, new Long[]{pricingDto.getCurrencyId()}, LocaleContextHolder.getLocale());
            return new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.CURRENCY_ID, MessageKeyConstant.NOT_FOUND);
        });
        if (currency.getStatus().equals(StatusEnum.INACTIVE.value)) {
            String msg = messageSource
                .getMessage(MessageKeyConstant.INACTIVE, new Long[]{pricingDto.getCurrencyId()}, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.CURRENCY_ID, MessageKeyConstant.INACTIVE);
        }
    }

    /**
     * kiem tra kế hoạch định giá
     *
     * @param pricingDto the pricing dto
     */
    private void validatePricingPlan(PricingReqDTO pricingDto, Long serviceId) {

        // check không truyền chiến lược định giá
        if (CollectionUtils.isEmpty(pricingDto.getPricingStrategies())) {
            String msg = messageSource
                .getMessage(MessageKeyConstant.PRICING_MULTI_PLANS_IS_NOT_EMPTY, new Long[]{pricingDto.getCurrencyId()},
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.PRICING_PLAN,
                MessageKeyConstant.PRICING_MULTI_PLANS_IS_NOT_EMPTY);
        }

        serviceRepository.findById(pricingDto.getServiceId()).filter(i -> !CollectionUtils.isEmpty(i.getCustomerTypeCode()))
            .ifPresent(i -> {
                Set<CustomerTypeEnum> serviceCustomerType = i.getCustomerTypeCode().stream().filter(Objects::nonNull)
                    .map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(pricingDto.getCustomerTypeCode()) &&
                    !serviceCustomerType.containsAll(pricingDto.getCustomerTypeCode())) {
                    String msg = messageSource.getMessage(MessageKeyConstant.PRICING_CUSTOMER_TYPE_CODE_INVALID, null,
                        LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.PRICING,
                        ErrorKey.Pricing.ID, MessageKeyConstant.PRICING_CUSTOMER_TYPE_CODE_INVALID);
                }
            });

        // check chu kỳ thanh toán
        validatePricingMultiPlan(pricingDto);

        validateCycleCode(pricingDto, serviceId);

        // check chiến lược định giá theo từng loại
        pricingDto.getPricingStrategies().forEach(e -> {
            switch (e.getPricingPlan()) {
                case FLAT_RATE:
                    // ke hoach dinh gia co dinh
                    validateFlatRate(e);
                    break;
                case UNIT:
                    // ke hoach dinh gia theo don vi
                    validateUnit(e);
                    validateUnitId(e);
                    break;
                default:
                    validateUnitId(e);
                    validateTierAndVolumeAndStairStep(e);
                    break;
            }
        });
    }

    /**
     * check mã chu kỳ phải là duy nhất trong 1 gói dịch vụ
     */
    private void validateCycleCode(PricingReqDTO pricingDto, Long serviceId) {
        // check mã chu kỳ phải là duy nhất trong 1 gói dịch vụ
        pricingDto.getPricingStrategies().forEach(pricingMultiPlan -> {
            if (serviceId != null) {
                Set<String> pricingCodes = pricingRepository.getListCycleCode(serviceId);
                if (pricingCodes.contains(pricingMultiPlan.getCycleCode())) {
                    String msg = messageSource
                        .getMessage(MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_CYCLE_CODE_ALL_DB, null, LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.PRICING_PLAN,
                        MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_CYCLE_CODE_ALL_DB);
                }
            }
            if (pricingMultiPlan.getCycleCode() != null && !pricingMultiPlan.getCycleCode().isEmpty()) {
                if (pricingDto.getPricingStrategies().stream().filter(
                    e -> Objects.equals(pricingMultiPlan.getCycleCode(), e.getCycleCode())).count() > 1) {
                    String msg = messageSource
                        .getMessage(MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_CYCLE_CODE, null, LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.PRICING_PLAN,
                        MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_CYCLE_CODE);
                }
            }

        });

        // check mã gói phải là duy nhất trong hệ thống
        if (pricingDto.getPricingCode() != null && serviceId != null) {
            Set<String> pricingCodes = pricingRepository.getListPricingCode(serviceId);
            if (pricingCodes.contains(pricingDto.getPricingCode())) {
                String msg = messageSource
                    .getMessage(MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_PACKAGE_CODE_ALL_DB, null, LocaleContextHolder.getLocale());
                throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.PRICING_PLAN,
                    MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_PACKAGE_CODE_ALL_DB);
            }
        }

    }

    /**
     * check chu kỳ thanh toán là duy nhất
     */
    private void validatePricingMultiPlan(PricingReqDTO pricingDto) {
        // check mỗi kế hoạch định giá chỉ có một chu kì thanh toán duy nhất
        pricingDto.getPricingStrategies().forEach(pricingMultiPlan -> {
            if (pricingDto.getPricingStrategies().stream().filter(
                e -> Objects.equals(pricingMultiPlan.getPaymentCycle(), e.getPaymentCycle()) && Objects
                    .equals(pricingMultiPlan.getCycleType(), e.getCycleType())).count() > 1) {
                String msg = messageSource
                    .getMessage(MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_PAYMENT_CYCLE, null, LocaleContextHolder.getLocale());
                throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.PRICING_PLAN,
                    MessageKeyConstant.PRICING_MULTI_PLANS_DUPLICATE_PAYMENT_CYCLE);
            }
            if (!CollectionUtils.isEmpty(pricingDto.getCustomerTypeCode()) &&
                !CollectionUtils.isEmpty(pricingMultiPlan.getCustomerTypeCode()) &&
                !pricingDto.getCustomerTypeCode().containsAll(pricingMultiPlan.getCustomerTypeCode())) {
                String msg = messageSource.getMessage(MessageKeyConstant.PRICING_CUSTOMER_TYPE_CODE_INVALID, null,
                    LocaleContextHolder.getLocale());
                throw new BadRequestException(msg, Resources.PRICING,
                    ErrorKey.Pricing.ID, MessageKeyConstant.PRICING_CUSTOMER_TYPE_CODE_INVALID);
            }
        });
    }

    /**
     * Kiểm tra đơn vị tính hợp lệ
     *
     * @param pricingPlan the pricing dto
     */
    private void validateUnitId(PricingStrategy pricingPlan) {
        Unit unit;
        unit = unitRepository.findById(pricingPlan.getUnitId()).orElseThrow(() -> {
            String msg = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, new Long[]{pricingPlan.getUnitId()},
                LocaleContextHolder.getLocale());
            return new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.UNIT_ID, MessageKeyConstant.NOT_FOUND);
        });
        if (unit.getStatus().equals(StatusEnum.INACTIVE.value)) {
            String msg = messageSource.getMessage(MessageKeyConstant.INACTIVE,
                new Long[]{pricingPlan.getUnitId()}, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.UNIT_ID, MessageKeyConstant.INACTIVE);
        }
    }

    /**
     * kiem tra ke hoach dinh gia co dinh
     *
     * @param pricingPlan the pricing dto
     */
    private void validateFlatRate(PricingStrategy pricingPlan) {
        if (Objects.isNull(pricingPlan.getPrice())) {
            throw new BadRequestException(getMessageNotNull(), Resources.PRICING,
                ErrorKey.Pricing.PRICE, Validation.NOT_NULL);
        }
        if (Objects.nonNull(pricingPlan.getUnitId())) {
            throw new BadRequestException(getMessageMustNull(), Resources.PRICING,
                ErrorKey.Pricing.UNIT_ID, Validation.NULL);
        }
        if (Objects.nonNull(pricingPlan.getFreeQuantity())) {
            throw new BadRequestException(getMessageMustNull(), Resources.PRICING,
                ErrorKey.Pricing.FEE_QUANTITY, Validation.NULL);
        }
        if (!CollectionUtils.isEmpty(pricingPlan.getUnitLimitedList())) {
            throw new BadRequestException(getMessageMustNull(), Resources.PRICING,
                ErrorKey.Pricing.UNIT_LIMITED_LIST, Validation.NULL);
        }
    }

    /**
     * kiem tra ke hoach dinh gia theo don vi
     *
     * @param pricingPlan the pricing dto
     */
    private void validateUnit(PricingStrategy pricingPlan) {
        if (Objects.isNull(pricingPlan.getUnitId())) {
            throw new BadRequestException(getMessageNotNull(), Resources.PRICING,
                ErrorKey.Pricing.UNIT_ID, Validation.NOT_NULL);
        }
        if (Objects.isNull(pricingPlan.getPrice())) {
            throw new BadRequestException(getMessageNotNull(), Resources.PRICING,
                ErrorKey.Pricing.PRICE, Validation.NOT_NULL);
        }
        if (!CollectionUtils.isEmpty(pricingPlan.getUnitLimitedList())) {
            throw new BadRequestException(getMessageMustNull(), Resources.PRICING,
                ErrorKey.Pricing.UNIT_LIMITED_LIST, Validation.NULL);
        }
    }

    /**
     * kiem tra ke hoach dinh gia theo luy ke
     *
     * @param pricingPlan the pricing dto
     */
    private void validateTierAndVolumeAndStairStep(PricingStrategy pricingPlan) {
        if (Objects.isNull(pricingPlan.getUnitId())) {
            throw new BadRequestException(getMessageNotNull(), Resources.PRICING,
                ErrorKey.Pricing.UNIT_ID, Validation.NOT_NULL);
        }
        if (Objects.nonNull(pricingPlan.getPrice())) {
            throw new BadRequestException(getMessageMustNull(), Resources.PRICING,
                ErrorKey.Pricing.PRICE, Validation.NULL);
        }
        if (Objects.nonNull(pricingPlan.getFreeQuantity())) {
            throw new BadRequestException(getMessageMustNull(), Resources.PRICING,
                ErrorKey.Pricing.FEE_QUANTITY, Validation.NULL);
        }
    }

    /**
     * kiem tra thông tin thuế có tồn tại không.
     *
     * @param pricingDto the pricing dto
     */
    private void validateTax(PricingReqDTO pricingDto) {
        if (Objects.nonNull(pricingDto.getTaxList())) {
            LinkedHashSet<RowTax> taxes = pricingDto.getTaxList().stream().filter(tax ->  Objects.nonNull(tax.getPercent())).collect(Collectors.toCollection(LinkedHashSet::new));
            if (!CollectionUtils.isEmpty(taxes)) {
                taxes.forEach(t -> t.setHasTax(pricingDto.getHasTax()));
                pricingDto.setTaxList(taxes);
                Set<Long> taxIds = taxes.stream().map(RowTax::getTaxId).collect(Collectors.toSet());
                Set<Tax> taxDbs = taxRepository.findByIdIn(taxIds);
                Set<Long> taxDbIds = taxDbs.stream().map(Tax::getId).collect(Collectors.toSet());
                StringBuilder msgError = new StringBuilder();
                boolean hasError = false;
                Set<Long> taxNotFound = new LinkedHashSet<>();
                // tax không tồn tại hệ thống
                if (!Objects.equals(taxes.size(), taxDbs.size())) {
                    for (Long id : taxIds) {
                        if (!taxDbIds.contains(id)) {
                            taxNotFound.add(id);
                        }
                    }
                }
                // tax đã bị xóa
                Set<Long> taxDeleted = taxDbs.stream()
                        .filter(t -> t.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                        .map(Tax::getId).collect(Collectors.toSet());
                // tax đã bị tắt hoạt động
                Set<Long> taxDisable =
                        taxDbs.stream().filter(t -> t.getStatus().equals(StatusEnum.INACTIVE.value))
                                .map(Tax::getId).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(taxNotFound)) {
                    hasError = true;
                    String stringIds = String.join(",",
                            taxNotFound.stream().map(Object::toString).collect(Collectors.toSet()));
                    msgError.append(messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                            new String[]{stringIds}, LocaleContextHolder.getLocale()));
                }
                if (!CollectionUtils.isEmpty(taxDeleted)) {
                    hasError = true;
                    if (!CollectionUtils.isEmpty(taxNotFound)) {
                        msgError.append("|");
                    }
                    String stringIds = String.join(",",
                            taxDeleted.stream().map(Object::toString).collect(Collectors.toSet()));
                    msgError.append(messageSource.getMessage(MessageKeyConstant.DELETED,
                            new String[]{stringIds}, LocaleContextHolder.getLocale()));
                }
                if (!CollectionUtils.isEmpty(taxDisable)) {
                    hasError = true;
                    if (!CollectionUtils.isEmpty(taxNotFound) || !CollectionUtils.isEmpty(taxDeleted)) {
                        msgError.append("|");
                    }
                    String stringIds = String.join(",",
                            taxDisable.stream().map(Object::toString).collect(Collectors.toSet()));
                    msgError.append(messageSource.getMessage(MessageKeyConstant.INACTIVE,
                            new String[]{stringIds}, LocaleContextHolder.getLocale()));
                }
                if (hasError) {
                    throw new BadRequestException(msgError.toString(), Resources.PRICING,
                            ErrorKey.Pricing.TAX_LIST, MessageKeyConstant.TAX_INVALID);
                }
            }
        }
    }

    /**
     * kiem tra danh sách dịch vụ bổ sung.
     *
     * @param pricingDto the pricing dto
     */
    private void validateAddon(PricingReqDTO pricingDto) {
        LinkedHashSet<RowAddon> addons = pricingDto.getAddonList();

        //validate addon đăng ký không vượt quá 30 addon
        if (!CollectionUtils.isEmpty(addons) && addons.size() >= ADDON_PRICING_MAX_ACCEPTED) {
            String msg = messageSource.getMessage(MessageKeyConstant.PUBLIC_ADDON_GREATER_THAN_MAX_ACCEPTED_VALUE, null,
                LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.ADDON,
                MessageKeyConstant.PUBLIC_ADDON_GREATER_THAN_MAX_ACCEPTED_VALUE);
        }

        if (!CollectionUtils.isEmpty(addons)) {
            Set<Long> addonIds =
                addons.stream().map(RowAddon::getId).collect(Collectors.toSet());
            Set<Addon> addonDbs = addonRepository.findByIdIn(addonIds);
            Set<Long> addonIdDbs = addonDbs.stream().map(Addon::getId).collect(Collectors.toSet());

            // ids không tồn tại hệ thống
            Set<Long> idsNotFound = new LinkedHashSet<>();
            if (!Objects.equals(addonIdDbs.size(), addonIds.size())) {
                for (Long id : addonIds) {
                    if (!addonIdDbs.contains(id)) {
                        idsNotFound.add(id);
                    }
                }
            }
            // ids đã bị xóa
            Set<Long> idsDeleted = addonDbs.stream()
                .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                .map(Addon::getId).collect(Collectors.toSet());

            generateDetailErrorPricing(idsNotFound, idsDeleted, null,
                ErrorKey.Pricing.ADDON_LIST, MessageKeyConstant.PRICING_ADDONS_INVALID);

            //nếu là gói 1 lần thì chỉ đc gắn với addon 1 lần
            if (Objects.equals(SubTypeEnum.ONCE.value, pricingDto.getIsOneTime())) {
                addonDbs.forEach(addon -> {
                    if (Objects.equals(SubTypeEnum.PERIODIC.value, addon.getBonusType())) {
                        String msg = messageSource.getMessage(MessageKeyConstant.PRICING_ADDONS_INVALID, null,
                            LocaleContextHolder.getLocale());
                        throw new BadRequestException(msg, Resources.ADDON, ErrorKey.Pricing.ADDON,
                            MessageKeyConstant.PRICING_ADDONS_INVALID);
                    }
                });
            }

        }

        //validate addon của pricing_multi_plan vượt quá 30 addon
        pricingDto.getPricingStrategies().forEach(pricingPlan -> {
            if (!CollectionUtils.isEmpty(pricingPlan.getAddonList())) {
                if (pricingPlan.getAddonList().size() >= ADDON_PRICING_MAX_ACCEPTED) {
                    String msg = messageSource.getMessage(MessageKeyConstant.PRIVATE_ADDON_GREATER_THAN_MAX_ACCEPTED_VALUE,
                        new Object[]{pricingPlan.getId().toString()},
                        LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.PRICING, ErrorKey.Pricing.ADDON,
                        MessageKeyConstant.PRIVATE_ADDON_GREATER_THAN_MAX_ACCEPTED_VALUE);
                }
                Set<Long> addonIds =
                    pricingPlan.getAddonList().stream().map(RowAddon::getId).collect(Collectors.toSet());
                Set<Addon> addonDbs = addonRepository.findByIdIn(addonIds);
                Set<Long> addonIdDbs = addonDbs.stream().map(Addon::getId).collect(Collectors.toSet());

                // ids không tồn tại hệ thống
                Set<Long> idsNotFound = new LinkedHashSet<>();
                if (!Objects.equals(addonIdDbs.size(), addonIds.size())) {
                    for (Long id : addonIds) {
                        if (!addonIdDbs.contains(id)) {
                            idsNotFound.add(id);
                        }
                    }
                }
                // ids đã bị xóa
                Set<Long> idsDeleted = addonDbs.stream()
                    .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                    .map(Addon::getId).collect(Collectors.toSet());

                generateDetailErrorPricing(idsNotFound, idsDeleted, null,
                    ErrorKey.Pricing.ADDON_LIST, MessageKeyConstant.PRICING_ADDONS_INVALID);
            }
        });

        //nếu là gói 1 lần thì chỉ đc gắn với addon 1 lần
        if (Objects.nonNull(pricingDto.getPricingStrategies())) {
            if (Objects.equals(SubTypeEnum.ONCE.value, pricingDto.getIsOneTime())) {
                pricingDto.getPricingStrategies().forEach(e -> {
                    Set<Long> addonIds = e.getAddonList().stream().map(RowAddon::getId).collect(Collectors.toSet());
                    Set<Addon> addonDbs = addonRepository.findByIdIn(addonIds);
                    addonDbs.forEach(addon -> {
                        if (Objects.equals(SubTypeEnum.PERIODIC.value, addon.getBonusType())) {
                            String msg = messageSource.getMessage(MessageKeyConstant.PRICING_ADDONS_INVALID, null,
                                LocaleContextHolder.getLocale());
                            throw new BadRequestException(msg, Resources.ADDON, ErrorKey.Pricing.ADDON,
                                MessageKeyConstant.PRICING_ADDONS_INVALID);
                        }
                    });


                });
            }
        }
    }

    /**
     * Trả lỗi chi tiết ids không tồn tại, ids bị xóa, ids tắt hoạt động
     *
     * @param idsNotFound the ids not found
     * @param idsDeleted the ids deleted
     * @param idsDisabled the ids disabled
     */
    private void generateDetailErrorPricing(Set<Long> idsNotFound, Set<Long> idsDeleted, Set<Long> idsDisabled, String fieldError,
        String errorCode) {
        StringBuilder msgError = new StringBuilder();
        boolean hasError = false;
        if (!CollectionUtils.isEmpty(idsNotFound)) {
            hasError = true;
            String stringIds = String.join(",",
                idsNotFound.stream().map(Object::toString).collect(Collectors.toSet()));
            msgError.append(messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                new String[]{stringIds}, LocaleContextHolder.getLocale()));
        }
        if (!CollectionUtils.isEmpty(idsDeleted)) {
            hasError = true;
            if (!CollectionUtils.isEmpty(idsNotFound)) {
                msgError.append("|");
            }
            String stringIds = String.join(",",
                idsDeleted.stream().map(Object::toString).collect(Collectors.toSet()));
            msgError.append(messageSource.getMessage(MessageKeyConstant.DELETED,
                new String[]{stringIds}, LocaleContextHolder.getLocale()));
        }
        if (!CollectionUtils.isEmpty(idsDisabled)) {
            hasError = true;
            if (!CollectionUtils.isEmpty(idsNotFound) || !CollectionUtils.isEmpty(idsDeleted)) {
                msgError.append("|");
            }
            String stringIds = String.join(",",
                idsDisabled.stream().map(Object::toString).collect(Collectors.toSet()));
            msgError.append(messageSource.getMessage(MessageKeyConstant.INACTIVE,
                new String[]{stringIds}, LocaleContextHolder.getLocale()));
        }
        if (hasError) {
            throw new BadRequestException(msgError.toString(), Resources.PRICING, fieldError, errorCode);
        }
    }

    /**
     * kiem tra feature hợp lệ
     *
     * @param pricingDto the pricing dto
     */
    private void validateFeatures(PricingReqDTO pricingDto) {
        Set<Long> featureIds = pricingDto.getFeatureList();
        if (!CollectionUtils.isEmpty(featureIds)) {
            Set<Feature> features;
            features = featureRepository.findByIdIn(featureIds);

            Set<Long> featuresDeleted = features.stream()
                .filter(f -> f.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                .map(Feature::getId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(featuresDeleted)) {
                String idsStr = String.join(",",
                    featuresDeleted.stream().map(Object::toString).collect(Collectors.toSet()));
                String msg = messageSource.getMessage(MessageKeyConstant.DELETED,
                    new String[]{idsStr}, LocaleContextHolder.getLocale());
                throw new BadRequestException(msg, Resources.FEATURE, ErrorKey.Feature.ID,
                    MessageKeyConstant.DELETED);
            }
            if (!Objects.equals(featureIds.size(), features.size())) {
                throw new BadRequestException(getMessageNotFound(), Resources.FEATURE,
                    ErrorKey.Feature.ID, MessageKeyConstant.NOT_FOUND);
            }
        }
    }

    /**
     * lưu gói dich vụ và dữ liệu bảng liên quan
     *
     * @param pricingDto the pricing dto
     * @return the map
     */
    private BaseResponseDTO savePricingEntity(PricingReqDTO pricingDto, IntegrationEnum integration, PortalType portalType, Boolean isCreateWithService, Boolean isApproved)  throws JsonProcessingException {
        //save seo
        ServiceCreateDTO serviceDTO = new ServiceCreateDTO();
        serviceDTO.setSeoReqDTO(pricingDto.getSeoReqDTO());

        ServiceEntity service = serviceRepository.findByIdAndDeletedFlag(pricingDto.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> {
                String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, serviceM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });
        String pricingName = StringUtil.replace(pricingDto.getPricingName());
        Seo seoNew = Objects.nonNull(pricingDto.getSeoReqDTO()) ?
            servicesService.saveSeoService(pricingDto.getServiceId(), service.getCategoriesId(), CAU_HINH_GOI_DICH_VU, serviceDTO) : // pricing
            seoService.saveSeoDefault(pricingName, //pricing
                pricingDto.getPricingName() + SeoTypeCodeConstant.TITLE_PAGE_PRICING, pricingDto.getDescription(), service.getCategoriesId(),
                pricingDto.getServiceId(), CAU_HINH_GOI_DICH_VU, true);

        PricingDraft pricing = pricingReqMapper.toEntity(pricingDto);
        pricing.setId(null);
        if (IntegrationEnum.INTEGRATION == integration) {
            pricing.setApprove(ApproveStatusEnum.AWAITING_APPROVAL.value);
            pricing.setStatus(StatusEnum.ACTIVE.value);
        } else {
            pricing.setApprove(ApproveStatusEnum.UNAPPROVED.value);
            pricing.setStatus(StatusEnum.INACTIVE.value);
        }
        pricing.setCreatedBy(AuthUtil.getCurrentUserId());
        if (!CollectionUtils.isEmpty(pricingDto.getFeatureList())) {
            saveFeatureList(pricingDto, pricing);
        }
        pricing.setPricingOrder(pricingDraftRepository.countByServiceId(pricingDto.getServiceId()) + 1);
        if (SubTypeEnum.PERIODIC.value.equals(pricingDto.getIsOneTime()) && !isCreateWithService) {
            pricing.setUpdateSubscriptionDate(pricingDto.getUpdateSubscriptionDate().value);
            pricing.setPaymentRequest(pricingDto.getPaymentRequest().value);
            pricing.setTypeActiveInPaymentType(pricingDto.getTypeActiveInPaymentType().value);
            pricing.setChangePricingDate(pricingDto.getChangePricingDate().value);
        }
        pricing.setPaymentCycle(null);
        pricing.setCycleType(null);
        pricing.setRecommendedStatus(pricingDto.getRecommendedStatus());
        pricing.setPriority(pricingDto.getPriority());
        pricing.setPortalType(portalType);
        pricing.setHasRenew(Objects.nonNull(pricingDto.getHasRenew()) ? pricingDto.getHasRenew().value : YesNoEnum.YES.value);
        pricing.setSeoId(seoNew.getId());
        pricing.setIsOneTime(pricingDto.getIsOneTime());
        if (Objects.nonNull(pricingDto.getPricingConfigDTO())) {
            pricing.setPricingConfig(pricingDto.getPricingConfigDTO());
        }
        if (Objects.nonNull(pricingDto.getPricingCommitmentTimeDTO())) {
            pricing.setPricingCommitmentTime(pricingDto.getPricingCommitmentTimeDTO());
        }
        if (Objects.nonNull(pricingDto.getPricingPromotionDTO())) {
            pricing.setPricingPromotion(pricingDto.getPricingPromotionDTO());
        }

        // Kiểm tra creationLayoutId tồn tại
        if (pricingDto.getCreationLayoutId() != null) {
            customLayoutRepository.findById(pricingDto.getCreationLayoutId())
                .orElseThrow(() -> throwPricingBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.CustomLayout.CREATION_LAYOUT_ID,
                    pricingDto.getCreationLayoutId().toString()));
            pricing.setCreationLayoutId(pricingDto.getCreationLayoutId());
        }

        VariantApplyDTO variantApply = pricingDto.getVariantApply();
        if (Objects.nonNull(variantApply.getVariantApply())) {
            pricing.setVariantApply(variantApply.getVariantApply());
        }
        pricing = pricingDraftRepository.save(pricing);

        // lưu các biến thể được gắn với gói dịch vụ
        if (Objects.nonNull(variantApply.getVariantApply())) {
            if (!variantApply.getVariantCode().isEmpty() && variantApply.getVariantApply().equals(VariantApplyEnum.SELECTED.getValue())) {
                List<PricingVariant> pricingVariantDrafts = new ArrayList<>();
                    List<Variant> variantInfor = variantRepository.findAllByVariantCodeIn(variantApply.getVariantCode());
                    for (Variant variant : variantInfor) {
                        PricingVariant pricingVariantDraft = new PricingVariant();
                        pricingVariantDraft.setPricingDraftId(pricing.getId());
                        pricingVariantDraft.setVariantId(variant.getId());
                        pricingVariantDraft.setVariantDraftId(variant.getVariantDraftId());
                        pricingVariantDrafts.add(pricingVariantDraft);
                    }
                pricingVariantRepository.saveAll(pricingVariantDrafts);
            }
            // TODO: Áp dụng cho trường hợp chọn tất cả
        }

        // pricing mặc định được approved khi đồng bộ
        if (IntegrationEnum.INTEGRATION == integration || isApproved) {
            PricingApproveReqDTO pricingApproveReqDTO = new PricingApproveReqDTO(ApproveStatusEnum.APPROVED, null);
            approvePricing(pricing.getId(), pricingApproveReqDTO, false);
        }

        // Luư thông tin setup fee tax
        List<PricingSetupFeeTax> setupFeeTaxes = new ArrayList<>();
        PricingDraft finalPricing = pricing;
        pricingDto.getSetupFees().forEach(e -> setupFeeTaxes.add(
                new PricingSetupFeeTax(
                        Objects.nonNull(e.getTax()) ? e.getTax().getTaxId() : null,
                        finalPricing.getId(),
                        pricingDto.getHasTaxSetupFee().value,
                        Objects.nonNull(e.getTax()) ? e.getTax().getPercent() : null,
                        e.getPrice(),
                        e.getName()
                )
        ));
        pricingSetupFeeTaxRepository.saveAll(setupFeeTaxes);

        // Lưu thông tin chiến lược định giá
        savePricingPlan(pricingDto, pricing);

        // lưu thuế của gói chính
        if (!CollectionUtils.isEmpty(pricingDto.getTaxList())) {
            Set<PricingTax> pricingTaxs = new LinkedHashSet<>();
            for (RowTax tax : pricingDto.getTaxList()) {
                PricingTax pricingTax = new PricingTax(null, tax.getTaxId(), tax.getPercent(),
                    pricingDto.getHasTax().value, null, pricing.getId());
                pricingTaxs.add(pricingTax);
            }
            pricingTaxRepository.saveAll(pricingTaxs);
        }

        // Lưu addon của gói chính
        if (!CollectionUtils.isEmpty(pricingDto.getAddonList())) {
            Set<PricingAddon> pricingAddons = new LinkedHashSet<>();
            Set<RowAddon> addons = pricingDto.getAddonList();
            for (RowAddon addon : addons) {
                PricingAddon pricingAddon = new PricingAddon(null, null, addon.getId(),
                    addon.getIsRequired().value, pricing.getId());
                pricingAddons.add(pricingAddon);
            }
            pricingAddonRepository.saveAll(pricingAddons);
        }

        // Lưu thông tin customer field của pricing
        updateDraftCustomFieldValue(pricingDto, EntityTypeEnum.PRICING.getValue(), pricing.getId());

        // Lưu thông tin ảnh gói cước
        createDraftPricingImage(pricingDto.getPricingImageId(), pricing.getId());

        // check cấu hình duyệt
        if (Objects.isNull(pricingDto.getType()) || !Objects.equals(pricingDto.getType(), "APP")) {
            checkConfigApprove(pricing.getId(), pricingDto, Boolean.TRUE, new ArrayList<>(), new PricingDetailResDTO());
        }

        return new BaseResponseDTO(pricing.getId());
    }

    /**
     * lưu thông tin chiến lược định giá
     */
    private void savePricingPlan(PricingReqDTO pricingDto, PricingDraft pricingDraft) {
        List<PricingMultiPlanAddon> allPricingMultiPlanAddons = new ArrayList<>();
        List<PricingPlanDetail> allPricingPlanDetails = new ArrayList<>();
        Boolean checkPricingConfigList = pricingRepository.checkPricingConfigList(pricingDraft.getServiceId());
        int index = 1;
        for (PricingStrategy pricingPlan : pricingDto.getPricingStrategies()) {
            PricingMultiPlan pricingMultiPlan = new PricingMultiPlan();
            PricingMultiPlan oldPricingMultiPlan = pricingMultiPlanRepository.getPricingMultiPlanByIdAndPricingDraftIdAndDeletedFlag(
                pricingPlan.getId(), pricingDraft.getId(), pricingDraft.getDeletedFlag());
            if (oldPricingMultiPlan != null) {
                pricingMultiPlan.setDescription(oldPricingMultiPlan.getDescription());
                pricingMultiPlan.setMigrationId(oldPricingMultiPlan.getMigrationId());
            }
            pricingMultiPlan.setPricingDraftId(pricingDraft.getId());
            pricingMultiPlan.setPlanName(pricingPlan.getPlanName());
            pricingMultiPlan.setPricingPlan(pricingPlan.getPricingPlan().value);
            if (SubTypeEnum.ONCE.value.equals(pricingDto.getIsOneTime())) {
                pricingMultiPlan.setPaymentCycle(-1L);
            } else {
                pricingMultiPlan.setPaymentCycle(pricingPlan.getPaymentCycle());
            }
            pricingMultiPlan.setCircleType(pricingPlan.getCycleType().value);
            pricingMultiPlan.setUnitId(pricingPlan.getUnitId());
            pricingMultiPlan.setCurrencyId(pricingDraft.getCurrencyId());
            pricingMultiPlan.setPrice(pricingPlan.getPrice());
            pricingMultiPlan.setTrialType(pricingPlan.getTrialType() == null ? null : Long.valueOf(pricingPlan.getTrialType().value));
            pricingMultiPlan.setNumberOfTrial(pricingPlan.getNumberOfTrial());
            pricingMultiPlan.setCycleCode(pricingPlan.getCycleCode());
            pricingMultiPlan.setFreeQuantity(
                Objects.isNull(pricingPlan.getFreeQuantity()) ? DEFAULT_QUANTITY : Math.toIntExact(pricingPlan.getFreeQuantity()));
            pricingMultiPlan.setCreatedAt(Timestamp.from(Instant.now()));
            pricingMultiPlan.setCreatedBy(pricingDraft.getCreatedBy());
            pricingMultiPlan.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
            pricingMultiPlan.setNumberOfCycle((Objects.isNull(pricingPlan.getNumberOfCycles()) || pricingPlan.getNumberOfCycles() == 0) ? -1
                : pricingPlan.getNumberOfCycles());
            pricingMultiPlan.setReferenceId(pricingPlan.getId());
            if (Objects.equals(SubTypeEnum.ONCE.value, pricingDto.getIsOneTime())) {
                pricingMultiPlan.setDefaultCircle(YesNoEnum.YES.value);
            } else if (Objects.nonNull(pricingPlan.getDefaultCircle())){
                pricingMultiPlan.setDefaultCircle(pricingPlan.getDefaultCircle().value);
            } else {
                pricingMultiPlan.setDefaultCircle(YesNoEnum.NO.value);
            }

            //nếu là gói 1 lần thì để hiển thị luôn
            if (SubTypeEnum.ONCE.value.equals(pricingDto.getIsOneTime())) {
                pricingMultiPlan.setDisplayStatus(1);
            } else {
                pricingMultiPlan.setDisplayStatus(pricingPlan.getDisplayStatus());
            }

            if (checkPricingConfigList) {
                pricingMultiPlan.setPriority(index);
                index++;
            }
            // Nếu customerType trong pricingPlan null -> áp dụng theo customer_type của pricing (giao diện mới không có chọn customer_type cho plan)
            pricingMultiPlan.setCustomerTypeCode(CollectionUtils.isEmpty(pricingPlan.getCustomerTypeCode())
                ? pricingDto.getCustomerTypeCode().stream().filter(Objects::nonNull).map(CustomerTypeEnum::getValue).collect(Collectors.toSet())
                : pricingPlan.getCustomerTypeCode().stream().filter(Objects::nonNull).map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
            pricingMultiPlan.setMinimumQuantity(Objects.nonNull(pricingPlan.getMinimumQuantity()) ? pricingPlan.getMinimumQuantity() : null);
            pricingMultiPlan.setMaximumQuantity(Objects.nonNull(pricingPlan.getMaximumQuantity()) ? pricingPlan.getMaximumQuantity() : null);
            PricingMultiPlan finalPricingMultiPlan = pricingMultiPlanRepository.save(pricingMultiPlan);

            // Lưu thông tin originalId (id của multiplan version 1)
            updateOriginalId(finalPricingMultiPlan);

            // lưu dữ liệu addon của từng chiến lược định giá
            if (!CollectionUtils.isEmpty(pricingPlan.getAddonList())) {
                allPricingMultiPlanAddons.addAll(pricingPlan.getAddonList().stream()
                    .map(addon -> new PricingMultiPlanAddon(finalPricingMultiPlan.getId(), addon.getId(), addon.getIsRequired().value,
                        addon.getPricingMultiPlanId()))
                    .collect(Collectors.toList()));
            }

            // lưu thông tin chi tiết của từng kế hoạch định giá
            if (!CollectionUtils.isEmpty(pricingPlan.getUnitLimitedList())) {
                allPricingPlanDetails.addAll(pricingPlan.getUnitLimitedList().stream().map(
                    e -> new PricingPlanDetail(finalPricingMultiPlan.getId(), e.getPrice(),
                        Objects.isNull(e.getUnitFrom()) ? null : Math.toIntExact(e.getUnitFrom()),
                        Objects.isNull(e.getUnitTo()) ? null : Math.toIntExact(e.getUnitTo()))).collect(
                    Collectors.toList()));
            }
        }
        pricingMultiPlanAddonRepository.saveAll(allPricingMultiPlanAddons);
        pricingPlanDetailRepository.saveAll(allPricingPlanDetails);
    }

    /**
     * Lưu thông tin ID version 1 của multiplan
     */
    private void updateOriginalId(PricingMultiPlan multiPlan) {
        if (multiPlan.getReferenceId() == null) {
            // Trường hợp tạo mới multiplan
            multiPlan.setOriginalId(multiPlan.getId());
        } else {
            // Trường hợp nâng version multiplan
            pricingMultiPlanRepository.findById(multiPlan.getReferenceId())
                .ifPresent(referencePMP -> multiPlan.setOriginalId(referencePMP.getOriginalId()));
        }
    }

    @Transactional
    @Override
    public BaseResponseDTO synchronizedCreatePricing(PricingIntegrationReqDTO pricingIntegrationDto, Long serviceId)
        throws JsonProcessingException {
        // dich vu
        validateService(pricingIntegrationDto);
        // thong tin chung
//        validateName(pricingIntegrationDto.getServiceId(), pricingIntegrationDto.getPricingName()); //Nghĩa fix 5/8: bỏ check trùng tên SPDV
//        validateCode(pricingIntegrationDto);
        // loại tiền tệ
        validateCurrency(pricingIntegrationDto);
        // chiến lược định giá
        validatePricingPlan(pricingIntegrationDto, serviceId);
        // thông tin thuế
        validateTax(pricingIntegrationDto);
        // dịch vụ bổ sung
        validateAddon(pricingIntegrationDto);
        // danh sach tinh nang
        validateFeatures(pricingIntegrationDto);
        PricingDraft pricing = pricingReqMapper.toEntity(pricingIntegrationDto);
        addPricingHistory(pricing, ApproveStatusEnum.APPROVED, pricing.getPortalType());
        return savePricingEntity(pricingIntegrationDto, IntegrationEnum.INTEGRATION, pricing.getPortalType(), false, false);
    }

    private void checkConfigApprove(Long id, PricingReqDTO pricingDto, Boolean isCreate, List<Integer> infoChange,
        PricingDetailResDTO pricingDetail) throws JsonProcessingException {
        // set params thay đổi - infoChange
        if (!isCreate && Objects.nonNull(pricingDetail)) { // TH UPDATE
            // LOẠI GÓI DỊCH VỤ
            if (!(pricingDetail.getCustomerTypeCode().size() == pricingDto.getCustomerTypeCode().size() &&
                pricingDetail.getCustomerTypeCode().containsAll(pricingDto.getCustomerTypeCode()))) {
                infoChange.add(ApprovedChangePricingEnum.CUSTOMER_TYPE.getValue());
            }
            // TÊN GÓI
            if (!Objects.equals(pricingDetail.getPricingName(), pricingDto.getPricingName())) {
                infoChange.add(ApprovedChangePricingEnum.PRICING_NAME.getValue());
            }
            // MÃ GÓI
            if (!Objects.equals(pricingDetail.getPricingCode(), pricingDto.getPricingCode())) {
                infoChange.add(ApprovedChangePricingEnum.PRICING_CODE.getValue());
            }
            // PARAMS TRONG CHIẾN LƯỢC ĐỊNH GIÁ
            if (Objects.equals(pricingDetail.getIsOneTime(), pricingDto.getIsOneTime())) {
                AtomicBoolean ADDON = new AtomicBoolean(false);
                AtomicBoolean MULTI_PLAN = new AtomicBoolean(false);
                AtomicBoolean CYCLE_CODE = new AtomicBoolean(false);
                AtomicBoolean PRICE = new AtomicBoolean(false);
                AtomicBoolean PAYMENT_CYCLE = new AtomicBoolean(false);
                AtomicBoolean NUMBER_OF_CYCLE = new AtomicBoolean(false);
                AtomicBoolean PARAM_OTHER = new AtomicBoolean(false);
                if (!Objects.equals((int) pricingDto.getPricingStrategies().stream().filter(e -> Objects.nonNull(e.getId())).count(), 0)) {
                    Map<Long, PricingStrategy> mapPricingStrategyDto = pricingDto.getPricingStrategies().stream()
                        .filter(e -> Objects.nonNull(e.getId()))
                        .collect(Collectors.toMap(PricingStrategy::getId, Function.identity()));
                    pricingDetail.getPricingStrategies().forEach((e -> {
                        PricingStrategy pricingStrategyDto = mapPricingStrategyDto.get(e.getId());
                        if (Objects.nonNull(pricingStrategyDto)) {
                            // KẾ HOẠCH ĐỊNH GIÁ
                            if (!Objects.equals(e.getPricingPlan(), pricingStrategyDto.getPricingPlan())) {
                                MULTI_PLAN.set(true);
                            }
                            // MÃ CHU KỲ
                            if (!Objects.equals(e.getCycleCode(), pricingStrategyDto.getCycleCode())) {
                                CYCLE_CODE.set(true);
                            }
                            // ĐƠN GIÁ
                            if (Objects.nonNull(e.getPrice()) && Objects.nonNull(pricingStrategyDto.getPrice()) &&
                                !Objects.equals(e.getPrice().intValue(), pricingStrategyDto.getPrice().intValue())) {
                                PRICE.set(true);
                            }
                            // ĐƠN GIÁ - lũy kế
                            if (!CollectionUtils.isEmpty(e.getUnitLimitedList()) || !CollectionUtils.isEmpty(
                                pricingStrategyDto.getUnitLimitedList())) {
                                Set<String> unitLimitedList = !CollectionUtils.isEmpty(e.getUnitLimitedList()) ?
                                    e.getUnitLimitedList().stream()
                                        .map(el -> ((Objects.nonNull(el.getUnitTo()) && !Objects.equals(el.getUnitTo(), -1L)) ? el.getUnitTo()
                                            .toString() : "-") +
                                            "-" +
                                            ((Objects.nonNull(el.getUnitFrom()) && !Objects.equals(el.getUnitFrom(), -1L)) ? el.getUnitFrom()
                                                .toString() : "-") +
                                            "-" +
                                            el.getPrice().toString())
                                        .collect(Collectors.toSet()) :
                                    new HashSet<>();
                                Set<String> unitLimitedListDto = !CollectionUtils.isEmpty(pricingStrategyDto.getUnitLimitedList()) ?
                                    pricingStrategyDto.getUnitLimitedList().stream()
                                        .map(el -> ((Objects.nonNull(el.getUnitTo()) && !Objects.equals(el.getUnitTo(), -1L)) ? el.getUnitTo()
                                            .toString() : "-") +
                                            "-" +
                                            ((Objects.nonNull(el.getUnitFrom()) && !Objects.equals(el.getUnitFrom(), -1L)) ? el.getUnitFrom()
                                                .toString() : "-") +
                                            "-" +
                                            el.getPrice().toString())
                                        .collect(Collectors.toSet()) :
                                    new HashSet<>();
                                if (!(unitLimitedList.size() == unitLimitedListDto.size() && unitLimitedList.containsAll(unitLimitedListDto))) {
                                    PRICE.set(true);
                                }
                            }
                            // CHU KỲ THANH TOÁN
                            if (!Objects.equals(e.getPaymentCycle(), pricingStrategyDto.getPaymentCycle()) ||
                                !Objects.equals(e.getCycleType().value, pricingStrategyDto.getCycleType().value)) {
                                PAYMENT_CYCLE.set(true);
                            }
                            // SỐ CHU KỲ
                            if (!Objects.equals(e.getNumberOfCycles(), pricingStrategyDto.getNumberOfCycles())) {
                                NUMBER_OF_CYCLE.set(true);
                            }
                            // DỊCH VỤ BỔ SUNG
                            Set<Long> addonIdList = !CollectionUtils.isEmpty(e.getAddonList()) ?
                                e.getAddonList().stream().map(AddOn::getId).collect(Collectors.toSet()) :
                                new HashSet<>();
                            Set<Long> addonIdListDto = !CollectionUtils.isEmpty(pricingStrategyDto.getAddonList()) ?
                                pricingStrategyDto.getAddonList().stream().map(RowAddon::getId).collect(Collectors.toSet()) :
                                new HashSet<>();
                            if (!(addonIdList.size() == addonIdListDto.size() && addonIdList.containsAll(addonIdListDto))) {
                                ADDON.set(true);
                            }
                            // PARAM_OTHER
                            if ((Objects.equals(pricingDto.getIsOneTime(), 1) && // ĐỐI TƯỢNG KHÁCH HÀNG
                                !(e.getCustomerTypeCode().size() == pricingStrategyDto.getCustomerTypeCode().size() &&
                                    e.getCustomerTypeCode().containsAll(pricingStrategyDto.getCustomerTypeCode()))) ||
                                // THỜI GIAN DÙNG THỬ
                                !Objects.equals(e.getNumberOfTrial(), pricingStrategyDto.getNumberOfTrial()) ||
                                !Objects.equals(e.getTrialType(), pricingStrategyDto.getTrialType()) ||
                                // SỐ LƯỢNG TỐI THIỂU
                                !Objects.equals(e.getMinimumQuantity(), pricingStrategyDto.getMinimumQuantity()) ||
                                // SỐ LƯỢNG TỐI ĐA
                                !Objects.equals(e.getMaximumQuantity(), pricingStrategyDto.getMaximumQuantity()) ||
                                // SỐ LƯỢNG MIỄN PHÍ
                                !Objects.equals(Objects.nonNull(e.getFreeQuantity()) ? e.getFreeQuantity() : DEFAULT_QUANTITY,
                                    Objects.nonNull(pricingStrategyDto.getFreeQuantity()) ? pricingStrategyDto.getFreeQuantity()
                                        : DEFAULT_QUANTITY) ||
                                // ĐƠN VỊ TÍNH
                                !Objects.equals(e.getUnitId(), pricingStrategyDto.getUnitId())
                            ) {
                                PARAM_OTHER.set(true);
                            }
                        }
                    }));
                    //
                    if (MULTI_PLAN.get()) {
                        infoChange.add(ApprovedChangePricingEnum.MULTI_PLAN.getValue());
                    }
                    if (CYCLE_CODE.get()) {
                        infoChange.add(ApprovedChangePricingEnum.CYCLE_CODE.getValue());
                    }
                    if (PRICE.get()) {
                        infoChange.add(ApprovedChangePricingEnum.PRICE.getValue());
                    }
                    if (PAYMENT_CYCLE.get()) {
                        infoChange.add(ApprovedChangePricingEnum.PAYMENT_CYCLE.getValue());
                    }
                    if (NUMBER_OF_CYCLE.get()) {
                        infoChange.add(ApprovedChangePricingEnum.NUMBER_OF_CYCLE.getValue());
                    }
                }
                // DỊCH VỤ BỔ SUNG CHUNG
                Set<PricingAddonRes> pricingAddons = pricingAddonRepository.findByPricingDraftIds(id, BonusTypeEnum.ONCE.value);
                Set<Long> addonIdListCommon = !CollectionUtils.isEmpty(pricingAddons) ?
                    pricingAddons.stream().map(PricingAddonRes::getId).collect(Collectors.toSet()) :
                    new HashSet<>();
                Set<Long> addonIdListCommonDto = !CollectionUtils.isEmpty(pricingDto.getAddonList()) ?
                    pricingDto.getAddonList().stream().map(RowAddon::getId).collect(Collectors.toSet()) :
                    new HashSet<>();
                if (!(addonIdListCommon.size() == addonIdListCommonDto.size() && addonIdListCommon.containsAll(addonIdListCommonDto))) {
                    ADDON.set(true);
                }
                if (ADDON.get()) {
                    infoChange.add(ApprovedChangePricingEnum.ADDON.getValue());
                }
                // CHIẾN LƯỢC ĐỊNH GIÁ
                if (!Objects.equals((int) pricingDto.getPricingStrategies().stream().filter(e -> Objects.isNull(e.getId())).count(), 0) ||
                    // THÊM CHIẾN LƯỢC ĐỊNH GIÁ
                    !Objects.equals((int) pricingDto.getPricingStrategies().stream().filter(e -> Objects.nonNull(e.getId())).count(),
                        // XÓA CHIẾN LƯỢC ĐỊNH GIÁ
                        (int) pricingDetail.getPricingStrategies().stream().filter(e -> Objects.nonNull(e.getId())).count())
                ) {
                    infoChange.add(ApprovedChangePricingEnum.PRICING_STRATEGY.getValue());
                }
                if (MULTI_PLAN.get() || CYCLE_CODE.get() || PRICE.get() || PAYMENT_CYCLE.get() || NUMBER_OF_CYCLE.get() ||
                    // SỬA CHIẾN LƯỢC ĐỊNH GIÁ
                    ADDON.get() || PARAM_OTHER.get()) {
                    infoChange.add(ApprovedChangePricingEnum.PRICING_STRATEGY.getValue());
                }
            }
            ObjectMapper mapper = new ObjectMapper();
            String oldTax = mapper.writeValueAsString(pricingDetail.getTaxList());
            String newTax = mapper.writeValueAsString(pricingDto.getTaxList());
            // THÔNG TIN THUẾ
            if (!Objects.equals(oldTax, newTax)) {
                infoChange.add(ApprovedChangePricingEnum.INFORMATION_PRICING.getValue());
            }
            String oldFee = mapper.writeValueAsString(pricingDetail.getSetupFees());
            String newFee = mapper.writeValueAsString(pricingDto.getSetupFees());

            // THÔNG TIN PHÍ THIẾT LẬP
            if (!Objects.equals(oldFee, newFee)) {
                infoChange.add(ApprovedChangePricingEnum.INFORMATION_SETUP_FREE.getValue());
            }

            // THÔNG TIN THIẾT LẬP
            if (Objects.equals(pricingDetail.getIsOneTime(), pricingDto.getIsOneTime()) && (
                (
                    (Objects.equals(pricingDetail.getIsOneTime(), 0) &&
                        (!Objects.equals(pricingDetail.getHasRefund(), pricingDto.getHasRefund()) ||
                            !Objects.equals(pricingDetail.getHasChangeQuantity(), pricingDto.getHasChangeQuantity())
                        )
                    )
                ) || (
                    Objects.equals(pricingDetail.getIsOneTime(), 1) &&
                        (!Objects.equals(pricingDetail.getPricingType(), pricingDto.getPricingType()) ||
                            !Objects.equals(pricingDetail.getHasChangePrice(), pricingDto.getHasChangePrice()) ||
                            !Objects.equals(pricingDetail.getHasChangeQuantity(), pricingDto.getHasChangeQuantity()) ||
                            !Objects.equals(pricingDetail.getHasRefund(), pricingDto.getHasRefund()) ||
                            !Objects.equals(pricingDetail.getHasRenew(), pricingDto.getHasRenew()) ||
                            !Objects.equals(pricingDetail.getCancelDate(), pricingDto.getCancelDate()) ||
                            !Objects.equals(pricingDetail.getActiveDate(), pricingDto.getActiveDate()) ||
                            !Objects.equals(pricingDetail.getUpdateSubscriptionDate(), pricingDto.getUpdateSubscriptionDate()) ||
                            !Objects.equals(pricingDetail.getChangePricingDate(), pricingDto.getChangePricingDate()) ||
                            !Objects.equals(pricingDetail.getChangePricingPaymentTime(), pricingDto.getChangePricingPaymentTime()) ||
                            !Objects.equals(pricingDetail.getTypeActiveInPaymentType(), pricingDto.getTypeActiveInPaymentType()) ||
                            !Objects.equals(pricingDetail.getPaymentRequest(), pricingDto.getPaymentRequest())
                        )
                ))
            ) { // TH CREATE
                infoChange.add(ApprovedChangePricingEnum.INFORMATION_SETUP.getValue());
            }
        } else {
            infoChange.add(ApprovedChangePricingEnum.NONE.getValue());
        }
        // check system config
        ApprovedRule approvedRule = approvedRuleRepository.findFirstByType(ApprovedTypeEnum.PRICING.getValue());
        if (Objects.isNull(approvedRule) || Objects.isNull(approvedRule.getConditionQuery())) {
            PricingApproveReqDTO pricingApproveReqDTO = new PricingApproveReqDTO();
            pricingApproveReqDTO.setStatus(ApproveStatusEnum.APPROVED);
            approvePricing(id, pricingApproveReqDTO, false);
            return;
        }
        String infoChangeString = "{" + infoChange.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";
        String queryCheck = String.format("select %s", approvedRule.getConditionQuery());
        List<Long> lstUserId = new ArrayList<>();
        lstUserId.add(-1L);
        lstUserId.add(AuthUtil.getCurrentUserId());
        String infoUser = "{" + lstUserId.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";
        Query query = entityManager.createNativeQuery(queryCheck);
        if (approvedRule.getConditionQuery().contains(":userId")) { // NHÀ CUNG CẤP
            query.setParameter("userId", infoUser);
        }
        if (approvedRule.getConditionQuery().contains(":pricingType")) { // LOẠI GÓI DỊCH VỤ: 1 LẦN - THUÊ BAO
            query.setParameter("pricingType", pricingDto.getIsOneTime());
        }
        if (approvedRule.getConditionQuery().contains(":changeInformation")) { // THAY ĐỔI THzÔNG TIN - CASE CREATE MẶC ĐỊNH LÀ FALSE
            query.setParameter("changeInformation", infoChangeString);
        }
        if (approvedRule.getConditionQuery().contains(":isAll")) {
            query.setParameter("isAll", -1);
        }
        if (approvedRule.getConditionQuery().contains(":isUpdate")) {
            query.setParameter("isUpdate", !isCreate);
        }
        if (!(Boolean) query.getSingleResult()) { // KHÔNG DÍNH DÁNG ĐẾN ĐK => PHÊ DUYỆT
            PricingApproveReqDTO pricingApproveReqDTO = new PricingApproveReqDTO();
            pricingApproveReqDTO.setStatus(ApproveStatusEnum.APPROVED);
            approvePricing(id, pricingApproveReqDTO, false);
        }
    }

    @Override
    @Transactional
    public BaseResponseDTO updatePricing(Long id, PricingReqDTO pricingDto, Long serviceId, Boolean isApproved) throws JsonProcessingException {
        // Kiểm tra pricing tồn tại
        PricingDraft currentPricing = getCurrentPricing(id);

        // Không được phép cập nhật khi trạng thái WAITING_APPROVE
        validateApproveStatusUpdate(currentPricing.getApprove());

        // Kiểm tra tên gói
//        if (!Objects.equals(currentPricing.getPricingName(), pricingDto.getPricingName())) {
//            validateNamePricing(currentPricing.getServiceId(), pricingDto.getPricingName(), id);
//        }  //Nghĩa fix 8/5: bỏ check trùng tên gói SPDV

        // Kiểm tra mã gói đã tồn tại trong db
        if (!Objects.equals(currentPricing.getPricingCode(), pricingDto.getPricingCode())) {
            pricingDto.setServiceId(currentPricing.getServiceId());
            if (pricingDto.getPricingCode() != null && !pricingDto.getPricingCode().isEmpty()) {
                validateCode(pricingDto);
            }
        }

        if (Objects.equals(currentPricing.getApprove(), ApproveStatusEnum.APPROVED.value)) {

//            // Không được phép thay đổi mã gói
//            if (!Objects.equals(currentPricing.getPricingCode(), pricingDto.getPricingCode())) {
//                throw throwPricingBadRequest(MessageKeyConstant.CANNOT_CHANGE, ErrorKey.CODE, "Pricing code");
//            }

            // Bắt buộc nhập lý do cập nhật
            if (Objects.isNull(pricingDto.getUpdateReason()) && SubTypeEnum.PERIODIC.value.equals(pricingDto.getIsOneTime())) {
                throw throwPricingBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.UPDATE_REASON, "Reason");
            }
        }

        // Kiểm tra loại tiền tệ
        if (!Objects.equals(currentPricing.getCurrencyId(), pricingDto.getCurrencyId())) {
            validateCurrency(pricingDto);
        }

        validatePricingPlan(pricingDto, serviceId);
        validateTax(pricingDto);
        validateAddon(pricingDto);
        validateFeaturesUpdate(pricingDto, currentPricing);

        executeUpdatePricing(id, pricingDto, currentPricing.getServiceId(), currentPricing.getRecommendedStatus(),
            currentPricing.getPricingOrder(), currentPricing.getStatus(), currentPricing.getMigrationId(), isApproved);

        return new BaseResponseDTO(id);
    }

    private void updateServiceGroupItemAfterUpdatePricing(Long pricingDraftId) {
        log.info(" === START updateServiceGroupItemAfterUpdatePricing with pricingDraftId = {} ===", pricingDraftId);
        // check trạng thái phê duyệt
        // chỉ update nhóm khi pricing đã duyệt
        boolean isPricingApproved = pricingDraftRepository.existsByIdAndApprove(pricingDraftId, ApproveStatusEnum.APPROVED.value);
        List<ServiceGroupDraft> lstNewServiceDraft = new ArrayList<>();
        if (isPricingApproved) {
            List<ServiceGroupPricingItem> lstPricingItem = serviceGroupPricingItemRepository.findLstGroupDraftPricingItemByPricingDraftId(
                pricingDraftId);
            // cho pricingId và multiPlanId mới nhất vào các pricing_item bị ảnh hưởng
            List<ServiceGroupDraft> lstServiceDraft = saveLatestPricingIdToPricingItem(pricingDraftId, lstPricingItem);

            // chuyển approve của các service group draft thành Chưa duyệt
            // Nếu cấu hình hệ thống cho phép chuyển Đã duyệt luôn sau khi update pricing/service/group -> chuyển group thành Đã duyệt
            if (Objects.nonNull(lstServiceDraft)) {
                for (ServiceGroupDraft groupDraft : lstServiceDraft) {
                    if (Objects.equals(groupDraft.getCreatedPortal(), PortalType.ADMIN.getValue())) {
                        groupDraft.setApprove(ApproveStatusEnum.APPROVED.value);
                        serviceGroupService.saveServiceGroupApproved(groupDraft, groupDraft.getApproveBy());
                        // lưu lịch sử: admin phê duyệt thì chỉ sinh 1 bản ghi lịch sử có trạng thái "Đã duyệt"
                        ActionLogServiceGroup actionLog = actionLogServiceGroupService.getActionLogServiceGroup(PortalType.ADMIN.getType(),
                            groupDraft, ApproveStatusEnum.APPROVED, groupDraft.getCreatedBy());
                        actionLogServiceGroupRepository.save(actionLog);
                    } else {
                        groupDraft.setApprove(ApproveStatusEnum.UNAPPROVED.value);
                        lstNewServiceDraft.add(groupDraft);
                    }
                }
                serviceGroupDraftRepository.saveAll(lstNewServiceDraft);
            }
        }
        log.info(" === END updateServiceGroupItemAfterUpdatePricing with pricingDraftId = {}, update for groupDraft id = {} ===",
            pricingDraftId, lstNewServiceDraft.stream().map(ServiceGroupDraft::getId).collect(Collectors.toList()));
    }

    // cho pricingId và multiPlanId mới nhất vào các pricing_item bị ảnh hưởng
    // return lst id của các service draft bị ảnh hưởng
    private List<ServiceGroupDraft> saveLatestPricingIdToPricingItem(Long pricingDraftId, List<ServiceGroupPricingItem> lstPricingItem) {
        log.info(" === START saveLatestPricingIdToPricingItem with pricingDraftId = {} ===", pricingDraftId);
        List<ServiceGroupPricingItem> lstNewPricingItem = new ArrayList<>();
        List<IPricingIdAndMultiPlanIdDTO> latestPricingIdAndMultiPlanId = pricingDraftRepository.getLatestPricingIdAndMultiPlanIdByDraftId(
            pricingDraftId);
        Map<Long, IPricingIdAndMultiPlanIdDTO> mapPricingPlanByReferenceId = latestPricingIdAndMultiPlanId.stream().collect(
            Collectors.toMap(IPricingIdAndMultiPlanIdDTO::getReferenceId, Function.identity()));

        if (Objects.nonNull(lstPricingItem)) {
            for (ServiceGroupPricingItem pricingItem : lstPricingItem) {
                if(Objects.nonNull(pricingItem.getPricingMultiPlanId()) &&
                    mapPricingPlanByReferenceId.containsKey(pricingItem.getPricingMultiPlanId())) {
                    IPricingIdAndMultiPlanIdDTO multiPlanData = mapPricingPlanByReferenceId.get(pricingItem.getPricingMultiPlanId());
                    IPricingMultiPlanQuantityDTO quantityData = pricingMultiPlanRepository.getQuantityDataByPricingMultiPlanId(multiPlanData.getPricingMultiPlanId());
                    pricingItem.setObjectId(multiPlanData.getPricingId());
                    pricingItem.setPricingMultiPlanId(multiPlanData.getPricingMultiPlanId());
                    // check số lượng tối thiểu với số lượng (quantity trong bảng sgp_item)
                    if (Objects.nonNull(quantityData.getMinQuantity()) && quantityData.getMinQuantity() > pricingItem.getQuantity()) {
                        pricingItem.setQuantity(quantityData.getMinQuantity());
                    } else if (Objects.nonNull(quantityData.getMaxQuantity()) && quantityData.getMaxQuantity() < pricingItem.getQuantity()) {    // check số lượng tối đa với số lượng (quantity trong bảng sgp_item)
                        pricingItem.setQuantity(quantityData.getMaxQuantity());
                    }
                    lstNewPricingItem.add(pricingItem);
                }
            }
            serviceGroupPricingItemRepository.saveAll(lstNewPricingItem);
        }

        List<Long> lstItemId = lstNewPricingItem.stream().map(ServiceGroupPricingItem::getId).collect(Collectors.toList());
        List<ServiceGroupDraft> lstGroupDraftId = serviceGroupDraftRepository.getLstGroupDraftIdByPricingItemIdIn(lstItemId);

        log.info(
            " === END saveLatestPricingIdToPricingItem with pricingDraftId = {}, set referenceId = {} and multiPlanId = {} to list serviceGroupPricingItem id = {} ===",
            pricingDraftId, mapPricingPlanByReferenceId.keySet(), mapPricingPlanByReferenceId.values().stream().
                map(IPricingIdAndMultiPlanIdDTO::getPricingMultiPlanId).collect(Collectors.toList()), lstItemId);

        return lstGroupDraftId;
    }

    @Override
    @Transactional
    public BaseResponseDTO updatePlanOfPricing(Long planId, Integer displayStatus) {

        // Kiểm tra pricing tồn tại
        if (displayStatus != 1 && displayStatus != 0) {
            throw new BadRequestException("bad request", Resources.PRICING, ErrorKey.Pricing.PRICING_PLAN,
                MessageKeyConstant.PLAN_DISPLAY_STATUS_OUT_OF_ACCEPTED_VALUE);
        }
        Optional<PricingMultiPlan> prm = pricingMultiPlanRepository.findById(planId);
        if (!prm.isPresent()) {
            String messageNotFound = messageSource.getMessage(
                MessageKeyConstant.NOT_FOUND, pricingM,
                LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(messageNotFound, Resources.TAX,
                ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        } else {
            PricingMultiPlan result = prm.get();
            result.setDisplayStatus(displayStatus);
            pricingMultiPlanRepository.save(result);
        }

        return new BaseResponseDTO(planId);
    }

    /**
     * Thực hiện cập nhật gói
     *  @param id         the id
     * @param pricingDto the pricing dto
     */
    private void executeUpdatePricing(Long id, PricingReqDTO pricingDto, Long serviceId, Integer recommendedStatus, Integer pricingOrder,
        Integer status, Long migrationId, Boolean isApproved) throws JsonProcessingException {
        PricingDetailResDTO pricingDetail = getPricingDetail(id, pricingDto.getPortalType(), PricingDetailInputEnum.PROCESSING);
        PricingDraft pricingDraft = pricingReqMapper.toEntity(pricingDto);
        pricingDraft.setId(id);
        pricingDraft.setApprove(ApproveTypeEnum.UNAPPROVED.value);
        pricingDraft.setCreatedBy(Objects.requireNonNull(AuthUtil.getCurrentUser()).getId());
        pricingDraft.setServiceId(serviceId);
        pricingDraft.setPricingOrder(pricingOrder);
        pricingDraft.setRecommendedStatus(recommendedStatus);
        pricingDraft.setStatus(status);
        pricingDraft.setMigrationId(migrationId);
        pricingDraft.setRecommendedStatus(pricingDto.getRecommendedStatus());
        pricingDraft.setPriority(pricingDto.getPriority());
        if (Objects.nonNull(pricingDto.getPricingConfigDTO())) {
            pricingDraft.setPricingConfig(pricingDto.getPricingConfigDTO());
        }
        if (Objects.nonNull(pricingDto.getPricingCommitmentTimeDTO())) {
            pricingDraft.setPricingCommitmentTime(pricingDto.getPricingCommitmentTimeDTO());
        }
        if (Objects.nonNull(pricingDto.getPricingPromotionDTO())) {
            pricingDraft.setPricingPromotion(pricingDto.getPricingPromotionDTO());
        }
        if (SubTypeEnum.PERIODIC.value.equals(pricingDto.getIsOneTime())) {
            pricingDraft.setUpdateSubscriptionDate(pricingDto.getUpdateSubscriptionDate().value);
            pricingDraft.setChangePricingDate(pricingDto.getChangePricingDate().value);
        }
        //Validate creationLayout
        Optional<CustomLayout> customLayoutOpt = null;
        if (pricingDto.getCreationLayoutId() != null) {
            customLayoutOpt = customLayoutRepository.findById(
                pricingDto.getCreationLayoutId() != null ? pricingDto.getCreationLayoutId() : -1L);
            if (!customLayoutOpt.isPresent()) {
                throw throwPricingBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.CustomLayout.CREATION_LAYOUT_ID,
                    pricingDto.getCreationLayoutId() != null ? pricingDto.getCreationLayoutId().toString() : null);
            }
            pricingDraft.setCreationLayoutId(pricingDto.getCreationLayoutId());
        }
        // set dữ liệu cũ bằng null nếu có
        pricingDraft.setPaymentCycle(null);
        pricingDraft.setCycleType(null);
        pricingDraft.setNumberOfCycles(null);
        pricingDraft.setPricingPlan(null);
        pricingDraft.setUnitId(null);
        pricingDraft.setTrialType(null);
        pricingDraft.setNumberOfTrial(null);
        pricingDraft.setPrice(null);

        saveFeatureList(pricingDto, pricingDraft);

        unitLimitedRepository.deleteByPricingDraftIdAndPricingIdIsNull(pricingDraft.getId());

        // Cập nhật bậc thang
        pricingMultiPlanRepository.deleteAllCurrenVersionProcessingByPricingDraftId(id);
        pricingPlanDetailRepository.deleteAllCurrenVersionProcessingByPricingDraftId(id);
        pricingMultiPlanAddonRepository.deleteAllCurrentVersionProcessingByPricingDraftId(id);
        savePricingPlan(pricingDto, pricingDraft);

        // Cập nhật thông tin thuế
        pricingTaxRepository.deleteByPricingDraftIdAndPricingIdIsNull(id);
        if (!CollectionUtils.isEmpty(pricingDto.getTaxList())) {
            Set<PricingTax> pricingTaxs = new LinkedHashSet<>();
            for (RowTax tax : pricingDto.getTaxList()) {
                PricingTax pricingTax = new PricingTax(null, tax.getTaxId(), tax.getPercent(),
                    pricingDto.getHasTax().value, null, id);
                pricingTaxs.add(pricingTax);
            }
            pricingTaxRepository.saveAll(pricingTaxs);
        }

        // Luư thông tin setup fee tax
        pricingSetupFeeTaxRepository.deleteByPricingDraftIdAndPricingIdIsNull(id);
        if (!CollectionUtils.isEmpty(pricingDto.getSetupFees())) {
            List<PricingSetupFeeTax> setupFeeTaxes = new ArrayList<>();
            pricingDto.getSetupFees().forEach(e -> setupFeeTaxes.add(new PricingSetupFeeTax(e.getTax().getTaxId(), id, pricingDto.getHasTaxSetupFee().value, e.getTax().getPercent(), e.getPrice(), e.getName())));
            pricingSetupFeeTaxRepository.saveAll(setupFeeTaxes);
        }

        // Cập nhật thông tin dịch vụ bổ sung chung
        pricingAddonRepository.deleteByPricingDraftIdAndPricingIdIsNull(id);
        if (!CollectionUtils.isEmpty(pricingDto.getAddonList())) {
            Set<PricingAddon> pricingAddons = new LinkedHashSet<>();
            for (RowAddon addon : pricingDto.getAddonList()) {
                PricingAddon pricingAddon = new PricingAddon(null, null, addon.getId(),
                    addon.getIsRequired().value, id);
                pricingAddons.add(pricingAddon);
            }
            pricingAddonRepository.saveAll(pricingAddons);
        }
        List<PricingVariant> pricingVariants = new ArrayList<>();
        VariantApplyDTO variantApply = pricingDto.getVariantApply();
        if (variantApply.getVariantApply().equals(VariantApplyEnum.SELECTED.getValue())) {
            // xóa pricingVariant theo pricingDraftId và pricingId null;
            pricingVariantRepository.deleteByPricingDraftIdAndPricingIdIsNull(id);
            // lấy danh sách variant theo dsach variantCode
            List<Variant> variants = variantRepository.findAllByVariantCodeIn(variantApply.getVariantCode());
            variants.forEach(item -> {
                PricingVariant pricingVariantDraft = new PricingVariant();
                pricingVariantDraft.setPricingDraftId(pricingDraft.getId());
                pricingVariantDraft.setVariantId(item.getId());
                pricingVariantDraft.setVariantDraftId(item.getVariantDraftId());
                pricingVariants.add(pricingVariantDraft);
            });
            // tạo lại pricingVariant với pricingDraft
            pricingVariantRepository.saveAll(pricingVariants);
        }
        // TODO: Áp dụng cho trường hợp chọn tất cả
        pricingDraftRepository.save(pricingDraft);

        // pricing mặc định được approved khi đồng bộ
        if (isApproved) {
            PricingApproveReqDTO pricingApproveReqDTO = new PricingApproveReqDTO(ApproveStatusEnum.APPROVED, null);
            approvePricing(id, pricingApproveReqDTO, false);
        }
        // Lưu thông tin ảnh gói cước
        createDraftPricingImage(pricingDto.getPricingImageId(), pricingDraft.getId());
        List<Integer> infoChange = new ArrayList<>();
        if (Objects.isNull(pricingDto.getType()) || !Objects.equals(pricingDto.getType(), "APP")) {
            checkConfigApprove(id, pricingDto, Boolean.FALSE, infoChange, pricingDetail);
        }
        // Cập nhật custom field của gói cước
        updateDraftCustomFieldValue(pricingDto, EntityTypeEnum.PRICING.getValue(), pricingDraft.getId());
    }

    private void saveFeatureList(PricingReqDTO pricingDto, PricingDraft pricingDraft) {
        if (!CollectionUtils.isEmpty(pricingDto.getFeatureList())) {
            String listFeatureId = pricingDto.getFeatureList().stream()
                .map(Object::toString).collect(Collectors.joining(","));
            pricingDraft.setListFeatureId(listFeatureId);
        }
    }

    /**
     * Validate tính năng.
     *
     * @param pricingDto     the pricing dto
     * @param currentPricing the current pricing
     */
    private void validateFeaturesUpdate(PricingReqDTO pricingDto, PricingDraft currentPricing) {
        String[] split = StringUtils.split(StringUtils.defaultString(currentPricing.getListFeatureId(),
            CharacterConstant.BLANK), CharacterConstant.COMMA);
        String[] featureArr = StringUtils.stripAll(split);
        Set<Long> featureIds = Arrays.stream(featureArr)
            .map(Long::valueOf).collect(Collectors.toSet());

        if (!Objects.equals(featureIds, pricingDto.getFeatureList())) {
            validateFeatures(pricingDto);
        }
    }

    /**
     * Validate approve status update.
     *
     * @param approve the approve
     */
    @Override
    public void validateApproveStatusUpdate(Integer approve) {
        ApproveTypeEnum approveStatus = ApproveTypeEnum.valueOf(approve);
        if (Objects.equals(approveStatus, ApproveTypeEnum.AWAITING_APPROVAL)) {
            throw throwPricingBadRequest(MessageKeyConstant.PRICING_NEED_AWAITING_APPROVAL, ErrorKey.APPROVE_STATUS, null);
        }
    }

    /**
     * Throw pricing bad request.
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     * @param param              the param
     *
     * @return bad request exception
     */
    private BadRequestException throwPricingBadRequest(String messageKeyConstant, String errorKey, String param) {
        String msg = messageSource.getMessage(messageKeyConstant, new String[]{param}, LocaleContextHolder.getLocale());
        return new BadRequestException(msg, Resources.PRICING, errorKey, messageKeyConstant);
    }

    /*
    @Transactional
    @Override
    public BaseResponseDTO synchronizedUpdatePricing(
            PricingIntegrationUpdateReqDTO pricingIntegrationDto) {
        PricingDraft pricingDraft = pricingDraftRepository
                .findByIdAndDeletedFlag(pricingIntegrationDto.getId(), DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                    return new BadRequestException(getMessageNotFound(), Resources.PRICING,
                            ErrorKey.Pricing.ID, MessageKeyConstant.NOT_FOUND);
                });
        unitLimitedRepository.deleteByPricingDraftIdAndPricingIdIsNull(pricingDraft.getId());
        pricingTaxRepository.deleteByPricingDraftIdAndPricingIdIsNull(pricingDraft.getId());
        pricingAddonRepository.deleteByPricingDraftIdAndPricingIdIsNull(pricingDraft.getId());

        PricingReqDTO pricingValidate = new PricingReqDTO();
        BeanUtils.copyProperties(pricingIntegrationDto, pricingValidate);
        validatePricingPlan(pricingValidate, pricingValidate.getServiceId());
        validateTax(pricingValidate);
        // dịch vụ bổ sung
        validateAddon(pricingValidate);
        // danh sach tinh nang
        validateFeatures(pricingValidate);
        return updateSynchronizedPricing(pricingIntegrationDto, pricingDraft);
    }
    */

    private BaseResponseDTO updateSynchronizedPricing(PricingIntegrationUpdateReqDTO pricingIntegrationDto, PricingDraft pricingDraft) {
        pricingDraft.setDescription(pricingIntegrationDto.getDescription());
        pricingDraft.setPricingPlan(pricingIntegrationDto.getPricingPlan().value);
        pricingDraft.setPrice(pricingIntegrationDto.getPrice());
        pricingDraft.setUnitId(pricingIntegrationDto.getUnitId());
        pricingDraft.setCurrencyId(pricingIntegrationDto.getCurrencyId());
        pricingDraft.setFreeQuantity(pricingIntegrationDto.getFreeQuantity());
        pricingDraft.setNumberOfTrial(pricingIntegrationDto.getNumberOfTrial());
        pricingDraft.setTrialType(pricingIntegrationDto.getTrialType().value);
        pricingDraft.setModifiedBy(AuthUtil.getCurrentUserId());
        pricingDraft.setApprove(ApproveStatusEnum.AWAITING_APPROVAL.value);
        // luu feature list
        String listFeatureId = String.join(",", pricingIntegrationDto.getFeatureList().stream()
            .map(Object::toString).collect(Collectors.toSet()));
        pricingDraft.setListFeatureId(listFeatureId);

        // luu pricing draft
        pricingDraftRepository.save(pricingDraft);
        // luu unitLimitedList
        if (!CollectionUtils.isEmpty(pricingIntegrationDto.getUnitLimitedList())) {
            Set<UnitLimited> rowLimiteds = new LinkedHashSet<>();
            for (RowLimited limited : pricingIntegrationDto.getUnitLimitedList()) {
                UnitLimited unitLimited = new UnitLimited(null, null, limited.getUnitFrom(),
                    limited.getUnitTo(), limited.getPrice(), pricingDraft.getId());
                rowLimiteds.add(unitLimited);
            }
            unitLimitedRepository.saveAll(rowLimiteds);
        }
        // lưu pricing_tax
        if (!CollectionUtils.isEmpty(pricingIntegrationDto.getTaxList())) {
            Set<PricingTax> pricingTaxs = new LinkedHashSet<>();
            for (RowTax tax : pricingIntegrationDto.getTaxList()) {
                PricingTax pricingTax = new PricingTax(null, tax.getTaxId(), tax.getPercent(),
                    tax.getHasTax().value, null, pricingDraft.getId());
                pricingTaxs.add(pricingTax);
            }
            pricingTaxRepository.saveAll(pricingTaxs);
        }
        // lưu pricing_addons
        if (!CollectionUtils.isEmpty(pricingIntegrationDto.getAddonList())) {
            Set<PricingAddon> pricingAddons = new LinkedHashSet<>();
            for (RowAddon addon : pricingIntegrationDto.getAddonList()) {
                PricingAddon pricingAddon = new PricingAddon(null, null, addon.getId(),
                    addon.getIsRequired().value, pricingDraft.getId());
                pricingAddons.add(pricingAddon);
            }
            pricingAddonRepository.saveAll(pricingAddons);
        }
        PricingApproveReqDTO pricingApproveReqDTO = new PricingApproveReqDTO(ApproveStatusEnum.APPROVED, null);
        approvePricing(pricingDraft.getId(), pricingApproveReqDTO, false);
        return new BaseResponseDTO(pricingDraft.getId());
    }

    @Override
    public Page<PricingAddonResDTO> getAddonPricing(Long pricingId, Set<Long> addonIds, Integer paymentCycle, PeriodTypeEnum cycleType,
        YesNoEnum orderService, String serviceName, String addonName, Long categoryId, BonusTypeEnum type, PeriodFilterTypeEnum addonType,
        YesNoEnum hasBonusTypeOnce, PortalType portal, Pageable pageable) {
        Long pricingOwnerId = AuthUtil.getCurrentParentId();
        //Nếu là màn xem chi tiết của admin thì chỉ lấy ra các addon của thằng pricing
        if (Objects.equals(portal, PortalType.ADMIN)) {
            if (!Objects.equals(pricingId, PricingConst.DEFAULT_ID)) {
                Long ownerId = pricingRepository.findOwnerPricingDraft(pricingId);
                pricingOwnerId = Objects.nonNull(ownerId) ? ownerId : PricingConst.DEFAULT_ID;
            }
        }
        List<Integer> owners =
            YesNoEnum.UNSET.equals(orderService) ? Arrays.asList(3, 2, 0, 1)
                : YesNoEnum.YES.equals(orderService) ? Arrays.asList(2, 3) : Arrays.asList(0, 1);
        type = PeriodFilterTypeEnum.ALL.equals(addonType) ? BonusTypeEnum.ONCE : type;

        // khởi tạo sort khi sort theo bonusType
        Order orderSort = pageable.getSort().getOrderFor("bonusType");
        if (Objects.nonNull(orderSort)) {
            List<Order> orders = new ArrayList<>();
            Direction pageSortDirection = orderSort.getDirection();
            orders.add(new Order(pageSortDirection, POPUP_ADDON_SORT_FIELD_BONUS_TYPE));
            orders.add(new Order(pageSortDirection, POPUP_ADDON_SORT_FIELD_TYPE));
            orders.add(new Order(pageSortDirection, POPUP_ADDON_SORT_FIELD_PAYMENT_CYCLE));
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(orders));
        }

        return pricingRepository.getAddonPricing(pricingOwnerId, addonIds, paymentCycle, cycleType.value, owners,
            serviceName, addonName, type.value, categoryId, hasBonusTypeOnce.value, pageable);
    }

    @Override
    public Page<PricingAddonResDTO> getAddonCreatePricing(Long pricingId, Set<Long> addonIds, Integer paymentCycle, PeriodTypeEnum cycleType,
        YesNoEnum orderService, String serviceName, String addonName, Long categoryId, BonusTypeEnum type,
        PeriodFilterTypeEnum addonType, Integer isOneTime, YesNoEnum hasBonusTypeOnce, PortalType portal, Long providerId, DisplayStatus status,
        Pageable pageable) {
        Long pricingOwnerId = AuthUtil.getCurrentParentId();
        //Nếu là màn xem chi tiết của admin thì chỉ lấy ra các addon của thằng pricing
        if (Objects.equals(portal, PortalType.ADMIN)) {
            if (!Objects.equals(pricingId, PricingConst.DEFAULT_ID)) {
                Long ownerId = pricingRepository.findOwnerPricingDraft(pricingId);
                pricingOwnerId = Objects.nonNull(ownerId) ? ownerId : PricingConst.DEFAULT_ID;
            } else if (!Objects.equals(providerId, PricingConst.DEFAULT_ID)) {
                pricingOwnerId = providerId;
            }
        }
        List<Integer> owners =
            YesNoEnum.UNSET.equals(orderService) ? Arrays.asList(3, 2, 0, 1)
                : YesNoEnum.YES.equals(orderService) ? Arrays.asList(2, 3) : Arrays.asList(0, 1);
        type = PeriodFilterTypeEnum.ALL.equals(addonType) ? BonusTypeEnum.ONCE : type;

        // khởi tạo sort khi sort theo bonusType
        Order orderSort = pageable.getSort().getOrderFor("bonusType");
        if (Objects.nonNull(orderSort)) {
            List<Order> orders = new ArrayList<>();
            Direction pageSortDirection = orderSort.getDirection();
            orders.add(new Order(pageSortDirection, POPUP_ADDON_SORT_FIELD_BONUS_TYPE));
            orders.add(new Order(pageSortDirection, POPUP_ADDON_SORT_FIELD_TYPE));
            orders.add(new Order(pageSortDirection, POPUP_ADDON_SORT_FIELD_PAYMENT_CYCLE));
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(orders));
        }

        PortalType getPortal= AuthUtil.getPortalOfUserRoles();

        return pricingRepository.getAddonCreatePricing(pricingOwnerId, addonIds, paymentCycle, cycleType.value, owners, isOneTime,
            serviceName, addonName, type.value, categoryId, hasBonusTypeOnce.value, status.value, getPortal.getValue(),pageable);
    }

    @Override
    public List<ServiceComboReportResDTO> getServiceNameFilterAddon(String name, Long pricingId, YesNoEnum isOsService) {
        List<Integer> owners =
            YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(3, 2, 0, 1)
                : YesNoEnum.YES.equals(isOsService) ? Arrays.asList(2, 3) : Arrays.asList(0, 1);
        return pricingRepository.getServiceNameFilterAddon(name, pricingId, owners,
            Objects.nonNull(AuthUtil.getCurrentParentId()) ? AuthUtil.getCurrentParentId() : -1L);
    }

    @Override
    public List<AddonsResDTO> getAddonNameFilterAddon(Integer paymentCycle, PeriodTypeEnum cycleType, String name, Long serviceId,
        Long pricingId, YesNoEnum isOsService) {
        List<Integer> owners =
            YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(3, 2, 0, 1)
                : YesNoEnum.YES.equals(isOsService) ? Arrays.asList(2, 3) : Arrays.asList(0, 1);
        return pricingRepository
            .getAddonNameFilterAddon(paymentCycle, cycleType.value, name, serviceId, pricingId, owners,
                Objects.nonNull(AuthUtil.getCurrentParentId()) ? AuthUtil.getCurrentParentId() : -1L);
    }

    @Override
    public List<PlanPeriodResDTO> getPeriodFilterAddon(Integer paymentCycle, PeriodTypeEnum cycleType, Long serviceId,
        Long addonId, Long pricingId,
        PeriodFilterTypeEnum filterType, YesNoEnum isOsService, String name) {
        List<Integer> owners =
            YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(3, 2, 0, 1)
                : YesNoEnum.YES.equals(isOsService) ? Arrays.asList(2, 3) : Arrays.asList(0, 1);
        // khởi tạo khi popup addon là popup chung cho cả pricing hoặc tìm kiếm theo chu kỳ: một lần
        BonusTypeEnum bonusType = BonusTypeEnum.UNSET;
        if (PeriodFilterTypeEnum.ALL.equals(filterType)) {
            bonusType = BonusTypeEnum.ONCE;
        }
        return pricingRepository
            .getPeriodFilterAddon(paymentCycle, cycleType.value, serviceId, addonId, pricingId, owners, bonusType.value,
                Objects.nonNull(AuthUtil.getCurrentParentId()) ? AuthUtil.getCurrentParentId() : -1L, name);
    }

    @Override
    public Pricing getPricing(Long id) {
        return pricingRepository.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
            String message = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });
    }

    @Override
    public List<ServiceComboReportResDTO> getServiceNameFilter(Long serviceId, String name, YesNoEnum isOsService, PortalType portal,
        CustomerTypeEnum customerType, Integer registerEcontract) {
        List<Integer> owners =
            YesNoEnum.NO.equals(isOsService) || YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(0, 1) : Arrays.asList(3, 2, 0, 1);
        Long ownerId = Objects.equals(portal, PortalType.DEV) ? AuthUtil.getCurrentParentId() : SubscriptionConstant.ADMIN;
        return subscriptionRepository.getServiceNameFilter(serviceId, name, owners, ownerId, customerType.getValue(), registerEcontract);
    }

    @Override
    public List<PricingComboPlanReportResDTO> getPricingNameFilter(Long serviceId, String name, YesNoEnum isOsService, PortalType portal,
        CustomerTypeEnum customerType, Integer registerEcontract) {
        List<Integer> owners =
            YesNoEnum.NO.equals(isOsService) || YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(0, 1) : Arrays.asList(3, 2, 0, 1);
        Long ownerId = Objects.equals(portal, PortalType.DEV) ? AuthUtil.getCurrentParentId() : SubscriptionConstant.ADMIN;
        return subscriptionRepository.getPricingNameFilter(serviceId, name, owners, ownerId, customerType.getValue(), registerEcontract);
    }

    @Override
    public List<PlanPeriodResDTO> getPeriodFilter(Long serviceId, Long comboId, String name, YesNoEnum isOsService, PortalType portal,
        CustomerTypeEnum customerType, Integer registerEcontract) {
        List<Integer> owners =
            YesNoEnum.NO.equals(isOsService) || YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(0, 1) : Arrays.asList(3, 2, 0, 1);
        Long ownerId = Objects.equals(portal, PortalType.DEV) ? AuthUtil.getCurrentParentId() : SubscriptionConstant.ADMIN;
        return subscriptionRepository.getPeriodFilter(serviceId, comboId, name, owners, ownerId, customerType.getValue(), registerEcontract);
    }

    @Override
    public List<UserNameFilterResDTO> getUserNameFilter(Long serviceId, YesNoEnum isOsService, PortalType portal, String name) {
        List<Integer> owners =
            YesNoEnum.YES.equals(isOsService) || YesNoEnum.UNSET.equals(isOsService) ? Arrays.asList(3, 2, 0, 1) : Arrays.asList(0, 1);
        if (PortalType.DEV.equals(portal)) {
            return new ArrayList<>();
        }
        return subscriptionRepository.getUserNamesFilter(name);
    }

    /**
     * Convert đơn vị thời gian thành enum
     *
     */
    public int convertStringToTimeEnum(String time) {
        switch (time.toLowerCase()) {
            case PricingConst.TIME_TYPE_WEEKLY:
                return TimeTypeEnum.WEEKLY.value;
            case PricingConst.TIME_TYPE_MONTHLY:
                return TimeTypeEnum.MONTHLY.value;
            case PricingConst.TIME_TYPE_YEARLY:
                return TimeTypeEnum.YEARLY.value;
            case PricingConst.TIME_TYPE_DAILY:
                return TimeTypeEnum.DAILY.value;
            default:
                return TimeTypeEnum.UNSET.value;
        }
    }

    /**
     * Danh sách chu kì thanh toán
     */
    @Override
    public List<PaymentCycleDTO> getPaymentCycle() {
        return pricingRepository.getPaymentCycle();
    }
    /**
     * Danh sách chu kì thanh toán theo id dịch vụ
     */
    public List<IPaymentCycleDTO> getPaymentCycleByMigrationIds(List<Long> serviceMigrationIds) {
        return pricingRepository.getPaymentCycleByMigrationIds(serviceMigrationIds);
    }

    @Override
    public List<PricingNameDTO> getListPricingName(String name, Long serviceId) {
        return pricingRepository.getListPricingName(name, serviceId);
    }

    @Override
    public Boolean validatePeriodCodeAndPricingCode(Long serviceId, ValidateCodeType type, String code, ValidateCodeAction action) {
        Set<String> pricingCodes = new HashSet<>();

        if (type.equals(ValidateCodeType.PRICING_CODE)) {
            pricingCodes = pricingRepository.getListPricingCode(serviceId);
        } else if (type.equals(ValidateCodeType.PERIOD_CODE)) {
            pricingCodes = pricingRepository.getListCycleCode(serviceId);
        }
        if (!pricingCodes.contains(code)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public Boolean validatePeriodCodeAndPricingCodeWithoutService(ValidateCodeType type, String code, ValidateCodeAction action) {
        if (type.equals(ValidateCodeType.PRICING_CODE)) {
            if (pricingRepository.checkCodeExists(code) || pricingDraftRepository.checkCodeExists(code)) return Boolean.TRUE;
        } else if (type.equals(ValidateCodeType.PERIOD_CODE)) {
            if (pricingMultiPlanRepository.checkCycleCodeExists(code))  return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public List<AddonDraft> checkCanRemoveCustomerType(Long pricingId, String customerType) {
        val listAddonCustomerType = pricingMultiPlanAddonRepository.getAllAddonIdByPricingIdAndCustomerTypeCode(pricingId, customerType);

        if (listAddonCustomerType.size() > 0) {
            Set<Long> setIds = new HashSet<>();
            for (AddonRelationPricingDTO item : listAddonCustomerType) {
                setIds.add(item.getAddonDraftId());
            }
            return addonDraftRepository.findAllById(setIds);
        } else {
            return null;
        }
    }

    @Override
    @Transactional
    public void updateLevelSetting(List<PricingLevelReqDTO> pricings, Long serviceId, Integer type) {
        Map<Long, Integer> mapPricingDraftIdLevel = new HashMap<>();
        Map<Long, Integer> mapPricingStrategyIdLevel = new HashMap<>();
        if (!CollectionUtils.isEmpty(pricings)) {
            pricings.forEach(p -> {
                mapPricingDraftIdLevel.put(p.getId(), p.getPriority());
                if (!CollectionUtils.isEmpty(p.getPricingStrategies())) {
                    p.getPricingStrategies().forEach(ps -> mapPricingStrategyIdLevel.put(ps.getId(), ps.getPriority()));
                }
            });
        }
        if (type == 1) {
            List<Pricing> pricingList = pricingRepository.findAllByPricingDraftIdInAndDeletedFlag(mapPricingDraftIdLevel.keySet(),
                DeletedFlag.NOT_YET_DELETED.getValue());
            if (!CollectionUtils.isEmpty(pricingList)) {
                pricingList.forEach(e -> e.setPriority(mapPricingDraftIdLevel.get(e.getPricingDraftId())));
                pricingRepository.saveAll(pricingList);
            }

            Set<PricingMultiPlan> pricingMultiPlanList = pricingMultiPlanRepository.findAllByIdIn(mapPricingStrategyIdLevel.keySet());
            if (!CollectionUtils.isEmpty(pricingMultiPlanList)) {
                pricingMultiPlanList.forEach(pmp -> pmp.setPriority(mapPricingStrategyIdLevel.get(pmp.getId())));
                pricingMultiPlanRepository.saveAll(pricingMultiPlanList);
            }
        } else if (type == 0) {
            List<ComboPlan> lstCp = comboPlanRepository.getAllByComboDraftId(serviceId);
            if (!CollectionUtils.isEmpty(lstCp)) {
                lstCp.forEach(cp -> cp.setPriority(mapPricingDraftIdLevel.get(cp.getId())));
                comboPlanRepository.saveAll(lstCp);
            }
        }
    }

    @Override
    @Transactional
    public void updateSettingInfo(PricingSettingReqDTO pricingSettingReqDTO, Long serviceId) {
        PricingSetupInfo pricingSetupInfo = pricingSetupInfoRepository.findFirstByServiceIdAndType(serviceId, pricingSettingReqDTO.getType());
        if (pricingSetupInfo != null) {
            BeanUtils.copyProperties(pricingSettingReqDTO, pricingSetupInfo);
            pricingSetupInfoRepository.save(pricingSetupInfo);
        } else {
            PricingSetupInfo pricingSetupInfoNew = new PricingSetupInfo();
            BeanUtils.copyProperties(pricingSettingReqDTO, pricingSetupInfoNew);
            pricingSetupInfoNew.setServiceId(serviceId);
            pricingSetupInfoRepository.save(pricingSetupInfoNew);
        }

        // setConvertedIds
        if (pricingSettingReqDTO.getType() == 1 && pricingSettingReqDTO.getTypeConfig() == 1 && !CollectionUtils.isEmpty(
            pricingSettingReqDTO.getPlans())) {
            Map<Long, Set> mapPricingMultiPlanIdList = new HashMap<>();
            for (Plan plan : pricingSettingReqDTO.getPlans()) {
                Set<Long> exchangedIds = new HashSet<>();
                if (!CollectionUtils.isEmpty(plan.getExchangedIds())) {
                    plan.getExchangedIds().forEach(pmpId -> {
                        if (!pmpId.equals(plan.getId())) {
                            exchangedIds.add(pmpId);
                        }
                    });
                }
                mapPricingMultiPlanIdList.put(plan.getId(), exchangedIds);
            }

            Set<PricingMultiPlan> pricingMultiPlans = pricingMultiPlanRepository.findAllByIdIn(mapPricingMultiPlanIdList.keySet());
            if (!CollectionUtils.isEmpty(pricingMultiPlans) && !CollectionUtils.isEmpty(mapPricingMultiPlanIdList)) {
                pricingMultiPlans.forEach(pmp -> pmp.setConvertedIds(mapPricingMultiPlanIdList.get(pmp.getId())));
                pricingMultiPlanRepository.saveAll(pricingMultiPlans);
            }
        } else if (pricingSettingReqDTO.getType() == 0 && pricingSettingReqDTO.getTypeConfig() == 1 && !CollectionUtils.isEmpty(
            pricingSettingReqDTO.getPlans())) {
            Map<Long, Set> mapPlanIdSet = new HashMap<>();
            for (Plan plan : pricingSettingReqDTO.getPlans()) {
                Set<Long> exchangedIds = new HashSet<>();
                if (!CollectionUtils.isEmpty(plan.getExchangedIds())) {
                    plan.getExchangedIds().forEach(pmpId -> {
                        if (!pmpId.equals(plan.getId())) {
                            exchangedIds.add(pmpId);
                        }
                    });
                }
                mapPlanIdSet.put(plan.getId(), exchangedIds);
            }

            List<ComboPlan> lstCp = comboPlanRepository.getAllByComboDraftId(serviceId);
            if (!CollectionUtils.isEmpty(lstCp)) {
                lstCp.forEach(cp -> cp.setConvertedIds(mapPlanIdSet.get(cp.getId())));
                comboPlanRepository.saveAll(lstCp);
            }
        }
    }

    @Override
    public PricingSettingReqDTO getSettingInfo(Long serviceId, Integer type) {
        PricingSettingReqDTO res = new PricingSettingReqDTO();
        PricingSetupInfo pricingSetupInfo = pricingSetupInfoRepository.getPricingSetupInfo(serviceId, type);
        if (pricingSetupInfo != null) {
            BeanUtils.copyProperties(pricingSetupInfo, res);
        } else {
            res.setAllowDiffPlans(1);
            res.setAllowDiffPlansInOther(1);
            res.setAllowPackUpgrade(1);
            res.setAllowPackDowngrade(0);
            res.setTypeConfig(0);
        }

        List<Plan> plans = new ArrayList<>();
        if (type == 1 && res.getTypeConfig() == 1) {
            List<PricingMultiPlan> pricingMultiPlans = pricingMultiPlanRepository.getPricingMultiPlanIdsByServiceIdAndApprove(serviceId);
            if (!CollectionUtils.isEmpty(pricingMultiPlans)) {
                pricingMultiPlans.forEach(pmp -> {
                    Set<Long> exchangedIds = pmp.getConvertedIds().stream().filter(Objects::nonNull).map(Long::parseLong)
                        .collect(Collectors.toSet());
                    Plan plan = new Plan();
                    plan.setId(pmp.getId());
                    plan.setExchangedIds(exchangedIds);
                    plans.add(plan);
                });
            }
        } else if (type == 0 && res.getTypeConfig() == 1) {
            List<ComboPlan> lstCp = comboPlanRepository.getAllByComboDraftId(serviceId);
            if (!CollectionUtils.isEmpty(lstCp)) {
                lstCp.forEach(cp -> {
                    Set<Long> exchangedIds = cp.getConvertedIds().stream().filter(Objects::nonNull).map(Long::parseLong)
                        .collect(Collectors.toSet());
                    Plan plan = new Plan();
                    plan.setId(cp.getId());
                    plan.setExchangedIds(exchangedIds);
                    plans.add(plan);
                });
            }
        }
        res.setPlans(plans);
        return res;
    }

    public Set<Long> getPlanSelected(PricingSetupInfo pricingSetupInfo, List<ComboPlan> lstCp, ComboPlan comboPlanSub) {
        Boolean checkAllByComboDraftId = comboPlanRepository.checkAllByComboDraftId(comboPlanSub.getComboDraftId());
        Set<Long> exchangedIds = new HashSet<>();
        if (pricingSetupInfo.getTypeConfig() == 1) {
            exchangedIds = comboPlanSub.getConvertedIds().stream().filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toSet());
        } else {
            for (ComboPlan cp : lstCp) {
                if (!cp.getId().equals(comboPlanSub.getId()) &&
                    (// 1: nâng cấp + hạ cấp
                        (pricingSetupInfo.getAllowPackUpgrade() == 1 && pricingSetupInfo.getAllowPackDowngrade() == 1) ||
                            // 2: nâng cấp + không hạ cấp
                            (pricingSetupInfo.getAllowPackUpgrade() == 1 && pricingSetupInfo.getAllowPackDowngrade() == 0 &&
                                (checkAllByComboDraftId ? cp.getPriority() > comboPlanSub.getPriority()
                                    : cp.getCreatedAt().compareTo(comboPlanSub.getCreatedAt()) < 0)) ||
                            // 3: không nâng cấp +  hạ cấp
                            (pricingSetupInfo.getAllowPackUpgrade() == 0 && pricingSetupInfo.getAllowPackDowngrade() == 1 &&
                                (checkAllByComboDraftId ? cp.getPriority() < comboPlanSub.getPriority()
                                    : cp.getCreatedAt().compareTo(comboPlanSub.getCreatedAt()) > 0))
                    )) {
                    exchangedIds.add(cp.getId());
                }
            }
        }
        return exchangedIds;
    }

    @Override
    public Long getNumberOfCycleBySubId(Long subId) {
        return pricingRepository.getNumberOfCycleBySubId(subId);
    }

    @Override
    public PricingSettingReqDTO getFastConfig(Long serviceId, PricingSettingReqDTO pricingSettingReqDTO) {
        PricingSetupInfo pricingSetupInfo = new PricingSetupInfo();
        BeanUtils.copyProperties(pricingSettingReqDTO, pricingSetupInfo);
        pricingSetupInfo.setTypeConfig(0);

        PricingSettingReqDTO res = new PricingSettingReqDTO();
        List<Plan> plans = new ArrayList<>();
        if (pricingSettingReqDTO.getType() == 1) {
            List<PricingMultiPlan> pricingMultiPlans = pricingMultiPlanRepository.getPricingMultiPlanIdsByServiceIdAndApprove(serviceId);
            Map<Long, Long> mapPricingMultiPlanIdPricingDraftId = new HashMap<>();
            if (!CollectionUtils.isEmpty(pricingMultiPlans)) {
                pricingMultiPlans.forEach(pmp -> mapPricingMultiPlanIdPricingDraftId.put(pmp.getId(), pmp.getPricingDraftId()));
                Boolean checkPricingConfigList = pricingRepository.checkPricingConfigList(serviceId);
                pricingMultiPlans.forEach(pricingMultiPlanSub -> {
                    Plan plan = new Plan();
                    plan.setId(pricingMultiPlanSub.getId());

                    // lọc pricing
                    List<Long> pricingIdsSelected = getPricingIdsSelected(pricingSetupInfo, pricingMultiPlanSub, serviceId,
                        checkPricingConfigList);

                    // lọc PricingMultiPlan
                    List<Long> pricingMultiPlanIdsSelected = getPricingMultiPlanIdsSelected(pricingSetupInfo, pricingMultiPlanSub,
                        pricingIdsSelected, checkPricingConfigList);

                    plan.setExchangedIds(new HashSet<>(pricingMultiPlanIdsSelected));
                    plans.add(plan);
                });
            }
        } else if (pricingSettingReqDTO.getType() == 0) {
            List<ComboPlan> lstCp = comboPlanRepository.getAllByComboDraftId(serviceId);
            if (!CollectionUtils.isEmpty(lstCp)) {
                lstCp.forEach(cp -> {
                    Plan plan = new Plan();
                    plan.setId(cp.getId());
                    Set<Long> exchangedIds = getPlanSelected(pricingSetupInfo, lstCp, cp);
                    plan.setExchangedIds(exchangedIds);
                    plans.add(plan);
                });
            }
        }
        res.setPlans(plans);
        return res;
    }

    @Override
    public PricingTransactionDTO getPricingTransaction(Long id) {
        return pricingRepository.getPricingTransactionById(id, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
            String message = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(message, Resources.PRICING, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });
    }

    /**
     * Lấy danh sách PricingIdsSelected
     *
     * @return the page
     */
    @Override
    public List<Long> getPricingIdsSelected(PricingSetupInfo pricingSetupInfo, PricingMultiPlan pricingMultiPlanSub, Long serviceId,
        Boolean checkPricingConfigList) {
        List<Pricing> pricingList = pricingRepository.getPricingConfigList(serviceId);
        Pricing pricingSub = pricingRepository.findByIdAndDeletedFlag(pricingMultiPlanSub.getPricingId(),
            DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        List<Long> pricingIdsSelected = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pricingList) && pricingSub != null) {
            List<Long> pricingIds = new ArrayList<>();
            pricingList.forEach(e -> pricingIds.add(e.getId()));
            if (!pricingIds.contains(pricingMultiPlanSub.getPricingId())) {
                pricingIdsSelected.addAll(pricingIds);
            } else {
                pricingList.forEach(pr -> {
                    if (pricingSub.getId().equals(pr.getId()) && pricingSetupInfo.getAllowDiffPlans() == 1) {
                        pricingIdsSelected.add(pr.getId());
                    } else if (// 1: thủ công => lấy tất cả gói
                        pricingSetupInfo.getTypeConfig() == 1 ||
                            // 2: mặc định + cho phép nâng cấp gói
                            (pricingSetupInfo.getTypeConfig() == 0 && pricingSetupInfo.getAllowPackUpgrade() == 1
                                && (checkPricingConfigList ? pr.getPriority() > pricingSub.getPriority()
                                : pr.getCreatedAt().compareTo(pricingSub.getCreatedAt()) < 0)) ||
                            // 3: mặc định + cho phép hạ cấp gói
                            (pricingSetupInfo.getTypeConfig() == 0 && pricingSetupInfo.getAllowPackDowngrade() == 1 && (
                                checkPricingConfigList ? pr.getPriority() < pricingSub.getPriority()
                                    : pr.getCreatedAt().compareTo(pricingSub.getCreatedAt()) > 0))
                    ) {
                        pricingIdsSelected.add(pr.getId());
                    }
                });
            }
        }
        return pricingIdsSelected;
    }

    /**
     * Lấy danh sách getPricingMultiPlanIdsSelected
     *
     * @return the page
     */
    @Override
    public List<Long> getPricingMultiPlanIdsSelected(PricingSetupInfo pricingSetupInfo, PricingMultiPlan pricingMultiPlanSub,
        List<Long> pricingIdsSelected, Boolean checkPricingConfigList) {
        List<PricingMultiPlan> muls = pricingMultiPlanRepository.findAllByPricingIdIn(pricingIdsSelected);
        List<Long> pricingMultiPlanIdsSelected = new ArrayList<>();
        boolean isPricingMultiPlanSubPrice = Objects.nonNull(pricingMultiPlanSub.getPrice());
        List<PricingPlanDetail> pricingPlanDetails = CollectionUtils.isEmpty(pricingMultiPlanSub.getPricingPlanDetails()) ?
            new ArrayList<>() :
            pricingMultiPlanSub.getPricingPlanDetails().stream().sorted(Comparator.comparing(PricingPlanDetail::getUnitFrom).reversed())
                .map(o -> {
                    PricingPlanDetail pricingPlanDetail = new PricingPlanDetail();
                    BeanUtils.copyProperties(o, pricingPlanDetail);
                    pricingPlanDetail.setPricingMultiPlanId(null);
                    pricingPlanDetail.setId(null);
                    pricingPlanDetail.setPricingMultiPlan(null);
                    return pricingPlanDetail;
                }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(muls)) {
            muls.forEach(mul -> {
                boolean isMulPrice = Objects.nonNull(mul.getPrice());
                mul.getPricingPlanDetails().stream().forEach(m -> m.setUnitFrom(Objects.isNull(m.getUnitFrom()) ? -1 : m.getUnitFrom()));
                List<PricingPlanDetail> mulPricingPlanDetails = CollectionUtils.isEmpty(mul.getPricingPlanDetails()) ?
                    new ArrayList<>() :
                    mul.getPricingPlanDetails().stream().sorted(Comparator.comparing(PricingPlanDetail::getUnitFrom).reversed()).map(o -> {
                        PricingPlanDetail pricingPlanDetail = new PricingPlanDetail();
                        BeanUtils.copyProperties(o, pricingPlanDetail);
                        pricingPlanDetail.setPricingMultiPlanId(null);
                        pricingPlanDetail.setId(null);
                        pricingPlanDetail.setPricingMultiPlan(null);
                        return pricingPlanDetail;
                    }).collect(Collectors.toList());
                // TH khi đổi gói => gói đăng ký trên sme là chu kỳ cũ => 1. khác gói => add tất cả mul, 2.cùng gói => add mul khác pricingPlan hoặc price
                if (!pricingIdsSelected.contains(pricingMultiPlanSub.getPricingId()) &&
                    // check sub ko phải gói dang bán
                    // khác pricing
                    (!mul.getPricingDraftId().equals(pricingMultiPlanSub.getPricingDraftId()) ||
//                               // cùng pricing
                        (mul.getPricingDraftId().equals(pricingMultiPlanSub.getPricingDraftId()) &&
                            // cùng pricing + khác pricingPlan
                            (!mul.getPricingPlan().equals(pricingMultiPlanSub.getPricingPlan())
                                // cùng pricing + cùng pricingPlan
                                || (mul.getPricingPlan().equals(pricingMultiPlanSub.getPricingPlan()) &&
                                // cùng pricing + cùng pricingPlan + khác price (case này chủ yếu check null price)
                                ((isMulPrice && isPricingMultiPlanSubPrice && mul.getPrice().compareTo(pricingMultiPlanSub.getPrice()) != 0)
                                    || (!isMulPrice && isPricingMultiPlanSubPrice)
                                    || (isMulPrice && !isPricingMultiPlanSubPrice)
                                    // cùng pricing + cùng pricingPlan + khác getPricingPlanDetails (ko ss price vì pricingPlan khác không có price)
                                    || (!isMulPrice && !isPricingMultiPlanSubPrice &&
                                    !Objects.equals(StringUtils.join(mulPricingPlanDetails, "|"), StringUtils.join(pricingPlanDetails, "|")))
                                )))))) {
                    pricingMultiPlanIdsSelected.add(mul.getId());
                }
                // TH khi đổi gói => gói đăng ký trên sme không phải là chu kỳ cũ
                if (pricingIdsSelected.contains(pricingMultiPlanSub.getPricingId()) && !mul.getId().equals(pricingMultiPlanSub.getId()) && (
                    // 1: mặc định + khác gói cước
                    (pricingSetupInfo.getTypeConfig() == 0 && !mul.getPricingId().equals(pricingMultiPlanSub.getPricingId()) && (
                        // 1.1: + cho phép Khác chu kỳ
                        pricingSetupInfo.getAllowDiffPlansInOther() == 1 ||
                            // 1.2: + không cho phép Khác chu kỳ
                            (pricingSetupInfo.getAllowDiffPlansInOther() == 0 &&
                                Objects.equals(mul.getPaymentCycle(), pricingMultiPlanSub.getPaymentCycle())
                                && Objects.equals(mul.getCircleType(), pricingMultiPlanSub.getCircleType())))) ||
                        // 2: mặc định + trong cùng một gói cước + cho phép cung chu kỳ
                        (pricingSetupInfo.getTypeConfig() == 0 && pricingSetupInfo.getAllowDiffPlans() == 1 &&
                            mul.getPricingId().equals(pricingMultiPlanSub.getPricingId()) && (
                            // 2.1: + nâng cấp + hạ cấp
                            (pricingSetupInfo.getAllowPackUpgrade() == 1 && pricingSetupInfo.getAllowPackDowngrade() == 1) ||
                                // 2.2: + nâng cấp + không hạ cấp
                                (pricingSetupInfo.getAllowPackUpgrade() == 1 && pricingSetupInfo.getAllowPackDowngrade() == 0
                                    && (checkPricingConfigList ? mul.getPriority() > pricingMultiPlanSub.getPriority()
                                    : mul.getCreatedAt().compareTo(pricingMultiPlanSub.getCreatedAt()) < 0)) ||
                                // 2.3: + không tăng cấp + hạ cấp
                                (pricingSetupInfo.getAllowPackUpgrade() == 0 && pricingSetupInfo.getAllowPackDowngrade() == 1
                                    && (checkPricingConfigList ? mul.getPriority() < pricingMultiPlanSub.getPriority()
                                    : mul.getCreatedAt().compareTo(pricingMultiPlanSub.getCreatedAt()) > 0)))) ||
                        // 3: thủ công => lấy những pmp được chọn
                        (pricingSetupInfo.getTypeConfig() == 1 && !CollectionUtils.isEmpty(pricingMultiPlanSub.getConvertedIds()) &&
                            pricingMultiPlanSub.getConvertedIds().stream().filter(Objects::nonNull).map(Long::parseLong)
                                .collect(Collectors.toSet()).contains(mul.getId()))
                )) {
                    pricingMultiPlanIdsSelected.add(mul.getId());
                }
            });
        }
        return pricingMultiPlanIdsSelected;
    }

    @Override
    public SubscriptionServiceBasicDTO getSubscriptionServiceBasic(Long serviceId, Long pricingId, Long pricingMultiPlanId) {

        servicesService.findByIdAndDeletedFlag(serviceId, DeletedFlag.NOT_YET_DELETED.getValue());

        Pricing pricing = pricingRepository.findByIdAndDeletedFlagAndServiceId(pricingId, DeletedFlag.NOT_YET_DELETED.getValue(), serviceId)
            .orElseThrow(() -> {
                String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, null,
                        LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.PRICING,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });

        // nếu dữ liệu cũ thì pricing phải có cycleType và paymentCycle
        if (pricingMultiPlanId.equals(-1L) && (Objects.isNull(pricing.getCycleType()) || Objects.isNull(pricing.getPaymentCycle()))) {
            String messageNotFound = messageSource.getMessage(
                MessageKeyConstant.INVALID_DATA, null,
                LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(messageNotFound,
                Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        }
        Integer paymentCycle = pricing.getPaymentCycle();
        Integer cycleType = pricing.getCycleType();

        if (!pricingMultiPlanId.equals(-1L)) {
            PricingMultiPlan currentPricingMultiPlan = pricingMultiPlanRepository
                .findByIdAndPricingIdAndDeletedFlag(pricingMultiPlanId, pricingId, DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> {
                    String messageNotFound = messageSource.getMessage(
                        MessageKeyConstant.NOT_FOUND, pricingM,
                        LocaleContextHolder.getLocale());
                    return new ResourceNotFoundException(messageNotFound,
                        Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                        MessageKeyConstant.NOT_FOUND);
                });
            paymentCycle =
                Objects.nonNull(currentPricingMultiPlan.getPaymentCycle()) ? Math.toIntExact(currentPricingMultiPlan.getPaymentCycle())
                    : null;
            cycleType = Objects.nonNull(currentPricingMultiPlan.getCircleType()) ? Math.toIntExact(currentPricingMultiPlan.getCircleType())
                : null;
        }

        val requirePaymentDate = PricingTypeEnum.PREPAY.value.equals(pricing.getPricingType()) ?
            new Date() : DateUtil.toDate(DateUtil.calculateCycleDate(new Date(), paymentCycle,
            CycleTypeEnum.valueOf(cycleType), false, 1));
        SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String strDate = formatter.format(requirePaymentDate);

        return pricingRepository.getServiceBasic(serviceId, pricingId, strDate, pricingMultiPlanId);
    }

    /**
     * Lấy danh sách giới hạn theo mã gói
     *
     * @param pricingId the pricing id
     * @return the unit limited by pricing id
     */
    @Override
    public List<PricingDetailResDTO.UnitLimited> getUnitLimitedByPricingId(Long pricingId) {
        List<PricingDetailResDTO.UnitLimited> outUnitLimited = new ArrayList<>();
        unitLimitedRepository.findByPricingIdOrderByUnitFromAsc(pricingId).ifPresent(unitLimiters -> unitLimiters.forEach(unitLimited -> {
            PricingDetailResDTO.UnitLimited limited = new PricingDetailResDTO.UnitLimited();
            limited.setPrice(unitLimited.getPrice());
            limited.setUnitFrom(unitLimited.getUnitFrom());
            limited.setUnitTo(unitLimited.getUnitTo() == -1 ? null : unitLimited.getUnitTo());
            outUnitLimited.add(limited);
        }));
        return outUnitLimited;
    }

    /**
     * Lấy danh sách gói dịch vụ theo dịch vụ
     *
     * @param serviceId the service id
     * @param customerTypeEnum Loại khách hàng sử dụng để lọc danh sách gói dịch vụ trả về (null nếu không cần lọc theo loại khách hàng)
     */
    @Override
    public List<PricingSaaSResDTO> findAllPricingByService(Long serviceId, Long pricingIdSub, CustomerTypeEnum customerTypeEnum, Long variantId) {
        Set<Long> maxVerIds = subscriptionRepository.findMaxVerIdsByServiceId(serviceId, variantId);
        List<PricingSaaSResDTO> response = subscriptionRepository.findAllPricingByService(maxVerIds);
        List<PricingSaaSResDTO> responseCustomerType = new ArrayList<>();
        for (PricingSaaSResDTO pr : response) {
            if (Objects.nonNull(pricingIdSub) && pr.getId().equals(pricingIdSub)) {
                continue;
            }
            if (customerTypeEnum == null || customerTypeEnum == CustomerTypeEnum.UNSET ||
                pr.getCustomerType() == null || pr.getCustomerType().contains(customerTypeEnum)) {
                responseCustomerType.add(pr);
            }
        }

        ServiceEntity service = servicesService.findByIdAndDeletedFlag(serviceId, 1);
        Long defaultPricingDraftId = getPricingDefault(service);
        YesNoEnum yesNoEnum = YesNoEnum.YES;
        if (Objects.nonNull(AuthUtil.getCurrentUser())) {
            boolean hasSub = subscriptionRepository.existsSubIsActiveByUserIdAndServiceId(AuthUtil.getCurrentUserId(), serviceId);
            if ((Objects.nonNull(service.getServiceTypeApplication()) || service.getAllowMultiSub() == 0) && hasSub) {
                yesNoEnum = YesNoEnum.NO;
            }
        }
        YesNoEnum finalYesNoEnum = yesNoEnum;
        List<PricingSaaSResDTO> listErr = new ArrayList<>();
        // Nâng cấp thiết bị
        Set<Long> setPricingId = response.stream().map(PricingSaaSResDTO::getId).collect(Collectors.toSet());
        Set<FileAttach> setFileAttach = fileAttachRepository.findByObjectTypeAndObjectIdIn(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(),
            setPricingId);
        Long smeId = ObjectUtil.getOrDefault(AuthUtil.getCurrentParentId(), -1L);
        val result = responseCustomerType.stream().peek(p -> {
            PricingApplyDTO pricingApplyDTOS = couponRepository.findByPricingLatestId(p.getId(), smeId, service.getUserId(),
                    Objects.nonNull(service.getProvinceId()) ? service.getProvinceId() : -1, -1L, customerTypeEnum != null ? customerTypeEnum.getValue() : "");
            p.setLatestCoupon(couponService.convertCouponPricing(pricingApplyDTOS));

            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(p.getId());
            // Số tiền trước thuế
            BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(p.getPriceValue(), taxes);
            p.setPrice(pricingPreTax);
            List<PricingMultiplePeriodResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodForPricing(p.getId(), customerTypeEnum, service);
            List<PricingMultiplePeriodResDTO> multiplePeriodResponse = new ArrayList<>();
            for (PricingMultiplePeriodResDTO mul : multiplePeriods) {
                if (customerTypeEnum == null || customerTypeEnum == CustomerTypeEnum.UNSET ||
                    mul.getCustomerTypeCode() == null || mul.getCustomerTypeCode().contains(customerTypeEnum)) {
                    multiplePeriodResponse.add(mul);
                }
            }
            p.setPricingMultiplePeriods(multiplePeriodResponse);
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                p.setCouponList(couponService.getCouponListByPricingIdOrPricingMultiPlanId(p.getId(), null, customerTypeEnum));
                p.setUnitLimitedList(getUnitLimitedByPricingId(p.getId()));
            }
            p.setFeatureList(getListFeatureByFeatureIds(p.getListFeatureId()));
            p.setSeoDTO(seoService.getSeoDetailSme(p.getPricingDraftId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU));
            p.setAllowSubscript(finalYesNoEnum);
            ServiceReaction reaction = serviceReactionService.getByServiceIdAndType(p.getPricingDraftId(), 3);
            if (reaction != null) {
                p.setReaction(true);
            }
            // Nếu có period nhưng không thuộc kiểu người dùng thì add vào listErr để remove
            if (!CollectionUtils.isEmpty(multiplePeriods) && CollectionUtils.isEmpty(multiplePeriodResponse)) {
                listErr.add(p);
            }

            //set creationLayoutId, Customfiled
            Optional<PricingDraft> pd = pricingDraftRepository.findById(p.getPricingDraftId());
            pd.ifPresent(pricingDraft -> {
                Long layoutId = pricingDraft.getCreationLayoutId();
                layoutId = layoutId != null ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.PRICING.getValue());
                List<com.onedx.common.dto.customFields.CustomFieldValueDTO> lstFieldValueDTO = customFieldManager.getListFieldDraftValue(layoutId,
                    EntityTypeEnum.PRICING.getValue(), pricingDraft.getId());
                p.setCreationLayoutId(layoutId);
                p.setLstCustomFields(lstFieldValueDTO);
            });

            if (Objects.nonNull(defaultPricingDraftId)) {
                p.setIsDefault(Objects.equals(defaultPricingDraftId, p.getPricingDraftId()));
            }
            // Gán ảnh gói cước
            setFileAttach.stream().filter(item -> Objects.equals(item.getObjectId(), p.getId())).findFirst().ifPresent(item -> {
                FileAttachDTO pricingImage = new FileAttachDTO();
                BeanUtils.copyProperties(item, pricingImage);
                p.setPricingImage(pricingImage);
            });

        }).collect(Collectors.toList());
        result.removeAll(listErr);
        return result;
    }

    /**
     * Lấy danh sách gói dịch vụ rút gọn theo dịch vụ
     *
     * @param serviceId the service id
     * @return the page
     */
    @Override
    public List<PricingSearchResponseDTO> findAllPricingShortByService(Long serviceId, String customerType) {
        List<PricingSearchDTO> listPricing = subscriptionRepository.findAllPricingShortByService(serviceId, customerType);
        List<PricingSearchDTO> listPricingDistinct = new ArrayList<>();
        Set<Long> ids = new HashSet<>();
        for (PricingSearchDTO pr : listPricing) {
            if (!ids.contains(pr.getId())) {
                ids.add(pr.getId());
                listPricingDistinct.add(pr);
            }
        }

        List<PricingSearchResponseDTO> response = new ArrayList<>();
        listPricingDistinct.forEach(pr -> {
            PricingSearchResponseDTO pricingDTO = new PricingSearchResponseDTO();
            BeanUtils.copyProperties(pr, pricingDTO);
            ServiceReaction reaction = serviceReactionService.getByServiceIdAndType(pr.getPricingDraftId(), 3);
            if (reaction != null) {
                pricingDTO.setReaction(true);
            }
            // set tính năng
            pricingDTO.setFeatureList(getListFeatureByFeatureIds(pr.getListFeatureId()));
            pricingDTO.setCustomerType(new SetConverter().convertToEntityAttribute(pr.getCustomerType())
                .stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet()));
            response.add(pricingDTO);
        });
        return response;
    }

    @Override
    public Page<IGetTopViewPricingDTO> getTopView(CustomerTypeEnum customerType, Set<ServiceProductTypeEnum> serviceProductTypes,
        Set<Long> categoryMigrationIds, Set<Long> serviceMigrationIds, Set<Long> categories, Pageable pageable) {
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        Long currentUserId = ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L);
        return pricingRepository.getTopView(customerType.getValue(), setServiceProductType, categoryMigrationIds, serviceMigrationIds,
            categories, currentUserId, pageable);
    }

    @Override
    public Page<IGetTopViewPricingDTO> getTopSelling(CustomerTypeEnum customerType, Set<ServiceProductTypeEnum> serviceProductTypes,
        Set<Long> categoryMigrationIds, Set<Long> serviceMigrationIds, Set<Long> categories, Pageable pageable) {
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        Long currentUserId = ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L);
        return pricingRepository.getTopSelling(customerType.getValue(), setServiceProductType, categories, categoryMigrationIds,
            serviceMigrationIds, currentUserId, pageable);
    }

    public Page<IGetTopViewPricingDTO> getFromServiceByMigrationIds(String pricingName, Integer paymentCycle, Integer circleType,
        Set<Long> serviceMigrationIds, Pageable pageable) {
        Long currentUserId = ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L);
        return pricingRepository.getListByServiceMigrationIds(SqlUtils.optimizeSearchLike(pricingName), paymentCycle, circleType,
            serviceMigrationIds, currentUserId, pageable);
    }

    public List<IDetailPricingMultiPlanDTO> getMultiPlanByPricingId(Long pricingId){
        Pricing pricing = pricingRepository.findByIdAndDeletedFlag(pricingId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(
            () -> exceptionFactory.resourceNotFound(MessageKeyConstant.NOT_FOUND, ErrorKey.ID, String.valueOf(pricingId)));
        // Lưu lịch sử xem gói
        interactiveHistoryService.view(InteractiveObjectTypeEnum.PRICING, pricing.getPricingDraftId(), pricingId, null, null);
        return pricingRepository.getListPlanByPricingId(pricingId, ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L));
    }

    /**
     * Lấy danh sách chức năng theo danh sách mã tính năng
     *
     * @param featureIdList the feature id list
     * @return the list feature by pricing id
     */
    private List<PricingDetailResDTO.Feature> getListFeatureByFeatureIds(String featureIdList) {
        String[] split = StringUtils.split(
            StringUtils.defaultString(
                featureIdList, CharacterConstant.BLANK),
            CharacterConstant.COMMA);
        String[] featureArr = StringUtils.stripAll(split);
        Set<Long> featureIds = Arrays.stream(featureArr).map(Long::valueOf).collect(Collectors.toSet());

        List<PricingDetailResDTO.Feature> outFeatures = new ArrayList<>();
        comboPlanRepository.getFeaturesCombo(featureIds).forEach(feature -> {
            PricingDetailResDTO.Feature feat = new PricingDetailResDTO.Feature();
            BeanUtils.copyProperties(feature, feat);
            feat.setIcon(feature.getIcon());
            feat.setFilePath(feature.getFilePath());
            feat.setType(feature.getType());
            feat.setDescription(feature.getDescription());
            feat.setId(feature.getId());
            feat.setName(feature.getName());
            outFeatures.add(feat);
        });
        outFeatures.sort(Comparator.comparing(item -> new ArrayList<>(featureIds).indexOf(item.getId())));
        return outFeatures;
    }

    /* ******************************************************************************************************************************************
     *
     * Custom field methods
     *
     *******************************************************************************************************************************************/

    /**
     * Tìm kiếm custom_layout từ layoutId
     */
    private CustomLayout getCustomLayout(Long layoutId) {
        layoutId = (layoutId != null) ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.PRICING.getValue());
        if (layoutId == null) {
            return null;
        }
        return customLayoutRepository.findById(layoutId).orElse(null);
    }

    /**
     * Cập nhật giá trị trong custom_field_draft_value (tạo mới nếu chưa có)
     */
    private void updateDraftCustomFieldValue(PricingReqDTO requestDto, Integer entityType, Long entityDraftId) {
        CustomLayout customLayout = getCustomLayout(requestDto.getCreationLayoutId());
        // Lấy pricingId mới nhất theo pricingDraftId
        Long entityId = pricingRepository.findLastIdByPricingDraftId(entityDraftId);
        if (customLayout == null || requestDto.getLstCustomField() == null) {
            return;
        }
        List<CustomFieldDraftValue> lstDraftValue = customFieldDraftValueRepository.findAllByEntityDraftIdAndEntityType(entityDraftId,
            entityType);
        Set<String> lstFieldCode = customLayout.getLstCustomField();
        for (CustomFieldValueDTO valueDTO : requestDto.getLstCustomField()) {
            if (!lstFieldCode.contains(valueDTO.getFieldCode())) {
                continue;
            }
            CustomField customField = customFieldRepository.findByCode(valueDTO.getFieldCode()).orElse(null);
            if (customField == null) {
                continue;
            }

            try {
                CustomFieldDraftValue draftValue = customFieldDraftValueRepository.findByFieldIdAndEntityDraftId(customField.getId(),
                        entityDraftId)
                    .orElse(new CustomFieldDraftValue());
                draftValue.setEntityType(entityType);
                draftValue.setEntityDraftId(entityDraftId);
                customFieldManager.getFieldValueFromDto(customField, valueDTO, draftValue, entityId, entityDraftId);
                lstDraftValue.add(draftValue);
            } catch (InputMismatchException | NullPointerException | JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        customFieldDraftValueRepository.saveAll(lstDraftValue);
    }

    @Override
    public PricingPriceInfoDTO getPricingPreTaxAmount(Long pricingId, Long pricingMultiPlanId) {
        PricingPlanEnum planEnum;
        BigDecimal price;
        BigDecimal perPrice = BigDecimal.ZERO;
        BigDecimal quantity;
        long minQuantity = 1L;
        long maxQuantity = -1L;
        List<SubscriptionCalculateDTO> planPriceList;
        BigDecimal freeQuantity;
        List<PricingTax> planTaxList;
        PricingPriceInfoDTO res = new PricingPriceInfoDTO();
        if (pricingMultiPlanId == null) {
            var pricing = findByIdAndDeletedFlag(pricingId, DeletedFlag.NOT_YET_DELETED.getValue());
            planEnum = PricingPlanEnum.valueOf(pricing.getPricingPlan());
            price = pricing.getPrice();
            quantity = BigDecimal.ONE;
            planPriceList = unitLimitedRepository.findByPricingId(pricingId).stream().map(SubscriptionCalculateDTO::new)
                .collect(Collectors.toList());
            freeQuantity = BigDecimal.valueOf(ObjectUtil.getOrDefault(pricing.getFreeQuantity(), 0L));
        } else {
            var pricingMultiPlan = pricingMultiPlanRepository.findById(pricingMultiPlanId)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING_PLAN, ErrorKey.ID, String.valueOf(pricingMultiPlanId)));
            planEnum = PricingPlanEnum.valueOf(pricingMultiPlan.getPricingPlan());
            price = pricingMultiPlan.getPrice();
            planPriceList = pricingPlanDetailRepository.findAllByPricingMultiPlanIdAndSubscriptionSetupFeeIdIsNullOrderByUnitFromAsc(
                pricingMultiPlanId).stream().map(SubscriptionCalculateDTO::new).collect(Collectors.toList());
            switch (planEnum) {
                case FLAT_RATE:
                case UNIT:
                    quantity = pricingMultiPlan.getMinimumQuantity() != null ? BigDecimal.valueOf(pricingMultiPlan.getMinimumQuantity())
                        : BigDecimal.ONE;
                    perPrice = pricingMultiPlan.getPrice() != null ? pricingMultiPlan.getPrice() : BigDecimal.ZERO;
                    minQuantity = pricingMultiPlan.getMinimumQuantity() != null ? pricingMultiPlan.getMinimumQuantity() : 1L;
                    maxQuantity = pricingMultiPlan.getMaximumQuantity() != null ? pricingMultiPlan.getMaximumQuantity() : -1L;
                    break;
                case TIER:
                case VOLUME:
                case STAIR_STEP:
                    quantity = planPriceList.isEmpty() ? BigDecimal.ONE : BigDecimal.valueOf(planPriceList.get(0).getUnitFrom());
                    perPrice = planPriceList.isEmpty() ? BigDecimal.ZERO : planPriceList.get(0).getUnitPrice();
                    List<Long> lstUnitFrom = planPriceList.stream().map(SubscriptionCalculateDTO::getUnitFrom).collect(Collectors.toList());
                    List<Long> lstUnitTo = planPriceList.stream().map(SubscriptionCalculateDTO::getUnitTo).collect(Collectors.toList());
                    minQuantity = lstUnitFrom.stream().min(Long::compare).orElse(1L);
                    if (!lstUnitTo.contains(-1L)) {
                        maxQuantity = lstUnitTo.stream().max(Long::compare).orElse(-1L);
                    }
                    break;
                default:
                    quantity = BigDecimal.ONE;
            }
            freeQuantity = BigDecimal.valueOf(ObjectUtil.getOrDefault(pricingMultiPlan.getFreeQuantity(), 0));
        }
        planTaxList = pricingTaxRepository.findAllByPricingId(pricingId);
        res.setMinQuantity(minQuantity);
        res.setMaxQuantity(maxQuantity);
        res.setPerPrice(new ContainedTaxListCalculator(planTaxList, perPrice).getPreTaxAmount());
        res.setPrice(PriceCalculator.calculatorWithoutTax(planEnum, price, quantity, planPriceList, freeQuantity, planTaxList).calcWithoutTax());
        return res;
    }

    @Override
    public List<PricingDetailRespDTO> findAllPricingByServiceMobile(Long serviceId, Set<CustomerTypeEnum> lstCustomerType,
        PromotionTypeEnum promotionTypeEnum, DiscountTypeEnum discountTypeEnum, BigDecimal discountValue, Set<Long> lstPricingId, Long variantId) {
        Set<Long> maxVerIds = subscriptionRepository.findMaxVerIdsByServiceId(serviceId, variantId);
        List<PricingDetailRespDTO> response = subscriptionRepository.findAllPricingByServiceMobile(maxVerIds);
        List<PricingDetailRespDTO> responseCustomerType = new ArrayList<>();
        for (PricingDetailRespDTO pr : response) {
            if (!lstPricingId.isEmpty() && !lstPricingId.contains(pr.getId())) {
                continue;
            }
            if (lstCustomerType.contains(CustomerTypeEnum.UNSET) || pr.getCustomerType() == null ||
                lstCustomerType.stream().anyMatch(pr.getCustomerType()::contains)) {
                responseCustomerType.add(pr);
                break;
            }
        }

        ServiceEntity service = servicesService.findByIdAndDeletedFlag(serviceId, 1);
        Long defaultPricingDraftId = getPricingDefault(service);
        List<PricingDetailRespDTO> listErr = new ArrayList<>();
        val result = responseCustomerType.stream().peek(p -> {
            List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(p.getId());
            // Số tiền trước thuế
            BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(p.getPriceValue(), taxes);
            p.setPrice(pricingPreTax);
            if (Objects.nonNull(pricingPreTax)) {
                if (Objects.equals(discountTypeEnum, DiscountTypeEnum.PRICE)) {
                    p.setPriceAfterDiscount(pricingPreTax.subtract(discountValue));
                }
                if (Objects.equals(discountTypeEnum, DiscountTypeEnum.PERCENT)) {
                    p.setPriceAfterDiscount(pricingPreTax.subtract(
                        pricingPreTax.multiply(discountValue.divide(BigDecimal.valueOf(100)))));
                }
            }
            List<PricingPlanDetailRespDTO> multiplePeriods = subMultiplePeriod.getPlanPeriodByPricingId(p.getId(), p.getIsOneTime(),
                lstCustomerType, promotionTypeEnum, discountTypeEnum, discountValue);
            List<PricingPlanDetailRespDTO> multiplePeriodResponse = new ArrayList<>();
            for (PricingPlanDetailRespDTO mul : multiplePeriods) {
                if (lstCustomerType.contains(CustomerTypeEnum.UNSET) || mul.getCustomerTypeCode() == null ||
                    lstCustomerType.stream().anyMatch(mul.getCustomerTypeCode()::contains)) {
                    multiplePeriodResponse.add(mul);
                }
            }
            p.setPricingMultiplePeriods(multiplePeriodResponse);
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                p.setUnitLimitedList(getUnitLimitedByPricingId(p.getId()));
            }
            ServiceReaction reaction = serviceReactionService.getByServiceIdAndType(p.getPricingDraftId(), 3);
            if (reaction != null) {
                p.setReaction(true);
            }
            // Nếu có period nhưng không thuộc kiểu người dùng thì add vào listErr để remove
            if (!CollectionUtils.isEmpty(multiplePeriods) && CollectionUtils.isEmpty(multiplePeriodResponse)) {
                listErr.add(p);
            }

            if (Objects.nonNull(defaultPricingDraftId)) {
                p.setIsDefault(Objects.equals(defaultPricingDraftId, p.getPricingDraftId()));
            }

        }).collect(Collectors.toList());
        result.removeAll(listErr);
        return result;
    }


    /**
     * Tạo và cập nhật ảnh gói cước
     */
    private void createDraftPricingImage(Long pricingImageId, Long pricingDraftId) {
        if (pricingImageId == null) {
            return;
        }
        FileAttach pricingImage = fileAttachRepository.findById(pricingImageId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.FILE_ATTACH, ErrorKey.ID,
                String.valueOf(pricingImageId)));
        pricingImage.setObjectType(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue());
        pricingImage.setObjectDraftId(pricingDraftId);
        fileAttachRepository.save(pricingImage);
    }

    private void approveDraftPricingImage(Long pricingDraftId, Long pricingId) {
        List<FileAttach> lstPricingImage = fileAttachRepository.findByObjectTypeAndObjectDraftId(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(),
            pricingDraftId);
        if (!lstPricingImage.isEmpty()) {
            lstPricingImage.stream().filter(item -> Objects.isNull(item.getObjectId()))
                .forEach(pricingImage -> pricingImage.setObjectId(pricingId));
            fileAttachRepository.saveAll(lstPricingImage);
        }
    }

    private FileAttachDTO getDraftPricingImage(Long pricingDraftId) {
        List<FileAttach> lstPricingImage = fileAttachRepository.findByObjectTypeAndObjectDraftId(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(),
            pricingDraftId);
        if (!lstPricingImage.isEmpty()) {
            FileAttachDTO response = new FileAttachDTO();
            BeanUtils.copyProperties(lstPricingImage.get(0), response);
            return response;
        }
        return null;
    }

    private FileAttachDTO getPricingImage(PricingDetailInputEnum type, Long pricingDraftId) {
        FileAttach fileAttach;
        if (type.equals(PricingDetailInputEnum.APPROVED)) {
            Pricing pricing = pricingRepository.findByDraftIdApproved(pricingDraftId).orElseThrow(() -> {
                String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, pricingM, LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(messageNotFound, Resources.PRICING,
                        ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            });
            fileAttach = fileAttachRepository.findFirstByObjectTypeAndObjectIdOrderByIdDesc(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(), pricing.getId()).orElse(null);
        } else {
            fileAttach = fileAttachRepository.findFirstByObjectTypeAndObjectDraftIdOrderByIdDesc(FileAttachTypeEnum.BOS_PRICING_IMAGE.getValue(), pricingDraftId);
        }
        //
        if (Objects.nonNull(fileAttach)) {
            FileAttachDTO response = new FileAttachDTO();
            BeanUtils.copyProperties(fileAttach, response);
            return response;
        }
       return null;
    }

    /**
     * Fill thông tin cấu hình của gói cước
     */
    private PricingConfigResDTO fillPricingConfigResponse(PricingConfigDTO pricingConfigDTO) {
        PricingConfigResDTO res = new PricingConfigResDTO();
        Long provinceId = ObjectUtils.isNotEmpty(pricingConfigDTO.getProvinceId()) ? pricingConfigDTO.getProvinceId().get(0) : null;
        Long districtId = ObjectUtils.isNotEmpty(pricingConfigDTO.getDistrictId()) ? pricingConfigDTO.getDistrictId().get(0) : null;
        Long wardId = ObjectUtils.isNotEmpty(pricingConfigDTO.getWardId()) ? pricingConfigDTO.getWardId().get(0) : null;
        Long streetId = ObjectUtils.isNotEmpty(pricingConfigDTO.getStreetId()) ? pricingConfigDTO.getStreetId().get(0) : null;
        if (Objects.nonNull(provinceId)) {
            // fill tỉnh thành phố
            res.setProvince(regionsRepository.getProvinceById(provinceId));
            if (Objects.nonNull(districtId)) {
                // fill quận huyện
                res.setDistrict(regionsRepository.getDistrictByProvinceIdAndId(provinceId, districtId));
                if (Objects.nonNull(wardId)) {
                    // fill phường xã
                    res.setWard(regionsRepository.getWardByProvinceIdAndDistrictIdAndId(provinceId, districtId, wardId));
                    if (Objects.nonNull(streetId)) {
                        // fill đường phố
                        res.setStreet(regionsRepository.getStreetByProvinceIdAndWardIdInAndIdIn(provinceId, wardId, streetId));
                    }
                }
            }
        }
        return res;
    }
}
