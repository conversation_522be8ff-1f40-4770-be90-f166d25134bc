/**
 * <AUTHOR> VinhNT
 * @version    : 1.0
 */
package com.service.pricing;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.constant.enums.coupon.CouponPricingTypeEnum;
import com.constant.enums.coupon.CouponServiceTypeEnum;
import com.constant.enums.pricing.PeriodFilterTypeEnum;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.pricing.ValidateCodeAction;
import com.constant.enums.pricing.ValidateCodeType;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.dto.addons.AddonsResDTO;
import com.dto.coupons.CouponPopupTreeResDTO;
import com.dto.mobile.PricingDetailRespDTO;
import com.dto.pricing.ApproveReqDTO;
import com.dto.pricing.IDetailPricingMultiPlanDTO;
import com.dto.pricing.IGetTopViewPricingDTO;
import com.dto.pricing.IPaymentCycleDTO;
import com.dto.pricing.IPricingSaaSResDTO;
import com.dto.pricing.PaymentCycleDTO;
import com.dto.pricing.PlanPeriodResDTO;
import com.dto.pricing.PricingAddonResDTO;
import com.dto.pricing.PricingApproveReqDTO;
import com.dto.pricing.PricingDetailHisResDTO;
import com.dto.pricing.PricingDetailResDTO;
import com.dto.pricing.PricingHistoryResDTO;
import com.dto.pricing.PricingIntegrationReqDTO;
import com.dto.pricing.PricingLevelReqDTO;
import com.dto.pricing.PricingListDevRes;
import com.dto.pricing.PricingNameDTO;
import com.dto.pricing.PricingPriceInfoDTO;
import com.dto.pricing.PricingReqDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.pricing.PricingSettingReqDTO;
import com.dto.pricing.PricingTransactionDTO;
import com.dto.report.dashboardSme.PricingComboPlanReportResDTO;
import com.dto.report.dashboardSme.ServiceComboReportResDTO;
import com.dto.report.dashboardSme.UserNameFilterResDTO;
import com.dto.services.PricingSearchResponseDTO;
import com.dto.subscription_plans.OrderListResponseDTO;
import com.dto.subscriptions.SubscriptionServiceBasicDTO;
import com.entity.addons.AddonDraft;
import com.entity.combo.ComboPlan;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.pricing.PricingSetupInfo;
import com.entity.services.ServiceEntity;
import com.enums.ApproveStatusEnum;
import com.enums.DisplayStatus;
import com.enums.ServiceDetailTabEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.pricings.PeriodTypeEnum;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingPeriodAddonDTO;

public interface PricingService {

    Pricing findByIdAndDeletedFlag(Long pricingId, Integer deletedFlag);

    boolean isLatestPricing(Pricing pricing);

    /**
     * Lấy danh sách các gói dịch vụ của admin-developer tạo
     */
    CouponPopupTreeResDTO getListPricing(Pageable pageable, String serviceName,
        CouponServiceTypeEnum serviceType, String pricingName,
        CouponPricingTypeEnum pricingType, Long categoryId, Integer paymentCycle,
        CycleTypeEnum cycleType, Set<Long> pricingIds, Set<Long> multiPlanId,
        Set<Long> combosId, Set<CustomerTypeEnum> customerTypes, PortalType portalType);

    /**
     * Lay chi tiet goi dich vu
     *
     * @param id the id
     *
     * @return the pricing detail
     */
    PricingDetailResDTO getPricingDetail(Long id, PortalType portalType, PricingDetailInputEnum type);

    /**
     * Dev Tắt/bật trạng thái gói dịch vụ (Pricing)
     *
     * @param status
     * * @param portal
     */
    BaseResponseDTO updateDisplay(Long id, DisplayStatus status, PortalType portal);

    /**
     * Lấy danh sách các gói dịch vụ của admin-developer tạo v2
     *
     */
    List<PricingListDevRes> getListPricingDevV2(Long serviceId, String name, CustomerTypeEnum customerTypeCode,
        DisplayStatus displayStatus, ApproveStatusEnum approveStatus, ServiceDetailTabEnum tab);

    /**
     * Lấy danh sách các gói dịch vụ tren admin-portal
     *
     */

    List<PricingListDevRes> getListPricingAdmin(Long serviceId, String name, CustomerTypeEnum customerTypeCode,
        DisplayStatus displayStatus, ApproveStatusEnum approveStatus, ServiceDetailTabEnum tab);

    /**
     * Xóa gói dịch vụ
     */
    void deletePricing(Long id);

    /**
     * Admin phê duyệt gói dịch vụ
     *
     */
    void approvePricing(Long id, PricingApproveReqDTO pricingApproveReqDTO, Boolean isApproveWithService);

    /**
     * Lay lich su thay doi goi
     *
     * @param id the id
     *
     * @return the pricing history
     */
    Page<PricingHistoryResDTO> getPricingHistory(Long id, PortalType portalType, Pageable pageable);


    /**
     * lay chi tiet tu phan lich su goi
     *
     * @param id         the id
     * @param portalType the portal type
     * @param approve    the approve
     *
     * @return the pricing from history
     */
    PricingDetailHisResDTO getPricingFromHistory(Long id, PortalType portalType, ApproveStatusEnum approve);

    String getTitleCycleType(Integer type);

    /**
     * Tao goi dich vu
     *
     * @param pricingDto the pricing dto
     * @return the map
     */
    BaseResponseDTO createPricing(PricingReqDTO pricingDto, Boolean isCreateWithService, Boolean isApproved) throws JsonProcessingException;

    /**
     * Cập nhật thứ tự hiển thị và gói khuyên dùng của gói dịch vụ
     *
     */
    void updateOrderAndRecommendOfSaas(List<OrderListResponseDTO> orderListResponseDTOs, Long serviceId);

    /**
     * Dong bo goi cuoc tu he thong DHSXKD sang oneSME
     *
     * @param pricingIntegrationDto the pricing integration dto
     * @return the base response DTO
     */
    BaseResponseDTO synchronizedCreatePricing(PricingIntegrationReqDTO pricingIntegrationDto, Long serviceid) throws JsonProcessingException;

    /**
     * Cập nhật gói dịch vụ
     *
     * @param pricingDto the pricing dto
     *
     * @return the base response dto
     */
    BaseResponseDTO updatePricing(Long id, PricingReqDTO pricingDto, Long serviceId, Boolean isApproved) throws JsonProcessingException;

    BaseResponseDTO updatePlanOfPricing(Long planId, Integer displayStatus);

    /**
     * Validate approve status update.
     *
     * @param approve the approve
     */
    void validateApproveStatusUpdate(Integer approve);

    /**
     * Lay danh sach dich vu bo sung
     *
     */
    Page<PricingAddonResDTO> getAddonPricing(Long pricingId, Set<Long> addonIds, Integer paymentCycle, PeriodTypeEnum cycleTypee,
        YesNoEnum orderService, String serviceName, String addonName, Long categoryId, BonusTypeEnum type, PeriodFilterTypeEnum addonType,
        YesNoEnum hasBonusTypeOnce, PortalType portal, Pageable pageable);

    Page<PricingAddonResDTO> getAddonCreatePricing(Long pricingId, Set<Long> addonIds, Integer paymentCycle, PeriodTypeEnum cycleTypee,
                                             YesNoEnum orderService, String serviceName, String addonName, Long categoryId, BonusTypeEnum type, PeriodFilterTypeEnum addonType, Integer isOneTime,
                                             YesNoEnum hasBonusTypeOnce, PortalType portal, Long providerId, DisplayStatus status, Pageable pageable);

    /**
     * Get pricing
     */
    Pricing getPricing(Long id);

    /**
     * Lấy tên service cho filter chọn gói dv
     */
    List<ServiceComboReportResDTO> getServiceNameFilter(Long serviceId, String name, YesNoEnum isOsService, PortalType portal,
        CustomerTypeEnum customerType, Integer registerEcontract);

    /**
     * Lấy tên pricing cho filter chọn gói dv
     */
    List<PricingComboPlanReportResDTO> getPricingNameFilter(Long serviceId, String name, YesNoEnum isOsService, PortalType portal,
        CustomerTypeEnum customerType, Integer registerEcontract);

    /**
     * Lấy chu kỳ thanh toán cho filter chọn gói dv
     */
    List<PlanPeriodResDTO> getPeriodFilter(Long serviceId, Long comboId, String name, YesNoEnum isOsService, PortalType portal,
        CustomerTypeEnum customerType, Integer registerEcontract);

    /**
     * Lấy danh sách các ten nha PT
     * @param name Tên addon
     * @return danh sách các addon
     */
    List<UserNameFilterResDTO> getUserNameFilter(Long serviceId, YesNoEnum isOsService, PortalType portal, String name);

    /**
     * Convert đơn vị thời gian thành enum
     *
     */
    int convertStringToTimeEnum(String time);

    /**
     * Lấy tên service cho filter popup addon - tạo pricing
     */
    List<ServiceComboReportResDTO> getServiceNameFilterAddon(String name, Long pricingId, YesNoEnum isOsService);

    /**
     * Lấy tên pricing cho filter popup addon - tạo pricing popup addon
     *
     */
    List<AddonsResDTO> getAddonNameFilterAddon(Integer paymentCycle, PeriodTypeEnum cycleType, String name, Long serviceId, Long pricingId,
        YesNoEnum isOsService);

    /**
     * Lấy chu kỳ thanh toán cho filter popup addon - tạo pricing popup addon
     */
    List<PlanPeriodResDTO> getPeriodFilterAddon(Integer paymentCycle, PeriodTypeEnum cycleType, Long serviceId, Long addonId,
        Long pricingId, PeriodFilterTypeEnum type, YesNoEnum isOsService, String name);

    /**
     * Xóa đi các phần tử trùng lặp theo key
     */
    <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor);

    /**
     * Danh sách chu kì thanh toán
     */
    List<PaymentCycleDTO> getPaymentCycle();

    /**
     * Danh sách chu kì thanh toán theo id dịch vụ
     */
    List<IPaymentCycleDTO> getPaymentCycleByMigrationIds(List<Long> serviceMigrationIds);
    /**
     * Lấy danh sách tên gói dịch vụ
     *
     * @param name tên gói dịch vụ
     * @return danh sách tên gói dịch vụ
     */
    List<PricingNameDTO> getListPricingName(String name, Long serviceId);

    /*
     * type: mã gói dịch vụ hay là mã chu kỳ
     * code: mã cần check trùng
     * action: hành động create hay là edit
     * */
    Boolean validatePeriodCodeAndPricingCode(Long serviceId, ValidateCodeType type, String code, ValidateCodeAction action);

    Boolean validatePeriodCodeAndPricingCodeWithoutService(ValidateCodeType type, String code, ValidateCodeAction action);

    List<AddonDraft> checkCanRemoveCustomerType(Long pricingId, String customerType);

    /**
     * Cập nhật level của gói dịch vụ
     *
     */
    void updateLevelSetting(List<PricingLevelReqDTO> pricings, Long serviceId, Integer Type);

    /**
     * Cập nhật thiết lập thông tin của gói dịch vụ
     *
     */
    void updateSettingInfo(PricingSettingReqDTO pricingSettingReqDTO, Long serviceId);

    /**
     * Lấy thông tin thiết lập của gói dịch vụ
     *
     */
    PricingSettingReqDTO getSettingInfo(Long serviceId, Integer type);

    /**
     * Lấy thông tin thiết lập nhanh của gói dịch vụ
     *
     */
    PricingSettingReqDTO getFastConfig(Long serviceId, PricingSettingReqDTO pricingSettingReqDTO);

    Set<Long> getPlanSelected(PricingSetupInfo pricingSetupInfo, List<ComboPlan> lstCp, ComboPlan comboPlanSub);

    Long getNumberOfCycleBySubId(Long subId);

    /**
     * Get pricing
     */
    PricingTransactionDTO getPricingTransaction(Long id);

    /**
     * Lấy danh sách PricingIdsSelected
     *
     * @return the page
     */
    List<Long> getPricingIdsSelected(PricingSetupInfo pricingSetupInfo, PricingMultiPlan pricingMultiPlanSub, Long serviceId,
        Boolean checkPricingConfigList);

    /**
     * Lấy danh sách getPricingMultiPlanIdsSelected
     *
     * @return the page
     */
    List<Long> getPricingMultiPlanIdsSelected(PricingSetupInfo pricingSetupInfo, PricingMultiPlan pricingMultiPlanSub,
        List<Long> pricingIdsSelected, Boolean checkPricingConfigList);

    /**
     * Xem thông tin dich vu khi sme subscription
     *
     * @param serviceId : Id service
     * @param pricingId : Id pricing
     * @return Du lieu service
     */
    SubscriptionServiceBasicDTO getSubscriptionServiceBasic(Long serviceId, Long pricingId, Long pricingMultiPlan);

    /**
     * Lấy danh sách giới hạn theo mã gói
     *
     * @param pricingId the pricing id
     * @return the unit limited by pricing id
     */
    List<PricingDetailResDTO.UnitLimited> getUnitLimitedByPricingId(Long pricingId);

    void requestApproveManyPricing(ApproveReqDTO reqDTO);

    void deletePricings(List<Long> ids);

    void changePricingDefault(Long serviceId, Long pricingId);

    Long getPricingDefault(ServiceEntity service);

    PricingSaaSResDTO getPricingSubscriptionByPricingId(Long pricingId);

    /**
     * Lấy thông tin preview pricing
     */
    PricingSaaSResDTO convertPricingSaaSResponse(IPricingSaaSResDTO source);

    /**
     * Lấy danh sách gói dịch vụ theo dịch vụ
     *
     * @param serviceId ID dịch vụ
     * @param customerTypeEnum Loại khách hàng sử dụng để lọc danh sách gói dịch vụ trả về (null nếu không cần lọc theo loại khách hàng)
     */
    List<PricingSaaSResDTO> findAllPricingByService(Long serviceId, Long pricingIdSub, CustomerTypeEnum customerTypeEnum, Long variantId);

    /**
     * Lấy danh sách gói dịch vụ rút gọn theo dịch vụ
     *
     * @param serviceId the service id
     * @return the page
     */
    List<PricingSearchResponseDTO> findAllPricingShortByService(Long serviceId, String customerType);

    /**
     * Danh sách các gói được xem nhiều nhất
     *
     * @param customerType        Loại khách hàng
     * @param serviceProductTypes Danh sách các productType của dịch vụ
     * @param categories          Danh sách category của dịch vụ
     */
    Page<IGetTopViewPricingDTO> getTopView(CustomerTypeEnum customerType, Set<ServiceProductTypeEnum> serviceProductTypes,
        Set<Long> categoryMigrationIds, Set<Long> serviceMigrationIds, Set<Long> categories, Pageable pageable);


    /**
     * Danh sách gói cho KHCN
     * @param pricingName Tên gói
     * @param paymentCycle  số chu kỳ thanh toán
     * @param circleType loại chu kỳ thanh toán (ngày,tuần, tháng, năm)
     * @param serviceMigrationIds id dịch vụ được lấy từ kho dữ liệu của đối tác
     */
    Page<IGetTopViewPricingDTO> getFromServiceByMigrationIds(String pricingName, Integer paymentCycle, Integer circleType, Set<Long> serviceMigrationIds,
        Pageable pageable);


    List<IDetailPricingMultiPlanDTO> getMultiPlanByPricingId(Long pricingId);

    /**
     * Danh sách các gói bán chạy nhất
     *
     * @param customerType        Loại khách hàng
     * @param serviceProductTypes Danh sách các productType của dịch vụ
     * @param categories          Danh sách category của dịch vụ
     */
    Page<IGetTopViewPricingDTO> getTopSelling(CustomerTypeEnum customerType, Set<ServiceProductTypeEnum> serviceProductTypes,
        Set<Long> categoryMigrationIds, Set<Long> serviceMigrationIds, Set<Long> categories, Pageable pageable);

    /**
     * Tìm giá trước thuế của gói dịch vụ
     */
    PricingPriceInfoDTO getPricingPreTaxAmount(Long pricingId, Long pricingMultiPlanId);


    List<PricingDetailRespDTO> findAllPricingByServiceMobile(Long serviceId, Set<CustomerTypeEnum> lstCustomerType,
        PromotionTypeEnum promotionTypeEnum, DiscountTypeEnum discountTypeEnum, BigDecimal discountValue, Set<Long> lstPricingId, Long variantId);

    List<SubscriptionPricingPeriodAddonDTO> getAddonByPricingId(Long pricingId, Long pricingMultiPlanId);
}
