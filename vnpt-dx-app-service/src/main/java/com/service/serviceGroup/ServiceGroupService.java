package com.service.serviceGroup;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.services.ServiceStatusEnum;
import com.dto.serviceGroup.DetailServiceGroupDTO;
import com.dto.serviceGroup.GroupPricingDetailDTO;
import com.dto.serviceGroup.IGetDetailPricingItemDTO;
import com.dto.serviceGroup.ListServiceGroupReqDTO;
import com.dto.serviceGroup.ListServiceGroupResDTO;
import com.dto.serviceGroup.ServiceGroupApprovedReqDTO;
import com.dto.serviceGroup.ServiceGroupApprovedResDTO;
import com.dto.serviceGroup.ServiceGroupDTO;
import com.dto.serviceGroup.ServiceGroupDetailDTO;
import com.dto.serviceGroup.ServiceGroupTableReqDTO;
import com.dto.serviceGroup.ServiceGroupTableResDTO;
import com.dto.services.ServiceSuggestionResDTO;
import com.entity.pricing.PricingPlanDetail;
import com.entity.serviceGroup.ServiceGroup;
import com.entity.serviceGroup.ServiceGroupDraft;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.dto.customFields.GetSPDVInfoDTO;
public interface ServiceGroupService {

    Page<ListServiceGroupResDTO> getListServiceGroup(PortalType portal, ListServiceGroupReqDTO requestDTO, Pageable pageable);

    ServiceGroupDetailDTO getDetailServiceGroup(Long groupDraftId, Long groupId, PricingDetailInputEnum type);

    BigDecimal calculatePriceServiceGroupPreTax(List<IGetDetailPricingItemDTO> serviceGroupPricingItemList);

    Long changeGroupStatus(Long groupId, ServiceStatusEnum status);

    void saveGroupDraftAfterUpdateItemIn(List<Long> lstItemId, Boolean isUpdateVariant);

    List<ServiceGroupDraft> saveGroupDraftAsUnapproved(List<ServiceGroupDraft> lstGroupDraft, Boolean isSetGroupUnapproved);

    /**
     * Tạo mới nhóm sản phẩm
     */
    Long createServiceGroup(ServiceGroupDTO serviceGroupDTO);

    /**
     * Cập nhật nhóm sản phẩm
     */
    Long updateServiceGroup(ServiceGroupDTO serviceGroupDTO);

    void saveServiceGroupApproved(ServiceGroupDraft serviceGroupDraft, Long currentUserId);

    /**
     * Xóa danh sách nhóm sản phẩm
     */
    void deleteServiceGroup(Set<Long> ids);

    /**
     * Phê duyệt nhóm sản phẩm
     */
    ServiceGroupApprovedResDTO approveServiceGroup(ServiceGroupApprovedReqDTO reqDTO);

    /**
     * Dev gửi yêu cầu phê duyệt nhóm sản phẩm
     */
    void requestApprovedServiceGroup(Set<Long> ids, PortalType portal);

    List<GroupPricingDetailDTO> getGroupDraftPricingDetail(Long groupDraftId, Long groupId);

    List<GroupPricingDetailDTO> getGroupPricingDetail(Long groupId);

    BigDecimal getGroupTotalTempPrice(Long groupId);

    /**
     * Tìm kiếm nhóm sản phẩm theo subscriptionId
     */
    ServiceGroup getServiceGroupBySubscriptionId(Long subscriptionId);

    /**
     * Chi tiết nhóm sản phẩm
     */
    DetailServiceGroupDTO getDetail(Long groupDraftId, Long userId);

    DetailServiceGroupDTO getDetailSme(Long groupId, Long userId);

    /**
     * Danh sách SPDV liên quan tới nhóm dịch vụ
     *
     * @param groupId ID nhóm dịch vụ
     */
    List<ServiceSuggestionResDTO> getSuggestions(Long groupId);

    /**
     * Pop up chọn sản phẩm dịch vụ
     */
    Page<GetSPDVInfoDTO> getServiceProductInfo(String productType, Integer portalType, String value, Long customerId, Integer searchProduct,
        List<String> lstCustomerType, Long categoryId, Integer isDevice, Integer serviceType, Integer searchPricing,
        Integer serviceOnOsType, List<Long> lstSelectedId, ServiceOwnerEnum serviceOwnerEnum, Pageable pageable);

    List<ServiceGroupTableResDTO> getServiceGroupTable(List<ServiceGroupTableReqDTO> req, Boolean isPopup);

    BigDecimal getPriceRangeByQuantity(Long quantity, List<PricingPlanDetail> planDetails);
}
