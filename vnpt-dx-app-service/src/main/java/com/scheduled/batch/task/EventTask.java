package com.scheduled.batch.task;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.dto.events.CouponEventMetadata;
import com.dto.events.ProductEventMetadata;
import com.entity.events.Events;
import com.enums.EventTypeEnum;
import com.event.ComponentChangedEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.repository.coupons.CouponRepository;
import com.repository.events.EventsRepository;
import com.scheduled.batch.BaseBatch;
import com.scheduled.batch.BatchService;
import com.service.events.EventsService;
import com.service.product_solutions.PackageBundlingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component("batch-event")
@Slf4j
@RequiredArgsConstructor
public class EventTask extends BaseBatch implements BatchService {

    @Autowired
    private EventsService eventsService;

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private PackageBundlingService packageBundlingService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CouponRepository couponRepository;

    /**
     * Tạo events cho các coupon hết hạn để xử lý
     */
    @Transactional(rollbackFor = Exception.class)
    public void scanExpiredCouponsDaily() {
        long startTime = System.currentTimeMillis();
        log.info("Bắt đầu quét coupon hết hạn hàng ngày...");
        try {
            Set<Long> expiredCouponIds = couponRepository.getExpiredCouponId();
            if (expiredCouponIds.isEmpty()) {
                return;
            }
            for (Long couponId : expiredCouponIds) {
                try {
                    // Tạo metadata cho event
                    CouponEventMetadata metadata = new CouponEventMetadata();
                    metadata.setCouponId(couponId);
                    // Tạo event COUPON_EXPIRED
                    Events event;
                    event = eventsService.saveEvent(
                        EventTypeEnum.COUPON_EXPIRED,
                        metadata
                    );
                } catch (Exception e) {
                    log.error("Lỗi khi tạo event cho coupon hết hạn {}: {}", couponId, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    // ==================== EVENT HANDLERS ====================

    /**
     * Xử lý event khi component thay đổi
     *
     * @param event ComponentChangedEvent chứa ID của event đã được tạo
     */
    @Transactional(rollbackFor = Exception.class)
    @Async(value = "asyncExecutorChangeEvent")
    @EventListener
    public void componentChangedEvent(ComponentChangedEvent event) {
        try {
            log.info("Nhận được ComponentChangedEvent với eventId: {}", event.getEventId());
            // Lấy thông tin event để xử lý
            Events eventData = eventsService.getEventById(event.getEventId());
            if (eventData == null) {
                log.warn("Không tìm thấy event với ID: {}", event.getEventId());
                return;
            }
            // Xử lý theo loại event
            handleEventByType(eventData);
            // Cập nhật status của event thành đã xử lý (status = 1)
            Events updatedEvent = eventsService.updateEventStatus(event.getEventId(), 1);
            log.info("Đã cập nhật thành công status cho event ID: {} thành đã xử lý", updatedEvent.getId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý ComponentChangedEvent với eventId {}: {}", event.getEventId(), e.getMessage(), e);
            // Cập nhật status thành lỗi (status = -1) nếu xử lý thất bại
            try {
                eventsService.updateEventStatus(event.getEventId(), -1);
            } catch (Exception updateException) {
                log.error("Lỗi khi cập nhật status lỗi cho event ID {}: {}", event.getEventId(), updateException.getMessage());
            }
            throw e;
        }
    }

    /**
     * Xử lý event theo loại
     */
    private void handleEventByType(Events eventData) {
        try {
            EventTypeEnum eventType = eventData.getType();
            log.info("Xử lý event type: {} với ID: {}", eventType, eventData.getId());

            switch (eventType) {
                // COUPON events
                case COUPON_EXPIRED:
                    handleCouponExpiredEvent(eventData);
                    break;
                case COUPON_APPLY_EXCEED:
                    handleCouponApplyExceedEvent(eventData);
                    break;
                case COUPON_UPGRADED:
                    handleCouponUpgradedEvent(eventData);
                    break;

                // PRODUCT events
                case PRICING_STATUS_CHANGED:
                    handlePricingStatusChangedEvent(eventData);
                    break;
                case PRICING_UPGRADED:
                    handlePricingUpgradedEvent(eventData);
                    break;
                case VARIANT_STATUS_CHANGED:
                    handleVariantStatusChangedEvent(eventData);
                    break;
                case VARIANT_UPGRADED:
                    handleVariantUpgradedEvent(eventData);
                    break;
                case ADDON_STATUS_CHANGED:
                    handleAddonStatusChangedEvent(eventData);
                    break;
                case ADDON_UPGRADED:
                    handleAddonUpgradedEvent(eventData);
                    break;

                default:
                    log.info("Event type {} không cần xử lý đặc biệt", eventType);
                    break;
            }
        } catch (Exception e) {
            log.error("Lỗi khi xử lý event type cho event ID {}: {}", eventData.getId(), e.getMessage(), e);
            throw e;
        }
    }

    // ==================== COUPON EVENTS ====================

    /**
     * Xử lý event khi coupon hết hạn
     */
    private void handleCouponExpiredEvent(Events eventData) {
        try {
            CouponEventMetadata metadata = parseMetadata(eventData.getMetadata(), CouponEventMetadata.class);
            log.info("Xử lý coupon expired event - couponId: {}", metadata.getCouponId());

            // Gọi service để xử lý coupon hết hạn - truyền eventType để biết cần xóa coupon
            packageBundlingService.handleComponentChanged(metadata.getCouponId(), "COUPON", "EXPIRED");

            log.info("Đã xử lý thành công coupon expired event - couponId: {}", metadata.getCouponId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý coupon expired event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi coupon hết số lượng áp dụng
     */
    private void handleCouponApplyExceedEvent(Events eventData) {
        try {
            CouponEventMetadata metadata = parseMetadata(eventData.getMetadata(), CouponEventMetadata.class);
            log.info("Xử lý coupon apply exceed event - couponId: {}", metadata.getCouponId());

            // Gọi service để xử lý coupon hết số lượng - truyền eventType để biết cần xóa coupon
            packageBundlingService.handleComponentChanged(metadata.getCouponId(), "COUPON", "APPLY_EXCEED");

            log.info("Đã xử lý thành công coupon apply exceed event - couponId: {}", metadata.getCouponId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý coupon apply exceed event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi coupon được cập nhật
     */
    private void handleCouponUpgradedEvent(Events eventData) {
        try {
            CouponEventMetadata metadata = parseMetadata(eventData.getMetadata(), CouponEventMetadata.class);
            log.info("Xử lý coupon upgraded event - couponId: {}, couponDraftId: {}",
                metadata.getCouponId(), metadata.getCouponDraftId());

            // Gọi service để xử lý coupon cập nhật - chỉ tính toán lại giá
            packageBundlingService.handleComponentChanged(metadata.getCouponId(), "COUPON", "UPGRADED");

            log.info("Đã xử lý thành công coupon upgraded event - couponId: {}", metadata.getCouponId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý coupon upgraded event: {}", e.getMessage(), e);
            throw e;
        }
    }

    // ==================== PRODUCT EVENTS ====================

    /**
     * Xử lý event khi trạng thái pricing thay đổi
     */
    private void handlePricingStatusChangedEvent(Events eventData) {
        try {
            ProductEventMetadata metadata = parseMetadata(eventData.getMetadata(), ProductEventMetadata.class);
            log.info("Xử lý pricing status changed event - pricingId: {}, status: {}",
                metadata.getPricingId(), metadata.getStatus());

            // Gọi service để xử lý pricing thay đổi trạng thái
            packageBundlingService.handleComponentChanged(metadata.getPricingId(), "PRICING", "STATUS_CHANGED");

            log.info("Đã xử lý thành công pricing status changed event - pricingId: {}", metadata.getPricingId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý pricing status changed event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi pricing được cập nhật
     */
    private void handlePricingUpgradedEvent(Events eventData) {
        try {
            ProductEventMetadata metadata = parseMetadata(eventData.getMetadata(), ProductEventMetadata.class);
            log.info("Xử lý pricing upgraded event - pricingId: {}, pricingDraftId: {}",
                metadata.getPricingId(), metadata.getPricingDraftId());

            // Gọi service để xử lý pricing cập nhật
            packageBundlingService.handleComponentChanged(metadata.getPricingId(), "PRICING", "UPGRADED");

            log.info("Đã xử lý thành công pricing upgraded event - pricingId: {}", metadata.getPricingId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý pricing upgraded event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi trạng thái variant thay đổi
     */
    private void handleVariantStatusChangedEvent(Events eventData) {
        try {
            ProductEventMetadata metadata = parseMetadata(eventData.getMetadata(), ProductEventMetadata.class);
            log.info("Xử lý variant status changed event - pricingId: {}, status: {}",
                metadata.getPricingId(), metadata.getStatus());

            // Gọi service để xử lý variant thay đổi trạng thái - sử dụng pricingId
            packageBundlingService.handleComponentChanged(metadata.getPricingId(), "VARIANT", "STATUS_CHANGED");

            log.info("Đã xử lý thành công variant status changed event - pricingId: {}", metadata.getPricingId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý variant status changed event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi variant được cập nhật
     */
    private void handleVariantUpgradedEvent(Events eventData) {
        try {
            ProductEventMetadata metadata = parseMetadata(eventData.getMetadata(), ProductEventMetadata.class);
            log.info("Xử lý variant upgraded event - variantId: {}, variantDraftId: {}",
                metadata.getVariantId(), metadata.getVariantDraftId());

            // Gọi service để xử lý variant cập nhật
            packageBundlingService.handleComponentChanged(metadata.getVariantId(), "VARIANT", "UPGRADED");

            log.info("Đã xử lý thành công variant upgraded event - variantId: {}", metadata.getVariantId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý variant upgraded event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi trạng thái addon thay đổi
     */
    private void handleAddonStatusChangedEvent(Events eventData) {
        try {
            ProductEventMetadata metadata = parseMetadata(eventData.getMetadata(), ProductEventMetadata.class);
            log.info("Xử lý addon status changed event - addonId: {}, status: {}",
                metadata.getAddonId(), metadata.getStatus());

            // Gọi service để xử lý addon thay đổi trạng thái
            packageBundlingService.handleComponentChanged(metadata.getAddonId(), "ADDON", "STATUS_CHANGED");

            log.info("Đã xử lý thành công addon status changed event - addonId: {}", metadata.getAddonId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý addon status changed event: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xử lý event khi addon được cập nhật
     */
    private void handleAddonUpgradedEvent(Events eventData) {
        try {
            ProductEventMetadata metadata = parseMetadata(eventData.getMetadata(), ProductEventMetadata.class);
            log.info("Xử lý addon upgraded event - addonId: {}, addonDraftId: {}",
                metadata.getAddonId(), metadata.getAddonDraftId());

            // Gọi service để xử lý addon cập nhật
            packageBundlingService.handleComponentChanged(metadata.getAddonId(), "ADDON", "UPGRADED");

            log.info("Đã xử lý thành công addon upgraded event - addonId: {}", metadata.getAddonId());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý addon upgraded event: {}", e.getMessage(), e);
            throw e;
        }
    }

    // ==================== HELPER METHODS ====================

    /**
     * Parse metadata từ Object sang class cụ thể
     */
    private <T> T parseMetadata(Object metadata, Class<T> targetClass) {
        if (targetClass.isInstance(metadata)) {
            return targetClass.cast(metadata);
        } else {
            // Convert từ JSON nếu cần
            return objectMapper.convertValue(metadata, targetClass);
        }
    }
}
