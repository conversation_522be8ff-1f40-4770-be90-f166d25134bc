package com.scheduled.batch.task;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.context.annotation.Description;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.onedx.common.entity.customField.CustomLayout;
import com.onedx.common.repository.exceptionHistory.ExceptionHistoryRepository;
import com.onedx.common.repository.schedule.ScheduleStatisticRepository;
import com.repository.customField.CustomFieldRepository;
import com.repository.customField.CustomLayoutRepository;
import com.repository.notification.FCMTokenRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("batch-cleanup")
@RequiredArgsConstructor
public class CleanupTask {

    private final JdbcTemplate jdbcTemplate;
    private final ScheduleStatisticRepository scheduleStatisticRepository;
    private final FCMTokenRepository fcmTokenRepository;
    private final ExceptionHistoryRepository exceptionHistoryRepository;
    private final CustomFieldRepository customFieldRepository;
    private final CustomLayoutRepository customLayoutRepository;

    @Transactional
    @Async(value = "asyncExecutorSingleton")
    public void removeOldNotifications() {
        long start = System.currentTimeMillis();
        log.info("removeOldNotifications: start");
        String query = "DELETE FROM vnpt_dev.notifications WHERE id IN (SELECT id FROM vnpt_dev.notifications WHERE created_at < NOW() - INTERVAL '2 years' LIMIT 5000)";
        jdbcTemplate.batchUpdate(query);
        long executionTime = System.currentTimeMillis() - start;
        log.info("removeOldNotifications: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime("cleanup", "removeOldNotifications", executionTime);
    }

    @Async(value = "asyncExecutorSingleton")
    public void cleanUpFCMToken() {
        long start = System.currentTimeMillis();
        log.info("cleanUpFCMToken: start");
        fcmTokenRepository.cleanUpFCMToken();
        log.info("cleanUpFCMToken: end");
        scheduleStatisticRepository.updateExecutionTime("cleanup", "cleanUpFCMToken", System.currentTimeMillis() - start);
    }

    @Async(value = "asyncExecutorSingleton")
    public void cleanUpExceptionHistory() {
        long start = System.currentTimeMillis();
        log.info("cleanUpExceptionHistory: start");
        exceptionHistoryRepository.cleanUp();
        log.info("cleanUpExceptionHistory: end");
        scheduleStatisticRepository.updateExecutionTime("cleanup", "asyncExecutorSingleton", System.currentTimeMillis() - start);
    }

    @Transactional
    @Description("Batch method for cleang unused custom fields")
    @Async(value = "asyncExecutorSingleton")
    public void cleanUpCustomFields() {
        long start = System.currentTimeMillis();
        log.info("cleanUpCustomFields: start");
        Set<String> fields = new HashSet<>();
        List<CustomLayout> customLayouts = customLayoutRepository.findAll();
        for (CustomLayout customLayout : customLayouts) {
            fields.addAll(customLayout.getLstCustomField());
            fields.addAll(customLayout.getLstStandardField());
        }
        customFieldRepository.cleanFieldNotUsed(fields);
        log.info("cleanUpCustomFields: end after {} ms", System.currentTimeMillis() - start);
        scheduleStatisticRepository.updateExecutionTime("cleanup", "cleanUpCustomFields", System.currentTimeMillis() - start);
    }
}
