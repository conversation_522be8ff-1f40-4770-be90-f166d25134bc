package com.scheduled.batch.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Description;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.onedx.common.constants.enums.emails.EmailSendStatusEnum;
import com.onedx.common.constants.enums.emails.EmailTemplateTypeEnum;
import com.onedx.common.constants.enums.emails.EmailTypeEnum;
import com.onedx.common.entity.emails.EmailTemplate;
import com.onedx.common.entity.emails.MailSendHistory;
import com.onedx.common.helpers.sendSmsVinaphone.DataCodingEnum;
import com.onedx.common.helpers.sendSmsVinaphone.SendSmsResult;
import com.onedx.common.helpers.sendSmsVinaphone.SendSmsVinaphoneUtils;
import com.onedx.common.helpers.sendSmsVinaphone.SmsVinaphoneConfiguration;
import com.onedx.common.repository.emails.EmailRepository;
import com.onedx.common.repository.emails.mailTemplate.EmailTemplateRepository;
import lombok.extern.slf4j.Slf4j;

@Component("batch-send-sms-innovation")
@Slf4j
@EnableConfigurationProperties(SmsVinaphoneConfiguration.class)
public class SendSmsInnovationTask {

    private final SmsVinaphoneConfiguration configuration;

    private final BlockingQueue<MailSendHistory> listToUpdate = new LinkedBlockingDeque<>();
    private final Map<String, EmailTemplate> mailTemplateMap = new HashMap<>();
    private final EmailTemplateRepository mailTemplateRepository;
    private final EmailRepository emailRepository;

    private final ApplicationEventPublisher applicationEventPublisher;
    private final SendSmsVinaphoneUtils sendSmsUtils;

    @Autowired
    @Qualifier("asyncExecutorSendSMS")
    private Executor executor;

    public SendSmsInnovationTask(SmsVinaphoneConfiguration configuration,
                                 EmailRepository emailRepository,
                                 ApplicationEventPublisher applicationEventPublisher,
                                 SendSmsVinaphoneUtils sendSmsUtils,
                                 EmailTemplateRepository emailTemplateRepository) {
        this.configuration = configuration;
        this.emailRepository = emailRepository;
        this.mailTemplateRepository = emailTemplateRepository;
        this.applicationEventPublisher = applicationEventPublisher;
        this.sendSmsUtils = sendSmsUtils;
    }

    public void initData() {
        List<EmailTemplate> emailTemplateList = mailTemplateRepository.findAllByParentCodeIsNotNullAndSendType(EmailTemplateTypeEnum.SMS.value);
        for (EmailTemplate template : emailTemplateList) {
            mailTemplateMap.put(template.getCode(), template);
        }
    }

    private void updateListSMS() {
        List<MailSendHistory> list = new ArrayList<>();
        listToUpdate.drainTo(list, 50);
        if (list.isEmpty()) return;
        emailRepository.saveAll(list);
    }

    public void putResultToList(MailSendHistory smsSendHistory) {
        try {
            this.listToUpdate.put(smsSendHistory);
        } catch (InterruptedException e) {
            log.error("SendSmsInnovationTask-putResultToList can't put id {} to list: {}", smsSendHistory.getId(), e.getMessage());
        }
    }

    @Async(value = "asyncExecutorSendSMS")
    @EventListener
    public void doorBellEventListener(SendSmsInnovationTask.SendSMSEvent sendEmailEvent) {
        sendEmailEvent.sendSMSAsync();
    }

    @Transactional
    @Async(value = "asyncExecutorReadSMS")
    @Description("Batch method for sending SMS")
    public void sendSMS() {
        if (Boolean.FALSE.equals(configuration.getEnable())) return;

        long start = System.currentTimeMillis();
        log.info("sendSMS: start");
        if (mailTemplateMap.isEmpty()) initData();

        //update kết quả cho các tin nhắn đã được process
        updateListSMS();

        //bắt đầu tìm các sms cần gửi và tiến hành update process và gửi
        if (!(executor instanceof ThreadPoolTaskExecutor))
            return;
        ThreadPoolTaskExecutor taskExecutor = ((ThreadPoolTaskExecutor) executor);
        int remainCapacity = taskExecutor.getThreadPoolExecutor().getQueue().remainingCapacity();
        if (remainCapacity < 50) {
            log.warn("Send mail aborted. Cause by Thread pool queue is full now, remain capacity: {} ", remainCapacity);
            return;
        }
        log.info("Thread pool ActiveCount {}, In Queue {}, Remain Capacity {} ", taskExecutor.getActiveCount(),
                remainCapacity, taskExecutor.getThreadPoolExecutor().getQueue().size());
        Pageable page = PageRequest.of(0, 50, Sort.by("id").ascending());
        Page<MailSendHistory> bySendStatus =
                emailRepository.findBySendStatusAndType(EmailSendStatusEnum.NOT_YET_SENT.value, EmailTypeEnum.SMS.value, page);
        if (bySendStatus == null || bySendStatus.isEmpty()) return;
        List<MailSendHistory> all = bySendStatus.getContent();
        List<Long> ids = all.stream().map(MailSendHistory::getId).collect(Collectors.toList());
        emailRepository.updateStatusByIdIn(ids, EmailSendStatusEnum.PROCESSING.value);

        //bắt đầu gửi bằng bất đồng bộ (sent event)
        all.forEach(send -> applicationEventPublisher.publishEvent(new SendSMSEvent(this, send)));

        log.info("sendSMS: end after {} ms", System.currentTimeMillis() - start);
    }

    public static class SendSMSEvent extends ApplicationEvent {

        public MailSendHistory smsSendHistory;

        private final transient SendSmsInnovationTask task;

        public SendSMSEvent(SendSmsInnovationTask source, MailSendHistory smsSendHistory) {
            super(source);
            this.task = source;
            this.smsSendHistory = smsSendHistory;
        }

        public void sendSMSAsync() {
            EmailTemplate emailTemplate = task.mailTemplateMap.get(smsSendHistory.getSmsTemplateCode());
            if (emailTemplate == null || StringUtils.isBlank(smsSendHistory.getReceiver())) {
                smsSendHistory.setSendStatus(EmailSendStatusEnum.ERROR.value);
                task.putResultToList(smsSendHistory);
                log.error("sendSMSAsync: emailTemplate for smsTemplateCode '{}' is null, sendStatus '{}'",
                    smsSendHistory.getSmsTemplateCode(), smsSendHistory.getSendStatus());
                return;
            }
            SendSmsResult result = task.sendSmsUtils.sendSms(
                    smsSendHistory.getSmsParams(),
                    emailTemplate.getTemplateSmsId(),
                    smsSendHistory.getReceiver(),
                    DataCodingEnum.valueOf(emailTemplate.getSmsDataCoding())
            );
            smsSendHistory.setSmsRequest(result.getRequest());
            smsSendHistory.setSmsResponse(result.getResponse());
            if (Boolean.TRUE.equals(result.getStatus())) {
                smsSendHistory.setSendStatus(EmailSendStatusEnum.SENT.value);
            } else {
                smsSendHistory.setSendStatus(EmailSendStatusEnum.ERROR.value);
            }
            task.putResultToList(smsSendHistory);
        }
    }
}
