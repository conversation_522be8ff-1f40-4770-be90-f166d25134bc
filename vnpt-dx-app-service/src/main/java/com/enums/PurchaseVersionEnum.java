package com.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum PurchaseVersionEnum {
    CREATED_VERSION(0),LATEST_VERSION(1), UNSET(-1);
    private int value;

    PurchaseVersionEnum(int value) {
        this.value = value;
    }
    private static final Map<Integer, PurchaseVersionEnum> map = new HashMap<>();
    static {
        for (PurchaseVersionEnum status : PurchaseVersionEnum.values()) {
            map.put(status.value, status);
        }
    }
    public static PurchaseVersionEnum fromValue(Integer purchaseVersion) {
        return purchaseVersion != null ? valueOf(purchaseVersion) : PurchaseVersionEnum.UNSET;
    }
    public static PurchaseVersionEnum valueOf(int value) {
        return map.get(value);
    }
}
