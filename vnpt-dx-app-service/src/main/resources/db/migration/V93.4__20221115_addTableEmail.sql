DROP TABLE IF EXISTS "vnpt_dev"."mc_notification_email";
CREATE TABLE "vnpt_dev"."mc_notification_email" (
  "id" int8 NOT NULL,
  "email" varchar COLLATE "pg_catalog"."default",
  "status" int2
)
;

-- ----------------------------
-- Records of mc_notification_email
-- ----------------------------
INSERT INTO "vnpt_dev"."mc_notification_email" VALUES (1, '<EMAIL>', 1);
INSERT INTO "vnpt_dev"."mc_notification_email" VALUES (2, '<EMAIL>', 1);

-- ----------------------------
-- Primary Key structure for table mc_notification_email
-- ----------------------------
ALTER TABLE "vnpt_dev"."mc_notification_email" ADD CONSTRAINT "apis_copy1_pkey" PRIMARY KEY ("id");