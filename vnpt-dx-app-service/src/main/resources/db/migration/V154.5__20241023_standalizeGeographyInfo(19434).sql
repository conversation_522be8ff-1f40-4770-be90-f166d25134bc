ALTER TABLE "vnpt_dev"."province"
  ADD COLUMN "region_name" varchar(20),
  ADD COLUMN "nation_name" varchar(100),
  ADD COLUMN "modified_at" timestamp default '2010-10-10 00:00:00',
  ADD COLUMN "deleted_flag" int2 DEFAULT 1;
COMMENT ON COLUMN "vnpt_dev"."province"."modified_at" IS 'Thời gian cập nhật cuối cùng';
COMMENT ON COLUMN "vnpt_dev"."province"."deleted_flag" IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';
COMMENT ON COLUMN "vnpt_dev"."province"."region_name" IS 'Tên vùng/ miền';
COMMENT ON COLUMN "vnpt_dev"."province"."nation_name" IS 'Tên quốc ';

ALTER TABLE "vnpt_dev"."district" 
  ADD COLUMN "modified_at" timestamp,
  ADD COLUMN "deleted_flag" int2 DEFAULT 1;
COMMENT ON COLUMN "vnpt_dev"."district"."modified_at" IS 'Thời gian cập nhật cuối cùng';
COMMENT ON COLUMN "vnpt_dev"."district"."deleted_flag" IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';
COMMENT ON COLUMN "vnpt_dev"."district"."id" IS 'ID quận/ huyện';
COMMENT ON COLUMN "vnpt_dev"."district"."province_id" IS 'ID tỉnh/ thành';
COMMENT ON COLUMN "vnpt_dev"."district"."province_code" IS 'Mã tỉnh/ thành';
COMMENT ON COLUMN "vnpt_dev"."district"."name" IS 'Tên quận/ huyện';
COMMENT ON COLUMN "vnpt_dev"."district"."code" IS 'Mã quận/ huyện';
COMMENT ON COLUMN "vnpt_dev"."district"."id_new" IS 'Id mới của district <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."district"."id_old" IS 'Id cũ của street <to-be-deleted>';
COMMENT ON TABLE "vnpt_dev"."district" IS 'Bảng lưu thông tin quận/ huyện';

ALTER TABLE "vnpt_dev"."ward" 
  ADD COLUMN "modified_at" timestamp,
  ADD COLUMN "deleted_flag" int2 DEFAULT 1;
COMMENT ON COLUMN "vnpt_dev"."ward"."modified_at" IS 'Thời gian cập nhật cuối cùng';
COMMENT ON COLUMN "vnpt_dev"."ward"."deleted_flag" IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';
COMMENT ON COLUMN "vnpt_dev"."ward"."id" IS 'ID xã/ phường';
COMMENT ON COLUMN "vnpt_dev"."ward"."district_id" IS 'ID quận/huyện';
COMMENT ON COLUMN "vnpt_dev"."ward"."name" IS 'Tên xã phường';
COMMENT ON COLUMN "vnpt_dev"."ward"."code" IS 'Mã xã phường';
COMMENT ON COLUMN "vnpt_dev"."ward"."id_new" IS 'Id mới của ward <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."ward"."id_old" IS 'Id cũ của ward <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."ward"."district_id_old" IS 'Id cũ của district <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."ward"."district_id_new" IS 'Id mới của district <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."ward"."province_id" IS 'ID tỉnh/thành phố';
COMMENT ON TABLE "vnpt_dev"."ward" IS 'Bảng lưu thông tin xã/phường/thị trấn';

ALTER TABLE "vnpt_dev"."street" 
  ADD COLUMN "modified_at" timestamp,
  ADD COLUMN "deleted_flag" int2 DEFAULT 1;
COMMENT ON COLUMN "vnpt_dev"."street"."modified_at" IS 'Thời gian cập nhật cuối cùng';
COMMENT ON COLUMN "vnpt_dev"."street"."deleted_flag" IS 'Trạng thái xóa (0: Đã xóa, 1: Chưa xóa)';

COMMENT ON COLUMN "vnpt_dev"."street"."province_code" IS 'Mã tỉnh/ thành';
COMMENT ON COLUMN "vnpt_dev"."street"."id_new" IS 'Id mới của street <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."street"."id_old" IS '<to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."street"."ward_id_old" IS 'Id cũ của ward <to-be-deleted>';
COMMENT ON COLUMN "vnpt_dev"."street"."ward_id_new" IS 'Id mới của ward <to-be-deleted>';
COMMENT ON TABLE "vnpt_dev"."street" IS 'Bảng lưu thông tin đường, phố, ngõ';

update vnpt_dev.province set region_name = regions.name from vnpt_dev.regions where region_id = regions.id;
update vnpt_dev.province set nation_name = nation.name from vnpt_dev.nation where nation_id = nation.id;

INSERT INTO vnpt_dev.schedules(bean_name, method_name, cron_expression, remark, job_status) VALUES
('syncing-geography', 'syncProvince', '0 0/30 2,3,4,5,6,7 * * ?', 'Đồng bộ thông tin địa lý từ ĐHSXKD mỗi 15 ngày', 1);