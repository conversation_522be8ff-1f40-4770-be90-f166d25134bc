insert into vnpt_dev.app_domain (code, type, value, status)
values ('PRICING', 1, 'Trên mỗi người dùng', 1);
insert into vnpt_dev.app_domain (code, type, value, status)
values ('PRICING', 2, 'Không giới hạn người dùng', 1);

INSERT INTO oauth_client_details
(client_id, client_secret, scope, authorized_grant_types,
 web_server_redirect_uri, authorities, access_token_validity,
 refresh_token_validity, additional_information, autoapprove)
VALUES ('vnpt_clientid', /*secret*/'$2a$10$F2dXfNuFjqezxIZp0ad5OeegW43cRdSiPgEtcetHspiNrUCi3iI6O', '',
        'password,authorization_code,refresh_token,client_credentials', null, null, 36000, 36000, null, true);

INSERT INTO oauth_client_details
(client_id, client_secret, scope, authorized_grant_types,
 web_server_redirect_uri, authorities, access_token_validity,
 refresh_token_validity, additional_information, autoapprove)
VALUES ('vnpt_mobileid', /*secret*/'$2a$10$F2dXfNuFjqezxIZp0ad5OeegW43cRdSiPgEtcetHspiNrUCi3iI6O', '',
        'password,authorization_code,refresh_token,client_credentials', null, null, 36000, 36000, null, true);

INSERT INTO vnpt_dev.role(id, deleted_flag, status, description, name)
VALUES (1, 1, 1, 'Super admin', 'ROLE_SUPER_ADMIN');

INSERT INTO vnpt_dev.users(id, deleted_flag, status, account_expired, account_locked, credentials_expired, email,
                           enabled, first_name, last_name, password, user_name)
VALUES (1, 1, 1, false, false, false, '<EMAIL>', true, 'super', 'admin',
           /*admin*/'$2y$12$8rHcYBna5G6wqiYV5yxUWu0UBmCnC/7kkF/6maLL3nG89Q7cQQgTy', 'admin');

INSERT INTO vnpt_dev.users_roles(user_id, role_id)
VALUES (1, 1);

INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (2, null, null, 1, null, null, 1, null, 'ROLE_ADMIN');
INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (3, null, null, 1, null, null, 1, null, 'ROLE_SME');
INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (4, null, null, 1, null, null, 1, null, 'ROLE_DEVELOPER');


SELECT setval('users_id_seq', (SELECT MAX(id) FROM users), true);
SELECT setval('batch_job_id_seq', (SELECT MAX(id) FROM vnpt_dev.batch_job), true);
SELECT setval('role_id_seq', (SELECT MAX(id) FROM vnpt_dev.role), true);


insert into app_domain (code, type, value, status)
values ('PAYMENT_STATUS', 0, 'Chưa thanh toán', 1);
insert into app_domain (code, type, value, status)
values ('PAYMENT_STATUS', 1, 'Chưa thanh toán', 1);
insert into app_domain (code, type, value, status)
values ('PAYMENT_STATUS', 2, 'Chưa thanh toán', 1);

insert into app_domain (code, type, value, status)
values ('ALL_IN', 0, 'Trọn gói', 1);
insert into app_domain (code, type, value, status)
values ('MONTHLY', 1, 'Theo tháng', 1);
insert into app_domain (code, type, value, status)
values ('YEARLY', 2, 'Theo năm', 1);
update app_domain
set code = 'CYCLE'
where code in ('ALL_IN', 'MONTHLY', 'YEARLY');

/*Bảng nation*/
delete
from nation;
insert into nation (id, name)
values (1, 'Việt Nam');
insert into nation (id, name)
values (2, 'Anh');
insert into nation (id, name)
values (3, 'Mỹ');
SELECT setval('nation_id_seq', (SELECT MAX(id) FROM nation), true);

delete
from province;
insert into province (id, name, nation_id, postal_code)
values (1, 'TP. Hồ Chí Minh', 1, '700000');
insert into province (id, name, nation_id, postal_code)
values (2, 'Hà Nội', 1, '100000');
insert into province (id, name, nation_id, postal_code)
values (3, 'Huế', 1, '530000');
insert into province (id, name, nation_id, postal_code)
values (4, 'Đà Nẵng', 1, '550000');
insert into province (id, name, nation_id, postal_code)
values (5, 'Bình Dương', 1, '590000');
insert into province (id, name, nation_id, postal_code)
values (6, 'Bắc Ninh', 1, '790000');
insert into province (id, name, nation_id, postal_code)
values (7, 'Thái Nguyên', 1, '250000');
insert into province (id, name, nation_id, postal_code)
values (8, 'Hải Phòng', 1, '180000');
insert into province (id, name, nation_id, postal_code)
values (9, 'Quảng Ninh', 1, '200000');
insert into province (id, name, nation_id, postal_code)
values (10, 'Đồng Nai', 1, '810000');
insert into province (id, name, nation_id, postal_code)
values (11, 'Thái Bình', 1, '410000');
insert into province (id, name, nation_id, postal_code)
values (12, 'Hải Dương', 1, '170000');
insert into province (id, name, nation_id, postal_code)
values (13, 'Quảng Nam', 1, '560000');
insert into province (id, name, nation_id, postal_code)
values (14, 'Bắc Giang', 1, '220000');
insert into province (id, name, nation_id, postal_code)
values (15, 'Bình Thuận', 1, '800000');
insert into province (id, name, nation_id)
values (16, 'Liverpool', 2);
insert into province (id, name, nation_id)
values (17, 'London', 2);
insert into province (id, name, nation_id)
values (18, 'Manchester', 2);
insert into province (id, name, nation_id)
values (19, 'Birmingham', 2);
insert into province (id, name, nation_id)
values (20, 'Leeds', 2);
insert into province (id, name, nation_id)
values (21, 'Glasgow', 2);
insert into province (id, name, nation_id)
values (22, 'Sheffield', 2);
insert into province (id, name, nation_id)
values (23, 'New York', 3);
insert into province (id, name, nation_id)
values (24, 'Los Angeles', 3);
insert into province (id, name, nation_id)
values (25, 'Chicago', 3);
insert into province (id, name, nation_id)
values (26, 'Houston', 3);
insert into province (id, name, nation_id)
values (27, 'Philadelphia', 3);
insert into province (id, name, nation_id)
values (28, 'Phoenix', 3);
insert into province (id, name, nation_id)
values (29, 'San Antonio', 3);
insert into province (id, name, nation_id)
values (30, 'San Diego', 3);
insert into province (id, name, nation_id)
values (31, 'Dallas', 3);
insert into province (id, name, nation_id)
values (32, 'San Jose', 3);
insert into province (id, name, nation_id)
values (33, 'San Francisco', 3);
-- SELECT setval('province_id_seq', (SELECT MAX(id) FROM province), true);

/*Bảng district*/
delete
from district;

insert into district (id, name, province_id)
values (1, 'Quận 1', 1);
insert into district (id, name, province_id)
values (2, 'Quận 2', 1);
insert into district (id, name, province_id)
values (3, 'Quận Tân Bình', 1);
insert into district (id, name, province_id)
values (4, 'TP. Thủ Đức', 1);
insert into district (id, name, province_id)
values (5, 'Quận Bình Tân', 1);
insert into district (id, name, province_id)
values (6, 'Quận 3', 1);
insert into district (id, name, province_id)
values (7, 'Quận 4', 1);
insert into district (id, name, province_id)
values (8, 'Quận 5', 1);
insert into district (id, name, province_id)
values (9, 'Quận 9', 1);
insert into district (id, name, province_id)
values (10, 'Hoàn Kiếm', 2);
insert into district (id, name, province_id)
values (11, 'Ba Đình', 2);
insert into district (id, name, province_id)
values (12, 'Thanh Xuân', 2);
insert into district (id, name, province_id)
values (13, 'Đống Đa', 2);
insert into district (id, name, province_id)
values (14, 'Hà Đông', 2);
insert into district (id, name, province_id)
values (15, 'Nam Từ Liêm', 2);
insert into district (id, name, province_id)
values (16, 'Cầu Giấy', 2);
insert into district (id, name, province_id)
values (17, 'Hoàng Mai', 2);
insert into district (id, name, province_id)
values (18, 'Sóc Sơn', 2);
insert into district (id, name, province_id)
values (19, 'TP Huế', 3);
insert into district (id, name, province_id)
values (20, 'TX Hương Thủy', 3);
insert into district (id, name, province_id)
values (21, 'TX Hương Trà', 3);
insert into district (id, name, province_id)
values (22, 'Phong Điền', 3);
insert into district (id, name, province_id)
values (23, 'Hoàng Sa', 4);
insert into district (id, name, province_id)
values (24, 'Hòa Vang', 4);
insert into district (id, name, province_id)
values (25, 'Thanh Khê', 4);
insert into district (id, name, province_id)
values (26, 'Sơn Trà', 4);
insert into district (id, name, province_id)
values (27, 'TP. Thủ Dầu Một', 5);
insert into district (id, name, province_id)
values (28, 'Bến Cát', 5);
insert into district (id, name, province_id)
values (29, 'Dầu Tiếng', 5);
insert into district (id, name, province_id)
values (30, 'Dĩ An', 5);
insert into district (id, name, province_id)
values (31, 'Tân Uyên', 5);
SELECT setval('district_id_seq', (SELECT MAX(id) FROM district), true);


insert into business_area (name)
values ('Nông nghiệp');
insert into business_area (name)
values ('Giáo dục');
insert into business_area (name)
values ('Y tế');
insert into business_area (name)
values ('Vận tải');
insert into business_area (name)
values ('Xây dựng');
insert into business_area (name)
values ('Sản xuất');
insert into business_area (name)
values ('Du lịch');
insert into business_area (name)
values ('Bán lẻ');

insert into business_size (name)
values ('Chỉ có bạn');
insert into business_size (name)
values ('2-9');
insert into business_size (name)
values ('10-99');
insert into business_size (name)
values ('100-299');
insert into business_size (name)
values ('300+');
INSERT INTO vnpt_dev.batch_job ("name", cron_time, created_by, created_at, modified_by, modified_at)
VALUES ('history_billing', '0 0/2 * * * ?', NULL, NULL, NULL, NULL);
update business_area
set name = 'Vận chuyển'
where name = 'Vận tải';
insert into business_area (name)
values ('Khác');
delete
from vnpt_dev.nation
where name != 'Việt Nam';

INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (5, null, null, 1, null, null, 1, null, 'ROLE_CUSTOMER_SUPPORT');
INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (6, null, null, 1, null, null, 1, null, 'ROLE_SME_EMPLOYEE');
INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (7, null, null, 1, null, null, 1, null, 'ROLE_DEVELOPER_OPERATOR');
INSERT INTO vnpt_dev.role (id, created_at, created_by, deleted_flag, modified_at, modified_by, status, description,
                           name)
VALUES (8, null, null, 1, null, null, 1, null, 'ROLE_DEVELOPER_BUSINESS');

update vnpt_dev.users u
set parent_id = -1
where email = '<EMAIL>';
INSERT INTO vnpt_dev.schedules (bean_name, method_name, method_params, cron_expression, remark, job_status, created_by,
                                created_at, modified_by, modified_at)
VALUES ('billing', 'dailyCalculator', NULL, '* */5 * * * ?', 'billing', 1, 'batch', NULL, 'batch', NULL);
insert into vnpt_dev.evaluation_criteria (id, name, deleted_flag)
values (1, 'Đáp ứng đầy đủ yêu cầu của Doanh nghiệp', 1),
       (2, 'Tốc độ xử lý nhanh', 1),
       (3, 'Giao diện thân thiện, dễ sử dụng', 1),
       (4, 'Hỗ trợ từ nhà cung cấp nhiệt tình chu đáo', 1);
INSERT INTO vnpt_dev.schedules
(id, bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by,
 modified_at)
VALUES (4, 'send-mail', 'sendMail', NULL, '*/30 * * * * ?', 'send-mail', 1, 'batch', NULL, 'batch', NULL),
       (5, 'send-mail', 'sendMailUpdateProcess', NULL, '* 0 */1 * * ?', 'send-mail', 1, 'batch', NULL, 'batch', NULL);
delete
from vnpt_dev.action_notification;
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (1, 'Quản lý tạo tài khoản và phân quyền', 1, 1, 1, -1, NULL, 'system', '2021-03-18 17:02:16.705', 'system',
        '2021-03-18 17:02:19.543', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (2, 'Admin tạo tài khoản nhóm Admin', 1, 0, 0, 1, 'AC-01', 'system', '2021-03-18 17:02:16.705', 'system',
        '2021-03-18 17:02:19.543', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (3, 'Admin tắt tài khoản nhóm Admin', 1, 0, 0, 1, 'AC-02', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-19 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (4, 'Admin bật tài khoản nhóm Admin', 1, 0, 0, 1, 'AC-03', 'system', '2021-03-18 17:02:16.705', 'system',
        '2021-03-18 17:02:19.543', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (5, 'Admin tắt tài khoản Dev/ SME', 1, 0, 0, 1, 'AC-04', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-22 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (6, 'Admin bật tài khoản Dev/ SME', 1, 0, 0, 1, 'AC-05', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-19 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (7, 'Admin chỉnh sửa thông tin tài khoản Dev/ SME', 1, 0, 0, 1, 'AC-06', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-19 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (8, 'Dev/SME tạo mới tài khoản cho chính mình', 1, 0, 0, 1, 'AC-07', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (9, 'Dev tạo mới tài khoản Dev Busniess/ Dev Operator', 1, 0, 0, 1, 'AC-08', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (10, 'SME tạo mới tài khoản SME Employee', 1, 0, 0, 1, 'AC-09', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (11, 'Dev/ SME Admin tắt tài khoản của nhân viên', 1, 0, 0, 1, 'AC-10', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (12, 'Dev/ SME Admin bật tài khoản của nhân viên', 1, 0, 0, 1, 'AC-11', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (13, 'Reset password thuộc nhóm Admin', 1, 0, 0, 1, 'AC-12', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-29 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (14, 'Reset password thuộc nhóm Dev/ SME', 1, 0, 0, 1, 'AC-13', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-29 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (15, 'Quản lý phiếu hỗ trợ', 1, 1, 1, -1, NULL, 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-29 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (16, 'SME tạo 1 phiếu hỗ trợ mới', 1, 0, 0, 15, 'TK-01', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-29 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (17, 'SME tạo 1 phiếu hỗ trợ mới', 1, 0, 1, 15, 'TK-02', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-29 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (18, 'SME sửa nội dung phiếu hỗ trợ', 1, 0, 0, 15, 'TK-03', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-29 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (19, 'SME sửa nội dung phiếu hỗ trợ', 1, 0, 1, 15, 'TK-04', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (20, 'Admin phân công người hỗ trợ', 1, 0, 1, 15, 'TK-05', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (21, 'Admin phân công người hỗ trợ', 1, 0, 0, 15, 'TK-06', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (22, 'Người dùng bất kỳ nhập phản hồi', 1, 0, 1, 15, 'TK-07', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (23, 'Người dùng bất kỳ nhập phản hồi', 1, 0, 1, 15, 'TK-08', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (24, 'Người dùng bất kỳ nhập phản hồi', 1, 0, 1, 15, 'TK-09', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (25, 'Người dùng bất kỳ sửa phản hồi', 1, 0, 1, 15, 'TK-10', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (26, 'Người dùng bất kỳ sửa phản hồi', 1, 0, 1, 15, 'TK-11', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (27, 'Người dùng bất kỳ sửa phản hồi', 1, 0, 1, 15, 'TK-12', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (28, 'Người dùng bất kỳ xóa phản hồi của mình', 0, 0, 0, 15, 'TK-13', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (29, 'Người dùng bất kỳ xóa phản hồi của mình', 0, 0, 0, 15, 'TK-14', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (30, 'Người dùng bất kỳ xóa phản hồi của mình', 0, 0, 0, 15, 'TK-15', 'system', '2021-03-18 00:00:00.000',
        'system', '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (31, 'Admin xóa phản hồi của người khác', 1, 0, 1, 15, 'TK-16', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (35, 'Admin xóa phản hồi của người khác', 1, 0, 1, 15, 'TK-17', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (36, 'Đóng phiếu hỗ trợ', 1, 0, 1, 15, 'TK-18', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (37, 'Đóng phiếu hỗ trợ', 1, 0, 1, 15, 'TK-19', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (38, 'Đóng phiếu hỗ trợ', 1, 0, 0, 15, 'TK-20', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (39, 'Đánh giá dịch vụ', 1, 1, 1, -1, NULL, 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (40, 'SME tạo đánh giá/ nhận xét', 1, 0, 0, 39, 'EV-01', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (41, 'SME tạo đánh giá/ nhận xét', 1, 0, 1, 39, 'EV-02', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (42, 'SME sửa đánh giá/ nhận xét', 1, 0, 0, 39, 'EV-03', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (43, 'SME sửa đánh giá/ nhận xét', 1, 0, 1, 39, 'EV-04', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (44, 'Dev tạo phản hồi', 1, 0, 1, 39, 'EV-05', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (45, 'Dev tạo phản hồi', 1, 0, 0, 39, 'EV-06', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (46, 'Dev sửa phản hồi', 1, 0, 1, 39, 'EV-07', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (47, 'Dev sửa phản hồi', 1, 0, 1, 39, 'EV-08', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (48, 'Admin xóa nhận xét', 1, 0, 1, 39, 'EV-09', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (49, 'Admin xóa nhận xét', 1, 0, 1, 39, 'EV-10', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (50, 'Admin xóa phản hồi', 1, 0, 1, 39, 'EV-11', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (51, 'Admin xóa phản hồi', 1, 0, 1, 39, 'EV-12', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.action_notification
(id, "name", is_send_email, is_send_sms, is_notification, parent_id, action_code, created_by, created_at, modified_by,
 modified_at, receiver)
VALUES (52, 'Hệ thống gửi nhắc nhở đánh giá', 1, 0, 1, 39, 'EV-13', 'system', '2021-03-18 00:00:00.000', 'system',
        '2021-03-18 00:00:00.000', NULL);
INSERT INTO vnpt_dev.schedules
(id, bean_name, method_name, method_params, cron_expression, remark, job_status, created_by, created_at, modified_by,
 modified_at)
VALUES (6, 'notification', 'autoRemindEvaluation', NULL, '0 0 0 1 * ?', 'notification', 1, 'batch', NULL, 'batch',
        NULL);

UPDATE vnpt_dev.schedules
SET cron_expression='0 0 0 1 * ?'
WHERE method_name = 'sendMailUpdateProcess';

UPDATE vnpt_dev.action_notification
SET receiver='Người được tạo tài khoản'
WHERE action_code = 'AC-01';
UPDATE vnpt_dev.action_notification
SET receiver='Người bị tắt tài khoản'
WHERE action_code = 'AC-02';
UPDATE vnpt_dev.action_notification
SET receiver='Người được bật tài khoản'
WHERE action_code = 'AC-03';
UPDATE vnpt_dev.action_notification
SET receiver='Người bị tắt tài khoản'
WHERE action_code = 'AC-04';
UPDATE vnpt_dev.action_notification
SET receiver='Người được bật tài khoản'
WHERE action_code = 'AC-05';
UPDATE vnpt_dev.action_notification
SET receiver='Người bị chỉnh sửa'
WHERE action_code = 'AC-06';
UPDATE vnpt_dev.action_notification
SET receiver='Người tạo tài khoản'
WHERE action_code = 'AC-07';
UPDATE vnpt_dev.action_notification
SET receiver='Người được tạo tài khoản'
WHERE action_code = 'AC-08';
UPDATE vnpt_dev.action_notification
SET receiver='Người được tạo tài khoản'
WHERE action_code = 'AC-09';
UPDATE vnpt_dev.action_notification
SET receiver='Người bị tắt tài khoản'
WHERE action_code = 'AC-10';
UPDATE vnpt_dev.action_notification
SET receiver='Người được bật tài khoản'
WHERE action_code = 'AC-11';
UPDATE vnpt_dev.action_notification
SET receiver='Chủ tài khoản'
WHERE action_code = 'AC-12';
UPDATE vnpt_dev.action_notification
SET receiver='Chủ tài khoản'
WHERE action_code = 'AC-13';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu hỗ trợ'
WHERE action_code = 'TK-01';
UPDATE vnpt_dev.action_notification
SET receiver='Admin'
WHERE action_code = 'TK-02';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu hỗ trợ'
WHERE action_code = 'TK-03';
UPDATE vnpt_dev.action_notification
SET receiver='Admin'
WHERE action_code = 'TK-04';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu'
WHERE action_code = 'TK-05';
UPDATE vnpt_dev.action_notification
SET receiver='Asignee (người được phân công)'
WHERE action_code = 'TK-06';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu'
WHERE action_code = 'TK-07';
UPDATE vnpt_dev.action_notification
SET receiver='Admin'
WHERE action_code = 'TK-08';
UPDATE vnpt_dev.action_notification
SET receiver='Asignee (người được phân công)'
WHERE action_code = 'TK-09';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu'
WHERE action_code = 'TK-10';
UPDATE vnpt_dev.action_notification
SET receiver='Admin'
WHERE action_code = 'TK-11';
UPDATE vnpt_dev.action_notification
SET receiver='Asignee (người được phân công)'
WHERE action_code = 'TK-12';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu'
WHERE action_code = 'TK-13';
UPDATE vnpt_dev.action_notification
SET receiver='Admin'
WHERE action_code = 'TK-14';
UPDATE vnpt_dev.action_notification
SET receiver='Asignee (người được phân công)'
WHERE action_code = 'TK-15';
UPDATE vnpt_dev.action_notification
SET receiver='Người tạo phản hồi'
WHERE action_code = 'TK-16';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu'
WHERE action_code = 'TK-17';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo phiếu'
WHERE action_code = 'TK-18';
UPDATE vnpt_dev.action_notification
SET receiver='Admin'
WHERE action_code = 'TK-19';
UPDATE vnpt_dev.action_notification
SET receiver='Asignee (người được phân công)'
WHERE action_code = 'TK-20';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo nhận xét'
WHERE action_code = 'EV-01';
UPDATE vnpt_dev.action_notification
SET receiver='Dev admin tạo dịch vụ'
WHERE action_code = 'EV-02';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo nhận xét'
WHERE action_code = 'EV-03';
UPDATE vnpt_dev.action_notification
SET receiver='Dev admin tạo dịch vụ'
WHERE action_code = 'EV-04';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo nhận xét'
WHERE action_code = 'EV-05';
UPDATE vnpt_dev.action_notification
SET receiver='Dev admin tạo dịch vụ'
WHERE action_code = 'EV-06';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo nhận xét'
WHERE action_code = 'EV-07';
UPDATE vnpt_dev.action_notification
SET receiver='Dev admin tạo dịch vụ'
WHERE action_code = 'EV-08';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo nhận xét'
WHERE action_code = 'EV-09';
UPDATE vnpt_dev.action_notification
SET receiver='Dev admin tạo dịch vụ'
WHERE action_code = 'EV-10';
UPDATE vnpt_dev.action_notification
SET receiver='SME tạo nhận xét'
WHERE action_code = 'EV-11';
UPDATE vnpt_dev.action_notification
SET receiver='Dev admin tạo dịch vụ'
WHERE action_code = 'EV-12';
UPDATE vnpt_dev.action_notification
SET receiver='SME đã sử dụng dịch vụ'
WHERE action_code = 'EV-13';
TRUNCATE TABLE vnpt_dev.action_notification RESTART IDENTITY RESTRICT;


INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (1, 'Quản lý tạo tài khoản và phân quyền', -1, 1, 1, 1, NULL, NULL, NULL, NULL, 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', '');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (2, 'Admin tạo tài khoản nhóm Admin', 1, 1, 0, 0, 'AC-01', 'A', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Người được tạo tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (3, 'Admin tắt tài khoản nhóm Admin', 1, 1, 0, 0, 'AC-02', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Người bị tắt tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (4, 'Admin bật tài khoản nhóm Admin', 1, 1, 0, 0, 'AC-03', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Người được bật tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (5, 'Admin tắt tài khoản Dev/ SME', 1, 1, 0, 0, 'AC-04', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Người bị tắt tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (6, 'Admin bật tài khoản Dev/ SME', 1, 1, 0, 0, 'AC-05', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Người được bật tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (7, 'Admin chỉnh sửa thông tin tài khoản Dev/ SME', 1, 1, 0, 0, 'AC-06', 'B', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người bị chỉnh sửa');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (8, 'Dev/SME tạo mới tài khoản cho chính mình', 1, 1, 0, 0, 'AC-07', 'A', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người tạo tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (9, 'Dev tạo mới tài khoản Dev Busniess/ Dev Operator', 1, 1, 0, 0, 'AC-08', 'A', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người được tạo tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (10, 'SME tạo mới tài khoản SME Employee', 1, 1, 0, 0, 'AC-09', 'A', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người được tạo tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (11, 'Dev/ SME Admin tắt tài khoản của nhân viên', 1, 1, 0, 0, 'AC-10', 'B', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người bị tắt tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (12, 'Dev/ SME Admin bật tài khoản của nhân viên', 1, 1, 0, 0, 'AC-11', 'B', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người được bật tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (13, 'Reset password thuộc nhóm Admin', 1, 1, 0, 0, 'AC-12', 'A', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Chủ tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (14, 'Reset password thuộc nhóm Dev/ SME', 1, 1, 0, 0, 'AC-13', 'A', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Chủ tài khoản');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (15, 'Quản lý phiếu hỗ trợ', -1, 1, 1, 1, NULL, NULL, NULL, NULL, 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', '');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (16, 'SME tạo 1 phiếu hỗ trợ mới', 15, 1, 0, 0, 'TK-01', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu hỗ trợ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (17, 'SME tạo 1 phiếu hỗ trợ mới', 15, 1, 0, 1, 'TK-02', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Admin (chăm sóc khách hàng)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (18, 'SME sửa nội dung phiếu hỗ trợ', 15, 1, 0, 0, 'TK-03', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu hỗ trợ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (19, 'SME sửa nội dung phiếu hỗ trợ', 15, 1, 0, 1, 'TK-04', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Admin (chăm sóc khách hàng)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (20, 'Admin phân công người hỗ trợ', 15, 1, 0, 1, 'TK-05', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (21, 'Admin phân công người hỗ trợ', 15, 1, 0, 0, 'TK-06', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Asignee (người được phân công)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (22, 'Người dùng bất kỳ nhập phản hồi', 15, 1, 0, 1, 'TK-07', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (23, 'Người dùng bất kỳ nhập phản hồi', 15, 1, 0, 1, 'TK-08', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Admin (chăm sóc khách hàng)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (24, 'Người dùng bất kỳ nhập phản hồi', 15, 1, 0, 1, 'TK-09', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Asignee (người được phân công)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (25, 'Người dùng bất kỳ sửa phản hồi', 15, 1, 0, 1, 'TK-10', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (26, 'Người dùng bất kỳ sửa phản hồi', 15, 1, 0, 1, 'TK-11', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Admin (chăm sóc khách hàng)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (27, 'Người dùng bất kỳ sửa phản hồi', 15, 1, 0, 1, 'TK-12', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Asignee (người được phân công)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (28, 'Người dùng bất kỳ xóa phản hồi của mình', 15, 0, 0, 0, 'TK-13', 'D', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (29, 'Người dùng bất kỳ xóa phản hồi của mình', 15, 0, 0, 0, 'TK-14', 'D', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Admin (chăm sóc khách hàng)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (30, 'Người dùng bất kỳ xóa phản hồi của mình', 15, 0, 0, 0, 'TK-15', 'D', 'D', 'D', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Asignee (người được phân công)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (31, 'Admin xóa phản hồi của người khác', 15, 1, 0, 1, 'TK-16', 'B', 'D', 'B', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'Người tạo phản hồi');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (35, 'Admin xóa phản hồi của người khác', 15, 1, 0, 1, 'TK-17', 'B', 'D', 'B', 'system',
        '2021-03-18 00:00:00.000', '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (36, 'Đóng phiếu hỗ trợ', 15, 1, 0, 1, 'TK-18', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo phiếu');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (37, 'Đóng phiếu hỗ trợ', 15, 1, 0, 1, 'TK-19', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Admin (chăm sóc khách hàng)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (38, 'Đóng phiếu hỗ trợ', 15, 1, 0, 0, 'TK-20', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Asignee (người được phân công)');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (39, 'Đánh giá dịch vụ', -1, 1, 1, 1, NULL, NULL, NULL, NULL, 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', '');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (40, 'SME tạo đánh giá/ nhận xét', 39, 1, 0, 0, 'EV-01', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo nhận xét');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (41, 'SME tạo đánh giá/ nhận xét', 39, 1, 0, 1, 'EV-02', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Dev admin tạo dịch vụ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (42, 'SME sửa đánh giá/ nhận xét', 39, 1, 0, 0, 'EV-03', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo nhận xét');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (43, 'SME sửa đánh giá/ nhận xét', 39, 1, 0, 1, 'EV-04', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Dev admin tạo dịch vụ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (44, 'Dev tạo phản hồi', 39, 1, 0, 1, 'EV-05', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo nhận xét');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (45, 'Dev tạo phản hồi', 39, 1, 0, 0, 'EV-06', 'B', 'D', 'D', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Dev admin tạo dịch vụ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (46, 'Dev sửa phản hồi', 39, 1, 0, 1, 'EV-07', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo nhận xét');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (47, 'Dev sửa phản hồi', 39, 1, 0, 1, 'EV-08', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Dev admin tạo dịch vụ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (48, 'Admin xóa nhận xét', 39, 1, 0, 1, 'EV-09', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo nhận xét');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (49, 'Admin xóa nhận xét', 39, 1, 0, 1, 'EV-10', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Dev admin tạo dịch vụ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (50, 'Admin xóa phản hồi', 39, 1, 0, 1, 'EV-11', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME tạo nhận xét');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (51, 'Admin xóa phản hồi', 39, 1, 0, 1, 'EV-12', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'Dev admin tạo dịch vụ');
INSERT INTO vnpt_dev.action_notification
(id, "name", parent_id, is_send_email, is_send_sms, is_notification, action_code, allow_change_email, allow_change_sms,
 allow_change_notification, created_by, created_at, modified_by, modified_at, receiver)
VALUES (52, 'Hệ thống gửi nhắc nhở đánh giá', 39, 1, 0, 1, 'EV-13', 'B', 'D', 'B', 'system', '2021-03-18 00:00:00.000',
        '<EMAIL>', '2021-04-05 00:00:00.000', 'SME đã sử dụng dịch vụ');

INSERT INTO vnpt_dev.categories (description, status, deleted_flag, created_by, created_at, modified_by, modified_at,
                                 name)
VALUES ('HR Software', 1, 1, NULL, '2021-04-28 11:51:05.732283', NULL, '2021-04-28 10:29:23.887', 'Phần mềm nhân sự'),
       ('ERP Software', 1, 1, NULL, '2021-04-28 11:51:06.732283', NULL, '2021-04-28 10:51:40.301',
        'Phần mềm quản trị doanh nghiệp'),
       ('Marketing Software', 1, 1, NULL, '2021-04-28 11:51:07.732283', NULL, '2021-04-28 10:01:11.203',
        'Phần mềm tiếp thị'),
       ('Collaboration Software', 1, 1, NULL, '2021-04-28 11:51:08.732283', NULL, '2021-04-28 10:50:38.449',
        'Phần mềm hợp tác doanh nghiệp'),
       ('Payment Gateway', 1, 1, NULL, '2021-04-28 11:51:09.732283', NULL, '2021-04-28 10:23:49.883',
        'Phần mềm thanh toán'),
       ('Sales Software', 1, 1, NULL, '2021-04-28 11:51:10.732283', NULL, '2021-04-28 10:43:56.424',
        'Phần mềm bán hàng'),
       ('Office Software', 1, 1, NULL, '2021-04-28 11:51:12.732283', NULL, '2021-04-28 10:01:00.473',
        'Phần mềm văn phòng'),
       ('Workflow Management Software', 1, 1, NULL, '2021-04-28 11:51:13.732283', NULL, '2021-04-28 09:59:16.36',
        'Phần mềm quản lý quy trình'),
       ('Accounting Software', 1, 1, NULL, '2021-04-28 11:51:14.732283', NULL, '2021-04-28 10:43:52.184',
        'Phần mềm kế toán'),
       ('Other', 1, 1, NULL, '2021-04-28 11:51:21.732283', NULL, '2021-04-28 10:29:21.686', 'Phần mềm khác');

insert into vnpt_dev.system_params (param_name, param_type, param_value)
values ('Cấu hình chương trình khuyến mại', 'COUPON', '1');