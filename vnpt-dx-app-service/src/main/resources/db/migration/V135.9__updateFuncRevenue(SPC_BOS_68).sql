-- C<PERSON><PERSON> nhật func tính doanh thu thực tế theo Target ID

DROP FUNCTION IF EXISTS "vnpt_dev"."func_get_actual_revenue_by_target_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_actual_revenue_by_target_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2)
  RETURNS TABLE("amount" float8, "subcode" varchar) AS $BODY$
DECLARE
sub_type text;
is_employee_code_apply varchar;
is_creator_apply varchar;
is_assignee_apply varchar;
mQuery text;
subApplyQuery text;
subCommonQuery text;
targetTypeText text;
mResult float8;
table_revenue_target text;
BEGIN
CASE
	WHEN object_id IS NULL THEN object_id = -1;
ELSE object_id = object_id;
END CASE;

execute 'SELECT param_text_value
         FROM vnpt_dev.system_params
         WHERE system_params.param_name::text = ''Cấu hình loại thuê bao hiển thị doanh thu mục tiêu''::text' into sub_type;
execute 'SELECT
		     coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isEmployeeCodeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu''' into is_employee_code_apply;
execute 'SELECT
			 coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAccountApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu''' into is_creator_apply;
execute 'SELECT
	         coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAssigneeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu''' into is_assignee_apply;

subApplyQuery = CONCAT('
allAdminDuplicate as (
select
    distinct mValue.admin_id as id
from vnpt_dev.crm_revenue_target as mTarget
    join vnpt_dev.crm_revenue_target_value as mValue on mValue.target_id = mTarget.id and mValue.admin_id is not null
where
    (CAST(''',start_time,''' AS DATE) between mTarget.start_date and mTarget.end_date) or (mTarget.start_date between CAST(''',start_time,''' AS DATE) and CAST(''',end_time,''' AS DATE))
),
subCreatorApply as (
  select distinct subscriptions.id
	from vnpt_dev.subscriptions
	  join allAdminDuplicate on allAdminDuplicate.id = subscriptions.created_by and ', is_creator_apply,'
	where
	  subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
),
mSubApply as (
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join mUserInfo on mUserInfo.user_id = subscriptions.user_id and ', is_assignee_apply, ' = true
	where
		 (subscriptions.id not in (select id from subCreatorApply) and ', is_creator_apply, ' = true) or (', is_creator_apply,' = false)
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join mAdminIdApply on mAdminIdApply.admin_code = subscriptions.employee_code and ', is_employee_code_apply, ' = true
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
	join mAdminIdApply on subscriptions.created_by = mAdminIdApply.id and ', is_creator_apply,'
	where
	subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
), ');

subCommonQuery = CONCAT(
'mSubCommonInfo as (
	select
		billings.id as bill_id,
		case when mSubApply.employee_code is not null then -1
		else mSubApply.user_id end as user_id,
		mSubApply.service_id,
		mSubApply.pricing_id,
		mSubApply.employee_code,
		case
			when billings.action_type = null then 0
			when billings.action_type = -1 then 0
			when billings.action_type = 5 then 1
			else 2
		end as target_type,
		billings.action_type,
		round(COALESCE(bill_item.amount_pre_tax,0)) as amount,
		round(COALESCE(bill_item.amount_after_tax,0)) as amount_after_tax,
		case
			when bill_item.amount_incurred < 0 then 0
			else round(bill_item.amount_incurred)
		end as amount_incurred,
		(billings.total_amount - billings .total_amount_after_adjustment) as refund
	from mSubApply
		left join vnpt_dev.billings on billings.subscriptions_id = mSubApply.id
		left join vnpt_dev.bill_item on bill_item.billing_id = billings.id
	where
	  billings.payment_date between CAST(''',start_time,''' AS DATE) AND CAST(''',end_time + interval '1 day' ,''' AS DATE)
		AND bill_item.object_type <> 3 AND billings.status = 2 AND mSubApply.pricing_id is not null), ');

CASE object_type
WHEN 0
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
				select id as user_id
				from vnpt_dev.users
			      join (
				         select lst_am_id, lst_admin_id
					       from vnpt_dev.crm_data_partition
					       where id in (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =',object_id ,' )
						) as part on users.assignee_id = ANY(part.lst_am_id) or users.assignee_id = ANY(part.lst_admin_id)
		),
		mAdminIdApply as (
				select id, admin_code as admin_code
				from vnpt_dev.users
			      join (
				        select lst_am_id, lst_admin_id
					      from vnpt_dev.crm_data_partition
					      where id in (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =',object_id ,' )
				    ) as part on users.id = ANY(part.lst_am_id) or users.id = ANY(part.lst_admin_id)
		),
		',subApplyQuery,'
		',subCommonQuery,'
        mEmployeeRevenue as (
				select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
	    select distinct
	        mSubCommonInfo.bill_id,
	        	case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
	        mSubCommonInfo.target_type
	    from mSubCommonInfo
		group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
		where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');

WHEN 1
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
			select id as user_id from vnpt_dev.users
			where assignee_id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' )
		),
		mAdminIdApply as (
					select id, admin_code from vnpt_dev.users
			where id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' )
		),
		',subApplyQuery,'
		service_info as (
			select id from vnpt_dev.services
			where id in (select service_id from vnpt_dev.crm_revenue_target_value where target_id =',object_id ,' )
		),',subCommonQuery,'
		mEmployeeRevenue as (
		select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
			mSubCommonInfo.bill_id,
			case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
			mSubCommonInfo.target_type
			from mSubCommonInfo
			join service_info on mSubCommonInfo.service_id = service_info.id and mSubCommonInfo.pricing_id is not null
					group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
					where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
ELSE mQuery = CONCAT(mQuery,
'with mUserInfo as (
   select id as user_id from vnpt_dev.users
   where assignee_id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' )),
 mAdminIdApply as (
   select id, admin_code from vnpt_dev.users
   where id in (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and target_id =', object_id ,' ) and admin_code is not null),
',subApplyQuery,'
',subCommonQuery,'
		mEmployeeRevenue as (
		  select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
				mSubCommonInfo.bill_id,
				case
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
				mSubCommonInfo.target_type
			from mSubCommonInfo
			group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
			where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
END CASE;

case sub_type
when '' then sub_type = '0,1,2';
else sub_type = sub_type;
end case;

CASE target_type
WHEN 0 then mQuery = CONCAT(mQuery,'select round(COALESCE(sum(mEmployeeRevenue.amount),0)), String_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
WHEN 1 then
mQuery = CONCAT(mQuery,'select count(*)::float8, string_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
END CASE;
        RAISE NOTICE 'mQuery: %', mQuery;

return Query execute mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

-- Cập nhật doanh func tính doạnh thu thực tế theo targetValue ID
DROP FUNCTION IF EXISTS "vnpt_dev"."func_get_actual_revenue_by_target_value_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2);
CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_actual_revenue_by_target_value_id"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2)
  RETURNS TABLE("amount" float8, "subcode" varchar) AS $BODY$
DECLARE
sub_type text;
is_employee_code_apply varchar;
is_creator_apply varchar;
is_assignee_apply varchar;
mQuery text;
subApplyQuery text;
subCommonQuery text;
targetTypeText text;
mResult float8;
table_revenue_target text;
BEGIN
CASE
	WHEN object_id IS NULL THEN object_id = -1;
ELSE object_id = object_id;
END CASE;
execute 'SELECT param_text_value
         FROM vnpt_dev.system_params
         WHERE system_params.param_name::text = ''Cấu hình loại thuê bao hiển thị doanh thu mục tiêu''::text' into sub_type;
execute 'SELECT
				   coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isEmployeeCodeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu'''	into is_employee_code_apply;
execute 'SELECT
				   coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAccountApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu'''	into is_creator_apply;
execute 'SELECT
				   coalesce(param_text_value::json -> ''setupRevenueRecognition'' ->> ''isAssigneeApply'', false::varchar)
         FROM vnpt_dev.system_params
         WHERE param_name = ''Cấu hình doanh thu mục tiêu'''	into is_assignee_apply;


subApplyQuery = CONCAT(
'allAdminDuplicate as (
select
    distinct mValue.admin_id as id
from vnpt_dev.crm_revenue_target as mTarget
    join vnpt_dev.crm_revenue_target_value as mValue on mValue.target_id = mTarget.id and mValue.admin_id is not null
where
    (CAST(''',start_time,''' AS DATE) between mTarget.start_date and mTarget.end_date) or (mTarget.start_date between CAST(''',start_time,''' AS DATE) and CAST(''',end_time,''' AS DATE))
),
subCreatorApply as (
		select distinct subscriptions.id
		from vnpt_dev.subscriptions
		join allAdminDuplicate on allAdminDuplicate.id = subscriptions.created_by and ', is_creator_apply,'
		where
		subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
),
mSubApply as (
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join mUserInfo on mUserInfo.user_id = subscriptions.user_id and ', is_assignee_apply, ' = true
	where
		 (subscriptions.id not in (select id from subCreatorApply) and ', is_creator_apply, ' = true) or (', is_creator_apply,' = false)
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
			join adminApplyInfo on adminApplyInfo.admin_code = subscriptions.employee_code and ', is_employee_code_apply, ' = true
	union
	select
			subscriptions.id, subscriptions.sub_code, subscriptions.user_id, subscriptions.service_id, subscriptions.pricing_id, subscriptions.employee_code
	from vnpt_dev.subscriptions
	join adminApplyInfo on subscriptions.created_by = adminApplyInfo.id and ', is_creator_apply,'
	where
	subscriptions.employee_code is null or (', is_employee_code_apply,' = false)
), ');

subCommonQuery = CONCAT(
'mSubCommonInfo as (
	select
		billings.id as bill_id,
		case when mSubApply.employee_code is not null then -1
		else mSubApply.user_id end as user_id,
		mSubApply.service_id,
		mSubApply.pricing_id,
		mSubApply.employee_code,
		case
			when billings.action_type = null then 0
			when billings.action_type = -1 then 0
			when billings.action_type = 5 then 1
			else 2
		end as target_type,
		billings.action_type,
		round(COALESCE(bill_item.amount_pre_tax,0)) as amount,
		round(COALESCE(bill_item.amount_after_tax,0)) as amount_after_tax,
		case
			when bill_item.amount_incurred < 0 then 0
			else round(bill_item.amount_incurred)
		end as amount_incurred,
		(billings.total_amount - billings .total_amount_after_adjustment) as refund
	from mSubApply
		left join vnpt_dev.billings on billings.subscriptions_id = mSubApply.id
		left join vnpt_dev.bill_item on bill_item.billing_id = billings.id
	where billings.payment_date between CAST(''',start_time,''' AS DATE) AND CAST(''',end_time + interval '1 day' ,''' AS DATE)
		AND bill_item.object_type <> 3 AND billings.status = 2 AND mSubApply.pricing_id is not null), ');

CASE object_type
WHEN 0
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
				select id as user_id
				from vnpt_dev.users
			      join (
				         select lst_am_id, lst_admin_id
					       from vnpt_dev.crm_data_partition
					       where id = (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =',object_id ,' )
						) as part on users.assignee_id = ANY(part.lst_am_id) or users.assignee_id = ANY(part.lst_admin_id)
		),
		adminApplyInfo as (
				select id, admin_code as admin_code
				from vnpt_dev.users
			      join (
				        select lst_am_id, lst_admin_id
					      from vnpt_dev.crm_data_partition
					      where id = (select partition_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =',object_id ,' )
				    ) as part on users.id = ANY(part.lst_am_id) or users.id = ANY(part.lst_admin_id)
		),
		',subApplyQuery,'
		',subCommonQuery,'
        mEmployeeRevenue as (
				select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
	    select distinct
	        mSubCommonInfo.bill_id,
	        	case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
	        mSubCommonInfo.target_type
	    from mSubCommonInfo
		group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
		where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');

WHEN 1
    THEN mQuery = CONCAT(mQuery,'
		with mUserInfo as (
			select id as user_id from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =',object_id ,' )
		),
		adminApplyInfo as (
					select id, admin_code from vnpt_dev.users
			where id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =', object_id ,' )
		),
		',subApplyQuery,'
		service_info as (
			select id from vnpt_dev.services
			where id = (select service_id from vnpt_dev.crm_revenue_target_value where id =',object_id ,' )
		),',subCommonQuery,'
		mEmployeeRevenue as (
		select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
			mSubCommonInfo.bill_id,
			case
				when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
			mSubCommonInfo.target_type
			from mSubCommonInfo
			join service_info on mSubCommonInfo.service_id = service_info.id and mSubCommonInfo.pricing_id is not null
					group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
					where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
ELSE mQuery = CONCAT(mQuery,'
		with mUserInfo as (
			select id as user_id from vnpt_dev.users
			where assignee_id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =', object_id ,' )
		),
		adminApplyInfo as (
					select id, admin_code from vnpt_dev.users
			where id = (select admin_id from vnpt_dev.crm_revenue_target_value where target_value is not null and id =', object_id ,' )
		),
		',subApplyQuery,'
		',subCommonQuery,'
		mEmployeeRevenue as (
		  select bill.bill_id, sum(bill.amount) as amount, bill.target_type from (
			select distinct
				mSubCommonInfo.bill_id,
				case
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax = 0 then 0
					when mSubCommonInfo.action_type = 1 and mSubCommonInfo.amount_after_tax <> 0 then
					round(COALESCE(mSubCommonInfo.amount_incurred*(mSubCommonInfo.amount/mSubCommonInfo.amount_after_tax),0))
					else sum(COALESCE(mSubCommonInfo.amount,0)) end as amount,
				mSubCommonInfo.target_type
			from mSubCommonInfo
			group by mSubCommonInfo.bill_id,mSubCommonInfo.amount,mSubCommonInfo.amount_incurred,mSubCommonInfo.amount_after_tax, mSubCommonInfo.target_type,mSubCommonInfo.action_type,mSubCommonInfo.refund) as bill
			where bill.bill_id is not null
			group by bill.bill_id, bill.target_type) ');
END CASE;

case sub_type
when '' then sub_type = '0,1,2';
else sub_type = sub_type;
end case;

CASE target_type
WHEN 0 then mQuery = CONCAT(mQuery,'select round(COALESCE(sum(mEmployeeRevenue.amount),0)), String_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
WHEN 1 then
mQuery = CONCAT(mQuery,'select count(*)::float8, string_agg(distinct mEmployeeRevenue.bill_id :: text,'','')::varchar
									from mEmployeeRevenue
									where mEmployeeRevenue.target_type in (',sub_type,') and mEmployeeRevenue.amount > 0 ');
END CASE;
        RAISE NOTICE 'mQuery: %', mQuery;

return Query execute mQuery;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

-- Cập nhật func lấy preview doanh thu mục tiêu
DROP FUNCTION IF EXISTS "vnpt_dev"."get_lst_target_actual_revenue_source"("is_target" int2, "i_object_id" text, "i_province_id" int8);
CREATE OR REPLACE FUNCTION "vnpt_dev"."get_lst_target_actual_revenue_source"("is_target" int2, "i_object_id" text, "i_province_id" int8)
  RETURNS TABLE("id" int8, "subcode" varchar, "provincename" varchar, "smename" varchar, "address" varchar, "email" varchar, "servicename" varchar, "pricingname" varchar, "numberofcycle" int2, "state" text, "preamounttax" float8, "paymentdate" timestamp, "assigneeid" int8) AS $BODY$
DECLARE
last_query text;
    targetQuery varchar;
    all_bill_selected text;
    tmp_last_query text =
        'SELECT DISTINCT
						t.id,
						t.subCode,
						t.provinceName,
						t.smeName,
						t.address,
						t.email,
						t.serviceName,
						t.pricingName,
						t.numberOfCycle,
						t.state,
						t.preAmountTax,
						t.paymentDate,
						t.assigneeId
        FROM (
            SELECT
							a.id,
							a.subCode,
							a.provinceName,
							a.smeName,
							a.address,
							a.email,
							a.serviceName,
							bill.pricing_name as pricingName,
							a.numberOfCycle,
							a.userId,
							users.assignee_id AS assigneeId,
							COALESCE(bill.payment_date, bill.created_at) AS paymentDate,
							CASE
									WHEN bill.action_type = -1 or bill.action_type = null THEN ''Đăng ký mới''
									WHEN bill.action_type = 1 THEN ''Chỉnh sửa''
									WHEN bill.action_type = 3 or bill.action_type = 4 THEN ''Kích hoạt''
									WHEN bill.bill_action_type = 5 THEN ''Gia hạn''
									ELSE ''Đổi gói''
							END as state,
							CASE
									WHEN bill.action_type = 1 THEN bill_change.amount
									ELSE COALESCE(b2.preAmountTax, 0) END AS preAmountTax,
							COALESCE (
									b2.afterAmountTax,
									-- b.afterAmountTax,
									0
							) AS afterAmountTax
            FROM tmp_table_all_sub a
							JOIN vnpt_dev.users ON users.id = a.userId
							LEFT JOIN tmp_table_bill bill on bill.sub_id = a.id
							LEFT JOIN tmp_table_bill_vnptpay_batch2 b2 ON bill.id = b2.id
							LEFT JOIN tmp_table_bill_change bill_change ON bill_change.id = bill.id
					  WHERE  ( ''-1'' in (%1$s) OR bill.id in (%1$s))) t
            ORDER BY t.paymentDate';

    tmp_all_sub_query VARCHAR = '
    DROP TABLE IF EXISTS tmp_table_all_sub;
    CREATE TEMP TABLE tmp_table_all_sub AS
    SELECT s.id AS id,
			s.sub_code AS subCode,
			s.user_id AS userId,
			CASE
					WHEN u.customer_type = ''KHDN'' THEN u.name
					WHEN u.customer_type = ''CN'' THEN concat(u.last_name,'' '', u.first_name)
					WHEN u.customer_type = ''HKD'' THEN u.name
					ELSE u.name
			END as smeName,
			u.address AS address,
			u.email AS email,
			p.name AS provinceName,
			s2.service_name AS serviceName,
			p2.pricing_name AS pricingName,
			s.number_of_cycles as numberOfCycle
		FROM vnpt_dev.subscriptions s
			LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
			LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
			LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
			LEFT JOIN vnpt_dev.pricing p2 ON s.pricing_id = p2.id
			LEFT JOIN vnpt_dev.services s2 ON p2.service_id = s2.id
		WHERE s.deleted_flag = 1
			AND s.pricing_id NOTNULL
			AND s.confirm_status = 1
			AND (%1$s = -1 OR u.province_id = %1$s)
    UNION ALL
    SELECT s.id AS id,
			s.sub_code AS subCode,
			s.user_id AS userId,
			CASE
					WHEN u.customer_type = ''KHDN'' THEN u.name
					WHEN u.customer_type = ''CN'' THEN concat(u.last_name, '' '',u.first_name)
					WHEN u.customer_type = ''HKD'' THEN u.name
					ELSE u.name
			END as smeName,
			u.address AS address,
			u.email AS email,
			p.name AS provinceName,
			s2.combo_name AS serviceName,
			p2.combo_name AS pricingName,
			s.number_of_cycles AS numberOfCycle
		FROM vnpt_dev.subscriptions s
			LEFT JOIN vnpt_dev.users u ON s.user_id = u.id
			LEFT JOIN vnpt_dev.users u2 ON s.registed_by = u2.id
			LEFT JOIN vnpt_dev.province p ON u.province_id = p.id
			LEFT JOIN vnpt_dev.combo_plan p2 ON s.combo_plan_id = p2.id
			LEFT JOIN vnpt_dev.combo s2 ON p2.combo_id = s2.id
		WHERE s.deleted_flag = 1
			AND s.combo_plan_id NOTNULL
			AND s.confirm_status = 1
			AND (%1$s = -1 OR u.province_id = %1$s)';
    all_sub_query VARCHAR;

-- tmp_table_bill ---
    table_bill_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill;
		CREATE TEMP TABLE tmp_table_bill AS
		SELECT
		  b2.id,
		  b2.action_type,
			p.pricing_name,
			b2.status,
			b2.payment_date,
			b2.created_at,
			b2.billing_code,
			b2.subscriptions_id AS sub_id,
			b2.total_amount,
			b2.billing_date,
			b2.total_amount_after_adjustment,
			inv.created_at as createdExportInvoice,
				inv.code,
			b2.portal_type as portal_type,
			b2.created_by as created_by,
			CASE
							-- bill_action_type: 0: tạo mới, 1: sửa, 2: đổi, 3, 4: kích hoạt lại,  5: gia hạn,
				WHEN b2.action_type = -1 or b2.action_type is null THEN 0 -- thuê bao tạo mới
				WHEN b2.action_type = 1 THEN 2  -- thuê bao sửa
				WHEN b2.action_type = 2 THEN 2	-- thuê bao đổi gói
				WHEN b2.action_type = 3 or b2.action_type = 4 THEN 0	-- thuê bao kích hoạt lại
				WHEN b2.action_type = 5 THEN 5	-- thuê bao gia hạn
				ELSE b2.action_type
			END AS bill_action_type
		FROM (
				SELECT b.id as id,
						b.subscriptions_id AS sub_id
				FROM tmp_table_bill_selected b
				WHERE b.status in (0,1,2,3,4)
				) mb
				JOIN tmp_table_bill_selected b2 ON b2.id = mb.id
				LEFT JOIN (
					SELECT string_agg(code, ''; ''::text) as code, billing_id, max(created_at) as created_at
					FROM vnpt_dev.e_invoice
					GROUP BY billing_id
				) inv ON b2.id = inv.billing_id
				LEFT JOIN vnpt_dev.pricing p on b2.pricing_id = p.id';


-- tmp_table_bill_vnptpay_batch2 --
    table_bill_vnptpay_batch2_query VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_vnptpay_batch2;
		CREATE TEMP TABLE tmp_table_bill_vnptpay_batch2 AS
		SELECT b.id,
			CASE
					WHEN sum(bi2.amount_pre_tax) < 0 THEN 0
					ELSE sum(bi2.amount_pre_tax)
			END AS preAmountTax,
			CASE
					WHEN b.total_amount_after_adjustment < 0 THEN 0
					ELSE b.total_amount_after_adjustment
			END AS afterAmountTax
		FROM tmp_table_bill_selected b
			LEFT JOIN vnpt_dev.bill_item bi2 ON bi2.billing_id = b.id
		WHERE b.status = 2 AND bi2.object_type <> 3
				--	AND b.created_by = ''batch''
		GROUP BY b.id, b.total_amount_after_adjustment';

    bill_selected VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_selected;
		CREATE TEMP TABLE tmp_table_bill_selected AS
		SELECT b.*
		    FROM vnpt_dev.billings b
		    WHERE ( ''-1'' in (%1$s) OR id in (%1$s))';

    bill_change VARCHAR = '
		DROP TABLE IF EXISTS tmp_table_bill_change;
		CREATE TEMP TABLE tmp_table_bill_change AS
		WITH bill_change as (
			SELECT DISTINCT b.id as bill_id, b.subscriptions_id as sub_id,
				round(bill_item.amount_pre_tax) as amount_pre_tax,
				round(bill_item.amount_after_tax) as amount_after_tax,
				CASE
						WHEN bill_item.amount_incurred < 0 THEN 0
						ELSE round(bill_item.amount_incurred)
				END as amount_incurred
			FROM vnpt_dev.billings b
					LEFT JOIN vnpt_dev.bill_item on bill_item.billing_id = b.id
			WHERE  b.action_type = 1 AND bill_item.object_type <> 3
			ORDER BY b.id desc
		)
		SELECT bill.id, sum(bill.amount) as amount
		FROM
				(
					SELECT bill_change.bill_id as id,
						CASE
							WHEN bill_change.amount_after_tax = 0 THEN 0
							ELSE round(COALESCE(bill_change.amount_incurred*(bill_change.amount_pre_tax/bill_change.amount_after_tax),0))
						END as amount
					FROM bill_change
					ORDER BY bill_change.bill_id desc
				) as bill
		GROUP BY bill.id
		ORDER BY bill.id desc';

-- đơn giá và tiền khuyến mãi gia hạn
BEGIN
    all_sub_query = FORMAT(
            tmp_all_sub_query,
            i_province_id
        );
-- Thêm filter quản lý doanh thu
CASE is_target
        WHEN 0 THEN
            CASE i_object_id
                WHEN 'ALL' THEN targetQuery = 'SELECT ''-1'' ';
ELSE targetQuery = concat('
								SELECT COALESCE(string_agg((SELECT
									f.subcode
								FROM vnpt_dev.func_get_actual_revenue_by_target_id(target.object_type, target.start_date, target.end_date, target.id, target.target_type) f(amount, subcode)), '','') , ''-2'')
								FROM vnpt_dev.crm_revenue_target target
                WHERE id IN (', i_object_id, ')');
END CASE;
ELSE
            CASE i_object_id
                WHEN 'ALL' THEN targetQuery = 'SELECT ''-1'' ';
ELSE targetQuery = concat('
								SELECT COALESCE(string_agg((SELECT
									f.subcode
								FROM vnpt_dev.func_get_actual_revenue_by_target_value_id(target.object_type, target.start_date, target.end_date, targetValue.id, target.target_type) f(amount, subcode)), '','') , ''-2'')
								FROM vnpt_dev.crm_revenue_target target
								    LEFT JOIN vnpt_dev.crm_revenue_target_value targetValue ON targetValue.target_id = target.id
                WHERE targetValue.id IN (', i_object_id, ')');
END CASE;
END CASE;
EXECUTE targetQuery INTO i_object_id;
all_bill_selected = FORMAT(bill_selected, i_object_id);

EXECUTE all_bill_selected;
EXECUTE bill_change;
EXECUTE all_sub_query;
EXECUTE table_bill_query;
EXECUTE table_bill_vnptpay_batch2_query;

last_query = format(tmp_last_query, i_object_id);
    RAISE NOTICE 'Last query: %',
        last_query;
RETURN QUERY EXECUTE last_query;
END
$BODY$
LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;
-- Bỏ các func cũ
drop view if exists vnpt_dev.feature_view_get_actual_revenue_sub_code;
DROP FUNCTION IF EXISTS "vnpt_dev"."func_get_actual_revenue_update"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "target_type" int2, "sub_type" text);
DROP FUNCTION IF EXISTS "vnpt_dev"."func_get_email_revenue_update"("object_type" int2, "start_time" date, "end_time" date, "object_id" int8, "sub_type" text);