CREATE OR REPLACE FUNCTION "vnpt_dev"."func_get_address_text"("i_provinceid" int8, "i_districtid" int8, "i_wardid" int8, "i_streetid" int8)
  RETURNS "pg_catalog"."text" AS $BODY$
BEGIN
    RETURN (
        SELECT CONCAT_WS(', ', street_detail.name, ward.name, district.name, province.name)
        FROM
            vnpt_dev.province
            LEFT JOIN vnpt_dev.district ON district.id = i_districtid AND district.province_id = province.id
            LEFT JOIN vnpt_dev.ward ON ward.id = i_wardid AND ward.district_id = district.id AND ward.province_code = province.code
            LEFT JOIN vnpt_dev.street_detail ON street_detail.id = i_streetid AND street_detail.province_code = province.code
        WHERE province.id = i_provinceid
        LIMIT 1
    );
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;