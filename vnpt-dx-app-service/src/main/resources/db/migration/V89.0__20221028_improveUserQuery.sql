Update schedules  set cron_expression ='0 0/20 * * * ?',  job_status=1 where method_name='updateUserSubscriptionStatus';
INSERT INTO schedules( "bean_name", "method_name", "method_params", "cron_expression", "remark", "job_status", "created_by", "created_at", "modified_by", "modified_at") VALUES ('user', 'updateEnterpriseSubscriptionStatus', NULL, '0 0/45 * * * ?', 'user', 1, 'batch', NULL, 'batch', NULL);

alter table users add column last_login timestamp(6) default  null;
UPDATE users 
SET last_login = history.created_at 
FROM
	(
	SELECT
		h.user_id,
		h.created_at 
	FROM
		history_login h
		INNER JOIN ( SELECT MAX ( ID ) AS ID FROM history_login GROUP BY user_id ) u ON h.ID = u.ID 
	) history 
WHERE
	users.ID = history.user_id;


CREATE OR REPLACE VIEW view_enterprise_user_classification_new AS
 WITH user_subs AS (
         SELECT subscriptions.user_id,
            count(subscriptions.id) FILTER (WHERE (subscriptions.status = 1)) AS num_trial,
            count(subscriptions.id) FILTER (WHERE (subscriptions.status = 2)) AS num_active,
            count(subscriptions.id) FILTER (WHERE ((subscriptions.status = 1) OR (subscriptions.status = 2))) AS num_active_or_trial,
            count(subscriptions.id) FILTER (WHERE ((subscriptions.status = 3) OR (subscriptions.status = 4))) AS num_cancel_or_finish
           FROM vnpt_dev.subscriptions
          WHERE ((subscriptions.deleted_flag = 1) AND (subscriptions.confirm_status = 1))
          GROUP BY subscriptions.user_id
        ), user_classification AS (
         SELECT users.id AS user_id,
            users.tin,
            users.email,
            users.phone_number,
            users.customer_type,
            users.status,
            users.created_at,
            user_subs.num_active_or_trial,
            user_subs.num_cancel_or_finish,
            users.provider_type,
            user_subs.num_trial,
            user_subs.num_active,
                CASE
                    WHEN (user_subs.num_active_or_trial > 0) THEN 0
                    WHEN ((user_subs.num_active_or_trial = 0) AND (user_subs.num_cancel_or_finish > 0) AND ((users.last_login)::date IS NOT NULL) AND ((users.last_login)::date < ((now() - '90 days'::interval))::date)) THEN 1
                    ELSE 2
                END AS type,
            users.migrate_time,
            users.migrate_code
           FROM ((vnpt_dev.users
             LEFT JOIN user_subs ON ((users.id = user_subs.user_id)))
             )
          WHERE ((users.deleted_flag = 1) AND (users.parent_id = '-1'::integer))
        ), user_enterprise_mapping AS (
         SELECT DISTINCT ON (enterprise_1.id) enterprise_1.id AS enterprise_id,
            users.id AS user_id
           FROM (vnpt_dev.enterprise enterprise_1
             JOIN vnpt_dev.users ON (((COALESCE(users.deleted_flag, 1) = 1) AND (users.parent_id = '-1'::integer) AND ((COALESCE(users.customer_type, 'KHDN'::character varying))::text = (COALESCE(enterprise_1.customer_type, 'KHDN'::character varying))::text) AND ((((COALESCE(users.customer_type, 'KHDN'::character varying))::text = ANY (ARRAY[('KHDN'::character varying)::text, ('HKD'::character varying)::text])) AND ((users.tin)::text = (enterprise_1.tin)::text)) OR (((users.customer_type)::text = 'CN'::text) AND (users.provider_type = ANY (ARRAY[0, 1, 2])) AND ((users.email)::text = (enterprise_1.email)::text)) OR (((users.customer_type)::text = 'CN'::text) AND (users.provider_type = 3) AND ((users.phone_number)::text = (enterprise_1.phone)::text))))))
          ORDER BY enterprise_1.id, users.id DESC
        ), user_enterprise_mapping_without_duplicating AS (
         SELECT DISTINCT ON (user_enterprise_mapping.user_id) user_enterprise_mapping.enterprise_id,
            user_enterprise_mapping.user_id
           FROM user_enterprise_mapping
          ORDER BY user_enterprise_mapping.user_id, user_enterprise_mapping.enterprise_id DESC
        )
 SELECT enterprise.id AS enterprise_id,
    user_classification.user_id,
    (COALESCE(user_classification.num_active_or_trial, (0)::bigint) + COALESCE(user_classification.num_cancel_or_finish, (0)::bigint)) AS num_subs,
    COALESCE(user_classification.num_trial, (0)::bigint) AS num_trial,
    COALESCE(user_classification.num_active, (0)::bigint) AS num_active,
    user_classification.type,
    user_classification.status,
    user_classification.created_at,
    user_classification.migrate_time,
    user_classification.migrate_code
   FROM ((user_classification
     JOIN user_enterprise_mapping_without_duplicating ON ((user_enterprise_mapping_without_duplicating.user_id = user_classification.user_id)))
     JOIN vnpt_dev.enterprise ON ((user_enterprise_mapping_without_duplicating.enterprise_id = enterprise.id)))
  WHERE (COALESCE((enterprise.deleted_flag)::integer, 1) = 1);