-- add api Thong tin lien he cua khach hang ca nhan tiem nang
INSERT INTO vnpt_dev.apis
(id, api_path, api_code, method)
VALUES ((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/update/update-information-contact',
        'ROLE_ADMIN_UPDATE_CONTACT_PERSONAL', 'PUT'),
        ((SELECT max(id) + 2 FROM vnpt_dev.apis), '/api/admin-portal/crm/enterprise-mgmt/details/information-contact-personal',
        'ROLE_ADMIN_GET_CONTACT_PERSONAL_DETAIL', 'GET');

INSERT INTO vnpt_dev.api_permission
(api_id, permission_id)
VALUES ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/crm/enterprise-mgmt/update/update-information-contact'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_KHACH_HANG_CAP_NHAT')),
       ((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/admin-portal/crm/enterprise-mgmt/details/information-contact-personal'),
        (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_KHACH_HANG_XEM_CHI_TIET'));

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;