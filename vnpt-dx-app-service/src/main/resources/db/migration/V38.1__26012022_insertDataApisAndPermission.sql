INSERT INTO vnpt_dev.apis
(id, api_path, api_code, method)
VALUES 
((SELECT max(id) + 1 FROM vnpt_dev.apis), '/api/dev-portal/services/delete', 'ROLE_DEV_DELETE_SERVICE', 'DELETE');

INSERT INTO vnpt_dev."permission"
(id, "name", code, parent_id, priority)
VALUES 
((SELECT max(id) + 1 FROM vnpt_dev."permission"), 'Xóa dịch vụ', 'XOA_DICH_VU_1', (SELECT id FROM vnpt_dev."permission" WHERE code = 'QUAN_LY_DICH_VU_1'), 9993775);

INSERT INTO vnpt_dev.api_permission
(api_id, permission_id)
VALUES 
((SELECT id FROM vnpt_dev.apis WHERE api_path = '/api/dev-portal/services/delete'), (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_DICH_VU_1'));

INSERT INTO vnpt_dev.roles_permissions
(role_id, permission_id, allow_edit)
VALUES
(10, (SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_DICH_VU_1'), 1);

INSERT INTO vnpt_dev.permission_portal 
(permission_id, portal_id)
VALUES
((SELECT id FROM vnpt_dev."permission" WHERE code = 'XOA_DICH_VU_1'), 2);

REFRESH MATERIALIZED VIEW CONCURRENTLY vnpt_dev.role_permission_api;