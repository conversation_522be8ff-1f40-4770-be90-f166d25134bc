package com.dto.users;

import java.util.List;
import javax.validation.constraints.NotNull;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeIdsReqDTO {

    @Schema(description = "Danh sách ID nhân viên cần xóa")
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    List<Long> lstEmployeeId;

}
