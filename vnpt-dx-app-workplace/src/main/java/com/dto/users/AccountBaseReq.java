package com.dto.users;

import java.util.Set;
import javax.validation.constraints.Pattern;
import com.onedx.common.constants.values.LoginConst;
import com.onedx.common.exception.MessageKeyConstant;
import lombok.Data;

@Data
public class AccountBaseReq {

    private Long id;

    @Pattern(regexp = LoginConst.EMAIL_REGEX, message = MessageKeyConstant.Validation.EMAIL)
    private String email;

    private Set<Long> wpRoles;

    private String firstName;

    private String lastName;


}
