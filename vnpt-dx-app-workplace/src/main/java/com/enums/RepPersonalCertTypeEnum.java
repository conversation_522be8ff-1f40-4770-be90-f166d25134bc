package com.enums;

import java.util.HashMap;
import java.util.Map;

public enum RepPersonalCertTypeEnum {

    CMTND_CCCD(1), PASSPORT(2), OTHER(4), CCCD(3), ALL(-1);

    public Integer type;

    RepPersonalCertTypeEnum(Integer type) {
        this.type = type;
    }

    private static final Map<Integer, RepPersonalCertTypeEnum> map = new HashMap<>();

    static {
        for (RepPersonalCertTypeEnum status : RepPersonalCertTypeEnum.values()) {
            map.put(status.type, status);
        }
    }

    public static RepPersonalCertTypeEnum valueOf(Integer value) {
        return map.get(value);
    }
}
