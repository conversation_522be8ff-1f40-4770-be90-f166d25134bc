package com.repository.user;


import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import com.entity.user.WPMetadataUser;

public interface WPMetadataUserRepository extends JpaRepository<WPMetadataUser, Long> {

    Optional<WPMetadataUser> findByUserUUID(UUID userDBUUID);

    Optional<WPMetadataUser> findByUserUUIDAndSmeUUIDAndApiKey(UUID employeeUUID, UUID smeUUID, String apiKey);
}
