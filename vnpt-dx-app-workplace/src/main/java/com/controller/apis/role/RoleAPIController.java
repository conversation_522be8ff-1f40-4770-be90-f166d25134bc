package com.controller.apis.role;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.component.BaseController;
import com.onedx.common.constants.values.APIKeyConst;
import com.constant.enums.common.StatusEnum;
import com.dto.apis.ThirdPartyAPIResDTO;
import com.dto.apis.ThirdPartyErrorAPIResDTO;
import com.dto.apis.role.request.RoleAPIReqDTO;
import com.service.apis.role.RoleAPIService;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Controller
@Slf4j
@RestController
@RequestMapping("/api/v1/partner")
public class RoleAPIController {

    @Autowired
    private RoleAPIService roleAPIService;

    /**
     * Tạo vai trò - Create role
     * @param roleAPIReqDTO Thông tin vcai trò đầu vào
     * @return Mã lỗi + Thông tin vai trò
     */
    @PreAuthorize("@dynamicAccessAspect.hasPermission(#apiKey, 'ROLE_CREATE_3RD_PARTY')")
    @PostMapping("/{smeUUID}/roles")
    public ThirdPartyAPIResDTO createRoleApi(
        @PathVariable UUID smeUUID,
        @RequestBody RoleAPIReqDTO roleAPIReqDTO,
        @RequestHeader(name = APIKeyConst.API_KEY_HEADER, required = false) String apiKey
    ) {
        roleAPIReqDTO.setApiKey(apiKey);
        roleAPIReqDTO.setSmeUUID(smeUUID);
        return roleAPIService.createRoleApi(roleAPIReqDTO);
    }

    /**
     * Xóa vai trò
     * @param roleCode Id vai trò
     * @return Mã lỗi
     */
    @PreAuthorize("@dynamicAccessAspect.hasPermission(#apiKey, 'ROLE_DELETE_3RD_PARTY')")
    @DeleteMapping("/{smeUUID}/roles/{roleCode}")
    public ThirdPartyErrorAPIResDTO deleteRoleByCodeApi(
        @PathVariable UUID smeUUID,
        @PathVariable String roleCode,
        @RequestHeader(name = APIKeyConst.API_KEY_HEADER, required = false) String apiKey
    )
    {
        return roleAPIService.deleteRoleByCodeApi(smeUUID, roleCode, apiKey);
    }

    /**
     * Xem chi tiết vai trò
     * @param roleCode mã vai trò
     * @return Mã lỗi
     */
    @PreAuthorize("@dynamicAccessAspect.hasPermission(#apiKey, 'ROLE_GET_DETAIL_3RD_PARTY')")
    @GetMapping("/{smeUUID}/roles/{roleCode}")
    public ThirdPartyErrorAPIResDTO getRoleDetailApi(
        @PathVariable UUID smeUUID,
        @PathVariable String roleCode,
        @RequestHeader(name = APIKeyConst.API_KEY_HEADER, required = false) String apiKey
    )
    {
        return roleAPIService.getRoleDetailApi(smeUUID, roleCode, apiKey);
    }


    /**
     * Cập nhật vai trò
     * @param roleAPIReqDTO Thông tin vai trò cập nhật
     * @param roleCode Id vai trò
     * @return Mã lỗi + Thông tin vai trò
     */
    @PreAuthorize("@dynamicAccessAspect.hasPermission(#apiKey, 'ROLE_UPDATE_3RD_PARTY')")
    @PutMapping("/{smeUUID}/roles/{roleCode}")
    public ThirdPartyAPIResDTO putRoleApi(
        @PathVariable UUID smeUUID,
        @RequestBody RoleAPIReqDTO roleAPIReqDTO,
        @PathVariable String roleCode,
        @RequestHeader(name = APIKeyConst.API_KEY_HEADER, required = false) String apiKey
    ) {
        roleAPIReqDTO.setApiKey(apiKey);
        return roleAPIService.updateRoleApi(smeUUID, roleCode, roleAPIReqDTO);
    }

    /**
     * Lấy danh sách quyền của vai trò
     * @param roleCode Id vai trò
     * @return Mã lỗi + Thông tin vai trò
     */
    @PreAuthorize("@dynamicAccessAspect.hasPermission(#apiKey, 'ROLE_GET_LIST_PERMISSION_3RD_PARTY')")
    @GetMapping("/{smeUUID}/roles/{roleCode}/permissions")
    public ThirdPartyAPIResDTO getLstPermissionApi(
        @PathVariable UUID smeUUID,
        @PathVariable String roleCode,
        @RequestHeader(name = APIKeyConst.API_KEY_HEADER, required = false) String apiKey
    ) {
        return roleAPIService.getLstPermissionApi(smeUUID, roleCode, apiKey);
    }

    @PreAuthorize("@dynamicAccessAspect.hasPermission(#apiKey, 'ROLE_GET_LIST_PERMISSION_3RD_PARTY')")
    @GetMapping("/{smeUUID}/roles")
    public ThirdPartyAPIResDTO getRolePageApi(
        @PathVariable UUID smeUUID,
        @RequestHeader(name = APIKeyConst.API_KEY_HEADER, required = false) String apiKey,
        @RequestParam(required = false, defaultValue = "") String roleName,
        @RequestParam(required = false, defaultValue = "ALL") StatusEnum status,
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @RequestParam(required = false, defaultValue = "10") Integer size,
        @RequestParam(required = false, defaultValue = "id,desc") String sort
    ) {
        BaseController.ListRequest requestPageable = new BaseController.ListRequest(size, page, sort);
        return roleAPIService.getRolePageApi(smeUUID,roleName, status, apiKey, requestPageable.getPageable());
    }

}
