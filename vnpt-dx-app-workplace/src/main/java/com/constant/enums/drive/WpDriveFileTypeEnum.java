package com.constant.enums.drive;

import com.google.common.io.Files;

import java.util.Objects;

public enum WpDriveFileTypeEnum {
    FOLDER(0), OTHER(1), DOC(2), XLS(3), PPT(4), PIC(5), PDF(6), SOUND(7), ZIP(8), VIDEO(9);

    public final int value;

    WpDriveFileTypeEnum(int label) {
        this.value = label;
    }

    public static WpDriveFileTypeEnum findById(int value) {
        for (WpDriveFileTypeEnum v : values()) {
            if (v.value == value) {
                return v;
            }
        }
        return null;
    }

    public static WpDriveFileTypeEnum findByFileExtension(String fileName) {
        String fileExtension = Files.getFileExtension(Objects.requireNonNull(fileName)).toUpperCase();
        switch (fileExtension) {
            case "":
                return FOLDER;
            case "DOC":
            case "DOCX":
            case "TXT":
            case "RTF":
            case "DOCM":
            case "DOT":
            case "DOTX":
            case "DOTM":
            case "WBK":
            case "WPD":
            case "WP5":
                return DOC;
            case "XLS":
            case "XLSX":
            case "XLSM":
            case "XLA":
            case "XLAM":
            case "XLL":
            case "XLM":
            case "XLT":
            case "XLTM":
            case "XLTX":
            case "XLW":
                return XLS;
            case "PPT":
            case "PPTX":
            case "PPTM":
            case "PPS":
            case "PPSX":
            case "PPSM":
            case "POT":
            case "POTX":
            case "POTM":
            case "PPA":
            case "PPAM":
            case "ODP":
            case "KEY":
                return PPT;
            case "PNG":
            case "GIF":
            case "JPEG":
            case "JPG":
            case "TIFF":
            case "WEBP":
            case "ICO":
            case "BMP":
            case "SVG":
            case "EPS":
            case "PSD":
            case "TGA":
            case "HEIC":
            case "HEIF":
            case "JFIF":
            case "JPE":
                return PIC;
            case "PDF":
            case "PS":
                return PDF;
            case "MP3":
            case "WAV":
            case "FLAC":
            case "M4A":
            case "AAC":
            case "OGG":
            case "WMA":
            case "AIFF":
            case "ALAC":
            case "APE":
            case "AU":
            case "MID":
            case "MIDI":
            case "MPA":
            case "MP2":
            case "MP1":
            case "MPC":
            case "RM":
            case "SND":
            case "VOC":
            case "VOX":
            case "WV":
            case "IT":
            case "MOD":
            case "MTM":
            case "UMX":
            case "MO3":
            case "OGA":
                return SOUND;
            case "ZIP":
            case "RAR":
            case "7Z":
            case "TAR":
            case "GZ":
            case "BZ2":
            case "XZ":
            case "TAR.GZ":
            case "TAR.BZ2":
            case "TAR.XZ":
                return ZIP;
            case "AVI":
            case "FLV":
            case "WMV":
            case "MOV":
            case "MPG":
            case "MPEG":
            case "MP4":
            case "MKV":
            case "WEBM":
            case "3GP":
            case "OGV":
            case "OGM":
            case "VOB":
                return VIDEO;
            default:
                return OTHER;
        }
    }
}
