package com.onedx.bos.repository.schedule;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import com.onedx.bos.entity.schedule.Schedule;

@Repository
public interface ScheduleRepository extends JpaRepository<Schedule, Long> {

    void deleteById(@NonNull Long id);

    Optional<Schedule> getById(Long id);

    Optional<List<Schedule>> findAllByJobStatus(Integer status);
}
