package com.onedx.bos.scheduled;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.config.CronTask;
import org.springframework.stereotype.Component;
import com.onedx.bos.constants.enums.schedule.ScheduleStatusEnum;
import com.onedx.bos.entity.schedule.Schedule;
import com.onedx.bos.repository.schedule.ScheduleRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@Data
public final class CronTaskRegistrar implements DisposableBean {

    public static final int INITIAL_CAPACITY = 16;
    private final Map<Runnable, ScheduledTask> scheduledTasks = new ConcurrentHashMap<>(INITIAL_CAPACITY);
    private final TaskScheduler taskScheduler;
    private final ScheduleRepository scheduleRepository;
    private final Environment environment;

    @Autowired
    public CronTaskRegistrar(TaskScheduler taskScheduler,
        ScheduleRepository scheduleRepository,
        Environment environment) {
        this.taskScheduler = taskScheduler;
        this.scheduleRepository = scheduleRepository;
        this.environment = environment;
    }

    public TaskScheduler getScheduler() {
        return this.taskScheduler;
    }

    public void addCronTask(Runnable task, String cronExpression) {
        addCronTask(new CronTask(task, cronExpression));
    }

    private void addCronTask(CronTask cronTask) {
        if (cronTask != null) {
            Runnable task = cronTask.getRunnable();
            if (this.scheduledTasks.containsKey(task)) {
                removeCronTask(task);
            }
            this.scheduledTasks.put(task, scheduleCronTask(cronTask));
        }
    }

    private void removeCronTask(CronTask cronTask) {
        if (cronTask != null) {
            Runnable task = cronTask.getRunnable();
            if (this.scheduledTasks.containsKey(task)) {
                removeCronTask(task);
            }
            this.scheduledTasks.put(task, scheduleCronTask(cronTask));
        }
    }

    public void removeCronTask(Runnable task) {
        ScheduledTask scheduledTask = this.scheduledTasks.remove(task);
        if (scheduledTask != null) {
            scheduledTask.cancel();
        }
    }

    public ScheduledTask scheduleCronTask(CronTask cronTask) {
        ScheduledTask scheduledTask = new ScheduledTask();
        scheduledTask.future = this.taskScheduler.schedule(cronTask.getRunnable(), cronTask.getTrigger());

        return scheduledTask;
    }

    @PostConstruct
    public void postConstruct() {
        String batchStatus = environment.getProperty("batch.active");
        if (Objects.isNull(batchStatus) || StringUtils.equals(batchStatus, "true")) {
            List<Schedule> schedules = scheduleRepository
                .findAllByJobStatus(ScheduleStatusEnum.NORMAL.ordinal())
                .orElse(new ArrayList<>());

            if (schedules.isEmpty()) {
                log.warn("schedule is empty");
            }

            schedules.forEach(schedule -> {
                SchedulingRunnable schedulingRunnable = SchedulingRunnable
                    .builder()
                    .beanName(schedule.getBeanName())
                    .methodName(schedule.getMethodName())
                    .params(schedule.getMethodParams())
                    .build();
                addCronTask(schedulingRunnable, schedule.getCronExpression());
            });
        }
    }

    @Override
    public void destroy() throws Exception {
        for (ScheduledTask task : this.scheduledTasks.values()) {
            task.cancel();
        }
        this.scheduledTasks.clear();
    }

}
