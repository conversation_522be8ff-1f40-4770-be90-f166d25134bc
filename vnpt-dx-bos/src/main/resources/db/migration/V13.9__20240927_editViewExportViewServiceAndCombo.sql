-- vnpt_dev.export_view_service_and_combo source

CREATE OR REPLACE VIEW vnpt_dev.export_view_service_and_combo
AS SELECT services.id,
    concat(services.id, '0000')::bigint AS unique_id,
        CASE
            WHEN services.service_owner = 0 OR services.service_owner = 1 THEN concat('(ON) ', services.service_name)
            ELSE concat('(OS) ', services.service_name)
        END AS name,
    services.user_id,
        CASE
            WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 1
            WHEN services.service_owner = ANY (ARRAY[2, 3]) THEN 2
            ELSE NULL::integer
        END AS subscription_type,
        CASE
            WHEN services.service_owner = ANY (ARRAY[0, 1]) THEN 1
            WHEN services.service_owner = ANY (ARRAY[2, 3]) THEN 2
            ELSE NULL::integer
        END AS sub_type,
    services.categories_id,
    services.approve,
    services.status,
    NULL::character varying AS categories_ids,
    services.created_at,
    services.modified_at
   FROM vnpt_dev.services
  WHERE services.deleted_flag = 1
UNION
 SELECT combo.id,
    concat(combo.id, '0001')::bigint AS unique_id,
    concat('(Combo) ', combo.combo_name) AS name,
    combo.user_id,
    3 AS subscription_type,
        CASE
            WHEN combo.combo_owner = ANY (ARRAY[0, 1]) THEN 1
            WHEN combo.combo_owner = ANY (ARRAY[2, 3]) THEN 2
            ELSE NULL::integer
        END AS sub_type,
    NULL::bigint AS categories_id,
    combo.approve,
    combo.status,
    combo.categories_id AS categories_ids,
    combo.created_at,
    combo.modified_at
   FROM vnpt_dev.combo JOIN (
       SELECT max(cmax.id) AS latest_id,
           cmax.combo_draft_id
       FROM vnpt_dev.combo cmax
       WHERE cmax.deleted_flag = 1
       GROUP BY cmax.combo_draft_id) latest_combo ON combo.id = latest_combo.latest_id
  WHERE combo.deleted_flag = 1
UNION
 SELECT service_group.id,
    concat(service_group.id, '0004')::bigint AS unique_id,
    concat('(Nhóm dịch vụ) ', service_group.name) AS name,
    service_group.user_id,
    4 AS subscription_type,
        CASE
            WHEN service_group.group_service_owner = ANY (ARRAY[0, 1]) THEN 1
            WHEN service_group.group_service_owner = ANY (ARRAY[2, 3]) THEN 2
            ELSE NULL::integer
        END AS sub_type,
    NULL::bigint AS categories_id,
    service_group.approve,
    service_group.status,
    replace(replace(service_group.categories_id::text, '{'::text, ''::text), '}'::text, ''::text) AS categories_ids,
    service_group.created_at,
    service_group.modified_at
   FROM vnpt_dev.service_group
  WHERE service_group.deleted_flag = 1 AND (service_group.id IN ( SELECT max(service_group_1.id) AS max
           FROM vnpt_dev.service_group service_group_1
          GROUP BY service_group_1.group_service_draft_id));