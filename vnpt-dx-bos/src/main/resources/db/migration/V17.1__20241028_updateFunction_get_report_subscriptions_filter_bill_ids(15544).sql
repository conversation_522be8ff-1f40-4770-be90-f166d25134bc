CREATE OR REPLACE FUNCTION "vnpt_dev"."get_report_subscriptions_filter_bill_ids"("is_target" int4, "i_customer_email" varchar, "i_customer_name" varchar, "i_object_id" varchar, "i_province_id" int8, "i_customer_type" varchar, "i_createdsource" int4, "i_migrate_start_date" varchar, "i_migrate_end_date" varchar, "i_migrate_codes" varchar, "i_start_date" varchar, "i_end_date" varchar, "i_status" int4, "i_service_id" int8, "i_combo_ids" varchar, "i_pricing_id" int8, "i_pricing_ids" varchar, "i_combo_plan_ids" varchar, "i_subscription_type" varchar, "i_category_service" int8, "i_category_combo" varchar, "i_employee_code" varchar, "i_subscription_state" int4, "i_creator" varchar, "i_cancelled_time_start" varchar, "i_cancelled_time_end" varchar, "i_affiliate_code" varchar, "i_provider_ids" varchar, "i_aff_agency_user_ids" varchar, "i_service_owner" varchar, "i_start_payment_date" varchar, "i_end_payment_date" varchar, "i_start_installed_date" varchar, "i_end_installed_date" varchar)
  RETURNS TABLE("subid" int8, "billid" int8) AS $BODY$

DECLARE
run_query text;
    raw_query
varchar = 'WITH subReportCTE AS (
    SELECT
                     distinct
           sub.id AS id,
           bill.id AS billId,
           sme.name AS smeName,
           sme.email AS email,
           sub.migrate_time AS migrateTime,
           CASE
               WHEN bill.bill_action_type = 3 THEN 0
               ELSE bill.bill_action_type
           END AS subscriptionState,
           CASE
               WHEN  COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) then ''ON''
               ELSE ''OS''
           END AS serviceOwner,
                     sub.installed_time as installedTime,
                     bill.payment_date as paymentTime,
                     CASE
               WHEN bill.bill_action_type = 0 THEN sub.created_at
               ELSE COALESCE(bill.payment_date, bill.created_at)
           END AS registrationDate,
           CASE
               WHEN sub.traffic_id IS NULL THEN (
                    CASE
                         WHEN sub.portal_type = 1 THEN concat(''Admin - '', register.email)
                         WHEN sub.portal_type = 2 THEN concat(''Dev - '', register.email)
                         WHEN sub.portal_type = 3 THEN ''OneSME''
                         ELSE ''''
                    END
                )
               WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id
               ELSE sub.traffic_user
           END AS creator,
           CASE
                WHEN sub.created_source_migration = 1 THEN 5
                WHEN sub.traffic_source = ''accesstrade'' THEN 6
                WHEN sub.traffic_source = ''apinfo'' THEN 8
                WHEN sub.affiliate_one IS NOT NULL THEN 7
                WHEN sub.traffic_id IS NOT NULL THEN 3
                WHEN sub.employee_code IS NOT NULL THEN 2
                WHEN sub.portal_type IN (1,2) THEN 4
                ELSE 1
           END AS createdSourceMigration,
           CASE
               WHEN sme_progress.name = ''Hủy'' OR sub.status IN (3, 4) THEN sub.cancelled_time
           END AS cancelledTime,
           CASE
               WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user
               WHEN sub.traffic_source = ''accesstrade'' THEN sub.traffic_user
               WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one
           END AS affiliateCode
        FROM vnpt_dev.subscriptions sub
            JOIN vnpt_dev.view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id
            LEFT JOIN vnpt_dev.users AS sme ON sub.user_id = sme.id
            LEFT JOIN vnpt_dev.users AS register ON sub.registed_by = register.id
            LEFT JOIN vnpt_dev.order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id
            LEFT JOIN vnpt_dev.order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)
            LEFT JOIN vnpt_dev.sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id
            LEFT JOIN vnpt_dev.view_report_sub_services AS subServices ON (sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id) or (sub.pricing_id IS NULL AND sub.service_id = subServices.service_id)
            LEFT JOIN vnpt_dev.view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id
        WHERE
              sub.deleted_flag = 1 AND
              sub.confirm_status = 1 AND
              (COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) -- ON lay het, OS chi lay neu co trang thai
                OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 3)
                OR (sub.os_3rd_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 2)) AND
              (%5$s = -1 OR sme.province_id = %5$s) AND
              (''%6$s'' = ''ALL'' OR sme.customer_type = ''%6$s'') AND
              (''%19$s'' = ''ALL'' OR (sub.service_group_id is not null AND ''SERVICE_GROUP''= ''%19$s'') OR (subServices.sub_type is not null AND ''SERVICE''= ''%19$s'') OR (subCombo.sub_type is not null AND ''COMBO''= ''%19$s'')) AND
              (''%22$s'' = ''ALL'' OR sub.employee_code = ''%22$s'') AND
              (%13$s = -2 OR sub.status = %13$s) AND
              (%14$s = -1 OR sub.service_id = %14$s) AND
              (
                (
                     subServices.sub_type is not null AND
                     (%20$s = -1 OR subServices.categories_id = %20$s) AND
                     (%16$s = -1 OR sub.pricing_id = %16$s) AND
                     (''%17$s'' = ''-1'' OR  sub.pricing_id = ANY ((''{'' || ''%17$s'' || ''}'')::int8[]))
                 ) OR
                 (
                      subCombo.sub_type is not null AND
                      (''%21$s'' = ''-1'' OR string_to_array(subCombo.categories_id, '','') && string_to_array(''%21$s'', '','')) AND
                      (%16$s = -1 OR ''%18$s'' = ''-1'' OR sub.combo_plan_id = ANY ((''{'' || ''%18$s'' || ''}'')::int8[])) AND
                      (%14$s = -1 OR ''%15$s'' = ''-1'' OR subCombo.combo_id = ANY ((''{'' || ''%15$s'' || ''}'')::int8[]))
                 )
              ) AND
              (''%28$s'' = ''-1'' OR COALESCE(subServices.provider_id, subCombo.provider_id) = ANY((''{'' || ''%28$s'' || ''}'')::int8[])) AND
              (''%29$s'' = ''-1'' OR bill.aff_agency_user_id = ANY((''{'' || ''%29$s'' || ''}'')::int8[]))
)
SELECT
            subReportCTE.id AS subId,
            subReportCTE.billId AS billId
       FROM subReportCTE
       WHERE
            (%23$s = -1 OR subReportCTE.subscriptionState = %23$s) AND
            (''%30$s'' = ''-1'' OR subReportCTE.serviceOwner = ''%30$s'') AND
            (CAST(''%31$s'' AS date) = cast(''1970-01-01'' as date) OR cast(subReportCTE.paymentTime as date) >= CAST(''%31$s'' AS date)) AND
            (CAST(''%32$s'' AS date) = cast(''3000-01-01'' as date) OR cast(subReportCTE.paymentTime as date) <= CAST(''%32$s'' AS date)) AND
            (CAST(''%33$s'' AS date) = cast(''1970-01-01'' as date) OR cast(subReportCTE.installedTime as date) >= CAST(''%33$s'' AS date)) AND
            (CAST(''%34$s'' AS date) = cast(''3000-01-01'' as date) OR cast(subReportCTE.installedTime as date) <= CAST(''%34$s'' AS date)) AND
            (cast(subReportCTE.registrationDate as date) >= CAST(''%11$s'' AS date)) AND
            (cast(subReportCTE.registrationDate as date) <= CAST(''%12$s'' AS date)) AND
            (''%24$s'' = ''-1'' OR subReportCTE.creator = ''%24$s'') AND
            (''%25$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') >= ''%25$s'') AND
            (''%26$s'' = '''' OR to_char(subReportCTE.cancelledTime, ''YYYY-MM-DD'') <= ''%26$s'') AND
            (''%8$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%8$s'') AND
            (''%9$s'' = '''' OR to_char(subReportCTE.migrateTime, ''YYYY-MM-DD'') >= ''%9$s'') AND
            (
                    (subReportCTE.createdSourceMigration <> 5) AND
                    ( -- Do RK lưu created_source khác với code BE --
                        %7$s = -1 OR (%7$s IN (1, 2, 3, 4, 6, 7, 8) AND %7$s = subReportCTE.createdSourceMigration)
                    )
            ) AND
            (''%27$s'' = '''' OR subReportCTE.affiliateCode = ''%27$s'') AND
            (''%3$s'' = '''' OR subReportCTE.smeName ILIKE (''%%'' || ''%3$s'' || ''%%'')) AND
            (''%2$s'' = '''' OR subReportCTE.email = ''%2$s'')
            ORDER BY subReportCTE.registrationDate DESC
';

BEGIN run_query = format(raw_query, is_target, i_customer_email, i_customer_name, i_object_id,
                         i_province_id, i_customer_type, i_createdsource, i_migrate_start_date,
                         i_migrate_end_date, i_migrate_codes, i_start_date, i_end_date,
                         i_status, i_service_id, i_combo_ids, i_pricing_id, i_pricing_ids,
                         i_combo_plan_ids, i_subscription_type, i_category_service, i_category_combo,
                         i_employee_code, i_subscription_state, i_creator, i_cancelled_time_start,
                         i_cancelled_time_end, i_affiliate_code, i_provider_ids, i_aff_agency_user_ids,
                                                 i_service_owner, i_start_payment_date, i_end_payment_date, i_start_installed_date, i_end_installed_date);
RAISE
NOTICE 'Run query: %', run_query;
RETURN QUERY EXECUTE run_query;
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;