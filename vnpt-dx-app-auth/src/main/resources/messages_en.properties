error.length=less than {0} and great than {1}
error.object.not.found=Item `{0}` does not exist or has been deleted
error.object.not.exist={0} (with {1} = {2}) does not exist
error.api.exception={0}
error.field.must.be.not.null=Field `{0}` must be not null
error.duplicate.name=duplicate name
error.duplicate.value= Value {0} currently existed
error.duplicate.identityNo = duplicate identityNo personal
error.field.wrong.format=`{0}` is an invalid format
error.object.being.used.in.another.object=WARNING your object being used in another object
error.can.not.delete.has.child=the record could not be deleted because of has child
error.can.not.delete.association=the record could not be deleted because of an association
error.invalid.file=invalid file
error.wrong.type=wrong type
error.field.too.long={0} too long
error.unsupported={0} unsupported {1}
error.io=io
error.field.must.be.null=Field `{0}` must be null
error.invalid.status=invalid status
error.invalid.field=field {0} invalid
error.forbbiden.resource=forbbiden resource
error.reset.token=invald reset token
error.data.format = Invalid data format
error.invalid.website= Website Invalid
error.vaild.value = invalid value
error.object.exist = object exits
error.object.not.root.admin = user is not root admin
error.file.excel.missing.column = File missing columns
error.file.excel.redundancy.column = File redundancy columns
error.invalid.can.not.change=Field {0} can not change value
error.data.out.of.range = Data value is out of range
error.invalid.data=Invalid data `{0}`
#Category
error.category.wrong.type=wrong category type
error.category.can.not.update=can not update


#Ticket
error.ticket.has.been.assigned=ticket has been assigned


#Validate annotation
error.valid.assert.false=Must be false.
error.valid.assert.true=Must be true.
error.valid.decimal.max=Must be less than ${inclusive == true ? 'or equal to ':''}{value}.
error.valid.decimal.min=Must be greater than ${inclusive == true ? 'or equal to ':''}{value}.
error.valid.digits=Numeric value out of bounds (<{integer} digits>.<{fraction} digits> expected).
error.valid.email=Must be a well-formed email address.
error.valid.future=Must be a future date.
error.valid.future.or.present=Must be a date in the present or in the future.
error.valid.max=Must be less than or equal to {1}.
error.valid.min=Must be greater than or equal to {1}.
error.valid.negative=Must be less than 0.
error.valid.negative.or.zero=Must be less than or equal to 0.
error.valid.not.blank=Must not be blank.
error.valid.not.empty=Must not be empty.
error.valid.null=Must be null.
error.valid.not.null=Field cannot NULL.
error.valid.range=Must be between {2} and {1}.
error.valid.past=Must be a past date.
error.valid.past.or.present=Must be a date in the past or in the present.
error.valid.pattern=Must match "{1}".
error.valid.positive=Must be greater than 0.
error.valid.positive.or.zero=Must be greater than or equal to 0.
error.valid.size=Size must be between {2} and {1}.
error.access.denied = Access denied
error.access.unauthorized = Full authentication is required to access this resource
error.valid.phone.pattern=Invalid phone number
error.invalid.send.sms=Invalid send sms gateway
error.invalid.verify.otp=Invalid verify otp
error.invalid.overdue.verify.otp=Invalid overdue verify otp
error.invalid.expired.invitation.link=Expired invitation link
error.invalid.used.invitation.link=Invitation link already used
error.not.due.yet.refresh.otp=Error not due yet refresh otp
error.portal.type.not.support=Portal type not supported

error.valid.length=Size must be between {2} and {1}.


#change password user
invalid.email=invalid email
invalid.phone.number=invalid phone number
invalid.rep.personal.cert.number=invalid rep personal cert number
invalid.password=invalid password
id.must.be.null=id must be null
passwords.do.not.match=passwords do not match
confirm.passwords.do.not.match=confirm passwords do not match
invalid.type=invalid type
the.new.password.must.be.different.from.the.old.one=the new password must be different from the old one
error.enter.information=Enter your complete information
error.user.disable=User disabled
error.user.not.activated = User is not activated
error.user.deny.personal.data.policy=User deny personal data policy 
error.existed.email = Email already exists
error.master.data.api=Has an error when call master data api
error.wrong.password=Wrong password

#\check exist
exists={0} exists
error.data.exists.not.active={0} exists not active.
error.data.exists.email.tax.code.not.active=exists email and tax code not active.

bad.request=Bad request
error.transaction.api = Has an error when call transaction api.
error.user.techId = Tech id already existed
error.invalid.captcha.token = Invalid ReCaptcha Token.
error.invalid.token = Invalid token
error.user.not.register = User not register

# Validate user activation
error.user.activated = User [{0}] is activated
error.user.activation.key.expired = Activation Key is expired
error.user.activation.key.not.matched = Activation Key is not matched

# Check duplicate phone/email when update/create sme account
error.exists.email = Exists email dev/admin
error.exists.email.display.email.list = Exists emails {0}
error.exists.phone = Exists phone dev/admin/employee
error.exists.phone.colleague= Duplicate phone with other colleague(s) in a same enterprise
error.exists.email.colleague= Duplicate email with other colleague(s) in a same enterprise
error.exists.email.enterprise.house_hold = Exists email sme/employee
error.exists.email.needs.confirmation = Exists email needs confirmation
error.exists.phone.enterprise.house_hold = Exists phone sme/employee
error.exists.email.phone.enterprise.house_hold = Exists email and phone sme/employee
error.exists.email.same.enterprise.household.employee = Exists employee's email in the same enterprise/household
error.exists.phone.same.enterprise.household.employee = Exists employee's phone in the same enterprise/household
error.weak.password= password is weak
error.register.have.exist.other.provider=Exists email registered by other provider

# Data policy
error.not.include.required.data.policies = Not include required policies {0}

# Mobile-app biometric authentication
error.biometric.auth.untrusted.challenge = Untrusted challenge
error.biometric.auth.invalid.challenge.form = Invalid biometric challenge format
error.biometric.auth.invalid.challenge.data = Invalid biometric challenge data
error.biometric.auth.expired.challenge = This challenge is expired
error.biometric.auth.user.device.existed = This device {0} is registered biometric authentication for user {1}
error.biometric.auth.user.device.not.existed = This device {0} is not registered biometric authentication for user {1}
error.biometric.auth.untrusted.e_signature = Untrusted eSignature

# Partner
error.partner.not.found = Partner not found