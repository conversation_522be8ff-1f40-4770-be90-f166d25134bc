package com.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.PathRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.client.endpoint.DefaultAuthorizationCodeTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.OAuth2AuthorizationCodeGrantRequest;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import com.common.aop.props.WhiteListProperties;
import com.onedx.common.utils.SecurityUtil;
import com.security.CustomRequestEntityConverter;
import com.security.oauth2.CustomOAuth2UserService;
import com.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository;
import com.security.oauth2.OAuth2AuthenticationFailureHandler;
import com.security.oauth2.OAuth2AuthenticationSuccessHandler;

@SuppressWarnings("deprecation")
@Configuration
@EnableResourceServer
public class OAuth2ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Autowired
    private DefaultTokenServices tokenServices;

    @Autowired
    private WhiteListProperties whiteList;

    @Autowired
    private HttpCookieOAuth2AuthorizationRequestRepository httpCookieOAuth2AuthorizationRequestRepository;

    @Autowired
    private CustomOAuth2UserService customOAuth2UserService;

    @Autowired
    private OAuth2AuthenticationSuccessHandler successHandler;

    @Autowired
    private OAuth2AuthenticationFailureHandler failureHandler;

    @Override
    public void configure(final HttpSecurity http) throws Exception {
        http.headers().xssProtection().and().contentSecurityPolicy("script-src 'self'");
        String[] lstWhitelistApi = SecurityUtil.parseWhiteListApis(whiteList.getApis());
        http.csrf().disable().authorizeRequests()
            .requestMatchers(PathRequest.toStaticResources().atCommonLocations()).permitAll()
            // Whitelist APIs in YML
            .antMatchers(lstWhitelistApi).permitAll()
            // Whitelist APIs for Swagger
            .antMatchers("/swagger-ui/**").permitAll()
            .antMatchers("/swagger-resources/**").permitAll()
            .antMatchers("/resources/**").permitAll()
            .antMatchers("/api/v3/**").permitAll()
            .antMatchers("/v3/api-docs/**").permitAll()
            .antMatchers("/swagger-ui.html").permitAll()
            .antMatchers("/actuator/**").permitAll()
            // Others
            .antMatchers("/login").permitAll()
            .antMatchers("/auth/**", "/oauth2/**").permitAll()
            .antMatchers("/sso/**").permitAll()
            .antMatchers("/api/sme-users/**").permitAll()
            .antMatchers("/3rd-party/**").permitAll()
            .antMatchers(HttpMethod.POST, "/oauth/login").permitAll()
            .antMatchers("/verify-otp-login").permitAll()
            .antMatchers("/oauth/refresh-token").permitAll()
            .antMatchers("/oauth/token").permitAll()
            .antMatchers("/oauth/token/revokeById/**").permitAll()
            .antMatchers("/api/users-dev/**").permitAll()
            .antMatchers("/api/users-sme/**").permitAll()
            .antMatchers("/token/revoke").permitAll()
            .antMatchers("/api/users/verify/**").permitAll()
            .antMatchers("/api/users/{id}/reset-password/{resetToken}").permitAll()
            .antMatchers("/api/users/forgot-password").permitAll()
            .antMatchers("/api/users/verify-otp-reset-password").permitAll()
            .antMatchers("/api/users/countries/**").permitAll()
            .antMatchers("/api/users/provinces/**").permitAll()
            .antMatchers("/api/users/districts/**").permitAll()
            .antMatchers("/api/users/business/areas/**").permitAll()
            .antMatchers("/api/users/business/scale/**").permitAll()
            .antMatchers("/api/users-sme/verify-otp-activate").permitAll()
            .antMatchers("/api/users-sme/get-tax-code").permitAll()
            .antMatchers("/api/users/ward/{id}/{code}/street").permitAll()
            .anyRequest().authenticated()
            .and()
            .oauth2Login()
            .authorizationEndpoint()
            .baseUri("/oauth2/authorize")
            .authorizationRequestRepository(httpCookieOAuth2AuthorizationRequestRepository)
            .and()
            .redirectionEndpoint()
            .baseUri("/oauth2/callback/*")
            .and()
            .userInfoEndpoint()
            .userService(customOAuth2UserService)
            .and()
            .successHandler(successHandler)
            .failureHandler(failureHandler);
    }

    @Bean
    public OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> accessTokenResponseClient() {
        DefaultAuthorizationCodeTokenResponseClient accessTokenResponseClient = new DefaultAuthorizationCodeTokenResponseClient();
        accessTokenResponseClient.setRequestEntityConverter(new CustomRequestEntityConverter());
        return accessTokenResponseClient;
    }

    @Override
    public void configure(final ResourceServerSecurityConfigurer config) {
        config.tokenServices(tokenServices);
    }
}
