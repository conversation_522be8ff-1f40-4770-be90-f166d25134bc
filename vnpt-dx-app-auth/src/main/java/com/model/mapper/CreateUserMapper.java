package com.model.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import com.model.dto.ImportUserDTO;
import com.model.entity.enterprise.Enterprise;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 14/04/2022 - 4:30 PM
 */
@Mapper(componentModel = "spring", uses = { UserFieldMapper.class })
public interface CreateUserMapper extends EntityMapper<ImportUserDTO, Enterprise> {

	@Mapping(source = "customerType", target = "customerTypeCode", qualifiedByName = "convertCustomerTypeToDTO")
	@Mapping(source = "repIdentityType", target = "identityType", qualifiedByName = "convertIdentityTypeToDTO")
	@Mapping(source = "repIdentityNo", target = "identityNumber")
	@Mapping(source = "repBirthday", target = "birthDate")
	@Mapping(source = "repSex", target = "gender")
	ImportUserDTO toDto(Enterprise e);

	@Mapping(source = "customerTypeCode", target = "customerType", qualifiedByName = "convertCustomerTypeToDB")
	@Mapping(source = "identityType", target = "repIdentityType", qualifiedByName = "convertIdentityTypeToDB")
	@Mapping(source = "identityNumber", target = "repIdentityNo")
	@Mapping(source = "birthDate", target = "repBirthday")
	@Mapping(source = "gender", target = "repSex")
	Enterprise toEntity(ImportUserDTO dto);
}
