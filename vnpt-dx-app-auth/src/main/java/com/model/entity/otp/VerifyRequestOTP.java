package com.model.entity.otp;

import java.util.UUID;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.onedx.common.exception.MessageKeyConstant;
import com.enums.otp.ActionTypeOTPEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VerifyRequestOTP {
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    @JsonProperty("hash")
    @Schema(description = "mã hash otp", example = "b13e6424-a419-4bb6-8f4f-80edc0fc5f80")
    UUID hash;
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    @JsonProperty("code")
    @Schema(description = "mã otp", example = "xyz987")
    String code;
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    @JsonProperty("username")
    @Schema(description = "tên đăng nhập", example = "<EMAIL>")
    String username;

    @JsonProperty("customerType")
    @Schema(description = "Loại khách hàng", example = "CN")
    String customerType;

    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    ActionTypeOTPEnum actionType;
    String clientId = "vnpt_clientid";


}
