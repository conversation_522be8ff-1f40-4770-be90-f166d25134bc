package com.model.request;

import java.util.UUID;
import javax.validation.constraints.NotNull;
import com.onedx.common.exception.MessageKeyConstant.Validation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class AccountUpdatePassReqDTO extends  AccountResetPassReq{

	//	@Pattern(regexp = LoginConst.PASSWORD_REGEX, message = MessageKeyConstant.Validation.PATTERN) // comment do chuoi regex moi duoc update, password cu chua chac da matching
//	@Size(min = 8, max = 16, message = MessageKeyConstant.Validation.SIZE) // comment do mot mat khau cu co the khong matching
	@Schema(description = "Mật khẩu cũ", example = "kasdh7856baA@")
	private String oldPassword;

	@NotNull(message = Validation.NOT_NULL)
	UUID uuid;

}
