package com.model.dto.user.biometric;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(Include.NON_NULL)
public class BiometricChallengeDataDTO {

    @Schema(description = "Id user xác thực")
    Long userId;

    @Schema(description = "MST/email tài khoản")
    String username;

    @Schema(description = "Id thiết bị yêu cầu")
    String deviceId;

    @Schema(description = "Thời gian tạo challenge")
    Date challengeCreatedAt;
}
