package com.model.dto.resquest;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import com.exception.MessageKeyConstant.Validation;
import com.onedx.common.annotation.TrimString;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TrimString(fieldNames = {"username", "portal"})
public class ForgotPasswordDTO {

	@NotNull(message = Validation.NOT_NULL)
	@NotBlank(message = Validation.NOT_BLANK)
	@Size(max = 100, message = Validation.SIZE)
	private String username;

	@NotNull(message = "portal type cannot be null")
	@Pattern(regexp = "^SME|BOS|DEV|ADMIN|ADMIN_AFFILIATE|AFFILIATE|MOBILE_APP|IOT_PORTAL$", message = "Portal type not supported")
	private String portal;

	private CustomerTypeEnum customerType;

	private Boolean isChangePasswordWorkplace = false; // true khi đổi mật khẩu trên workplace
}
