package com.model.dto;

import java.sql.Date;
import java.util.HashSet;
import java.util.Set;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import com.exception.MessageKeyConstant;
import com.exception.MessageKeyConstant.Validation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.OptBoolean;
import com.model.request.AccountReq;
import com.onedx.common.annotation.TrimString;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.constants.enums.users.UserGenderEnum;
import com.onedx.common.constants.values.LoginConst;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@TrimString(fieldNames = {"smeName", "email", "password", "phoneNumber", "address",
    "corporateTaxCode", "firstName", "lastName", "website", "description"},
    groups = {RegisterSME.CreateSME.class})
public class RegisterSME {

    @Size(max = 500, groups = {RegisterSME.CreateSME.class})
    @Schema(description = "Tên doanh nghiệp", example = "Công ty cổ phần ACB ")
    private String smeName;

    @Schema(description = "ID của quy mô doanh nghiệp", example = "1")
    private Long businessScale;

    @Schema(description = "ID lĩnh vực kinh doanh", example = "2")
    private Long businessAreas;

    @NotBlank(groups = {RegisterSME.CreateSME.class}, message = MessageKeyConstant.Validation.NOT_BLANK)
    @Pattern(groups = {RegisterSME.CreateSME.class}, regexp = LoginConst.EMAIL_REGEX, message = MessageKeyConstant.Validation.PATTERN)
    @Size(groups = {RegisterSME.CreateSME.class}, min = 1, max = 100, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Địa chỉ email", example = "<EMAIL>")
    private String email;

    @Pattern(regexp = LoginConst.PASSWORD_REGEX, message = MessageKeyConstant.Validation.PATTERN,
        groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class})
    @Size(min = 8, max = 16, message = MessageKeyConstant.Validation.SIZE,
        groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class})
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK, groups = {
        RegisterSME.CreateSME.class})
    @Schema(description = "Mật khẩu đăng nhập", example = "123456@Aa")
    private String password;

    @Pattern(regexp = LoginConst.PASSWORD_REGEX, message = MessageKeyConstant.Validation.PATTERN,
        groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class})
    @Size(min = 8, max = 16, message = MessageKeyConstant.Validation.SIZE, groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class})
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK, groups = {RegisterSME.CreateSME.class})
    @Schema(description = "Mật khẩu đăng nhập", example = "123456@Aa")
    private String confirmPassword;

    @Pattern(regexp = LoginConst.PHONE_MOBILE_REGEX, message = MessageKeyConstant.Validation.PATTERN,
        groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class,
            AccountReq.BusinessDev.class, AccountReq.BusinessSme.class})
    @NotBlank(groups = {RegisterSME.CreateSME.class}, message = MessageKeyConstant.Validation.NOT_BLANK)
    @Schema(description = "Số điện thoại doanh nghiệp", example = "***********")
    private String phoneNumber;

    @Size(min = 1, max = 500, message = MessageKeyConstant.Validation.SIZE, groups = {
        RegisterSME.CreateSME.class, RegisterDev.Createdev.class,
        AccountReq.BusinessSme.class, AccountReq.BusinessDev.class})
    @Schema(description = "Địa chỉ doanh nghiệp", example = "số 57 Huỳnh Thúc Kháng, quận Đống Đa, thành phố Hà Nội, Việt Nam")
    private String address;

    @Size(min = 1, max = 500, message = MessageKeyConstant.Validation.SIZE, groups = {
        RegisterSME.CreateSME.class, RegisterDev.Createdev.class,
        AccountReq.BusinessSme.class, AccountReq.BusinessDev.class})
    @Schema(description = "Mã nhân viên giới thiệu", example = "57")
    private String employeeCode;

    @JsonProperty("taxCode")
    @NotBlank(groups = {RegisterSME.CreateSME.class}, message = MessageKeyConstant.Validation.NOT_BLANK)
    @Pattern(groups = {RegisterSME.CreateSME.class}, regexp = LoginConst.TIN_REGEX, message = Validation.PATTERN)
    @Schema(description = "Mã số thuế doanh nghiệp", example = "*********")
    private String corporateTaxCode;   // mã số thuế

    @JsonProperty("countryId")
    @NotNull(groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class,
        AccountReq.BusinessSme.class,
        AccountReq.BusinessDev.class}, message = MessageKeyConstant.Validation.NOT_NULL)
    @Schema(description = "ID quốc gia", example = "12")
    private Long nationId;

    @NotNull(groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class,
        AccountReq.BusinessSme.class,
        AccountReq.BusinessDev.class}, message = MessageKeyConstant.Validation.NOT_NULL)
    @Schema(description = "ID tỉnh/thành phố", example = "21")
    private Long provinceId;

    // @NotNull(groups = {RegisterSME.CreateSME.class, RegisterDev.Createdev.class,
    //     AccountReq.BusinessSme.class,
    //     AccountReq.BusinessDev.class}, message = MessageKeyConstant.Validation.NOT_NULL)
    @Schema(description = "Mã tỉnh/thành phố", example = "HNI")
    private String provinceCode;

    @Schema(description = "ID quận/huyện", example = "3")
    private Long districtId;

    @JsonProperty("birthdate")
    @NotNull(groups = {UserDTO.Create.class, UserDTO.UpdateDev.class,
        RegisterDev.Createdev.class}, message = MessageKeyConstant.Validation.NOT_NULL)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy", lenient = OptBoolean.FALSE)
    @Schema(description = "Ngày sinh của người đăng ký tài khoản", example = "08/08/1999")
    private Date birthday;

    @JsonProperty("firstname")
    @NotBlank(groups = {RegisterSME.CreateSME.class,
        RegisterDev.Createdev.class}, message = MessageKeyConstant.Validation.NOT_BLANK)
    @Size(min = 1, max = 20, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Tên của người đăng ký tài khoản", example = "Hải")
    private String firstName;

    @JsonProperty("lastname")
    @NotBlank(groups = {RegisterSME.CreateSME.class,
        RegisterDev.Createdev.class}, message = MessageKeyConstant.Validation.NOT_BLANK)
    @Size(min = 1, max = 20, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Họ và tên đẹm của người đăng ký tài khoản", example = "Tống Duy")
    private String lastName;

    @Size(max = 100, message = MessageKeyConstant.Validation.SIZE, groups = {
        RegisterSME.CreateSME.class})
    @Pattern(regexp = LoginConst.URL_REGEX, message = MessageKeyConstant.Validation.PATTERN, groups = {
        RegisterSME.CreateSME.class})
    @Schema(description = "Website của doanh nghiệp", example = "rikkei.vn")
    private String website;

    @Schema(description = "Mô tả thêm")
    private String description;

    private Set<RoleDTO> roles;

    @Schema(description = "Giới tính nhà phát triển", example = "MALE")
    private UserGenderEnum gender;

    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL, groups = {
        RegisterSME.CreateSME.class})
    @Schema(description = "Đường dẫn chuyển hướng sang port tương ứng cho SME|Admin|Developer")
    private String redirectUrl;

    @Schema(description = "ID đường", example = "3")
    private Long streetId;

    @Schema(description = "ID phường/xã", example = "3")
    private Long wardId;

    @Size(max = 13, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Mã số BHXH", example = "1234567890")
    private String socialInsuranceNumber;

//    @NotBlank(groups = {RegisterSME.CreateSME.class}, message = Validation.NOT_BLANK)
    @Schema(description = "Captcha token")
    private String captchaToken;

    @NotNull(groups = {
        RegisterSME.CreateSME.class}, message = Validation.NOT_NULL)
    @Schema(description = "Loại khách hàng tạo tài khoản", example = "PERSONAL")
    private String customerType;

    @JsonIgnore
    private Long id;
    
    @Schema(description = "Để biết xem KHDN/HKD có xác nhận đăng ký khi bị trùng thông tin hay không")
    private Boolean isConfirmRegister;

    @Schema(description = "Giá trị email ẩn 5 ký tự trước kí tự @")
    private String emailMask;

    @Schema(description = "Số điện thoại ần 5 kí tự cuối cùng")
    private String phoneMask;
    
    @Schema(description = "Giá trị hash của otp")
    private String hash;

    @Schema(description = "Địa chỉ IP")
    private String ipAddress;

    private Set<Integer> confirmedDataPolicies = new HashSet<>();
    public interface CreateSME {

    }

    //nguồn tạo từ bên thứ 3 (hỗ trợ tích hợp IDC Portals), default bằng 0 là từ web của SME, 1 là từ IDC Portal
    private Integer source3rdParty = 0;

    //trường hớp thứ 4 của task http://jira.vnpt-technology.vn/browse/SPC_SUPPORTONESME-256 (truyền thêm userId)
    private Long userId;

    //Nguồn tạo
    private CreatedSourceMigrationEnum createdSourceMigrationEnum;

    // Bổ sung thông tin mapping user onesme vs tk idp
    private String techId;

    // Tạo cho partner
    private String partnerId;

    private String apiKey;
}
