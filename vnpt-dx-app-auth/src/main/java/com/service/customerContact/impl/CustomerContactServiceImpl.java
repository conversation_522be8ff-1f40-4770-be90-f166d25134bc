package com.service.customerContact.impl;

import org.springframework.stereotype.Service;
import com.repository.customerContact.CustomerContactRepository;
import com.service.customerContact.CustomerContactService;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CustomerContactServiceImpl implements CustomerContactService {

    private final CustomerContactRepository contactRepository;

    @Override
    public void onConvertedToEnterprise(Long contactId, Long enterpriseId, Long userId) {
        // Chuyển đổi các báo giá tạo cho liên hệ sang đối tượng khách hàng. TODO: <PERSON><PERSON><PERSON> cách khác không sử dụng chéo DB
        contactRepository.updateQuotationObject(contactId, enterpriseId, userId);
    }
}
