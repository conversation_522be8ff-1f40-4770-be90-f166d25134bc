package com.service.fieldForce;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.exception.Resources;
import com.model.entity.crm.CrmDataPartition;
import com.model.entity.crm.CrmMappingEnterprisePartition;
import com.model.entity.crm.CrmMappingUserPartition;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.crm.CrmPermissionEnum;
import com.onedx.common.constants.values.BatchConst;
import com.onedx.common.exception.ErrorKey;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.repository.cache.CacheRepository;
import com.repository.crm.CrmDataPartitionRepository;
import com.repository.crm.CrmMappingEnterprisePartitionRepository;
import com.repository.crm.CrmMappingUserPartitionRepository;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CrmDataPartitionUtil {

    @Autowired
    private CrmMappingEnterprisePartitionRepository mappingEnterprisePartitionRepository;
    @Autowired
    private CacheRepository cacheRepository;
    @Autowired
    private CrmMappingUserPartitionRepository mappingUserPartitionRepository;
    @Autowired
    private CrmDataPartitionRepository dataPartitionRepository;

    public void updateEnterpriseMappingPartition(Long enterpriseId) {
        String batchScanEnterpriseStatus = cacheRepository.getStringValue(BatchConst.BATCH_SCAN_ENTERPRISE_STATUS, BatchConst.FALSE);
        log.info("BATCH_SCAN_ENTERPRISE_STATUS is '{}' !", batchScanEnterpriseStatus);
        if(BatchConst.FALSE.compareTo(batchScanEnterpriseStatus) == 0) {
            Set<Long> partitionIdMapping = dataPartitionRepository.listPartitionIdMatchedObjectAndCondition(
                CrmObjectTypeEnum.ENTERPRISE.getValue(), enterpriseId);
            CrmMappingEnterprisePartition mappingEnterprisePartition = new CrmMappingEnterprisePartition();
            mappingEnterprisePartition.setEnterpriseId(enterpriseId);
            mappingEnterprisePartition.setLstPartitionId(partitionIdMapping.toArray(new Long[0]));
            mappingEnterprisePartitionRepository.save(mappingEnterprisePartition);
            cacheRepository.saveStringValue(BatchConst.BATCH_SCAN_ENTERPRISE_PARTITION_CHANGED, BatchConst.FALSE);
        } else {
            log.warn("Skip scanning mapping for enterprise because of job running!");
        }
    }
    public void updateUserMappingPartition(Long userId) {
        String batchScanUserStatus = cacheRepository.getStringValue(BatchConst.BATCH_SCAN_USER_STATUS, BatchConst.FALSE);
        log.info("BATCH_SCAN_USER_STATUS is '{}' !", batchScanUserStatus);
        if(BatchConst.FALSE.compareTo(batchScanUserStatus) == 0) {
            Long start = System.currentTimeMillis();
            log.info(" startTime : {} for userId : {}", start, userId );
            Set<Long> partitionIdMapping = dataPartitionRepository.listPartitionIdMatchedObjectAndCondition(
                CrmObjectTypeEnum.USER.getValue(), userId);
            Long end = System.currentTimeMillis();
            log.info(" endTime : {}", end );
            log.info(" updateUserMappingPartition total time : {} ms for userId: {}", (end - start), userId );
            CrmMappingUserPartition mappingUserPartition = new CrmMappingUserPartition();
            mappingUserPartition.setUserId(userId);
            mappingUserPartition.setLstPartitionId(partitionIdMapping.toArray(new Long[0]));
            mappingUserPartitionRepository.save(mappingUserPartition);
            cacheRepository.saveStringValue(BatchConst.BATCH_SCAN_USER_PARTITION_CHANGED, BatchConst.FALSE);
        } else {
            log.warn("Skip scanning mapping for user because of job running!");
        }
    }

    public Set<Long> getPartitionIdByObjectRole(Long adminId, CrmPermissionEnum permissionEnum) {
        Set<Long> havePermissionToObject = dataPartitionRepository.listPartitionIdHavePermissionToObject(adminId, permissionEnum.getValue());
        Set<Long> havePermissionToChildObject = dataPartitionRepository.listPartitionIdHavePermissionToChildObject(adminId,
            permissionEnum.getValue());
        Set<Long> output = new HashSet<>(havePermissionToObject);

        List<CrmDataPartition> lstAllPartition = dataPartitionRepository.findAll();
        for (Long partitionId : havePermissionToChildObject) {
            CrmDataPartition partition = lstAllPartition.stream().filter(item -> Objects.equals(item.getId(), partitionId)).findFirst()
                .orElseThrow(() -> new ResourceNotFoundException(MessageKeyConstant.NOT_FOUND, Resources.DATA_PARTITION, ErrorKey.ID,
                    String.valueOf(partitionId)));
            appendChildPartitionId(partition, output, lstAllPartition);
        }
        return output;
    }

    private void appendChildPartitionId(CrmDataPartition partition, Set<Long> output, List<CrmDataPartition> lstAllPartition) {
        for (CrmDataPartition child : lstAllPartition) {
            if (Objects.equals(child.getParentId(), partition.getId()) && !output.contains(child.getId())) {
                output.add(child.getId());
                appendChildPartitionId(child, output, lstAllPartition);
            }
        }
    }
}
