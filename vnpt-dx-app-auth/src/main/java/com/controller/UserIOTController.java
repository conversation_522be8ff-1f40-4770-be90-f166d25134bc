package com.controller;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import com.model.dto.RegisterPersonalDTO;
import com.model.dto.RegisterSME;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Validated
@RestController
@RequestMapping("/api/iot-portal/users")
@Slf4j
public class UserIOTController {

    @Autowired
    private UserService userService;

    /**
     * <PERSON><PERSON><PERSON> ký tài khoản SME
     */
    @PostMapping("/register")
    @Operation(description = "Đăng kí tài khoản SME")
    @ResponseStatus(HttpStatus.CREATED)
    public RegisterSME registerSme(
        @Validated(RegisterSME.CreateSME.class) @RequestBody RegisterSME accountReq,
        HttpServletRequest request
    ) {
        accountReq.setCreatedSourceMigrationEnum(CreatedSourceMigrationEnum.IOT_PORTAL);
        return userService.registerSme(accountReq, request, PortalType.IOT);
    }

    /**
     * đăng ký user sme, được login cả dev portal
     *
     */
    @PostMapping("/register-personal")
    @Operation(description = "Đăng ký tài khoản bằng tk cá nhân")
    @ResponseStatus(HttpStatus.CREATED)
    RegisterPersonalDTO registryPersonal(@Validated(com.model.dto.RegisterPersonalDTO.CreateSME.class)
    @RequestBody RegisterPersonalDTO accountReq, HttpServletRequest httpServletRequest) {
        accountReq.setPortalType(PortalType.IOT);
        return userService.registerPersonal(accountReq, httpServletRequest, PortalType.IOT);

    }

}
